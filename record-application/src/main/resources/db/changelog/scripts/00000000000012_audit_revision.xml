<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd">
    
    <changeSet id="audit_log_revision_seq" author="system">
        <comment>Create sequence for audit log revision</comment>
        <createSequence cacheSize="1" 
                        cycle="false" 
                        dataType="bigint" 
                        incrementBy="50" 
                        maxValue="9223372036854775807"
                        minValue="1" 
                        sequenceName="audit_log_revision_seq" 
                        startValue="1"
                        schemaName="record"/>
    </changeSet>
    
    <changeSet id="audit_log_revision_createTable" author="system">
        <comment>Create audit log revision table</comment>
        <createTable tableName="audit_log_revision" schemaName="record">
            <column name="audit_log_revision_id" type="INTEGER">
                <constraints nullable="false" primaryKey="true" primaryKeyName="audit_log_revision_pkey"/>
            </column>
            <column name="timestamp" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="auditor" type="VARCHAR(255)"/>
        </createTable>
    </changeSet>
    
</databaseChangeLog>
