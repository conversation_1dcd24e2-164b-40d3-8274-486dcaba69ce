<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
        http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.20.xsd">

    <!-- ============================================ -->
    <!-- ADD CONVERSION_REFERENCE TO MAIN TABLES -->
    <!-- ============================================ -->

<!--    <changeSet id="00000000000010-1" author="system">-->
<!--        <comment>Add conversion_reference column to record table</comment>-->
<!--        <addColumn tableName="record">-->
<!--            <column name="conversion_reference" type="VARCHAR(255)"/>-->
<!--        </addColumn>-->
<!--    </changeSet>-->

<!--    <changeSet id="00000000000010-2" author="system">-->
<!--        <comment>Add conversion_reference column to record_type table</comment>-->
<!--        <addColumn tableName="record_type">-->
<!--            <column name="conversion_reference" type="VARCHAR(255)"/>-->
<!--        </addColumn>-->
<!--    </changeSet>-->

    <changeSet id="00000000000010-3" author="system">
        <comment>Add conversion_reference column to association table</comment>
        <addColumn tableName="association">
            <column name="conversion_reference" type="VARCHAR(255)"/>
        </addColumn>
    </changeSet>

    <changeSet id="00000000000010-4" author="system">
        <comment>Add conversion_reference column to association_type table</comment>
        <addColumn tableName="association_type">
            <column name="conversion_reference" type="VARCHAR(255)"/>
        </addColumn>
    </changeSet>

    <changeSet id="00000000000010-5" author="system">
        <comment>Add conversion_reference column to document table</comment>
        <addColumn tableName="document" schemaName="record">
            <column name="conversion_reference" type="VARCHAR(255)"/>
        </addColumn>
    </changeSet>

    <changeSet id="00000000000010-6" author="system">
        <comment>Add conversion_reference column to document_type table</comment>
        <addColumn tableName="document_type" schemaName="record">
            <column name="conversion_reference" type="VARCHAR(255)"/>
        </addColumn>
    </changeSet>

    <changeSet id="00000000000010-7" author="system">
        <comment>Add conversion_reference column to code_lookup table</comment>
        <addColumn tableName="code_lookup" schemaName="record">
            <column name="conversion_reference" type="VARCHAR(255)"/>
        </addColumn>
    </changeSet>

    <changeSet id="00000000000010-8" author="system">
        <comment>Add conversion_reference column to entity_fee table</comment>
        <addColumn tableName="entity_fee" schemaName="record">
            <column name="conversion_reference" type="VARCHAR(255)"/>
        </addColumn>
    </changeSet>

    <changeSet id="00000000000010-9" author="system">
        <comment>Add conversion_reference column to entity_group table</comment>
        <addColumn tableName="entity_group" schemaName="record">
            <column name="conversion_reference" type="VARCHAR(255)"/>
        </addColumn>
    </changeSet>

    <changeSet id="00000000000010-10" author="system">
        <comment>Add conversion_reference column to entity_note table</comment>
        <addColumn tableName="entity_note" schemaName="record">
            <column name="conversion_reference" type="VARCHAR(255)"/>
        </addColumn>
    </changeSet>

    <changeSet id="00000000000010-11" author="system">
        <comment>Add conversion_reference column to app_properties table</comment>
        <addColumn tableName="app_properties" schemaName="record">
            <column name="conversion_reference" type="VARCHAR(255)"/>
        </addColumn>
    </changeSet>

    <changeSet id="00000000000010-12" author="system">
        <comment>Add conversion_reference column to module_definition table</comment>
        <addColumn tableName="module_definition">
            <column name="conversion_reference" type="VARCHAR(255)"/>
        </addColumn>
    </changeSet>

    <!-- ============================================ -->
    <!-- MODIFY CREATED_BY AND LAST_MODIFIED_BY COLUMNS IN MAIN TABLES -->
    <!-- ============================================ -->

    <changeSet id="00000000000010-13" author="system">
        <comment>Modify created_by and last_modified_by columns in record table</comment>
        <modifyDataType tableName="record" columnName="created_by" newDataType="VARCHAR(1000)"/>
        <modifyDataType tableName="record" columnName="last_modified_by" newDataType="VARCHAR(1000)"/>
        <addDefaultValue tableName="record" columnName="created_by" defaultValueComputed="current_user"/>
        <addDefaultValue tableName="record" columnName="last_modified_by" defaultValueComputed="current_user"/>
    </changeSet>

    <changeSet id="00000000000010-14" author="system">
        <comment>Modify created_by and last_modified_by columns in record_type table</comment>
        <modifyDataType tableName="record_type" columnName="created_by" newDataType="VARCHAR(1000)"/>
        <modifyDataType tableName="record_type" columnName="last_modified_by" newDataType="VARCHAR(1000)"/>
        <addDefaultValue tableName="record_type" columnName="created_by" defaultValueComputed="current_user"/>
        <addDefaultValue tableName="record_type" columnName="last_modified_by" defaultValueComputed="current_user"/>
    </changeSet>

    <changeSet id="00000000000010-15" author="system">
        <comment>Modify created_by and last_modified_by columns in association table</comment>
        <modifyDataType tableName="association" columnName="created_by" newDataType="VARCHAR(1000)"/>
        <modifyDataType tableName="association" columnName="last_modified_by" newDataType="VARCHAR(1000)"/>
        <addDefaultValue tableName="association" columnName="created_by" defaultValueComputed="current_user"/>
        <addDefaultValue tableName="association" columnName="last_modified_by" defaultValueComputed="current_user"/>
    </changeSet>

    <changeSet id="00000000000010-16" author="system">
        <comment>Modify created_by and last_modified_by columns in association_type table</comment>
        <modifyDataType tableName="association_type" columnName="created_by" newDataType="VARCHAR(1000)"/>
        <modifyDataType tableName="association_type" columnName="last_modified_by" newDataType="VARCHAR(1000)"/>
        <addDefaultValue tableName="association_type" columnName="created_by" defaultValueComputed="current_user"/>
        <addDefaultValue tableName="association_type" columnName="last_modified_by" defaultValueComputed="current_user"/>
    </changeSet>

    <changeSet id="00000000000010-17" author="system">
        <comment>Add default values to created_by and last_modified_by columns in document table</comment>
        <addDefaultValue tableName="document" schemaName="record" columnName="created_by" defaultValueComputed="current_user"/>
        <addDefaultValue tableName="document" schemaName="record" columnName="last_modified_by" defaultValueComputed="current_user"/>
    </changeSet>

    <changeSet id="00000000000010-18" author="system">
        <comment>Add default values to created_by and last_modified_by columns in document_type table</comment>
        <addDefaultValue tableName="document_type" schemaName="record" columnName="created_by" defaultValueComputed="current_user"/>
        <addDefaultValue tableName="document_type" schemaName="record" columnName="last_modified_by" defaultValueComputed="current_user"/>
    </changeSet>

    <changeSet id="00000000000010-19" author="system">
        <comment>Add default values to created_by and last_modified_by columns in code_lookup table</comment>
        <addDefaultValue tableName="code_lookup" schemaName="record" columnName="created_by" defaultValueComputed="current_user"/>
        <addDefaultValue tableName="code_lookup" schemaName="record" columnName="last_modified_by" defaultValueComputed="current_user"/>
    </changeSet>

    <changeSet id="00000000000010-20" author="system">
        <comment>Add default values to created_by and last_modified_by columns in entity_fee table</comment>
        <addDefaultValue tableName="entity_fee" schemaName="record" columnName="created_by" defaultValueComputed="current_user"/>
        <addDefaultValue tableName="entity_fee" schemaName="record" columnName="last_modified_by" defaultValueComputed="current_user"/>
    </changeSet>

    <changeSet id="00000000000010-21" author="system">
        <comment>Add default values to created_by and last_modified_by columns in entity_group table</comment>
        <addDefaultValue tableName="entity_group" schemaName="record" columnName="created_by" defaultValueComputed="current_user"/>
        <addDefaultValue tableName="entity_group" schemaName="record" columnName="last_modified_by" defaultValueComputed="current_user"/>
    </changeSet>

    <changeSet id="00000000000010-22" author="system">
        <comment>Add default values to created_by and last_modified_by columns in entity_note table</comment>
        <addDefaultValue tableName="entity_note" schemaName="record" columnName="created_by" defaultValueComputed="current_user"/>
        <addDefaultValue tableName="entity_note" schemaName="record" columnName="last_modified_by" defaultValueComputed="current_user"/>
    </changeSet>

    <changeSet id="00000000000010-23" author="system">
        <comment>Modify created_by and last_modified_by columns in app_properties table</comment>
        <modifyDataType tableName="app_properties" schemaName="record" columnName="created_by" newDataType="VARCHAR(1000)"/>
        <modifyDataType tableName="app_properties" schemaName="record" columnName="last_modified_by" newDataType="VARCHAR(1000)"/>
        <addDefaultValue tableName="app_properties" schemaName="record" columnName="created_by" defaultValueComputed="current_user"/>
        <addDefaultValue tableName="app_properties" schemaName="record" columnName="last_modified_by" defaultValueComputed="current_user"/>
    </changeSet>

    <changeSet id="00000000000010-24" author="system">
        <comment>Modify created_by and last_modified_by columns in module_definition table</comment>
        <modifyDataType tableName="module_definition" columnName="created_by" newDataType="VARCHAR(1000)"/>
        <modifyDataType tableName="module_definition" columnName="last_modified_by" newDataType="VARCHAR(1000)"/>
        <addDefaultValue tableName="module_definition" columnName="created_by" defaultValueComputed="current_user"/>
        <addDefaultValue tableName="module_definition" columnName="last_modified_by" defaultValueComputed="current_user"/>
    </changeSet>

    <!-- ============================================ -->
    <!-- RENAME CREATED_AT/LAST_MODIFIED_AT IN APP_PROPERTIES TABLE -->
    <!-- ============================================ -->

    <changeSet id="00000000000010-25" author="system">
        <comment>Rename audit columns in app_properties table</comment>
        <renameColumn schemaName="record"
                      tableName="app_properties"
                      oldColumnName="created_at"
                      newColumnName="created_date"
                      columnDataType="TIMESTAMP WITH TIME ZONE"/>
        <renameColumn schemaName="record"
                      tableName="app_properties"
                      oldColumnName="last_modified_at"
                      newColumnName="last_modified_date"
                      columnDataType="TIMESTAMP WITH TIME ZONE"/>
    </changeSet>

    <!-- ============================================ -->
    <!-- MODIFY CREATED_DATE AND LAST_MODIFIED_DATE TO TIMESTAMP WITH TIME ZONE -->
    <!-- ============================================ -->

    <changeSet id="00000000000010-26" author="system">
        <comment>Modify created_date and last_modified_date columns in record table</comment>
        <modifyDataType tableName="record" columnName="created_date" newDataType="TIMESTAMP WITH TIME ZONE"/>
        <modifyDataType tableName="record" columnName="last_modified_date" newDataType="TIMESTAMP WITH TIME ZONE"/>
        <addDefaultValue tableName="record" columnName="created_date" defaultValueComputed="now()"/>
        <addDefaultValue tableName="record" columnName="last_modified_date" defaultValueComputed="now()"/>
    </changeSet>

    <changeSet id="00000000000010-27" author="system">
        <comment>Modify created_date and last_modified_date columns in record_type table</comment>
        <modifyDataType tableName="record_type" columnName="created_date" newDataType="TIMESTAMP WITH TIME ZONE"/>
        <modifyDataType tableName="record_type" columnName="last_modified_date" newDataType="TIMESTAMP WITH TIME ZONE"/>
        <addDefaultValue tableName="record_type" columnName="created_date" defaultValueComputed="now()"/>
        <addDefaultValue tableName="record_type" columnName="last_modified_date" defaultValueComputed="now()"/>
    </changeSet>

    <changeSet id="00000000000010-28" author="system">
        <comment>Modify created_date and last_modified_date columns in association table</comment>
        <modifyDataType tableName="association" columnName="created_date" newDataType="TIMESTAMP WITH TIME ZONE"/>
        <modifyDataType tableName="association" columnName="last_modified_date" newDataType="TIMESTAMP WITH TIME ZONE"/>
        <addDefaultValue tableName="association" columnName="created_date" defaultValueComputed="now()"/>
        <addDefaultValue tableName="association" columnName="last_modified_date" defaultValueComputed="now()"/>
    </changeSet>

    <changeSet id="00000000000010-29" author="system">
        <comment>Modify created_date and last_modified_date columns in association_type table</comment>
        <modifyDataType tableName="association_type" columnName="created_date" newDataType="TIMESTAMP WITH TIME ZONE"/>
        <modifyDataType tableName="association_type" columnName="last_modified_date" newDataType="TIMESTAMP WITH TIME ZONE"/>
        <addDefaultValue tableName="association_type" columnName="created_date" defaultValueComputed="now()"/>
        <addDefaultValue tableName="association_type" columnName="last_modified_date" defaultValueComputed="now()"/>
    </changeSet>

    <changeSet id="00000000000010-30" author="system">
        <comment>Modify created_date and last_modified_date columns in module_definition table</comment>
        <modifyDataType tableName="module_definition" columnName="created_date" newDataType="TIMESTAMP WITH TIME ZONE"/>
        <modifyDataType tableName="module_definition" columnName="last_modified_date" newDataType="TIMESTAMP WITH TIME ZONE"/>
        <addDefaultValue tableName="module_definition" columnName="created_date" defaultValueComputed="now()"/>
        <addDefaultValue tableName="module_definition" columnName="last_modified_date" defaultValueComputed="now()"/>
    </changeSet>

    <changeSet id="00000000000010-31" author="system">
        <comment>Add default values to created_date and last_modified_date columns in app_properties table</comment>
        <addDefaultValue tableName="app_properties" schemaName="record" columnName="created_date" defaultValueComputed="now()"/>
        <addDefaultValue tableName="app_properties" schemaName="record" columnName="last_modified_date" defaultValueComputed="now()"/>
    </changeSet>

    <!-- ============================================ -->
    <!-- ADD PROPERTIES COLUMN TO TABLES MISSING IT -->
    <!-- ============================================ -->

    <changeSet id="00000000000010-32" author="system">
        <comment>Add properties column to document_type table</comment>
        <addColumn tableName="document_type" schemaName="record">
            <column name="properties" type="JSONB"/>
        </addColumn>
    </changeSet>

    <changeSet id="00000000000010-33" author="system">
        <comment>Add properties column to app_properties table</comment>
        <addColumn tableName="app_properties" schemaName="record">
            <column name="properties" type="JSONB"/>
        </addColumn>
    </changeSet>

    <changeSet id="00000000000010-34" author="system">
        <comment>Add properties column to module_definition table</comment>
        <addColumn tableName="module_definition">
            <column name="properties" type="JSONB"/>
        </addColumn>
    </changeSet>

    <!-- ============================================ -->
    <!-- ADD MISSING COLUMNS TO AUDIT LOG TABLES -->
    <!-- ============================================ -->

    <!-- Add conversion_reference, created_by, and created_date to all audit_log tables -->

    <changeSet id="00000000000010-35" author="system">
        <comment>Add conversion_reference to audit_log_record table</comment>
        <addColumn tableName="audit_log_record">
            <column name="conversion_reference" type="VARCHAR(255)"/>
        </addColumn>
    </changeSet>

    <changeSet id="00000000000010-36" author="system">
        <comment>Add conversion_reference to audit_log_record_type table</comment>
        <addColumn tableName="audit_log_record_type">
            <column name="conversion_reference" type="VARCHAR(255)"/>
        </addColumn>
    </changeSet>

    <changeSet id="00000000000010-37" author="system">
        <comment>Add conversion_reference to audit_log_association table</comment>
        <addColumn tableName="audit_log_association">
            <column name="conversion_reference" type="VARCHAR(255)"/>
        </addColumn>
    </changeSet>

    <changeSet id="00000000000010-38" author="system">
        <comment>Add conversion_reference to audit_log_association_type table</comment>
        <addColumn tableName="audit_log_association_type">
            <column name="conversion_reference" type="VARCHAR(255)"/>
        </addColumn>
    </changeSet>

    <changeSet id="00000000000010-39" author="system">
        <comment>Add conversion_reference to audit_log_document table</comment>
        <addColumn tableName="audit_log_document" schemaName="record">
            <column name="conversion_reference" type="VARCHAR(255)"/>
        </addColumn>
    </changeSet>

    <changeSet id="00000000000010-40" author="system">
        <comment>Add conversion_reference and properties to audit_log_document_type table</comment>
        <addColumn tableName="audit_log_document_type" schemaName="record">
            <column name="conversion_reference" type="VARCHAR(255)"/>
            <column name="properties" type="JSONB"/>
        </addColumn>
    </changeSet>

    <changeSet id="00000000000010-41" author="system">
        <comment>Add conversion_reference to audit_log_code_lookup table</comment>
        <addColumn tableName="audit_log_code_lookup" schemaName="record">
            <column name="conversion_reference" type="VARCHAR(255)"/>
        </addColumn>
    </changeSet>

    <changeSet id="00000000000010-42" author="system">
        <comment>Add conversion_reference to audit_log_entity_fee table</comment>
        <addColumn tableName="audit_log_entity_fee" schemaName="record">
            <column name="conversion_reference" type="VARCHAR(255)"/>
        </addColumn>
    </changeSet>

    <changeSet id="00000000000010-43" author="system">
        <comment>Add conversion_reference to audit_log_entity_group table</comment>
        <addColumn tableName="audit_log_entity_group" schemaName="record">
            <column name="conversion_reference" type="VARCHAR(255)"/>
        </addColumn>
    </changeSet>

    <changeSet id="00000000000010-44" author="system">
        <comment>Add conversion_reference to audit_log_entity_note table</comment>
        <addColumn tableName="audit_log_entity_note" schemaName="record">
            <column name="conversion_reference" type="VARCHAR(255)"/>
        </addColumn>
    </changeSet>

    <changeSet id="00000000000010-45" author="system">
        <comment>Add conversion_reference and properties to audit_log_app_properties table</comment>
        <addColumn tableName="audit_log_app_properties" schemaName="record">
            <column name="conversion_reference" type="VARCHAR(255)"/>
            <column name="properties" type="JSONB"/>
        </addColumn>
    </changeSet>

    <changeSet id="00000000000010-46" author="system">
        <comment>Add conversion_reference and properties to audit_log_module_definition table</comment>
        <addColumn tableName="audit_log_module_definition">
            <column name="conversion_reference" type="VARCHAR(255)"/>
            <column name="properties" type="JSONB"/>
        </addColumn>
    </changeSet>

    <!-- ============================================ -->
    <!-- RENAME LAST_MODIFIED_AT TO LAST_MODIFIED_DATE IN AUDIT LOG TABLES -->
    <!-- ============================================ -->

    <changeSet id="00000000000010-47" author="system">
        <comment>Rename last_modified_at in audit_log_record table</comment>
        <renameColumn tableName="audit_log_record"
                      oldColumnName="last_modified_at"
                      newColumnName="last_modified_date"
                      columnDataType="TIMESTAMP WITH TIME ZONE"/>
    </changeSet>

    <changeSet id="00000000000010-48" author="system">
        <comment>Rename last_modified_at in audit_log_record_type table</comment>
        <renameColumn tableName="audit_log_record_type"
                      oldColumnName="last_modified_at"
                      newColumnName="last_modified_date"
                      columnDataType="TIMESTAMP WITH TIME ZONE"/>
    </changeSet>

    <changeSet id="00000000000010-49" author="system">
        <comment>Rename last_modified_at in audit_log_association table</comment>
        <renameColumn tableName="audit_log_association"
                      oldColumnName="last_modified_at"
                      newColumnName="last_modified_date"
                      columnDataType="TIMESTAMP WITH TIME ZONE"/>
    </changeSet>

    <changeSet id="00000000000010-50" author="system">
        <comment>Rename last_modified_at in audit_log_association_type table</comment>
        <renameColumn tableName="audit_log_association_type"
                      oldColumnName="last_modified_at"
                      newColumnName="last_modified_date"
                      columnDataType="TIMESTAMP WITH TIME ZONE"/>
    </changeSet>

    <changeSet id="00000000000010-51" author="system">
        <comment>Rename last_modified_at in audit_log_document table</comment>
        <renameColumn tableName="audit_log_document"
                      schemaName="record"
                      oldColumnName="last_modified_at"
                      newColumnName="last_modified_date"
                      columnDataType="TIMESTAMP WITH TIME ZONE"/>
    </changeSet>

    <changeSet id="00000000000010-52" author="system">
        <comment>Rename last_modified_at in audit_log_document_type table</comment>
        <renameColumn tableName="audit_log_document_type"
                      schemaName="record"
                      oldColumnName="last_modified_at"
                      newColumnName="last_modified_date"
                      columnDataType="TIMESTAMP WITH TIME ZONE"/>
    </changeSet>

    <changeSet id="00000000000010-53" author="system">
        <comment>Rename last_modified_at in audit_log_code_lookup table</comment>
        <renameColumn tableName="audit_log_code_lookup"
                      schemaName="record"
                      oldColumnName="last_modified_at"
                      newColumnName="last_modified_date"
                      columnDataType="TIMESTAMP WITH TIME ZONE"/>
    </changeSet>

    <changeSet id="00000000000010-54" author="system">
        <comment>Rename last_modified_at in audit_log_entity_fee table</comment>
        <renameColumn tableName="audit_log_entity_fee"
                      schemaName="record"
                      oldColumnName="last_modified_at"
                      newColumnName="last_modified_date"
                      columnDataType="TIMESTAMP WITH TIME ZONE"/>
    </changeSet>

    <changeSet id="00000000000010-55" author="system">
        <comment>Rename last_modified_at in audit_log_entity_group table</comment>
        <renameColumn tableName="audit_log_entity_group"
                      schemaName="record"
                      oldColumnName="last_modified_at"
                      newColumnName="last_modified_date"
                      columnDataType="TIMESTAMP WITH TIME ZONE"/>
    </changeSet>

    <changeSet id="00000000000010-56" author="system">
        <comment>Rename last_modified_at in audit_log_entity_note table</comment>
        <renameColumn tableName="audit_log_entity_note"
                      schemaName="record"
                      oldColumnName="last_modified_at"
                      newColumnName="last_modified_date"
                      columnDataType="TIMESTAMP WITH TIME ZONE"/>
    </changeSet>

    <changeSet id="00000000000010-57" author="system">
        <comment>Rename last_modified_at in audit_log_app_properties table</comment>
        <renameColumn tableName="audit_log_app_properties"
                      schemaName="record"
                      oldColumnName="last_modified_at"
                      newColumnName="last_modified_date"
                      columnDataType="TIMESTAMP WITH TIME ZONE"/>
    </changeSet>

    <changeSet id="00000000000010-58" author="system">
        <comment>Rename last_modified_at in audit_log_module_definition table</comment>
        <renameColumn tableName="audit_log_module_definition"
                      oldColumnName="last_modified_at"
                      newColumnName="last_modified_date"
                      columnDataType="TIMESTAMP WITH TIME ZONE"/>
    </changeSet>

    <!-- ============================================ -->
    <!-- MODIFY LAST_MODIFIED_BY IN AUDIT LOG TABLES -->
    <!-- ============================================ -->

    <changeSet id="00000000000010-59" author="system">
        <comment>Modify last_modified_by in audit_log_record table</comment>
        <modifyDataType tableName="audit_log_record" columnName="last_modified_by" newDataType="VARCHAR(1000)"/>
    </changeSet>

    <changeSet id="00000000000010-60" author="system">
        <comment>Modify last_modified_by in audit_log_record_type table</comment>
        <modifyDataType tableName="audit_log_record_type" columnName="last_modified_by" newDataType="VARCHAR(1000)"/>
    </changeSet>

    <changeSet id="00000000000010-61" author="system">
        <comment>Modify last_modified_by in audit_log_association table</comment>
        <modifyDataType tableName="audit_log_association" columnName="last_modified_by" newDataType="VARCHAR(1000)"/>
    </changeSet>

    <changeSet id="00000000000010-62" author="system">
        <comment>Modify last_modified_by in audit_log_association_type table</comment>
        <modifyDataType tableName="audit_log_association_type" columnName="last_modified_by" newDataType="VARCHAR(1000)"/>
    </changeSet>

    <changeSet id="00000000000010-63" author="system">
        <comment>Modify last_modified_by in audit_log_module_definition table</comment>
        <modifyDataType tableName="audit_log_module_definition" columnName="last_modified_by" newDataType="VARCHAR(1000)"/>
    </changeSet>

    <changeSet id="00000000000010-64" author="system">
        <comment>Modify last_modified_by in audit_log_app_properties table</comment>
        <modifyDataType tableName="audit_log_app_properties" schemaName="record" columnName="last_modified_by" newDataType="VARCHAR(1000)"/>
    </changeSet>

    <!-- ============================================ -->
    <!-- RENAME CREATED_AT TO CREATED_DATE IN AUDIT LOG TABLES -->
    <!-- ============================================ -->

    <changeSet id="00000000000010-65" author="system">
        <comment>Rename created_at to created_date in audit_log_record table</comment>
        <renameColumn tableName="audit_log_record"
                      oldColumnName="created_at"
                      newColumnName="created_date"
                      columnDataType="TIMESTAMP WITH TIME ZONE"/>
    </changeSet>

    <changeSet id="00000000000010-66" author="system">
        <comment>Rename created_at to created_date in audit_log_record_type table</comment>
        <renameColumn tableName="audit_log_record_type"
                      oldColumnName="created_at"
                      newColumnName="created_date"
                      columnDataType="TIMESTAMP WITH TIME ZONE"/>
    </changeSet>

    <changeSet id="00000000000010-67" author="system">
        <comment>Rename created_at to created_date in audit_log_association table</comment>
        <renameColumn tableName="audit_log_association"
                      oldColumnName="created_at"
                      newColumnName="created_date"
                      columnDataType="TIMESTAMP WITH TIME ZONE"/>
    </changeSet>

    <changeSet id="00000000000010-68" author="system">
        <comment>Rename created_at to created_date in audit_log_association_type table</comment>
        <renameColumn tableName="audit_log_association_type"
                      oldColumnName="created_at"
                      newColumnName="created_date"
                      columnDataType="TIMESTAMP WITH TIME ZONE"/>
    </changeSet>

    <changeSet id="00000000000010-69" author="system">
        <comment>Rename created_at to created_date in audit_log_module_definition table</comment>
        <renameColumn tableName="audit_log_module_definition"
                      oldColumnName="created_at"
                      newColumnName="created_date"
                      columnDataType="TIMESTAMP WITH TIME ZONE"/>
    </changeSet>

    <!-- ============================================ -->
    <!-- MODIFY CREATED_BY IN AUDIT LOG TABLES -->
    <!-- ============================================ -->

    <changeSet id="00000000000010-70" author="system">
        <comment>Modify created_by in audit_log_record table</comment>
        <modifyDataType tableName="audit_log_record" columnName="created_by" newDataType="VARCHAR(1000)"/>
    </changeSet>

    <changeSet id="00000000000010-71" author="system">
        <comment>Modify created_by in audit_log_record_type table</comment>
        <modifyDataType tableName="audit_log_record_type" columnName="created_by" newDataType="VARCHAR(1000)"/>
    </changeSet>

    <changeSet id="00000000000010-72" author="system">
        <comment>Modify created_by in audit_log_association table</comment>
        <modifyDataType tableName="audit_log_association" columnName="created_by" newDataType="VARCHAR(1000)"/>
    </changeSet>

    <changeSet id="00000000000010-73" author="system">
        <comment>Modify created_by in audit_log_association_type table</comment>
        <modifyDataType tableName="audit_log_association_type" columnName="created_by" newDataType="VARCHAR(1000)"/>
    </changeSet>

    <changeSet id="00000000000010-74" author="system">
        <comment>Modify created_by in audit_log_module_definition table</comment>
        <modifyDataType tableName="audit_log_module_definition" columnName="created_by" newDataType="VARCHAR(1000)"/>
    </changeSet>

    <!-- ============================================ -->
    <!-- MODIFY LAST_MODIFIED_BY IN AUDIT LOG TABLES (RECORD SCHEMA) -->
    <!-- ============================================ -->

    <changeSet id="00000000000010-75" author="system">
        <comment>Modify last_modified_by in audit_log_document table</comment>
        <modifyDataType tableName="audit_log_document" schemaName="record" columnName="last_modified_by" newDataType="VARCHAR(1000)"/>
    </changeSet>

    <changeSet id="00000000000010-76" author="system">
        <comment>Modify last_modified_by in audit_log_document_type table</comment>
        <modifyDataType tableName="audit_log_document_type" schemaName="record" columnName="last_modified_by" newDataType="VARCHAR(1000)"/>
    </changeSet>

    <changeSet id="00000000000010-77" author="system">
        <comment>Modify last_modified_by in audit_log_code_lookup table</comment>
        <modifyDataType tableName="audit_log_code_lookup" schemaName="record" columnName="last_modified_by" newDataType="VARCHAR(1000)"/>
    </changeSet>

    <changeSet id="00000000000010-78" author="system">
        <comment>Modify last_modified_by in audit_log_entity_fee table</comment>
        <modifyDataType tableName="audit_log_entity_fee" schemaName="record" columnName="last_modified_by" newDataType="VARCHAR(1000)"/>
    </changeSet>

    <changeSet id="00000000000010-79" author="system">
        <comment>Modify last_modified_by in audit_log_entity_group table</comment>
        <modifyDataType tableName="audit_log_entity_group" schemaName="record" columnName="last_modified_by" newDataType="VARCHAR(1000)"/>
    </changeSet>

    <changeSet id="00000000000010-80" author="system">
        <comment>Modify last_modified_by in audit_log_entity_note table</comment>
        <modifyDataType tableName="audit_log_entity_note" schemaName="record" columnName="last_modified_by" newDataType="VARCHAR(1000)"/>
    </changeSet>

    <!-- ============================================ -->
    <!-- ADD CREATED_DATE AND CREATED_BY TO AUDIT LOG TABLES IN RECORD SCHEMA -->
    <!-- ============================================ -->

    <changeSet id="00000000000010-81" author="system">
        <comment>Add created_date and created_by to audit_log_document table</comment>
        <addColumn tableName="audit_log_document" schemaName="record">
            <column name="created_date" type="TIMESTAMP WITH TIME ZONE"/>
            <column name="created_by" type="VARCHAR(1000)"/>
        </addColumn>
    </changeSet>

    <changeSet id="00000000000010-82" author="system">
        <comment>Add created_date and created_by to audit_log_document_type table</comment>
        <addColumn tableName="audit_log_document_type" schemaName="record">
            <column name="created_date" type="TIMESTAMP WITH TIME ZONE"/>
            <column name="created_by" type="VARCHAR(1000)"/>
        </addColumn>
    </changeSet>

    <changeSet id="00000000000010-83" author="system">
        <comment>Add created_date and created_by to audit_log_code_lookup table</comment>
        <addColumn tableName="audit_log_code_lookup" schemaName="record">
            <column name="created_date" type="TIMESTAMP WITH TIME ZONE"/>
            <column name="created_by" type="VARCHAR(1000)"/>
        </addColumn>
    </changeSet>

    <changeSet id="00000000000010-84" author="system">
        <comment>Add created_date and created_by to audit_log_entity_fee table</comment>
        <addColumn tableName="audit_log_entity_fee" schemaName="record">
            <column name="created_date" type="TIMESTAMP WITH TIME ZONE"/>
            <column name="created_by" type="VARCHAR(1000)"/>
        </addColumn>
    </changeSet>

    <changeSet id="00000000000010-85" author="system">
        <comment>Add created_date and created_by to audit_log_entity_group table</comment>
        <addColumn tableName="audit_log_entity_group" schemaName="record">
            <column name="created_date" type="TIMESTAMP WITH TIME ZONE"/>
            <column name="created_by" type="VARCHAR(1000)"/>
        </addColumn>
    </changeSet>

    <changeSet id="00000000000010-86" author="system">
        <comment>Add created_date and created_by to audit_log_entity_note table</comment>
        <addColumn tableName="audit_log_entity_note" schemaName="record">
            <column name="created_date" type="TIMESTAMP WITH TIME ZONE"/>
            <column name="created_by" type="VARCHAR(1000)"/>
        </addColumn>
    </changeSet>

    <changeSet id="00000000000010-87" author="system">
        <comment>Add created_date and created_by to audit_log_app_properties table</comment>
        <addColumn tableName="audit_log_app_properties" schemaName="record">
            <column name="created_date" type="TIMESTAMP WITH TIME ZONE"/>
            <column name="created_by" type="VARCHAR(1000)"/>
        </addColumn>
    </changeSet>

    <!-- ============================================ -->
    <!-- RENAME MODULE_ID AND MODULE_UUID IN MODULE_DEFINITION TABLES -->
    <!-- ============================================ -->

    <changeSet id="00000000000010-88" author="system">
        <comment>Rename module_id in audit_log_module_definition table</comment>
        <renameColumn tableName="audit_log_module_definition"
                      oldColumnName="module_id"
                      newColumnName="module_definition_id"
                      columnDataType="BIGINT"/>
    </changeSet>

    <changeSet id="00000000000010-89" author="system">
        <comment>Rename module_id in module_definition table</comment>
        <renameColumn tableName="module_definition"
                      oldColumnName="module_id"
                      newColumnName="module_definition_id"
                      columnDataType="BIGINT"/>
    </changeSet>

    <changeSet id="00000000000010-90" author="system">
        <comment>Rename module_uuid in audit_log_module_definition table</comment>
        <renameColumn tableName="audit_log_module_definition"
                      oldColumnName="module_uuid"
                      newColumnName="module_definition_uuid"
                      columnDataType="UUID"/>
    </changeSet>

    <changeSet id="00000000000010-91" author="system">
        <comment>Rename module_uuid in module_definition table</comment>
        <renameColumn tableName="module_definition"
                      oldColumnName="module_uuid"
                      newColumnName="module_definition_uuid"
                      columnDataType="UUID"/>
    </changeSet>

</databaseChangeLog>