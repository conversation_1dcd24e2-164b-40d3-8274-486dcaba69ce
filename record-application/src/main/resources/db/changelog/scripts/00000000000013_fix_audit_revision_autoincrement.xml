<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd">
    
    <changeSet id="fix_audit_log_revision_autoincrement" author="system">
        <preConditions onFail="MARK_RAN">
            <tableExists tableName="audit_log_revision" schemaName="record"/>
        </preConditions>
        <comment>Add auto-increment to audit_log_revision_id column</comment>
        <addAutoIncrement tableName="audit_log_revision"
                          schemaName="record"
                          columnName="audit_log_revision_id"
                          columnDataType="INTEGER"/>
    </changeSet>
    
</databaseChangeLog>
