{"moduleCode": "DOG", "moduleType": "PROFILE", "moduleName": "Dog Registry Module", "version": "1.0.0", "description": "Defines the persisted data schema, relationships, and lifecycle for registered dogs. Vaccinations are modeled as dynamic records with individual requirements.", "config": {"schema": {"dogName": {"type": "string", "label": "Dog Name", "required": true, "validations": [{"type": "<PERSON><PERSON><PERSON><PERSON>", "message": "Dog name must be at least 1 character", "value": 1}, {"type": "max<PERSON><PERSON><PERSON>", "message": "Dog name cannot exceed 100 characters", "value": 100}]}, "breed": {"type": "string", "label": "Breed", "required": true, "validations": [{"type": "<PERSON><PERSON><PERSON><PERSON>", "message": "Breed must be at least 1 character", "value": 1}, {"type": "max<PERSON><PERSON><PERSON>", "message": "Breed cannot exceed 100 characters", "value": 100}], "defaultValues": ["Mixed", "Labrador Retriever", "German Shepherd", "Golden Retriever", "Bulldog", "<PERSON>odle", "<PERSON><PERSON><PERSON>", "Rottweiler", "Yorkshire Terrier", "Boxer"]}, "primaryColor": {"type": "string", "label": "Primary Color", "required": false, "validations": [{"type": "<PERSON><PERSON><PERSON><PERSON>", "message": "Primary Color must be at least 1 character", "value": 1}, {"type": "max<PERSON><PERSON><PERSON>", "message": "Primary Color cannot exceed 50 characters", "value": 50}], "defaultValues": ["Black", "<PERSON>", "White", "Golden", "<PERSON>", "Brindle", "<PERSON>", "Mixed"]}, "secondaryColor": {"type": "string", "label": "Secondary Color", "required": false, "validations": [{"type": "<PERSON><PERSON><PERSON><PERSON>", "message": "Secondary Color must be at least 1 character", "value": 1}, {"type": "max<PERSON><PERSON><PERSON>", "message": "Secondary Color cannot exceed 50 characters", "value": 50}], "defaultValues": ["Black", "<PERSON>", "White", "Golden", "<PERSON>", "Brindle", "<PERSON>", "Mixed", "None"]}, "sex": {"type": "string", "label": "Sex", "required": true, "validations": [{"type": "<PERSON><PERSON><PERSON><PERSON>", "message": "Sex must be at least 1 character", "value": 1}, {"type": "max<PERSON><PERSON><PERSON>", "message": "Sex cannot exceed 50 characters", "value": 50}], "defaultValues": ["Male", "Female"]}, "birthDate": {"type": "date", "label": "Birth Date", "required": false, "validations": [{"type": "datePastMin", "unit": "years", "value": 0, "message": "Dog birth date must be in the past."}, {"type": "datePastMax", "unit": "years", "value": 32, "message": "Dog birth must be 32 years ago or less."}]}, "isSpayedOrNeutered": {"type": "boolean", "label": "Spayed/Neutered", "required": true, "default": false, "validations": [{"type": "<PERSON><PERSON><PERSON><PERSON>", "message": "Spayed/Neutered selection must be at least 1 character", "value": 1}, {"type": "max<PERSON><PERSON><PERSON>", "message": "Spayed/Neutered selection cannot exceed 50 characters", "value": 50}]}, "spayOrNeuterDocument": {"type": "file", "label": "Spay/Neuter Document", "required": false}, "microchipNumber": {"type": "string", "label": "Microchip Number"}, "vaccinations": {"type": "object", "label": "Vaccinations", "schema": {"rabies": {"type": "object", "label": "Rabies Vaccination", "schema": {}}}}}, "association": {"dogOwner": {"associationType": "DOG_OWNER", "moduleCode": "INDIVIDUAL", "label": "Dog Owner", "multiple": true}, "dogAddress": {"associationType": "DOG_ADDRESS", "moduleCode": "PARCEL", "label": "Physical Address", "multiple": false}}, "associations": [{"associationType": "DOG_OWNER", "moduleCode": "INDIVIDUAL", "label": "Dog Owner", "multiple": true}, {"associationType": "DOG_ADDRESS", "moduleCode": "PARCEL", "label": "Physical Address", "multiple": false}]}}