{"moduleCode": "DOG_LICENSE", "moduleName": "Dog Licensing Module", "description": "Comprehensive dog registration and licensing system", "version": "1.0.0", "config": {"recordTypes": [{"typeCode": "DOG_OWNER", "typeName": "Dog Owner", "description": "Individual or organization that owns a dog", "parentTypeCode": null, "properties": {"schema": {"type": "object", "properties": {"firstName": {"type": "string", "label": "First Name", "required": true, "maxLength": 100}, "lastName": {"type": "string", "label": "Last Name", "required": true, "maxLength": 100}, "email": {"type": "string", "label": "Email Address", "required": true, "format": "email"}, "phone": {"type": "string", "label": "Phone Number", "required": true, "pattern": "^\\d{10}$"}, "address": {"type": "object", "label": "Address", "properties": {"street": {"type": "string", "label": "Street Address"}, "city": {"type": "string", "label": "City"}, "state": {"type": "string", "label": "State"}, "zipCode": {"type": "string", "label": "ZIP Code", "pattern": "^\\d{5}$"}}}, "isSenior": {"type": "boolean", "label": "Senior Citizen (65+)", "default": false}}}}, "config_json": {"displayFields": ["firstName", "lastName", "email", "phone"], "searchableFields": ["firstName", "lastName", "email"], "sortableFields": ["lastName", "firstName"], "defaultStatus": "ACTIVE"}}, {"typeCode": "DOG", "typeName": "Dog", "description": "Dog being registered", "parentTypeCode": null, "properties": {"schema": {"type": "object", "properties": {"dogName": {"type": "string", "label": "Dog Name", "required": true, "maxLength": 100}, "breed": {"type": "string", "label": "Breed", "required": true, "maxLength": 100}, "color": {"type": "string", "label": "Primary Color", "required": true, "enum": ["Black", "<PERSON>", "White", "Golden", "<PERSON>", "Mixed"]}, "sex": {"type": "string", "label": "Sex", "required": true, "enum": ["Male", "Female"]}, "birthYear": {"type": "integer", "label": "Birth Year", "required": true, "minimum": 1990, "maximum": 2024}, "weight": {"type": "number", "label": "Weight (lbs)", "required": false, "minimum": 0, "maximum": 300}, "isSpayedOrNeutered": {"type": "boolean", "label": "Spayed/Neutered", "required": true, "default": false}, "microchipNumber": {"type": "string", "label": "Microchip Number", "required": false, "maxLength": 50}, "rabiesVaccinationDate": {"type": "date", "label": "Rabies Vaccination Date", "required": true}, "rabiesVaccinationExpiry": {"type": "date", "label": "Rabies Vaccination Expiry", "required": true}, "veterinarianName": {"type": "string", "label": "Veterinarian Name", "required": false, "maxLength": 200}}}}, "config_json": {"displayFields": ["<PERSON><PERSON><PERSON>", "breed", "color", "sex", "birthYear"], "searchableFields": ["<PERSON><PERSON><PERSON>", "breed", "microchipNumber"], "sortableFields": ["<PERSON><PERSON><PERSON>", "breed"], "defaultStatus": "ACTIVE"}}, {"typeCode": "DOG_LICENSE", "typeName": "Dog License", "description": "Annual dog license record", "parentTypeCode": null, "properties": {"schema": {"type": "object", "properties": {"licenseNumber": {"type": "string", "label": "License Number", "required": true, "pattern": "^DL-\\d{4}-\\d{6}$", "example": "DL-2024-000001"}, "licenseYear": {"type": "integer", "label": "License Year", "required": true, "minimum": 2020, "maximum": 2030}, "issueDate": {"type": "date", "label": "Issue Date", "required": true}, "expirationDate": {"type": "date", "label": "Expiration Date", "required": true}, "status": {"type": "string", "label": "License Status", "required": true, "enum": ["PENDING", "ACTIVE", "EXPIRED", "REVOKED", "SUSPENDED"], "default": "PENDING"}, "tagNumber": {"type": "string", "label": "Tag Number", "required": false, "maxLength": 50}, "notes": {"type": "string", "label": "Notes", "required": false, "maxLength": 1000}, "dog": {"type": "object", "label": "Dog Information", "required": false, "properties": {"isSpayedOrNeutered": {"type": "boolean", "label": "Is Spayed/Neutered", "required": false}, "isExempt": {"type": "boolean", "label": "Is Exempt", "required": false, "default": false}}}, "owner": {"type": "object", "label": "Owner Information", "required": false, "properties": {"isSenior": {"type": "boolean", "label": "Is Senior Citizen", "required": false}}}, "dogUuid": {"type": "string", "label": "Dog UUID", "required": false}, "licenseType": {"type": "string", "label": "License Type", "required": false}, "previousExpirationDate": {"type": "date", "label": "Previous Expiration Date", "required": false}}}}, "config_json": {"displayFields": ["licenseNumber", "licenseYear", "status", "expirationDate"], "searchableFields": ["licenseNumber", "tagNumber"], "sortableFields": ["licenseNumber", "issueDate", "expirationDate"], "defaultStatus": "PENDING", "workflowStates": {"PENDING": {"label": "Pending Approval", "allowedTransitions": ["ACTIVE", "REVOKED"]}, "ACTIVE": {"label": "Active", "allowedTransitions": ["EXPIRED", "REVOKED", "SUSPENDED"]}, "EXPIRED": {"label": "Expired", "allowedTransitions": ["ACTIVE"]}, "REVOKED": {"label": "Revoked", "allowedTransitions": []}, "SUSPENDED": {"label": "Suspended", "allowedTransitions": ["ACTIVE", "REVOKED"]}}, "feeRules": [{"feeCode": "DL-M-LICENSE", "feeName": "Dog License Base Fee", "description": "Base fee for all dog licenses - always applied", "amount": 10.0, "condition": "true"}, {"feeCode": "DL-M-DATA", "feeName": "Schenectady Fee (Enumeration)", "description": "Data processing fee - always applied", "amount": 2.5, "condition": "true"}, {"feeCode": "DL-S-ALT", "feeName": "State Surcharge - Altered", "description": "State surcharge for spayed/neutered dogs", "amount": 1.0, "condition": "record.properties.dog.isSpayedOrNeutered && !record.properties.dog.isExempt"}, {"feeCode": "DL-M-UNALT", "feeName": "<PERSON><PERSON> Unaltered <PERSON>e", "description": "Municipal fee for unaltered dogs", "amount": 0.0, "condition": "!record.properties.dog.isSpayedOrNeutered && !record.properties.dog.isExempt"}, {"feeCode": "DL-S-UNALT", "feeName": "State Surcharge - Unaltered", "description": "State surcharge for non-spayed/neutered dogs", "amount": 3.0, "condition": "!record.properties.dog.isSpayedOrNeutered && !record.properties.dog.isExempt"}, {"feeCode": "DL-M-SENIOR", "feeName": "Senior Citizen Discount", "description": "Discount for senior citizens (not exempt)", "amount": -10.0, "condition": "record.properties.owner.isSenior && !record.properties.dog.isExempt"}, {"feeCode": "DL-M-EXEMPT", "feeName": "License Fee Exemption", "description": "License fee exemption for exempt dogs", "amount": -12.5, "condition": "record.properties.dog.isExempt"}, {"feeCode": "DL-M-EXEMPTSTATEALT", "feeName": "License Fee Exemption", "description": "State fee exemption for exempt altered dogs", "amount": -1.0, "condition": "record.properties.dog.isExempt && record.properties.dog.isSpayedOrNeutered"}, {"feeCode": "DL-M-EXEMPTSTATEUNALT", "feeName": "License Fee Exemption", "description": "State fee exemption for exempt unaltered dogs", "amount": -3.0, "condition": "record.properties.dog.isExempt && !record.properties.dog.isSpayedOrNeutered"}, {"feeCode": "DL-M-EXEMPTUNALT", "feeName": "Schenectady Unaltered Fee exemption", "description": "Municipal unaltered fee exemption for exempt dogs", "amount": -5.0, "condition": "record.properties.dog.isExempt && !record.properties.dog.isSpayedOrNeutered"}], "validationRules": [{"field": "expirationDate", "rule": "must_be_after", "compareField": "issueDate", "message": "Expiration date must be after issue date"}, {"field": "rabiesVaccinationExpiry", "rule": "must_be_after", "compareField": "expirationDate", "message": "Rabies vaccination must be valid through license expiration"}], "autoActions": [{"trigger": "30_days_before_expiration", "action": "send_renewal_reminder", "params": {"emailTemplate": "dog_license_renewal_reminder"}}, {"trigger": "on_expiration", "action": "update_status", "params": {"newStatus": "EXPIRED"}}]}}], "associationTypes": [{"associationName": "OWNER_TO_LICENSE", "description": "Links dog owner to their dog license", "parentType": "DOG_OWNER", "childType": "DOG_LICENSE", "cardinality": "ONE_TO_MANY", "required": true}, {"associationName": "DOG_TO_LICENSE", "description": "Links dog to its license", "parentType": "DOG", "childType": "DOG_LICENSE", "cardinality": "ONE_TO_ONE", "required": true}, {"associationName": "OWNER_TO_DOG", "description": "Links owner to their dog(s)", "parentType": "DOG_OWNER", "childType": "DOG", "cardinality": "ONE_TO_MANY", "required": true}], "documentTypes": [{"documentTypeCode": "RABIES_CERTIFICATE", "documentTypeName": "Rabies Vaccination Certificate", "description": "Proof of rabies vaccination", "required": true, "allowedFormats": ["PDF", "JPG", "PNG"], "maxSizeMB": 5}, {"documentTypeCode": "SPAY_NEUTER_CERTIFICATE", "documentTypeName": "Spay/Neuter Certificate", "description": "Proof of spay/neuter procedure", "required": false, "allowedFormats": ["PDF", "JPG", "PNG"], "maxSizeMB": 5}, {"documentTypeCode": "DOG_PHOTO", "documentTypeName": "Dog Photo", "description": "Photo of the dog", "required": false, "allowedFormats": ["JPG", "PNG"], "maxSizeMB": 2}], "permissions": {"CREATE_DOG_LICENSE": ["CLERK", "ADMIN"], "UPDATE_DOG_LICENSE": ["CLERK", "ADMIN"], "DELETE_DOG_LICENSE": ["ADMIN"], "VIEW_DOG_LICENSE": ["CLERK", "ADMIN", "PUBLIC"], "APPROVE_DOG_LICENSE": ["CLERK", "ADMIN"], "REVOKE_DOG_LICENSE": ["ADMIN"]}, "settings": {"autoGenerateLicenseNumber": true, "licenseNumberPrefix": "DL", "licenseNumberFormat": "DL-{YEAR}-{SEQUENCE:6}", "defaultLicenseDurationMonths": 12, "renewalWindowDays": 30, "gracePeriodDays": 15, "enableEmailNotifications": true, "enableSMSNotifications": false}, "events": [{"code": "dogDeceased", "name": "Dog Deceased", "description": "Event triggered when a dog is marked as deceased", "strategyType": "STATUS_CHANGE", "applicableToRecordTypes": ["DOG"], "config": {"newStatus": "Deceased", "notificationRequired": true}}, {"code": "dogLost", "name": "Dog Lost", "description": "Event triggered when a dog is reported lost", "strategyType": "PROPERTY_UPDATE", "applicableToRecordTypes": ["DOG"], "config": {"propertyName": "lost", "propertyValue": true, "notificationRequired": true}}, {"code": "dogFound", "name": "Dog Found", "description": "Event triggered when a lost dog is found", "strategyType": "PROPERTY_UPDATE", "applicableToRecordTypes": ["DOG"], "config": {"propertyName": "lost", "propertyValue": false, "notificationRequired": true}}, {"code": "dogImpounded", "name": "Dog Impounded", "description": "Event triggered when a dog is impounded", "strategyType": "PROPERTY_UPDATE", "applicableToRecordTypes": ["DOG"], "config": {"propertyName": "dogImpoundmentDate", "notificationRequired": true}}, {"code": "licenseRevoked", "name": "License Revoked", "description": "Event triggered when a dog license is revoked", "strategyType": "STATUS_CHANGE", "applicableToRecordTypes": ["DOG_LICENSE"], "config": {"newStatus": "REVOKED", "notificationRequired": true}}, {"code": "licenseSuspended", "name": "License Suspended", "description": "Event triggered when a dog license is suspended", "strategyType": "STATUS_CHANGE", "applicableToRecordTypes": ["DOG_LICENSE"], "config": {"newStatus": "SUSPENDED", "notificationRequired": true}}, {"code": "licenseRenewed", "name": "License Renewed", "description": "Event triggered when a dog license is renewed", "strategyType": "STATUS_CHANGE", "applicableToRecordTypes": ["DOG_LICENSE"], "config": {"newStatus": "ACTIVE", "notificationRequired": true}}]}}