package com.scube.record.infrastructure.db.entity.module;

import com.fasterxml.jackson.databind.JsonNode;
import com.scube.audit.auditable.entity.AuditableEntity;
import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.JdbcTypeCode;
import org.hibernate.annotations.UpdateTimestamp;
import org.hibernate.type.SqlTypes;

import java.time.Instant;
import java.util.UUID;

/**
 * Entity representing a module definition.
 * Stores the complete module configuration as JSON.
 * Modules define record types, associations, fee rules, and other configurations.
 */
@Entity
@Table(name = "module_definition")
@Data
@NoArgsConstructor
@AllArgsConstructor
@AttributeOverrides({
    @AttributeOverride(name = "id", column = @Column(name = "module_definition_id")),
    @AttributeOverride(name = "uuid", column = @Column(name = "module_definition_uuid"))
})
public class ModuleDefinition extends AuditableEntity  {

    @Column(name = "module_code", unique = true, nullable = false, length = 100)
    private String moduleCode;

    @Column(name = "module_name", nullable = false)
    private String moduleName;

    @Column(name = "description", columnDefinition = "TEXT")
    private String description;

    @Column(name = "version", length = 50)
    private String version;

    /**
     * Complete module configuration stored as JSON.
     * Contains recordTypes, associationTypes, documentTypes, permissions, settings, etc.
     */
    @JdbcTypeCode(SqlTypes.JSON)
    @Column(name = "config", columnDefinition = "jsonb", nullable = false)
    private JsonNode config;

    @Column(name = "is_active", nullable = false)
    private Boolean isActive = true;

}

