server:
  port: 9004
  servlet:
    context-path: /api/license
  compression:
    enabled: true
  error:
    include-message: always
    include-binding-errors: always
    include-stacktrace: never
    include-exception: false
    include-client-error-message: true
    include-server-error-message: false
    send-client-error-email: true
    send-server-error-email: true
  forward-headers-strategy: framework

service:
  base-url: ${APP_BASE_URL:https://clerkxpress.com}

logging:
  pattern:
    level: "%5p [tenantId=%X{tenantId:-}], [%X{traceId:-},%X{spanId:-}] [user=%X{userEmail:-}]"
  level:
    com.scube.rabbit: "warn"
    org.springframework.web: "info"
    org.hibernate: "error"
    liquibase: "warn"
    com.scube.licensing: "info"
    com.scube.licensing.infrastructure.middleware.QueryLoggingInterceptor: "warn"

spring:
  application:
    name: LicenseService
  threads:
    virtual:
      enabled: false
  servlet:
    multipart:
      enabled: true
      file-size-threshold: 2KB
      max-file-size: 200MB
      max-request-size: 200MB

  jpa:
    hibernate:
      ddl-auto: none
    database-platform: org.hibernate.dialect.PostgreSQLDialect
    show-sql: false
    properties:
      hibernate:

  mvc:
    async:
      request-timeout: 1200000




springdoc:
  show-actuator: true
  swagger-ui:
    enabled: false
    filter: true

keycloak:
  host: http://keycloak.keycloak.svc.cluster.local:8080
  public-host: https://auth.clerkxpress.com
  admin:
    url: ${keycloak.host}
    realm: master
    client-id: ${KEYCLOAK_ADMIN_CLIENT_ID}
    client-secret: ${KEYCLOAK_ADMIN_CLIENT_SECRET}
  swagger:
    url: ${keycloak.public-host}
    realm: clerkXpress
    client-id: ${KEYCLOAK_SWAGGER_AUTH_CLIENT_ID}

com.c4-soft.springaddons.oidc:
  ops:
    - iss: ${keycloak.host}
      jwkSetUri: ${keycloak.host}
      username-claim: preferred_username
      authorities:
        - path: $.realm_access.roles
        - path: $.resource_access.*.roles
    - iss: ${keycloak.public-host}
      jwk-set-uri: ${keycloak.host}
      username-claim: preferred_username
      authorities:
        - path: $.realm_access.roles
        - path: $.resource_access.*.roles
  resource-server:
    permit-all:
      - "/"
      - "/v3/api-docs/**"
      - "/swagger-ui/**"
      - "/swagger-ui.html"
      - "/actuator/**"
      - "/public/**"
    cors:
      - path: /**
        allowed-origin-patterns:
          - https://clerkxpress.com

multi-tenancy:
  enabled: true
  async:
    enabled: true
  caching:
    enabled: true
  keycloak:
    enabled: true
  management:
    enabled: true
  rabbitmq:
    enabled: true
    host: "rabbitmq.backend.svc.cluster.local"
    port: 5672
  scheduling:
    enabled: true
  database:
    enabled: true
    type: DATABASE_PER_TENANT
    default-tenant: postgres
    tenancy-format: "%s"
    liquibase:
      change-log: "classpath:/db/changelog/db.changelog-master.xml"
    datasource:
      url: "*******************************************************************"
      username: ${SPRING_DATASOURCE_USERNAME}
      password: ${SPRING_DATASOURCE_PASSWORD}
      driver-class-name: org.postgresql.Driver
      hikari:
        maximumPoolSize: 5
        minimumIdle: 5
        idleTimeout: 30000
        maxLifetime: 60000
        connectionTimeout: 30000

# valid values: google, nominatim
geocoding.type: "mock"
nominatim.api.url: "https://nominatim.openstreetmap.org/search"

scheduling:
  enabled: true

com.scube.client:
  auth: "http://scube-auth-service-srv:9001/api/auth"
  document: "http://scube-document-service-srv:9003/api/document-service"
  config: "http://scube-config-service-srv:10001/api/config"
  documentTemplate: "http://scube-document-template-service-srv:9009/api/document-template"
  calculation: "http://scube-calculation-service-srv:9002/api/calculation"
  license: "http://scube-license-service-srv:9004/api/license"
