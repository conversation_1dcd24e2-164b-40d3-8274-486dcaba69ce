<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd">
    <changeSet author="postgres (generated)" id="**********062-8162">
        <sql>
            DO '
            DECLARE
            function_info RECORD;
            query_str TEXT;
            BEGIN
            FOR function_info IN (SELECT distinct r.routine_name, pg_get_function_identity_arguments(oid) as args
            FROM information_schema.routines r
            JOIN pg_proc p ON p.proname = r.routine_name
            WHERE r.routine_schema = ''license'' AND r.routine_type = ''FUNCTION''
            and routine_name not ILIKE ''%dblink%'')
            LOOP
            query_str := ''DROP FUNCTION IF EXISTS "'' || function_info.routine_name || ''"('' || function_info.args ||
            '') CASCADE'';
            --RAISE NOTICE ''Executing query: %'', query_str;
            EXECUTE query_str;
            END LOOP;
            END '
        </sql>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********062-8172">
        <sql>
            DO '
            DECLARE
            view_name text;
            BEGIN
            FOR view_name IN (SELECT table_name FROM information_schema.tables WHERE table_schema = ''license''
            AND table_type = ''VIEW'')
            LOOP
            EXECUTE ''DROP VIEW IF EXISTS "'' || view_name || ''" CASCADE'';
            END LOOP;
            END ';
        </sql>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-414">
        <addColumn tableName="address">
            <column name="properties" type="jsonb"/>
        </addColumn>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-415">
        <addColumn tableName="app_properties">
            <column name="properties" type="jsonb"/>
        </addColumn>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-416">
        <addColumn tableName="association">
            <column name="properties" type="jsonb"/>
        </addColumn>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-417">
        <addColumn tableName="audit_log_address">
            <column name="properties" type="jsonb"/>
        </addColumn>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-418">
        <addColumn tableName="audit_log_app_properties">
            <column name="properties" type="jsonb"/>
        </addColumn>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-419">
        <addColumn tableName="audit_log_association">
            <column name="properties" type="jsonb"/>
        </addColumn>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-420">
        <addColumn tableName="audit_log_contact">
            <column name="properties" type="jsonb"/>
        </addColumn>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-421">
        <addColumn tableName="audit_log_contact_group">
            <column name="properties" type="jsonb"/>
        </addColumn>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-422">
        <addColumn tableName="audit_log_contact_type">
            <column name="properties" type="jsonb"/>
        </addColumn>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-423">
        <addColumn tableName="audit_log_contact_type_group">
            <column name="properties" type="jsonb"/>
        </addColumn>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-424">
        <addColumn tableName="audit_log_custom_entity_instance">
            <column name="properties" type="jsonb"/>
        </addColumn>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-425">
        <addColumn tableName="audit_log_custom_entity_sub_type">
            <column name="properties" type="jsonb"/>
        </addColumn>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-426">
        <addColumn tableName="audit_log_custom_entity_type">
            <column name="properties" type="jsonb"/>
        </addColumn>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-427">
        <addColumn tableName="audit_log_document">
            <column name="properties" type="jsonb"/>
        </addColumn>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-428">
        <addColumn tableName="audit_log_document_type">
            <column name="properties" type="jsonb"/>
        </addColumn>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-429">
        <addColumn tableName="audit_log_event_type">
            <column name="properties" type="jsonb"/>
        </addColumn>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-430">
        <addColumn tableName="audit_log_license">
            <column name="properties" type="jsonb"/>
        </addColumn>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-431">
        <addColumn tableName="audit_log_license_activity">
            <column name="properties" type="jsonb"/>
        </addColumn>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-432">
        <addColumn tableName="audit_log_license_activity_fee">
            <column name="properties" type="jsonb"/>
        </addColumn>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-433">
        <addColumn tableName="audit_log_license_status">
            <column name="properties" type="jsonb"/>
        </addColumn>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-434">
        <addColumn tableName="audit_log_license_type">
            <column name="properties" type="jsonb"/>
        </addColumn>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-435">
        <addColumn tableName="audit_log_license_type_fee">
            <column name="properties" type="jsonb"/>
        </addColumn>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-436">
        <addColumn tableName="audit_log_license_type_fee_conditional">
            <column name="properties" type="jsonb"/>
        </addColumn>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-437">
        <addColumn tableName="audit_log_license_type_setting">
            <column name="properties" type="jsonb"/>
        </addColumn>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-438">
        <addColumn tableName="audit_log_merge_request">
            <column name="properties" type="jsonb"/>
        </addColumn>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-439">
        <addColumn tableName="audit_log_multi_form_argument">
            <column name="properties" type="jsonb"/>
        </addColumn>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-440">
        <addColumn tableName="audit_log_multi_form_body_field">
            <column name="properties" type="jsonb"/>
        </addColumn>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-441">
        <addColumn tableName="audit_log_multi_form_builder">
            <column name="properties" type="jsonb"/>
        </addColumn>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-442">
        <addColumn tableName="audit_log_multi_form_conditional_display">
            <column name="properties" type="jsonb"/>
        </addColumn>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-443">
        <addColumn tableName="audit_log_multi_form_element_options">
            <column name="properties" type="jsonb"/>
        </addColumn>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-444">
        <addColumn tableName="audit_log_multi_form_form_element">
            <column name="properties" type="jsonb"/>
        </addColumn>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-445">
        <addColumn tableName="audit_log_multi_form_on_form_submit_api">
            <column name="properties" type="jsonb"/>
        </addColumn>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-446">
        <addColumn tableName="audit_log_multi_form_on_page_next_api">
            <column name="properties" type="jsonb"/>
        </addColumn>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-447">
        <addColumn tableName="audit_log_multi_form_page">
            <column name="properties" type="jsonb"/>
        </addColumn>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-448">
        <addColumn tableName="audit_log_multi_form_query_string">
            <column name="properties" type="jsonb"/>
        </addColumn>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-449">
        <addColumn tableName="audit_log_multi_form_request_body">
            <column name="properties" type="jsonb"/>
        </addColumn>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-450">
        <addColumn tableName="audit_log_multi_form_request_slug">
            <column name="properties" type="jsonb"/>
        </addColumn>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-451">
        <addColumn tableName="audit_log_multi_form_response">
            <column name="properties" type="jsonb"/>
        </addColumn>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-452">
        <addColumn tableName="audit_log_multi_form_response_error">
            <column name="properties" type="jsonb"/>
        </addColumn>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-453">
        <addColumn tableName="audit_log_multi_form_response_field">
            <column name="properties" type="jsonb"/>
        </addColumn>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-454">
        <addColumn tableName="audit_log_multi_form_response_success">
            <column name="properties" type="jsonb"/>
        </addColumn>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-455">
        <addColumn tableName="audit_log_multi_form_section">
            <column name="properties" type="jsonb"/>
        </addColumn>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-456">
        <addColumn tableName="audit_log_multi_form_trigger">
            <column name="properties" type="jsonb"/>
        </addColumn>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-457">
        <addColumn tableName="audit_log_multi_form_validation">
            <column name="properties" type="jsonb"/>
        </addColumn>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-458">
        <addColumn tableName="audit_log_participant">
            <column name="properties" type="jsonb"/>
        </addColumn>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-459">
        <addColumn tableName="audit_log_participant_address">
            <column name="properties" type="jsonb"/>
        </addColumn>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-460">
        <addColumn tableName="audit_log_participant_address_type">
            <column name="properties" type="jsonb"/>
        </addColumn>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-461">
        <addColumn tableName="audit_log_participant_group">
            <column name="properties" type="jsonb"/>
        </addColumn>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-462">
        <addColumn tableName="audit_log_participant_status">
            <column name="properties" type="jsonb"/>
        </addColumn>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-463">
        <addColumn tableName="audit_log_participant_type">
            <column name="properties" type="jsonb"/>
        </addColumn>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-464">
        <addColumn tableName="audit_log_participant_type_group">
            <column name="properties" type="jsonb"/>
        </addColumn>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-465">
        <addColumn tableName="audit_log_profile_type">
            <column name="properties" type="jsonb"/>
        </addColumn>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-466">
        <addColumn tableName="audit_log_search_form_argument">
            <column name="properties" type="jsonb"/>
        </addColumn>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-467">
        <addColumn tableName="audit_log_search_form_argument_filter">
            <column name="properties" type="jsonb"/>
        </addColumn>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-468">
        <addColumn tableName="audit_log_search_form_builder">
            <column name="properties" type="jsonb"/>
        </addColumn>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-469">
        <addColumn tableName="audit_log_search_form_element">
            <column name="properties" type="jsonb"/>
        </addColumn>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-470">
        <addColumn tableName="audit_log_search_form_section">
            <column name="properties" type="jsonb"/>
        </addColumn>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-471">
        <addColumn tableName="audit_log_table_column_field">
            <column name="properties" type="jsonb"/>
        </addColumn>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-472">
        <addColumn tableName="audit_log_table_custom_field">
            <column name="properties" type="jsonb"/>
        </addColumn>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-473">
        <addColumn tableName="audit_log_table_type">
            <column name="properties" type="jsonb"/>
        </addColumn>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-474">
        <addColumn tableName="audit_log_tenant">
            <column name="properties" type="jsonb"/>
        </addColumn>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-475">
        <addColumn tableName="contact">
            <column name="properties" type="jsonb"/>
        </addColumn>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-476">
        <addColumn tableName="contact_group">
            <column name="properties" type="jsonb"/>
        </addColumn>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-477">
        <addColumn tableName="contact_type">
            <column name="properties" type="jsonb"/>
        </addColumn>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-478">
        <addColumn tableName="contact_type_group">
            <column name="properties" type="jsonb"/>
        </addColumn>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-479">
        <addColumn tableName="custom_entity_instance">
            <column name="properties" type="jsonb"/>
        </addColumn>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-480">
        <addColumn tableName="custom_entity_sub_type">
            <column name="properties" type="jsonb"/>
        </addColumn>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-481">
        <addColumn tableName="custom_entity_type">
            <column name="properties" type="jsonb"/>
        </addColumn>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-482">
        <addColumn tableName="document">
            <column name="properties" type="jsonb"/>
        </addColumn>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-483">
        <addColumn tableName="document_type">
            <column name="properties" type="jsonb"/>
        </addColumn>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-484">
        <addColumn tableName="event_type">
            <column name="properties" type="jsonb"/>
        </addColumn>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-485">
        <addColumn tableName="license">
            <column name="properties" type="jsonb"/>
        </addColumn>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-486">
        <addColumn tableName="license_activity">
            <column name="properties" type="jsonb"/>
        </addColumn>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-487">
        <addColumn tableName="license_activity_fee">
            <column name="properties" type="jsonb"/>
        </addColumn>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-488">
        <addColumn tableName="license_status">
            <column name="properties" type="jsonb"/>
        </addColumn>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-489">
        <addColumn tableName="license_type">
            <column name="properties" type="jsonb"/>
        </addColumn>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-490">
        <addColumn tableName="license_type_fee">
            <column name="properties" type="jsonb"/>
        </addColumn>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-491">
        <addColumn tableName="license_type_fee_conditional">
            <column name="properties" type="jsonb"/>
        </addColumn>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-492">
        <addColumn tableName="license_type_setting">
            <column name="properties" type="jsonb"/>
        </addColumn>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-493">
        <addColumn tableName="merge_request">
            <column name="properties" type="jsonb"/>
        </addColumn>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-494">
        <addColumn tableName="multi_form_argument">
            <column name="properties" type="jsonb"/>
        </addColumn>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-495">
        <addColumn tableName="multi_form_body_field">
            <column name="properties" type="jsonb"/>
        </addColumn>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-496">
        <addColumn tableName="multi_form_builder">
            <column name="properties" type="jsonb"/>
        </addColumn>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-497">
        <addColumn tableName="multi_form_conditional_display">
            <column name="properties" type="jsonb"/>
        </addColumn>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-498">
        <addColumn tableName="multi_form_element_options">
            <column name="properties" type="jsonb"/>
        </addColumn>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-499">
        <addColumn tableName="multi_form_form_element">
            <column name="properties" type="jsonb"/>
        </addColumn>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-500">
        <addColumn tableName="multi_form_on_form_submit_api">
            <column name="properties" type="jsonb"/>
        </addColumn>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-501">
        <addColumn tableName="multi_form_on_page_next_api">
            <column name="properties" type="jsonb"/>
        </addColumn>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-502">
        <addColumn tableName="multi_form_page">
            <column name="properties" type="jsonb"/>
        </addColumn>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-503">
        <addColumn tableName="multi_form_query_string">
            <column name="properties" type="jsonb"/>
        </addColumn>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-504">
        <addColumn tableName="multi_form_request_body">
            <column name="properties" type="jsonb"/>
        </addColumn>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-505">
        <addColumn tableName="multi_form_request_slug">
            <column name="properties" type="jsonb"/>
        </addColumn>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-506">
        <addColumn tableName="multi_form_response">
            <column name="properties" type="jsonb"/>
        </addColumn>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-507">
        <addColumn tableName="multi_form_response_error">
            <column name="properties" type="jsonb"/>
        </addColumn>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-508">
        <addColumn tableName="multi_form_response_field">
            <column name="properties" type="jsonb"/>
        </addColumn>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-509">
        <addColumn tableName="multi_form_response_success">
            <column name="properties" type="jsonb"/>
        </addColumn>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-510">
        <addColumn tableName="multi_form_section">
            <column name="properties" type="jsonb"/>
        </addColumn>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-511">
        <addColumn tableName="multi_form_trigger">
            <column name="properties" type="jsonb"/>
        </addColumn>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-512">
        <addColumn tableName="multi_form_validation">
            <column name="properties" type="jsonb"/>
        </addColumn>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-513">
        <addColumn tableName="participant">
            <column name="properties" type="jsonb"/>
        </addColumn>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-514">
        <addColumn tableName="participant_address">
            <column name="properties" type="jsonb"/>
        </addColumn>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-515">
        <addColumn tableName="participant_address_type">
            <column name="properties" type="jsonb"/>
        </addColumn>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-516">
        <addColumn tableName="participant_group">
            <column name="properties" type="jsonb"/>
        </addColumn>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-517">
        <addColumn tableName="participant_status">
            <column name="properties" type="jsonb"/>
        </addColumn>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-518">
        <addColumn tableName="participant_type">
            <column name="properties" type="jsonb"/>
        </addColumn>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-519">
        <addColumn tableName="participant_type_group">
            <column name="properties" type="jsonb"/>
        </addColumn>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-520">
        <addColumn tableName="profile_type">
            <column name="properties" type="jsonb"/>
        </addColumn>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-521">
        <addColumn tableName="search_form_argument">
            <column name="properties" type="jsonb"/>
        </addColumn>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-522">
        <addColumn tableName="search_form_argument_filter">
            <column name="properties" type="jsonb"/>
        </addColumn>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-523">
        <addColumn tableName="search_form_builder">
            <column name="properties" type="jsonb"/>
        </addColumn>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-524">
        <addColumn tableName="search_form_element">
            <column name="properties" type="jsonb"/>
        </addColumn>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-525">
        <addColumn tableName="search_form_section">
            <column name="properties" type="jsonb"/>
        </addColumn>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-526">
        <addColumn tableName="table_column_field">
            <column name="properties" type="jsonb"/>
        </addColumn>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-527">
        <addColumn tableName="table_custom_field">
            <column name="properties" type="jsonb"/>
        </addColumn>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-528">
        <addColumn tableName="table_type">
            <column name="properties" type="jsonb"/>
        </addColumn>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-529">
        <addColumn tableName="tenant">
            <column name="properties" type="jsonb"/>
        </addColumn>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-628">
        <addColumn tableName="audit_log_custom_field_value">
            <column name="properties" type="jsonb"/>
        </addColumn>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-629">
        <addColumn tableName="custom_field_value">
            <column name="properties" type="jsonb"/>
        </addColumn>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-530">
        <addColumn tableName="address">
            <column defaultValueComputed="gen_random_uuid()" name="address_uuid" type="uuid">
                <constraints nullable="false"/>
            </column>
        </addColumn>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-800">
        <sql>
            update address
            set address_uuid = entity_id::uuid;
        </sql>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-531">
        <addColumn tableName="audit_log_address">
            <column defaultValueComputed="gen_random_uuid()" name="address_uuid" type="uuid"/>
        </addColumn>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-801">
        <sql>
            update audit_log_address
            set address_uuid = entity_id::uuid;
        </sql>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-532">
        <addColumn tableName="app_properties">
            <column defaultValueComputed="gen_random_uuid()" name="app_properties_uuid" type="uuid">
                <constraints nullable="false"/>
            </column>
        </addColumn>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-533">
        <addColumn tableName="audit_log_app_properties">
            <column defaultValueComputed="gen_random_uuid()" name="app_properties_uuid" type="uuid"/>
        </addColumn>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-534">
        <addColumn tableName="association">
            <column defaultValueComputed="gen_random_uuid()" name="association_uuid" type="uuid">
                <constraints nullable="false"/>
            </column>
        </addColumn>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-535">
        <addColumn tableName="audit_log_association">
            <column defaultValueComputed="gen_random_uuid()" name="association_uuid" type="uuid"/>
        </addColumn>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-536">
        <addColumn tableName="audit_log_contact_group">
            <column defaultValueComputed="gen_random_uuid()" name="contact_group_uuid" type="uuid"/>
        </addColumn>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-537">
        <addColumn tableName="contact_group">
            <column defaultValueComputed="gen_random_uuid()" name="contact_group_uuid" type="uuid">
                <constraints nullable="false"/>
            </column>
        </addColumn>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-538">
        <addColumn tableName="audit_log_contact_type_group">
            <column defaultValueComputed="gen_random_uuid()" name="contact_type_group_uuid" type="uuid"/>
        </addColumn>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-539">
        <addColumn tableName="contact_type_group">
            <column defaultValueComputed="gen_random_uuid()" name="contact_type_group_uuid" type="uuid">
                <constraints nullable="false"/>
            </column>
        </addColumn>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-540">
        <addColumn tableName="audit_log_contact_type">
            <column defaultValueComputed="gen_random_uuid()" name="contact_type_uuid" type="uuid"/>
        </addColumn>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-541">
        <addColumn tableName="contact_type">
            <column defaultValueComputed="gen_random_uuid()" name="contact_type_uuid" type="uuid">
                <constraints nullable="false"/>
            </column>
        </addColumn>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-542">
        <addColumn tableName="audit_log_contact">
            <column defaultValueComputed="gen_random_uuid()" name="contact_uuid" type="uuid"/>
        </addColumn>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-543">
        <addColumn tableName="contact">
            <column defaultValueComputed="gen_random_uuid()" name="contact_uuid" type="uuid">
                <constraints nullable="false"/>
            </column>
        </addColumn>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-544">
        <addColumn tableName="audit_log_custom_entity_instance">
            <column defaultValueComputed="gen_random_uuid()" name="custom_entity_instance_uuid" type="uuid"/>
        </addColumn>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-802">
        <sql>
            update audit_log_custom_entity_instance
            set custom_entity_instance_uuid = entity_id::uuid;
        </sql>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-545">
        <addColumn tableName="custom_entity_instance">
            <column defaultValueComputed="gen_random_uuid()" name="custom_entity_instance_uuid" type="uuid">
                <constraints nullable="false"/>
            </column>
        </addColumn>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-803">
        <sql>
            update custom_entity_instance
            set custom_entity_instance_uuid = entity_id::uuid;
        </sql>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-546">
        <addColumn tableName="audit_log_custom_entity_sub_type">
            <column defaultValueComputed="gen_random_uuid()" name="custom_entity_sub_type_uuid" type="uuid"/>
        </addColumn>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-547">
        <addColumn tableName="custom_entity_sub_type">
            <column defaultValueComputed="gen_random_uuid()" name="custom_entity_sub_type_uuid" type="uuid">
                <constraints nullable="false"/>
            </column>
        </addColumn>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-548">
        <addColumn tableName="audit_log_custom_entity_type">
            <column defaultValueComputed="gen_random_uuid()" name="custom_entity_type_uuid" type="uuid"/>
        </addColumn>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-549">
        <addColumn tableName="custom_entity_type">
            <column defaultValueComputed="gen_random_uuid()" name="custom_entity_type_uuid" type="uuid">
                <constraints nullable="false"/>
            </column>
        </addColumn>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-550">
        <addColumn tableName="audit_log_document_type">
            <column defaultValueComputed="gen_random_uuid()" name="document_type_uuid" type="uuid"/>
        </addColumn>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-551">
        <addColumn tableName="document_type">
            <column defaultValueComputed="gen_random_uuid()" name="document_type_uuid" type="uuid">
                <constraints nullable="false"/>
            </column>
        </addColumn>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-552">
        <addColumn tableName="audit_log_document">
            <column defaultValueComputed="gen_random_uuid()" name="document_uuid" type="uuid"/>
        </addColumn>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-804">
        <sql>
            update audit_log_document
            set document_uuid = entity_id::uuid;
        </sql>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-553">
        <addColumn tableName="document">
            <column defaultValueComputed="gen_random_uuid()" name="document_uuid" type="uuid">
                <constraints nullable="false"/>
            </column>
        </addColumn>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-810">
        <sql>
            update document
            set document_uuid = entity_id::uuid;
        </sql>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-554">
        <addColumn tableName="audit_log_event_type">
            <column defaultValueComputed="gen_random_uuid()" name="event_type_uuid" type="uuid"/>
        </addColumn>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-555">
        <addColumn tableName="event_type">
            <column defaultValueComputed="gen_random_uuid()" name="event_type_uuid" type="uuid">
                <constraints nullable="false"/>
            </column>
        </addColumn>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-556">
        <addColumn tableName="audit_log_license_activity_fee">
            <column defaultValueComputed="gen_random_uuid()" name="license_activity_fee_uuid" type="uuid"/>
        </addColumn>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-557">
        <addColumn tableName="license_activity_fee">
            <column defaultValueComputed="gen_random_uuid()" name="license_activity_fee_uuid" type="uuid">
                <constraints nullable="false"/>
            </column>
        </addColumn>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-558">
        <addColumn tableName="audit_log_license_activity">
            <column defaultValueComputed="gen_random_uuid()" name="license_activity_uuid" type="uuid"/>
        </addColumn>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-559">
        <addColumn tableName="license_activity">
            <column defaultValueComputed="gen_random_uuid()" name="license_activity_uuid" type="uuid">
                <constraints nullable="false"/>
            </column>
        </addColumn>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-560">
        <addColumn tableName="audit_log_license_status">
            <column defaultValueComputed="gen_random_uuid()" name="license_status_uuid" type="uuid"/>
        </addColumn>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-561">
        <addColumn tableName="license_status">
            <column defaultValueComputed="gen_random_uuid()" name="license_status_uuid" type="uuid">
                <constraints nullable="false"/>
            </column>
        </addColumn>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-562">
        <addColumn tableName="audit_log_license_type_fee_conditional">
            <column defaultValueComputed="gen_random_uuid()" name="license_type_fee_conditional_uuid" type="uuid"/>
        </addColumn>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-563">
        <addColumn tableName="license_type_fee_conditional">
            <column defaultValueComputed="gen_random_uuid()" name="license_type_fee_conditional_uuid" type="uuid">
                <constraints nullable="false"/>
            </column>
        </addColumn>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-564">
        <addColumn tableName="audit_log_license_type_fee">
            <column defaultValueComputed="gen_random_uuid()" name="license_type_fee_uuid" type="uuid"/>
        </addColumn>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-565">
        <addColumn tableName="license_type_fee">
            <column defaultValueComputed="gen_random_uuid()" name="license_type_fee_uuid" type="uuid">
                <constraints nullable="false"/>
            </column>
        </addColumn>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-566">
        <addColumn tableName="audit_log_license_type_setting">
            <column defaultValueComputed="gen_random_uuid()" name="license_type_setting_uuid" type="uuid"/>
        </addColumn>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-567">
        <addColumn tableName="license_type_setting">
            <column defaultValueComputed="gen_random_uuid()" name="license_type_setting_uuid" type="uuid">
                <constraints nullable="false"/>
            </column>
        </addColumn>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-568">
        <addColumn tableName="audit_log_license_type">
            <column defaultValueComputed="gen_random_uuid()" name="license_type_uuid" type="uuid"/>
        </addColumn>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-569">
        <addColumn tableName="license_type">
            <column defaultValueComputed="gen_random_uuid()" name="license_type_uuid" type="uuid">
                <constraints nullable="false"/>
            </column>
        </addColumn>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-570">
        <addColumn tableName="audit_log_license">
            <column defaultValueComputed="gen_random_uuid()" name="license_uuid" type="uuid"/>
        </addColumn>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-805">
        <sql>
            update audit_log_license
            set license_uuid = entity_id::uuid;
        </sql>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-571">
        <addColumn tableName="license">
            <column defaultValueComputed="gen_random_uuid()" name="license_uuid" type="uuid">
                <constraints nullable="false"/>
            </column>
        </addColumn>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-811">
        <sql>
            update license
            set license_uuid = entity_id::uuid;
        </sql>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-572">
        <addColumn tableName="audit_log_merge_request">
            <column defaultValueComputed="gen_random_uuid()" name="merge_request_uuid" type="uuid"/>
        </addColumn>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-806">
        <sql>
            update audit_log_merge_request
            set merge_request_uuid = entity_id::uuid;
        </sql>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-573">
        <addColumn tableName="merge_request">
            <column defaultValueComputed="gen_random_uuid()" name="merge_request_uuid" type="uuid">
                <constraints nullable="false"/>
            </column>
        </addColumn>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-812">
        <sql>
            update merge_request
            set merge_request_uuid = entity_id::uuid;
        </sql>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-574">
        <addColumn tableName="audit_log_multi_form_argument">
            <column defaultValueComputed="gen_random_uuid()" name="multi_form_argument_uuid" type="uuid"/>
        </addColumn>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-575">
        <addColumn tableName="multi_form_argument">
            <column defaultValueComputed="gen_random_uuid()" name="multi_form_argument_uuid" type="uuid">
                <constraints nullable="false"/>
            </column>
        </addColumn>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-576">
        <addColumn tableName="audit_log_multi_form_body_field">
            <column defaultValueComputed="gen_random_uuid()" name="multi_form_body_field_uuid" type="uuid"/>
        </addColumn>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-577">
        <addColumn tableName="multi_form_body_field">
            <column defaultValueComputed="gen_random_uuid()" name="multi_form_body_field_uuid" type="uuid">
                <constraints nullable="false"/>
            </column>
        </addColumn>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-578">
        <addColumn tableName="audit_log_multi_form_builder">
            <column defaultValueComputed="gen_random_uuid()" name="multi_form_builder_uuid" type="uuid"/>
        </addColumn>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-807">
        <sql>
            update audit_log_multi_form_builder
            set multi_form_builder_uuid = entity_id::uuid;
        </sql>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-579">
        <addColumn tableName="multi_form_builder">
            <column defaultValueComputed="gen_random_uuid()" name="multi_form_builder_uuid" type="uuid">
                <constraints nullable="false"/>
            </column>
        </addColumn>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-813">
        <sql>
            update multi_form_builder
            set multi_form_builder_uuid = entity_id::uuid;
        </sql>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-580">
        <addColumn tableName="audit_log_multi_form_conditional_display">
            <column defaultValueComputed="gen_random_uuid()" name="multi_form_conditional_display_uuid" type="uuid"/>
        </addColumn>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-581">
        <addColumn tableName="multi_form_conditional_display">
            <column defaultValueComputed="gen_random_uuid()" name="multi_form_conditional_display_uuid" type="uuid">
                <constraints nullable="false"/>
            </column>
        </addColumn>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-582">
        <addColumn tableName="audit_log_multi_form_element_options">
            <column defaultValueComputed="gen_random_uuid()" name="multi_form_element_options_uuid" type="uuid"/>
        </addColumn>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-583">
        <addColumn tableName="multi_form_element_options">
            <column defaultValueComputed="gen_random_uuid()" name="multi_form_element_options_uuid" type="uuid">
                <constraints nullable="false"/>
            </column>
        </addColumn>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-584">
        <addColumn tableName="audit_log_multi_form_form_element">
            <column defaultValueComputed="gen_random_uuid()" name="multi_form_form_element_uuid" type="uuid"/>
        </addColumn>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-585">
        <addColumn tableName="multi_form_form_element">
            <column defaultValueComputed="gen_random_uuid()" name="multi_form_form_element_uuid" type="uuid">
                <constraints nullable="false"/>
            </column>
        </addColumn>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-586">
        <addColumn tableName="audit_log_multi_form_on_form_submit_api">
            <column defaultValueComputed="gen_random_uuid()" name="multi_form_on_form_submit_api_uuid" type="uuid"/>
        </addColumn>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-587">
        <addColumn tableName="multi_form_on_form_submit_api">
            <column defaultValueComputed="gen_random_uuid()" name="multi_form_on_form_submit_api_uuid" type="uuid">
                <constraints nullable="false"/>
            </column>
        </addColumn>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-588">
        <addColumn tableName="audit_log_multi_form_on_page_next_api">
            <column defaultValueComputed="gen_random_uuid()" name="multi_form_on_page_next_api_uuid" type="uuid"/>
        </addColumn>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-589">
        <addColumn tableName="multi_form_on_page_next_api">
            <column defaultValueComputed="gen_random_uuid()" name="multi_form_on_page_next_api_uuid" type="uuid">
                <constraints nullable="false"/>
            </column>
        </addColumn>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-590">
        <addColumn tableName="audit_log_multi_form_page">
            <column defaultValueComputed="gen_random_uuid()" name="multi_form_page_uuid" type="uuid"/>
        </addColumn>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-591">
        <addColumn tableName="multi_form_page">
            <column defaultValueComputed="gen_random_uuid()" name="multi_form_page_uuid" type="uuid">
                <constraints nullable="false"/>
            </column>
        </addColumn>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-592">
        <addColumn tableName="audit_log_multi_form_query_string">
            <column defaultValueComputed="gen_random_uuid()" name="multi_form_query_string_uuid" type="uuid"/>
        </addColumn>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-593">
        <addColumn tableName="multi_form_query_string">
            <column defaultValueComputed="gen_random_uuid()" name="multi_form_query_string_uuid" type="uuid">
                <constraints nullable="false"/>
            </column>
        </addColumn>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-594">
        <addColumn tableName="audit_log_multi_form_request_body">
            <column defaultValueComputed="gen_random_uuid()" name="multi_form_request_body_uuid" type="uuid"/>
        </addColumn>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-595">
        <addColumn tableName="multi_form_request_body">
            <column defaultValueComputed="gen_random_uuid()" name="multi_form_request_body_uuid" type="uuid">
                <constraints nullable="false"/>
            </column>
        </addColumn>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-596">
        <addColumn tableName="audit_log_multi_form_request_slug">
            <column defaultValueComputed="gen_random_uuid()" name="multi_form_request_slug_uuid" type="uuid"/>
        </addColumn>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-597">
        <addColumn tableName="multi_form_request_slug">
            <column defaultValueComputed="gen_random_uuid()" name="multi_form_request_slug_uuid" type="uuid">
                <constraints nullable="false"/>
            </column>
        </addColumn>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-598">
        <addColumn tableName="audit_log_multi_form_response_error">
            <column defaultValueComputed="gen_random_uuid()" name="multi_form_response_error_uuid" type="uuid"/>
        </addColumn>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-599">
        <addColumn tableName="multi_form_response_error">
            <column defaultValueComputed="gen_random_uuid()" name="multi_form_response_error_uuid" type="uuid">
                <constraints nullable="false"/>
            </column>
        </addColumn>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-600">
        <addColumn tableName="audit_log_multi_form_response_field">
            <column defaultValueComputed="gen_random_uuid()" name="multi_form_response_field_uuid" type="uuid"/>
        </addColumn>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-601">
        <addColumn tableName="multi_form_response_field">
            <column defaultValueComputed="gen_random_uuid()" name="multi_form_response_field_uuid" type="uuid">
                <constraints nullable="false"/>
            </column>
        </addColumn>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-602">
        <addColumn tableName="audit_log_multi_form_response_success">
            <column defaultValueComputed="gen_random_uuid()" name="multi_form_response_success_uuid" type="uuid"/>
        </addColumn>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-603">
        <addColumn tableName="multi_form_response_success">
            <column defaultValueComputed="gen_random_uuid()" name="multi_form_response_success_uuid" type="uuid">
                <constraints nullable="false"/>
            </column>
        </addColumn>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-604">
        <addColumn tableName="audit_log_multi_form_response">
            <column defaultValueComputed="gen_random_uuid()" name="multi_form_response_uuid" type="uuid"/>
        </addColumn>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-605">
        <addColumn tableName="multi_form_response">
            <column defaultValueComputed="gen_random_uuid()" name="multi_form_response_uuid" type="uuid">
                <constraints nullable="false"/>
            </column>
        </addColumn>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-606">
        <addColumn tableName="audit_log_multi_form_section">
            <column defaultValueComputed="gen_random_uuid()" name="multi_form_section_uuid" type="uuid"/>
        </addColumn>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-607">
        <addColumn tableName="multi_form_section">
            <column defaultValueComputed="gen_random_uuid()" name="multi_form_section_uuid" type="uuid">
                <constraints nullable="false"/>
            </column>
        </addColumn>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-608">
        <addColumn tableName="audit_log_multi_form_trigger">
            <column defaultValueComputed="gen_random_uuid()" name="multi_form_trigger_uuid" type="uuid"/>
        </addColumn>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-609">
        <addColumn tableName="multi_form_trigger">
            <column defaultValueComputed="gen_random_uuid()" name="multi_form_trigger_uuid" type="uuid">
                <constraints nullable="false"/>
            </column>
        </addColumn>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-610">
        <addColumn tableName="audit_log_multi_form_validation">
            <column defaultValueComputed="gen_random_uuid()" name="multi_form_validation_uuid" type="uuid"/>
        </addColumn>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-611">
        <addColumn tableName="multi_form_validation">
            <column defaultValueComputed="gen_random_uuid()" name="multi_form_validation_uuid" type="uuid">
                <constraints nullable="false"/>
            </column>
        </addColumn>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-612">
        <addColumn tableName="audit_log_participant_address_type">
            <column defaultValueComputed="gen_random_uuid()" name="participant_address_type_uuid" type="uuid"/>
        </addColumn>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-613">
        <addColumn tableName="participant_address_type">
            <column defaultValueComputed="gen_random_uuid()" name="participant_address_type_uuid" type="uuid">
                <constraints nullable="false"/>
            </column>
        </addColumn>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-614">
        <addColumn tableName="audit_log_participant_address">
            <column defaultValueComputed="gen_random_uuid()" name="participant_address_uuid" type="uuid"/>
        </addColumn>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-615">
        <addColumn tableName="participant_address">
            <column defaultValueComputed="gen_random_uuid()" name="participant_address_uuid" type="uuid">
                <constraints nullable="false"/>
            </column>
        </addColumn>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-616">
        <addColumn tableName="audit_log_participant_group">
            <column defaultValueComputed="gen_random_uuid()" name="participant_group_uuid" type="uuid"/>
        </addColumn>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-617">
        <addColumn tableName="participant_group">
            <column defaultValueComputed="gen_random_uuid()" name="participant_group_uuid" type="uuid">
                <constraints nullable="false"/>
            </column>
        </addColumn>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-618">
        <addColumn tableName="audit_log_participant_status">
            <column defaultValueComputed="gen_random_uuid()" name="participant_status_uuid" type="uuid"/>
        </addColumn>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-619">
        <addColumn tableName="participant_status">
            <column defaultValueComputed="gen_random_uuid()" name="participant_status_uuid" type="uuid">
                <constraints nullable="false"/>
            </column>
        </addColumn>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-620">
        <addColumn tableName="audit_log_participant_type_group">
            <column defaultValueComputed="gen_random_uuid()" name="participant_type_group_uuid" type="uuid"/>
        </addColumn>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-621">
        <addColumn tableName="participant_type_group">
            <column defaultValueComputed="gen_random_uuid()" name="participant_type_group_uuid" type="uuid">
                <constraints nullable="false"/>
            </column>
        </addColumn>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-622">
        <addColumn tableName="audit_log_participant_type">
            <column defaultValueComputed="gen_random_uuid()" name="participant_type_uuid" type="uuid"/>
        </addColumn>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-623">
        <addColumn tableName="participant_type">
            <column defaultValueComputed="gen_random_uuid()" name="participant_type_uuid" type="uuid">
                <constraints nullable="false"/>
            </column>
        </addColumn>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-624">
        <addColumn tableName="audit_log_participant">
            <column defaultValueComputed="gen_random_uuid()" name="participant_uuid" type="uuid"/>
        </addColumn>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-808">
        <sql>
            update audit_log_participant
            set participant_uuid = entity_id::uuid;
        </sql>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-625">
        <addColumn tableName="participant">
            <column defaultValueComputed="gen_random_uuid()" name="participant_uuid" type="uuid">
                <constraints nullable="false"/>
            </column>
        </addColumn>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-814">
        <sql>
            update participant
            set participant_uuid = entity_id::uuid;
        </sql>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-626">
        <addColumn tableName="audit_log_profile_type">
            <column defaultValueComputed="gen_random_uuid()" name="profile_type_uuid" type="uuid"/>
        </addColumn>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-627">
        <addColumn tableName="profile_type">
            <column defaultValueComputed="gen_random_uuid()" name="profile_type_uuid" type="uuid">
                <constraints nullable="false"/>
            </column>
        </addColumn>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-630">
        <addColumn tableName="audit_log_search_form_argument_filter">
            <column defaultValueComputed="gen_random_uuid()" name="search_form_argument_filter_uuid" type="uuid"/>
        </addColumn>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-631">
        <addColumn tableName="search_form_argument_filter">
            <column defaultValueComputed="gen_random_uuid()" name="search_form_argument_filter_uuid" type="uuid">
                <constraints nullable="false"/>
            </column>
        </addColumn>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-632">
        <addColumn tableName="audit_log_search_form_argument">
            <column defaultValueComputed="gen_random_uuid()" name="search_form_argument_uuid" type="uuid"/>
        </addColumn>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-633">
        <addColumn tableName="search_form_argument">
            <column defaultValueComputed="gen_random_uuid()" name="search_form_argument_uuid" type="uuid">
                <constraints nullable="false"/>
            </column>
        </addColumn>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-634">
        <addColumn tableName="audit_log_search_form_builder">
            <column defaultValueComputed="gen_random_uuid()" name="search_form_builder_uuid" type="uuid"/>
        </addColumn>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-635">
        <addColumn tableName="search_form_builder">
            <column defaultValueComputed="gen_random_uuid()" name="search_form_builder_uuid" type="uuid">
                <constraints nullable="false"/>
            </column>
        </addColumn>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-636">
        <addColumn tableName="audit_log_search_form_element">
            <column defaultValueComputed="gen_random_uuid()" name="search_form_element_uuid" type="uuid"/>
        </addColumn>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-637">
        <addColumn tableName="search_form_element">
            <column defaultValueComputed="gen_random_uuid()" name="search_form_element_uuid" type="uuid">
                <constraints nullable="false"/>
            </column>
        </addColumn>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-638">
        <addColumn tableName="audit_log_search_form_section">
            <column defaultValueComputed="gen_random_uuid()" name="search_form_section_uuid" type="uuid"/>
        </addColumn>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-639">
        <addColumn tableName="search_form_section">
            <column defaultValueComputed="gen_random_uuid()" name="search_form_section_uuid" type="uuid">
                <constraints nullable="false"/>
            </column>
        </addColumn>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-640">
        <addColumn tableName="audit_log_table_column_field">
            <column defaultValueComputed="gen_random_uuid()" name="table_column_field_uuid" type="uuid"/>
        </addColumn>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-641">
        <addColumn tableName="table_column_field">
            <column defaultValueComputed="gen_random_uuid()" name="table_column_field_uuid" type="uuid">
                <constraints nullable="false"/>
            </column>
        </addColumn>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-642">
        <addColumn tableName="audit_log_table_custom_field">
            <column defaultValueComputed="gen_random_uuid()" name="table_custom_field_uuid" type="uuid"/>
        </addColumn>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-643">
        <addColumn tableName="table_custom_field">
            <column defaultValueComputed="gen_random_uuid()" name="table_custom_field_uuid" type="uuid">
                <constraints nullable="false"/>
            </column>
        </addColumn>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-644">
        <addColumn tableName="audit_log_table_type">
            <column defaultValueComputed="gen_random_uuid()" name="table_type_uuid" type="uuid"/>
        </addColumn>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-645">
        <addColumn tableName="table_type">
            <column defaultValueComputed="gen_random_uuid()" name="table_type_uuid" type="uuid">
                <constraints nullable="false"/>
            </column>
        </addColumn>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-646">
        <addColumn tableName="audit_log_tenant">
            <column defaultValueComputed="gen_random_uuid()" name="tenant_uuid" type="uuid"/>
        </addColumn>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-809">
        <sql>
            update audit_log_tenant
            set tenant_uuid = entity_id::uuid;
        </sql>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-647">
        <addColumn tableName="tenant">
            <column defaultValueComputed="gen_random_uuid()" name="tenant_uuid" type="uuid">
                <constraints nullable="false"/>
            </column>
        </addColumn>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-815">
        <sql>
            update tenant
            set tenant_uuid = entity_id::uuid;
        </sql>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-648">
        <addColumn tableName="audit_log_custom_field_value">
            <column defaultValueComputed="gen_random_uuid()" name="custom_field_value_uuid" type="uuid"/>
        </addColumn>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-649">
        <addColumn tableName="custom_field_value">
            <column defaultValueComputed="gen_random_uuid()" name="custom_field_value_uuid" type="uuid">
                <constraints nullable="false"/>
            </column>
        </addColumn>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-344">
        <addUniqueConstraint columnNames="address_uuid" constraintName="uk_fpaqq51qpmx36ukrqcp5ssxnn"
                             tableName="address"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-346">
        <addUniqueConstraint columnNames="app_properties_uuid" constraintName="uk_ejhsvs246fqrv8h21wf6apkcl"
                             tableName="app_properties"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-347">
        <addUniqueConstraint columnNames="association_uuid" constraintName="uk_r5i3k82pfnfludh5nt64g5lsf"
                             tableName="association"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-348">
        <addUniqueConstraint columnNames="contact_uuid" constraintName="uk_hyv9tc11ecs95koc1uy4od20r"
                             tableName="contact"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-349">
        <addUniqueConstraint columnNames="contact_group_uuid" constraintName="uk_fwvudyi5gq8p2qlq9nll3wvw9"
                             tableName="contact_group"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-350">
        <addUniqueConstraint columnNames="contact_type_uuid" constraintName="uk_bcogkqex5wscsp073q32lmb9r"
                             tableName="contact_type"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-351">
        <addUniqueConstraint columnNames="contact_type_group_uuid" constraintName="uk_9smg6uslute27xtwfevgrv7b0"
                             tableName="contact_type_group"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-352">
        <addUniqueConstraint columnNames="custom_entity_instance_uuid" constraintName="uk_5erastfuv5hccyxiayf3hwnso"
                             tableName="custom_entity_instance"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-354">
        <addUniqueConstraint columnNames="custom_entity_sub_type_uuid" constraintName="uk_63368u95a6d6331wmfwu301uc"
                             tableName="custom_entity_sub_type"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-355">
        <addUniqueConstraint columnNames="custom_entity_type_uuid" constraintName="uk_smgclcalew591dtjld2sm21o7"
                             tableName="custom_entity_type"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-356">
        <addUniqueConstraint columnNames="custom_field_value_uuid" constraintName="uk_3es2djmnn6isw2elb4q77ph7q"
                             tableName="custom_field_value"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-358">
        <addUniqueConstraint columnNames="document_uuid" constraintName="uk_nnwo6jivqns9t2wvqtmm0w8f5"
                             tableName="document"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-359">
        <addUniqueConstraint columnNames="document_type_uuid" constraintName="uk_movvvemn87eee36agvq68vbt0"
                             tableName="document_type"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-360">
        <addUniqueConstraint columnNames="event_type_uuid" constraintName="uk_37l2h8owxkxjhr913h8pfrnhx"
                             tableName="event_type"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-361">
        <addUniqueConstraint columnNames="license_uuid" constraintName="uk_33yeap7tkrnmxhcs58fx6wvmc"
                             tableName="license"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-363">
        <addUniqueConstraint columnNames="license_activity_uuid" constraintName="uk_qepq6p6cxo5m19baw4u6uxwxr"
                             tableName="license_activity"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-364">
        <addUniqueConstraint columnNames="license_activity_fee_uuid" constraintName="uk_9943ukyct9mf4sbmbn9v91ip3"
                             tableName="license_activity_fee"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-365">
        <addUniqueConstraint columnNames="license_status_uuid" constraintName="uk_evff57a47ol38kj40fn8tj4id"
                             tableName="license_status"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-366">
        <addUniqueConstraint columnNames="license_type_uuid" constraintName="uk_hjy841abbb2duaim9eskx72w8"
                             tableName="license_type"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-367">
        <addUniqueConstraint columnNames="license_type_fee_uuid" constraintName="uk_mjscj8e1k45o9snmcg6we5eaq"
                             tableName="license_type_fee"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-368">
        <addUniqueConstraint columnNames="license_type_fee_conditional_uuid"
                             constraintName="uk_dml6we50tlj7052voufy1fxs8" tableName="license_type_fee_conditional"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-369">
        <addUniqueConstraint columnNames="license_type_setting_uuid" constraintName="uk_rbxax274u57el6xkm668e6r1b"
                             tableName="license_type_setting"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-370">
        <addUniqueConstraint columnNames="merge_request_uuid" constraintName="uk_6v5hh25bctx0sgm2yy69lok8i"
                             tableName="merge_request"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-371">
        <addUniqueConstraint columnNames="multi_form_argument_uuid" constraintName="uk_csdkbu0pdgtvopjjsk1nu6oua"
                             tableName="multi_form_argument"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-372">
        <addUniqueConstraint columnNames="multi_form_body_field_uuid" constraintName="uk_24yrwc8g1k29i749d9y6xikf1"
                             tableName="multi_form_body_field"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-373">
        <addUniqueConstraint columnNames="multi_form_builder_uuid" constraintName="uk_i1i8gy6np0m0hk00bwv7hk6hs"
                             tableName="multi_form_builder"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-374">
        <addUniqueConstraint columnNames="multi_form_conditional_display_uuid"
                             constraintName="uk_2outhehbirlos3o26pcxvy3wh" tableName="multi_form_conditional_display"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-375">
        <addUniqueConstraint columnNames="multi_form_element_options_uuid" constraintName="uk_k50l7huih5348d9ffti7b4ddh"
                             tableName="multi_form_element_options"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-376">
        <addUniqueConstraint columnNames="multi_form_form_element_uuid" constraintName="uk_o2qk9erasbj9mer66cbcv8k1a"
                             tableName="multi_form_form_element"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-377">
        <addUniqueConstraint columnNames="multi_form_on_form_submit_api_uuid"
                             constraintName="uk_fmj2dnflud07alpaaomp4te08" tableName="multi_form_on_form_submit_api"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-378">
        <addUniqueConstraint columnNames="multi_form_on_page_next_api_uuid"
                             constraintName="uk_jc1epyyla85jjua7lm1b4td9p" tableName="multi_form_on_page_next_api"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-379">
        <addUniqueConstraint columnNames="multi_form_page_uuid" constraintName="uk_k23p43bf5ln7ijy9c1l2cs5jw"
                             tableName="multi_form_page"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-380">
        <addUniqueConstraint columnNames="multi_form_query_string_uuid" constraintName="uk_m4enakr1jbd059t55svgwt8ls"
                             tableName="multi_form_query_string"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-381">
        <addUniqueConstraint columnNames="multi_form_request_body_uuid" constraintName="uk_sxas9egp4rgwvvslnrmerk9a"
                             tableName="multi_form_request_body"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-382">
        <addUniqueConstraint columnNames="multi_form_request_slug_uuid" constraintName="uk_9buvcn2wsrfdhhyfttd2cx587"
                             tableName="multi_form_request_slug"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-383">
        <addUniqueConstraint columnNames="multi_form_response_uuid" constraintName="uk_e2ytdbiq7henvf1tkfa8h7so3"
                             tableName="multi_form_response"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-384">
        <addUniqueConstraint columnNames="multi_form_response_error_uuid" constraintName="uk_oa60i4pa86xmreqi4vx4axsec"
                             tableName="multi_form_response_error"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-385">
        <addUniqueConstraint columnNames="multi_form_response_field_uuid" constraintName="uk_1a4hwoxd6dk2897kpda2tuxaj"
                             tableName="multi_form_response_field"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-386">
        <addUniqueConstraint columnNames="multi_form_response_success_uuid"
                             constraintName="uk_fputdpvgk2o8xfv7g6u0pbnr9" tableName="multi_form_response_success"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-387">
        <addUniqueConstraint columnNames="multi_form_section_uuid" constraintName="uk_nnhe5i65t4q4m0dol9gy0yl9e"
                             tableName="multi_form_section"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-388">
        <addUniqueConstraint columnNames="multi_form_trigger_uuid" constraintName="uk_tc9ldxohxflyia4479pqx74m8"
                             tableName="multi_form_trigger"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-389">
        <addUniqueConstraint columnNames="multi_form_validation_uuid" constraintName="uk_53thsuey3ioaqg1xdgu11drg"
                             tableName="multi_form_validation"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-390">
        <addUniqueConstraint columnNames="participant_uuid" constraintName="uk_lvibuj817skxdj4r1deb5ha5l"
                             tableName="participant"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-392">
        <addUniqueConstraint columnNames="participant_address_uuid" constraintName="uk_kasl8qfeys4v06w5f2t14qcoa"
                             tableName="participant_address"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-393">
        <addUniqueConstraint columnNames="participant_address_type_uuid" constraintName="uk_5iqolvyrgl6oxcr7heuk4tcfs"
                             tableName="participant_address_type"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-394">
        <addUniqueConstraint columnNames="participant_group_uuid" constraintName="uk_1pgqxejh18xslu8h0asd19n7p"
                             tableName="participant_group"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-396">
        <addUniqueConstraint columnNames="participant_status_uuid" constraintName="uk_1qsua7frl7mo0jol87oteu3np"
                             tableName="participant_status"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-397">
        <addUniqueConstraint columnNames="participant_type_uuid" constraintName="uk_2jgi2nk23ybh6ou32bnf01y7m"
                             tableName="participant_type"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-398">
        <addUniqueConstraint columnNames="participant_type_group_uuid" constraintName="uk_an6mr0pfii097hdv5mw7hc99p"
                             tableName="participant_type_group"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-399">
        <addUniqueConstraint columnNames="profile_type_uuid" constraintName="uk_gpv23etlb2uukjodpiriomj1b"
                             tableName="profile_type"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-401">
        <addUniqueConstraint columnNames="search_form_argument_uuid" constraintName="uk_jxbv91julg0bhpy2fmk4g5sgk"
                             tableName="search_form_argument"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-402">
        <addUniqueConstraint columnNames="search_form_argument_filter_uuid"
                             constraintName="uk_do6jm2wcjvptmpib1go4a5gn2" tableName="search_form_argument_filter"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-403">
        <addUniqueConstraint columnNames="search_form_builder_uuid" constraintName="uk_7clt1m6u5a0nm15g02cpvhf0e"
                             tableName="search_form_builder"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-404">
        <addUniqueConstraint columnNames="search_form_element_uuid" constraintName="uk_cni64676mk1di44df0dxjovpi"
                             tableName="search_form_element"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-405">
        <addUniqueConstraint columnNames="search_form_section_uuid" constraintName="uk_lgnxpivofqdnuekubf81sbrxy"
                             tableName="search_form_section"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-406">
        <addUniqueConstraint columnNames="table_column_field_uuid" constraintName="uk_a1ffaol4ul26gqwsutjtgjaua"
                             tableName="table_column_field"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-407">
        <addUniqueConstraint columnNames="table_custom_field_uuid" constraintName="uk_7t4xkp3apj8ld1h809vfpdu19"
                             tableName="table_custom_field"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-408">
        <addUniqueConstraint columnNames="table_type_uuid" constraintName="uk_kyivy5fxohc0ieb3h60nsd2a9"
                             tableName="table_type"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-409">
        <addUniqueConstraint columnNames="tenant_uuid" constraintName="uk_8ildjky9h7nqe418cbhhw3liv"
                             tableName="tenant"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-160">
        <dropNotNullConstraint columnDataType="uuid" columnName="json_storage_uuid" tableName="audit_log_json_storage"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-161">
        <addDefaultValue columnDataType="uuid" columnName="json_storage_uuid" defaultValueComputed="gen_random_uuid()"
                         tableName="audit_log_json_storage"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-162">
        <addDefaultValue columnDataType="uuid" columnName="json_storage_uuid" defaultValueComputed="gen_random_uuid()"
                         tableName="json_storage"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-320">
        <dropNotNullConstraint columnDataType="uuid" columnName="scheduler_uuid" tableName="scheduler"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-309">
        <dropNotNullConstraint columnDataType="uuid" columnName="property_type_uuid"
                               tableName="audit_log_property_type"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-310">
        <addDefaultValue columnDataType="uuid" columnName="property_type_uuid" defaultValueComputed="gen_random_uuid()"
                         tableName="audit_log_property_type"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-311">
        <addDefaultValue columnDataType="uuid" columnName="property_type_uuid" defaultValueComputed="gen_random_uuid()"
                         tableName="property_type"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-686">
        <dropUniqueConstraint constraintName="audit_log_property_type_property_type_uuid_key"
                              tableName="audit_log_property_type"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-684">
        <dropUniqueConstraint constraintName="audit_log_json_storage_json_storage_uuid_key"
                              tableName="audit_log_json_storage"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-345">
        <addUniqueConstraint columnNames="association_type, address_id" constraintName="uk_psr3inle3kkvi1eqyhhyvs91m"
                             tableName="address"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-353">
        <addUniqueConstraint columnNames="association_type, custom_entity_instance_id"
                             constraintName="uk_ayl7rudi2nq90otn8hb96bs3e" tableName="custom_entity_instance"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-357">
        <addUniqueConstraint columnNames="association_type, document_id" constraintName="uk_grtb7tyy025is840cdt9d09mm"
                             tableName="document"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-362">
        <addUniqueConstraint columnNames="association_type, license_id" constraintName="uk_mbpeef7jhmf248d2h37antavq"
                             tableName="license"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-391">
        <addUniqueConstraint columnNames="association_type, participant_id"
                             constraintName="uk_qbsd4l9wem9pcinned2cah6rs" tableName="participant"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-400">
        <addUniqueConstraint columnNames="table_name, property_name" constraintName="property_type_unique"
                             tableName="property_type"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-653">
        <addForeignKeyConstraint baseColumnNames="revision_id" baseTableName="audit_log_json_storage"
                                 constraintName="fk8w7jiq37ymubrux28bj4fmmq4" deferrable="false"
                                 initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION"
                                 referencedColumnNames="audit_log_revision_id" referencedTableName="audit_log_revision"
                                 validate="true"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-657">
        <addForeignKeyConstraint baseColumnNames="revision_id" baseTableName="audit_log_scheduler"
                                 constraintName="fkggi0jubkjahhqj63qb2hyn8kb" deferrable="false"
                                 initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION"
                                 referencedColumnNames="audit_log_revision_id" referencedTableName="audit_log_revision"
                                 validate="true"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-658">
        <addForeignKeyConstraint baseColumnNames="revision_id" baseTableName="audit_log_merge_request"
                                 constraintName="fkmnipn4s7imd5ptd28x5v46qo8" deferrable="false"
                                 initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION"
                                 referencedColumnNames="audit_log_revision_id" referencedTableName="audit_log_revision"
                                 validate="true"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-662">
        <addForeignKeyConstraint baseColumnNames="revision_id" baseTableName="audit_log_property_type"
                                 constraintName="fkph3vvdjiaedcco923ivk6qkxc" deferrable="false"
                                 initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION"
                                 referencedColumnNames="audit_log_revision_id" referencedTableName="audit_log_revision"
                                 validate="true"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-666">
        <dropForeignKeyConstraint baseTableName="profile_form_argument" constraintName="fk1a8f5xbq7g2w3nps1y20qbwq5"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-668">
        <dropForeignKeyConstraint baseTableName="profile_form_section" constraintName="fk4c5h0mih8eokdoog9vdix61vl"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-669">
        <dropForeignKeyConstraint baseTableName="profile_form_section" constraintName="fk4suov775ox51se4xt6bpspy8s"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-670">
        <dropForeignKeyConstraint baseTableName="audit_log_profile_form_builder"
                                  constraintName="fk6g9eg2ppd9m7gipvr7lkc1vx6"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-671">
        <dropForeignKeyConstraint baseTableName="profile_form_argument_filter"
                                  constraintName="fk7jje3qxfqma78owf42v86mmh3"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-672">
        <dropForeignKeyConstraint baseTableName="audit_log_profile_form_element"
                                  constraintName="fk7k3mcod5dum81m0qj5ybh1hsq"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-673">
        <dropForeignKeyConstraint baseTableName="profile_form_element" constraintName="fkca4y6uc7f6hb07sce0cdheau0"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-674">
        <dropForeignKeyConstraint baseTableName="profile_form_argument" constraintName="fkcvjq64tos8yygoql8txlm2hw0"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-675">
        <dropForeignKeyConstraint baseTableName="audit_log_profile_form_argument"
                                  constraintName="fkd5qbpwfaqi2384rw91ka6f9rh"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-676">
        <dropForeignKeyConstraint baseTableName="profile_form_section_group"
                                  constraintName="fkdsm6jrt4wh3m46t3jv1sfhat1"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-677">
        <dropForeignKeyConstraint baseTableName="audit_log_profile_form_argument_filter"
                                  constraintName="fkewu0gk7fkk2q8l2fgd1g6h3pc"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-678">
        <dropForeignKeyConstraint baseTableName="profile_form_argument" constraintName="fkgj0g570k0x8be1y80neybxlyv"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-679">
        <dropForeignKeyConstraint baseTableName="audit_log_profile_form_section_group"
                                  constraintName="fkl7d3ux2kmk8uk5ahnlkenlc7w"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-680">
        <dropForeignKeyConstraint baseTableName="profile_form_argument_filter"
                                  constraintName="fklnusdficm775b64gmr7ihciio"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-681">
        <dropForeignKeyConstraint baseTableName="audit_log_profile_form_section"
                                  constraintName="fkowyoluhrjbq47ywpymudea3i6"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-682">
        <dropForeignKeyConstraint baseTableName="profile_type" constraintName="fkrsbdqtfhcpawjgyvq1qp49kph"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-683">
        <dropForeignKeyConstraint baseTableName="profile_form_argument_filter"
                                  constraintName="fkt6yn0ob53f7sn5oifwi7v3c3l"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-687">
        <dropUniqueConstraint constraintName="merge_request_entity_id_key" tableName="merge_request"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-688">
        <dropUniqueConstraint constraintName="uk8s1f994p4la2ipb13me2xqm1w" tableName="domain_event_entry"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-689">
        <dropUniqueConstraint constraintName="uk_1tkeepx7gvskxkrub0bfqyvk5" tableName="profile_form_builder"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-690">
        <dropUniqueConstraint constraintName="uk_7nalbf0yjq8hnsrrtsasmvq1h" tableName="address"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-691">
        <dropUniqueConstraint constraintName="uk_d4sp0lf3d5r9odhnqyda7ti1a" tableName="participant"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-692">
        <dropUniqueConstraint constraintName="uk_document_entity_id" tableName="document"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-693">
        <dropUniqueConstraint constraintName="uk_e1uucjseo68gopmnd0vgdl44h" tableName="snapshot_event_entry"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-694">
        <dropUniqueConstraint constraintName="uk_fjsjdxn80vcxic3wyq5cwpfy6" tableName="tenant"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-695">
        <dropUniqueConstraint constraintName="uk_fwe6lsa8bfo6hyas6ud3m8c7x" tableName="domain_event_entry"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-696">
        <dropUniqueConstraint constraintName="uk_ht4qr080hpco8fob5cy8yjyh4" tableName="custom_entity_instance"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-697">
        <dropUniqueConstraint constraintName="uk_j3ta166lgj76nbmhj8a1w0236" tableName="multi_form_builder"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-698">
        <dropUniqueConstraint constraintName="uk_nd1alljxllnqg802tmtcb5egy" tableName="license"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-699">
        <dropUniqueConstraint constraintName="uk_participant_status_code" tableName="participant_status"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-700">
        <dropUniqueConstraint constraintName="ukhlr8io86j74qy298xf720n16v" tableName="dead_letter_entry"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-701">
        <sql>
            DROP TABLE IF EXISTS association_value_entry;
        </sql>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-702">
        <dropTable tableName="audit_log_profile_form_argument"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-703">
        <dropTable tableName="audit_log_profile_form_argument_filter"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-704">
        <dropTable tableName="audit_log_profile_form_builder"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-705">
        <dropTable tableName="audit_log_profile_form_element"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-706">
        <dropTable tableName="audit_log_profile_form_section"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-707">
        <dropTable tableName="audit_log_profile_form_section_group"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-722">
        <sql>
            DROP TABLE IF EXISTS dead_letter_entry;
        </sql>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-723">
        <sql>
            DROP TABLE IF EXISTS domain_event_entry;
        </sql>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-724">
        <sql>
            DROP TABLE IF EXISTS duplicate_owner_ownerid;
        </sql>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-725">
        <sql>
            DROP TABLE IF EXISTS duplicate_owner_phone;
        </sql>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-726">
        <sql>
            DROP TABLE IF EXISTS duplicateaddress;
        </sql>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-727">
        <sql>
            DROP TABLE IF EXISTS existing_license;
        </sql>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-728">
        <dropTable tableName="profile_form_argument"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-729">
        <dropTable tableName="profile_form_argument_filter"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-730">
        <dropTable tableName="profile_form_builder"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-731">
        <dropTable tableName="profile_form_element"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-732">
        <dropTable tableName="profile_form_section"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-733">
        <dropTable tableName="profile_form_section_group"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-734">
        <sql>
            DROP TABLE IF EXISTS saga_entry;
        </sql>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-735">
        <sql>
            DROP TABLE IF EXISTS snapshot_event_entry;
        </sql>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-736">
        <sql>
            DROP TABLE IF EXISTS token_entry;
        </sql>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-737">
        <dropColumn columnName="created_by" tableName="audit_log_scheduler"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-738">
        <dropColumn columnName="created_date" tableName="audit_log_scheduler"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-756">
        <dropColumn columnName="profile_form_builder_id" tableName="audit_log_profile_type"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-757">
        <dropColumn columnName="profile_form_builder_id" tableName="profile_type"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-759">
        <dropColumn columnName="profile_form_argument_filter_id" tableName="audit_log_search_form_argument_filter"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-760">
        <dropColumn columnName="profile_form_argument_filter_id" tableName="search_form_argument_filter"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-787">
        <dropSequence sequenceName="association_value_entry_seq"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-788">
        <dropSequence sequenceName="domain_event_entry_seq"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-1">
        <dropForeignKeyConstraint baseTableName="license_activity_fee" constraintName="fk3q03bys6i6gluehlfe9a80xr2"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-2">
        <addForeignKeyConstraint baseColumnNames="license_activity_id" baseTableName="license_activity_fee"
                                 constraintName="fk3q03bys6i6gluehlfe9a80xr2" onDelete="NO ACTION" onUpdate="NO ACTION"
                                 referencedColumnNames="license_activity_id" referencedTableName="license_activity"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-3">
        <dropForeignKeyConstraint baseTableName="license_activity" constraintName="fk632xb116gmjr0no5rb8smylb6"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-4">
        <addForeignKeyConstraint baseColumnNames="license_id" baseTableName="license_activity"
                                 constraintName="fk632xb116gmjr0no5rb8smylb6" onDelete="NO ACTION" onUpdate="NO ACTION"
                                 referencedColumnNames="license_id" referencedTableName="license"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-5">
        <dropNotNullConstraint columnDataType="boolean" columnName="approved" tableName="audit_log_license"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-6">
        <dropDefaultValue columnDataType="boolean" columnName="approved" tableName="audit_log_license"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-7">
        <dropNotNullConstraint columnDataType="boolean" columnName="approved" tableName="audit_log_participant"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-11">
        <modifyDataType columnName="approved_date" newDataType="timestamp" tableName="audit_log_license"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-12">
        <modifyDataType columnName="approved_date" newDataType="timestamp" tableName="audit_log_participant"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-13">
        <modifyDataType columnName="approved_date" newDataType="timestamp" tableName="license"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-14">
        <modifyDataType columnName="approved_date" newDataType="timestamp" tableName="participant"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-16">
        <dropNotNullConstraint columnDataType="varchar(255)" columnName="association_type"
                               tableName="audit_log_address"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-17">
        <dropDefaultValue columnDataType="varchar(255)" columnName="association_type" tableName="audit_log_address"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-18">
        <dropNotNullConstraint columnDataType="varchar(255)" columnName="association_type"
                               tableName="audit_log_custom_entity_instance"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-20">
        <dropNotNullConstraint columnDataType="varchar(255)" columnName="association_type"
                               tableName="audit_log_document"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-21">
        <dropDefaultValue columnDataType="varchar(255)" columnName="association_type" tableName="audit_log_document"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-22">
        <dropNotNullConstraint columnDataType="varchar(255)" columnName="association_type"
                               tableName="audit_log_license"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-23">
        <dropDefaultValue columnDataType="varchar(255)" columnName="association_type" tableName="audit_log_license"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-24">
        <dropNotNullConstraint columnDataType="varchar(255)" columnName="association_type"
                               tableName="audit_log_participant"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-25">
        <dropDefaultValue columnDataType="varchar(255)" columnName="association_type"
                          tableName="audit_log_participant"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-26">
        <dropDefaultValue columnDataType="varchar(255)" columnName="association_type"
                          tableName="custom_entity_instance"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-32">
        <modifyDataType columnName="code" newDataType="varchar(255)" tableName="audit_log_license_status"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-33">
        <dropNotNullConstraint columnDataType="varchar(255)" columnName="code" tableName="audit_log_license_status"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-34">
        <dropDefaultValue columnDataType="varchar(255)" columnName="code" tableName="audit_log_license_status"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-35">
        <modifyDataType columnName="code" newDataType="varchar(255)" tableName="license_status"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-36">
        <dropDefaultValue columnDataType="varchar(255)" columnName="code" tableName="license_status"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-37">
        <modifyDataType columnName="created_by" newDataType="varchar(255)" tableName="address"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-38">
        <modifyDataType columnName="created_by" newDataType="varchar(255)" tableName="app_properties"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-39">
        <modifyDataType columnName="created_by" newDataType="varchar(255)" tableName="association"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-40">
        <modifyDataType columnName="created_by" newDataType="varchar(255)" tableName="contact"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-41">
        <modifyDataType columnName="created_by" newDataType="varchar(255)" tableName="contact_group"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-42">
        <modifyDataType columnName="created_by" newDataType="varchar(255)" tableName="contact_type"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-43">
        <modifyDataType columnName="created_by" newDataType="varchar(255)" tableName="contact_type_group"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-44">
        <modifyDataType columnName="created_by" newDataType="varchar(255)" tableName="custom_entity_instance"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-45">
        <modifyDataType columnName="created_by" newDataType="varchar(255)" tableName="custom_entity_sub_type"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-46">
        <modifyDataType columnName="created_by" newDataType="varchar(255)" tableName="custom_entity_type"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-47">
        <modifyDataType columnName="created_by" newDataType="varchar(255)" tableName="custom_field_value"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-48">
        <modifyDataType columnName="created_by" newDataType="varchar(255)" tableName="document"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-49">
        <modifyDataType columnName="created_by" newDataType="varchar(255)" tableName="document_type"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-50">
        <modifyDataType columnName="created_by" newDataType="varchar(255)" tableName="event_type"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-51">
        <modifyDataType columnName="created_by" newDataType="varchar(255)" tableName="json_storage"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-52">
        <modifyDataType columnName="created_by" newDataType="varchar(255)" tableName="license"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-53">
        <modifyDataType columnName="created_by" newDataType="varchar(255)" tableName="license_activity"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-54">
        <modifyDataType columnName="created_by" newDataType="varchar(255)" tableName="license_activity_fee"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-55">
        <modifyDataType columnName="created_by" newDataType="varchar(255)" tableName="license_status"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-56">
        <modifyDataType columnName="created_by" newDataType="varchar(255)" tableName="license_type"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-57">
        <modifyDataType columnName="created_by" newDataType="varchar(255)" tableName="license_type_fee"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-58">
        <modifyDataType columnName="created_by" newDataType="varchar(255)" tableName="license_type_fee_conditional"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-59">
        <modifyDataType columnName="created_by" newDataType="varchar(255)" tableName="license_type_setting"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-60">
        <modifyDataType columnName="created_by" newDataType="varchar(255)" tableName="merge_request"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-61">
        <modifyDataType columnName="created_by" newDataType="varchar(255)" tableName="multi_form_argument"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-62">
        <modifyDataType columnName="created_by" newDataType="varchar(255)" tableName="multi_form_body_field"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-63">
        <modifyDataType columnName="created_by" newDataType="varchar(255)" tableName="multi_form_builder"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-64">
        <modifyDataType columnName="created_by" newDataType="varchar(255)" tableName="multi_form_conditional_display"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-65">
        <modifyDataType columnName="created_by" newDataType="varchar(255)" tableName="multi_form_element_options"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-66">
        <modifyDataType columnName="created_by" newDataType="varchar(255)" tableName="multi_form_form_element"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-67">
        <modifyDataType columnName="created_by" newDataType="varchar(255)" tableName="multi_form_on_form_submit_api"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-68">
        <modifyDataType columnName="created_by" newDataType="varchar(255)" tableName="multi_form_on_page_next_api"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-69">
        <modifyDataType columnName="created_by" newDataType="varchar(255)" tableName="multi_form_page"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-70">
        <modifyDataType columnName="created_by" newDataType="varchar(255)" tableName="multi_form_query_string"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-71">
        <modifyDataType columnName="created_by" newDataType="varchar(255)" tableName="multi_form_request_body"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-72">
        <modifyDataType columnName="created_by" newDataType="varchar(255)" tableName="multi_form_request_slug"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-73">
        <modifyDataType columnName="created_by" newDataType="varchar(255)" tableName="multi_form_response"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-74">
        <modifyDataType columnName="created_by" newDataType="varchar(255)" tableName="multi_form_response_error"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-75">
        <modifyDataType columnName="created_by" newDataType="varchar(255)" tableName="multi_form_response_field"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-76">
        <modifyDataType columnName="created_by" newDataType="varchar(255)" tableName="multi_form_response_success"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-77">
        <modifyDataType columnName="created_by" newDataType="varchar(255)" tableName="multi_form_section"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-78">
        <modifyDataType columnName="created_by" newDataType="varchar(255)" tableName="multi_form_trigger"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-79">
        <modifyDataType columnName="created_by" newDataType="varchar(255)" tableName="multi_form_validation"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-80">
        <modifyDataType columnName="created_by" newDataType="varchar(255)" tableName="participant"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-81">
        <modifyDataType columnName="created_by" newDataType="varchar(255)" tableName="participant_address"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-82">
        <modifyDataType columnName="created_by" newDataType="varchar(255)" tableName="participant_address_type"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-83">
        <modifyDataType columnName="created_by" newDataType="varchar(255)" tableName="participant_group"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-84">
        <modifyDataType columnName="created_by" newDataType="varchar(255)" tableName="participant_status"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-85">
        <modifyDataType columnName="created_by" newDataType="varchar(255)" tableName="participant_type"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-86">
        <modifyDataType columnName="created_by" newDataType="varchar(255)" tableName="participant_type_group"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-87">
        <modifyDataType columnName="created_by" newDataType="varchar(255)" tableName="profile_type"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-88">
        <modifyDataType columnName="created_by" newDataType="varchar(255)" tableName="property_type"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-89">
        <modifyDataType columnName="created_by" newDataType="varchar(255)" tableName="scheduler"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-90">
        <modifyDataType columnName="created_by" newDataType="varchar(255)" tableName="search_form_argument"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-91">
        <modifyDataType columnName="created_by" newDataType="varchar(255)" tableName="search_form_argument_filter"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-92">
        <modifyDataType columnName="created_by" newDataType="varchar(255)" tableName="search_form_builder"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-93">
        <modifyDataType columnName="created_by" newDataType="varchar(255)" tableName="search_form_element"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-94">
        <modifyDataType columnName="created_by" newDataType="varchar(255)" tableName="search_form_section"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-95">
        <modifyDataType columnName="created_by" newDataType="varchar(255)" tableName="table_column_field"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-96">
        <modifyDataType columnName="created_by" newDataType="varchar(255)" tableName="table_custom_field"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-97">
        <modifyDataType columnName="created_by" newDataType="varchar(255)" tableName="table_type"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-98">
        <modifyDataType columnName="created_by" newDataType="varchar(255)" tableName="tenant"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-99">
        <modifyDataType columnName="created_date" newDataType="timestamp" tableName="address"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-100">
        <modifyDataType columnName="created_date" newDataType="timestamp" tableName="app_properties"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-101">
        <modifyDataType columnName="created_date" newDataType="timestamp" tableName="association"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-102">
        <modifyDataType columnName="created_date" newDataType="timestamp" tableName="contact"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-103">
        <modifyDataType columnName="created_date" newDataType="timestamp" tableName="contact_group"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-104">
        <modifyDataType columnName="created_date" newDataType="timestamp" tableName="contact_type"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-105">
        <modifyDataType columnName="created_date" newDataType="timestamp" tableName="contact_type_group"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-106">
        <modifyDataType columnName="created_date" newDataType="timestamp" tableName="custom_entity_instance"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-107">
        <modifyDataType columnName="created_date" newDataType="timestamp" tableName="custom_entity_sub_type"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-108">
        <modifyDataType columnName="created_date" newDataType="timestamp" tableName="custom_entity_type"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-109">
        <modifyDataType columnName="created_date" newDataType="timestamp" tableName="custom_field_value"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-110">
        <modifyDataType columnName="created_date" newDataType="timestamp" tableName="json_storage"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-111">
        <modifyDataType columnName="created_date" newDataType="timestamp" tableName="license"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-112">
        <modifyDataType columnName="created_date" newDataType="timestamp" tableName="license_activity_fee"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-113">
        <modifyDataType columnName="created_date" newDataType="timestamp" tableName="license_status"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-114">
        <modifyDataType columnName="created_date" newDataType="timestamp" tableName="license_type"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-115">
        <modifyDataType columnName="created_date" newDataType="timestamp" tableName="license_type_fee"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-116">
        <modifyDataType columnName="created_date" newDataType="timestamp" tableName="license_type_fee_conditional"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-117">
        <modifyDataType columnName="created_date" newDataType="timestamp" tableName="license_type_setting"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-118">
        <modifyDataType columnName="created_date" newDataType="timestamp" tableName="merge_request"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-119">
        <modifyDataType columnName="created_date" newDataType="timestamp" tableName="multi_form_argument"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-120">
        <modifyDataType columnName="created_date" newDataType="timestamp" tableName="multi_form_body_field"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-121">
        <modifyDataType columnName="created_date" newDataType="timestamp" tableName="multi_form_builder"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-122">
        <modifyDataType columnName="created_date" newDataType="timestamp" tableName="multi_form_conditional_display"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-123">
        <modifyDataType columnName="created_date" newDataType="timestamp" tableName="multi_form_element_options"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-124">
        <modifyDataType columnName="created_date" newDataType="timestamp" tableName="multi_form_form_element"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-125">
        <modifyDataType columnName="created_date" newDataType="timestamp" tableName="multi_form_on_form_submit_api"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-126">
        <modifyDataType columnName="created_date" newDataType="timestamp" tableName="multi_form_on_page_next_api"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-127">
        <modifyDataType columnName="created_date" newDataType="timestamp" tableName="multi_form_page"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-128">
        <modifyDataType columnName="created_date" newDataType="timestamp" tableName="multi_form_query_string"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-129">
        <modifyDataType columnName="created_date" newDataType="timestamp" tableName="multi_form_request_body"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-130">
        <modifyDataType columnName="created_date" newDataType="timestamp" tableName="multi_form_request_slug"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-131">
        <modifyDataType columnName="created_date" newDataType="timestamp" tableName="multi_form_response"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-132">
        <modifyDataType columnName="created_date" newDataType="timestamp" tableName="multi_form_response_error"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-133">
        <modifyDataType columnName="created_date" newDataType="timestamp" tableName="multi_form_response_field"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-134">
        <modifyDataType columnName="created_date" newDataType="timestamp" tableName="multi_form_response_success"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-135">
        <modifyDataType columnName="created_date" newDataType="timestamp" tableName="multi_form_section"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-136">
        <modifyDataType columnName="created_date" newDataType="timestamp" tableName="multi_form_validation"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-137">
        <modifyDataType columnName="created_date" newDataType="timestamp" tableName="participant"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-138">
        <modifyDataType columnName="created_date" newDataType="timestamp" tableName="participant_address"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-139">
        <modifyDataType columnName="created_date" newDataType="timestamp" tableName="participant_address_type"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-140">
        <modifyDataType columnName="created_date" newDataType="timestamp" tableName="participant_group"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-141">
        <modifyDataType columnName="created_date" newDataType="timestamp" tableName="participant_type"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-142">
        <modifyDataType columnName="created_date" newDataType="timestamp" tableName="participant_type_group"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-143">
        <modifyDataType columnName="created_date" newDataType="timestamp" tableName="profile_type"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-144">
        <modifyDataType columnName="created_date" newDataType="timestamp" tableName="property_type"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-145">
        <modifyDataType columnName="created_date" newDataType="timestamp" tableName="scheduler"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-146">
        <addNotNullConstraint columnDataType="timestamp" columnName="created_date" tableName="scheduler"
                              validate="true"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-147">
        <modifyDataType columnName="created_date" newDataType="timestamp" tableName="search_form_argument"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-148">
        <modifyDataType columnName="created_date" newDataType="timestamp" tableName="search_form_argument_filter"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-149">
        <modifyDataType columnName="created_date" newDataType="timestamp" tableName="search_form_builder"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-150">
        <modifyDataType columnName="created_date" newDataType="timestamp" tableName="search_form_element"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-151">
        <modifyDataType columnName="created_date" newDataType="timestamp" tableName="search_form_section"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-152">
        <modifyDataType columnName="created_date" newDataType="timestamp" tableName="table_column_field"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-153">
        <modifyDataType columnName="created_date" newDataType="timestamp" tableName="table_custom_field"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-154">
        <modifyDataType columnName="created_date" newDataType="timestamp" tableName="table_type"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-155">
        <modifyDataType columnName="created_date" newDataType="timestamp" tableName="tenant"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-1562">
        <modifyDataType columnName="denied_comment" newDataType="text" tableName="audit_log_license"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-1572">
        <modifyDataType columnName="denied_comment" newDataType="text" tableName="license"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-158">
        <modifyDataType columnName="issued_date" newDataType="timestamp" tableName="license"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-159">
        <dropNotNullConstraint columnDataType="jsonb" columnName="json_data" tableName="audit_log_json_storage"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-163">
        <modifyDataType columnName="last_modified_by" newDataType="varchar(255)" tableName="address"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-164">
        <modifyDataType columnName="last_modified_by" newDataType="varchar(255)" tableName="app_properties"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-165">
        <modifyDataType columnName="last_modified_by" newDataType="varchar(255)" tableName="association"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-166">
        <modifyDataType columnName="last_modified_by" newDataType="varchar(255)" tableName="audit_log_json_storage"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-167">
        <dropNotNullConstraint columnDataType="varchar(255)" columnName="last_modified_by"
                               tableName="audit_log_json_storage"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-168">
        <dropNotNullConstraint columnDataType="varchar(255)" columnName="last_modified_by"
                               tableName="audit_log_merge_request"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-169">
        <modifyDataType columnName="last_modified_by" newDataType="varchar(255)" tableName="audit_log_property_type"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-170">
        <dropNotNullConstraint columnDataType="varchar(255)" columnName="last_modified_by"
                               tableName="audit_log_property_type"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-171">
        <modifyDataType columnName="last_modified_by" newDataType="varchar(255)" tableName="audit_log_scheduler"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-172">
        <modifyDataType columnName="last_modified_by" newDataType="varchar(255)" tableName="contact"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-173">
        <modifyDataType columnName="last_modified_by" newDataType="varchar(255)" tableName="contact_group"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-174">
        <modifyDataType columnName="last_modified_by" newDataType="varchar(255)" tableName="contact_type"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-175">
        <modifyDataType columnName="last_modified_by" newDataType="varchar(255)" tableName="contact_type_group"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-176">
        <modifyDataType columnName="last_modified_by" newDataType="varchar(255)" tableName="custom_entity_instance"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-177">
        <modifyDataType columnName="last_modified_by" newDataType="varchar(255)" tableName="custom_entity_sub_type"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-178">
        <modifyDataType columnName="last_modified_by" newDataType="varchar(255)" tableName="custom_entity_type"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-179">
        <modifyDataType columnName="last_modified_by" newDataType="varchar(255)" tableName="custom_field_value"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-180">
        <modifyDataType columnName="last_modified_by" newDataType="varchar(255)" tableName="document"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-181">
        <modifyDataType columnName="last_modified_by" newDataType="varchar(255)" tableName="document_type"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-182">
        <modifyDataType columnName="last_modified_by" newDataType="varchar(255)" tableName="event_type"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-183">
        <modifyDataType columnName="last_modified_by" newDataType="varchar(255)" tableName="json_storage"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-184">
        <modifyDataType columnName="last_modified_by" newDataType="varchar(255)" tableName="license"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-185">
        <modifyDataType columnName="last_modified_by" newDataType="varchar(255)" tableName="license_activity"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-186">
        <modifyDataType columnName="last_modified_by" newDataType="varchar(255)" tableName="license_activity_fee"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-187">
        <modifyDataType columnName="last_modified_by" newDataType="varchar(255)" tableName="license_status"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-188">
        <modifyDataType columnName="last_modified_by" newDataType="varchar(255)" tableName="license_type"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-189">
        <modifyDataType columnName="last_modified_by" newDataType="varchar(255)" tableName="license_type_fee"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-190">
        <modifyDataType columnName="last_modified_by" newDataType="varchar(255)"
                        tableName="license_type_fee_conditional"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-191">
        <modifyDataType columnName="last_modified_by" newDataType="varchar(255)" tableName="license_type_setting"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-192">
        <modifyDataType columnName="last_modified_by" newDataType="varchar(255)" tableName="multi_form_argument"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-193">
        <modifyDataType columnName="last_modified_by" newDataType="varchar(255)" tableName="multi_form_body_field"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-194">
        <modifyDataType columnName="last_modified_by" newDataType="varchar(255)" tableName="multi_form_builder"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-195">
        <modifyDataType columnName="last_modified_by" newDataType="varchar(255)"
                        tableName="multi_form_conditional_display"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-196">
        <modifyDataType columnName="last_modified_by" newDataType="varchar(255)"
                        tableName="multi_form_element_options"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-197">
        <modifyDataType columnName="last_modified_by" newDataType="varchar(255)" tableName="multi_form_form_element"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-198">
        <modifyDataType columnName="last_modified_by" newDataType="varchar(255)"
                        tableName="multi_form_on_form_submit_api"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-199">
        <modifyDataType columnName="last_modified_by" newDataType="varchar(255)"
                        tableName="multi_form_on_page_next_api"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-200">
        <modifyDataType columnName="last_modified_by" newDataType="varchar(255)" tableName="multi_form_page"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-201">
        <modifyDataType columnName="last_modified_by" newDataType="varchar(255)" tableName="multi_form_query_string"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-202">
        <modifyDataType columnName="last_modified_by" newDataType="varchar(255)" tableName="multi_form_request_body"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-203">
        <modifyDataType columnName="last_modified_by" newDataType="varchar(255)" tableName="multi_form_request_slug"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-204">
        <modifyDataType columnName="last_modified_by" newDataType="varchar(255)" tableName="multi_form_response"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-205">
        <modifyDataType columnName="last_modified_by" newDataType="varchar(255)" tableName="multi_form_response_error"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-206">
        <modifyDataType columnName="last_modified_by" newDataType="varchar(255)" tableName="multi_form_response_field"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-207">
        <modifyDataType columnName="last_modified_by" newDataType="varchar(255)"
                        tableName="multi_form_response_success"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-208">
        <modifyDataType columnName="last_modified_by" newDataType="varchar(255)" tableName="multi_form_section"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-209">
        <modifyDataType columnName="last_modified_by" newDataType="varchar(255)" tableName="multi_form_trigger"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-210">
        <modifyDataType columnName="last_modified_by" newDataType="varchar(255)" tableName="multi_form_validation"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-211">
        <modifyDataType columnName="last_modified_by" newDataType="varchar(255)" tableName="participant"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-212">
        <modifyDataType columnName="last_modified_by" newDataType="varchar(255)" tableName="participant_address"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-213">
        <modifyDataType columnName="last_modified_by" newDataType="varchar(255)" tableName="participant_address_type"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-214">
        <modifyDataType columnName="last_modified_by" newDataType="varchar(255)" tableName="participant_group"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-215">
        <modifyDataType columnName="last_modified_by" newDataType="varchar(255)" tableName="participant_status"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-216">
        <modifyDataType columnName="last_modified_by" newDataType="varchar(255)" tableName="participant_type"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-217">
        <modifyDataType columnName="last_modified_by" newDataType="varchar(255)" tableName="participant_type_group"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-218">
        <modifyDataType columnName="last_modified_by" newDataType="varchar(255)" tableName="profile_type"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-219">
        <modifyDataType columnName="last_modified_by" newDataType="varchar(255)" tableName="property_type"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-220">
        <modifyDataType columnName="last_modified_by" newDataType="varchar(255)" tableName="scheduler"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-221">
        <modifyDataType columnName="last_modified_by" newDataType="varchar(255)" tableName="search_form_argument"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-222">
        <modifyDataType columnName="last_modified_by" newDataType="varchar(255)"
                        tableName="search_form_argument_filter"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-223">
        <modifyDataType columnName="last_modified_by" newDataType="varchar(255)" tableName="search_form_builder"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-224">
        <modifyDataType columnName="last_modified_by" newDataType="varchar(255)" tableName="search_form_element"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-225">
        <modifyDataType columnName="last_modified_by" newDataType="varchar(255)" tableName="search_form_section"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-226">
        <modifyDataType columnName="last_modified_by" newDataType="varchar(255)" tableName="table_column_field"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-227">
        <modifyDataType columnName="last_modified_by" newDataType="varchar(255)" tableName="table_custom_field"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-228">
        <modifyDataType columnName="last_modified_by" newDataType="varchar(255)" tableName="table_type"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-229">
        <modifyDataType columnName="last_modified_by" newDataType="varchar(255)" tableName="tenant"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-230">
        <modifyDataType columnName="last_modified_date" newDataType="timestamp" tableName="address"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-231">
        <modifyDataType columnName="last_modified_date" newDataType="timestamp" tableName="app_properties"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-232">
        <modifyDataType columnName="last_modified_date" newDataType="timestamp" tableName="association"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-233">
        <modifyDataType columnName="last_modified_date" newDataType="timestamp" tableName="audit_log_json_storage"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-234">
        <dropNotNullConstraint columnDataType="timestamp" columnName="last_modified_date"
                               tableName="audit_log_json_storage"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-235">
        <modifyDataType columnName="last_modified_date" newDataType="timestamp" tableName="audit_log_merge_request"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-236">
        <dropNotNullConstraint columnDataType="timestamp" columnName="last_modified_date"
                               tableName="audit_log_merge_request"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-237">
        <modifyDataType columnName="last_modified_date" newDataType="timestamp" tableName="audit_log_property_type"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-238">
        <dropNotNullConstraint columnDataType="timestamp" columnName="last_modified_date"
                               tableName="audit_log_property_type"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-239">
        <modifyDataType columnName="last_modified_date" newDataType="timestamp" tableName="audit_log_scheduler"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-240">
        <modifyDataType columnName="last_modified_date" newDataType="timestamp" tableName="contact"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-241">
        <modifyDataType columnName="last_modified_date" newDataType="timestamp" tableName="contact_group"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-242">
        <modifyDataType columnName="last_modified_date" newDataType="timestamp" tableName="contact_type"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-243">
        <modifyDataType columnName="last_modified_date" newDataType="timestamp" tableName="contact_type_group"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-244">
        <modifyDataType columnName="last_modified_date" newDataType="timestamp" tableName="custom_entity_instance"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-245">
        <modifyDataType columnName="last_modified_date" newDataType="timestamp" tableName="custom_entity_sub_type"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-246">
        <modifyDataType columnName="last_modified_date" newDataType="timestamp" tableName="custom_entity_type"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-247">
        <modifyDataType columnName="last_modified_date" newDataType="timestamp" tableName="custom_field_value"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-248">
        <modifyDataType columnName="last_modified_date" newDataType="timestamp" tableName="json_storage"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-249">
        <modifyDataType columnName="last_modified_date" newDataType="timestamp" tableName="license"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-250">
        <modifyDataType columnName="last_modified_date" newDataType="timestamp" tableName="license_activity_fee"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-251">
        <modifyDataType columnName="last_modified_date" newDataType="timestamp" tableName="license_status"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-252">
        <modifyDataType columnName="last_modified_date" newDataType="timestamp" tableName="license_type"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-253">
        <modifyDataType columnName="last_modified_date" newDataType="timestamp" tableName="license_type_fee"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-254">
        <modifyDataType columnName="last_modified_date" newDataType="timestamp"
                        tableName="license_type_fee_conditional"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-255">
        <modifyDataType columnName="last_modified_date" newDataType="timestamp" tableName="license_type_setting"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-256">
        <modifyDataType columnName="last_modified_date" newDataType="timestamp" tableName="merge_request"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-257">
        <modifyDataType columnName="last_modified_date" newDataType="timestamp" tableName="multi_form_argument"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-258">
        <modifyDataType columnName="last_modified_date" newDataType="timestamp" tableName="multi_form_body_field"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-259">
        <modifyDataType columnName="last_modified_date" newDataType="timestamp" tableName="multi_form_builder"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-260">
        <modifyDataType columnName="last_modified_date" newDataType="timestamp"
                        tableName="multi_form_conditional_display"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-261">
        <modifyDataType columnName="last_modified_date" newDataType="timestamp" tableName="multi_form_element_options"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-262">
        <modifyDataType columnName="last_modified_date" newDataType="timestamp" tableName="multi_form_form_element"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-263">
        <modifyDataType columnName="last_modified_date" newDataType="timestamp"
                        tableName="multi_form_on_form_submit_api"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-264">
        <modifyDataType columnName="last_modified_date" newDataType="timestamp"
                        tableName="multi_form_on_page_next_api"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-265">
        <modifyDataType columnName="last_modified_date" newDataType="timestamp" tableName="multi_form_page"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-266">
        <modifyDataType columnName="last_modified_date" newDataType="timestamp" tableName="multi_form_query_string"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-267">
        <modifyDataType columnName="last_modified_date" newDataType="timestamp" tableName="multi_form_request_body"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-268">
        <modifyDataType columnName="last_modified_date" newDataType="timestamp" tableName="multi_form_request_slug"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-269">
        <modifyDataType columnName="last_modified_date" newDataType="timestamp" tableName="multi_form_response"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-270">
        <modifyDataType columnName="last_modified_date" newDataType="timestamp" tableName="multi_form_response_error"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-271">
        <modifyDataType columnName="last_modified_date" newDataType="timestamp" tableName="multi_form_response_field"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-272">
        <modifyDataType columnName="last_modified_date" newDataType="timestamp"
                        tableName="multi_form_response_success"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-273">
        <modifyDataType columnName="last_modified_date" newDataType="timestamp" tableName="multi_form_section"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-274">
        <modifyDataType columnName="last_modified_date" newDataType="timestamp" tableName="multi_form_validation"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-275">
        <modifyDataType columnName="last_modified_date" newDataType="timestamp" tableName="participant"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-276">
        <modifyDataType columnName="last_modified_date" newDataType="timestamp" tableName="participant_address"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-277">
        <modifyDataType columnName="last_modified_date" newDataType="timestamp" tableName="participant_address_type"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-278">
        <modifyDataType columnName="last_modified_date" newDataType="timestamp" tableName="participant_group"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-279">
        <modifyDataType columnName="last_modified_date" newDataType="timestamp" tableName="participant_type"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-280">
        <modifyDataType columnName="last_modified_date" newDataType="timestamp" tableName="participant_type_group"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-281">
        <modifyDataType columnName="last_modified_date" newDataType="timestamp" tableName="profile_type"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-282">
        <modifyDataType columnName="last_modified_date" newDataType="timestamp" tableName="property_type"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-283">
        <modifyDataType columnName="last_modified_date" newDataType="timestamp" tableName="scheduler"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-284">
        <addNotNullConstraint columnDataType="timestamp" columnName="last_modified_date" tableName="scheduler"
                              validate="true"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-285">
        <modifyDataType columnName="last_modified_date" newDataType="timestamp" tableName="search_form_argument"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-286">
        <modifyDataType columnName="last_modified_date" newDataType="timestamp"
                        tableName="search_form_argument_filter"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-287">
        <modifyDataType columnName="last_modified_date" newDataType="timestamp" tableName="search_form_builder"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-288">
        <modifyDataType columnName="last_modified_date" newDataType="timestamp" tableName="search_form_element"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-289">
        <modifyDataType columnName="last_modified_date" newDataType="timestamp" tableName="search_form_section"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-290">
        <modifyDataType columnName="last_modified_date" newDataType="timestamp" tableName="table_column_field"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-291">
        <modifyDataType columnName="last_modified_date" newDataType="timestamp" tableName="table_custom_field"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-292">
        <modifyDataType columnName="last_modified_date" newDataType="timestamp" tableName="table_type"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-293">
        <modifyDataType columnName="last_modified_date" newDataType="timestamp" tableName="tenant"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-294">
        <modifyDataType columnName="modifier" newDataType="varchar(255)" tableName="audit_log_license"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-295">
        <modifyDataType columnName="modifier" newDataType="varchar(255)" tableName="license"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-296">
        <dropNotNullConstraint columnDataType="varchar(255)" columnName="name" tableName="participant_status"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-297">
        <modifyDataType columnName="paid_date" newDataType="timestamp" tableName="audit_log_license_activity_fee"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-298">
        <modifyDataType columnName="paid_date" newDataType="timestamp" tableName="license_activity_fee"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-301">
        <modifyDataType columnName="property_name" newDataType="varchar(250)" tableName="audit_log_property_type"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-302">
        <dropNotNullConstraint columnDataType="varchar(250)" columnName="property_name"
                               tableName="audit_log_property_type"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-303">
        <dropDefaultValue columnDataType="varchar(250)" columnName="property_name" tableName="audit_log_property_type"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-304">
        <modifyDataType columnName="property_name" newDataType="varchar(250)" tableName="property_type"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-305">
        <dropDefaultValue columnDataType="varchar(250)" columnName="property_name" tableName="property_type"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-306">
        <dropNotNullConstraint columnDataType="varchar(255)" columnName="property_type"
                               tableName="audit_log_property_type"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-307">
        <dropDefaultValue columnDataType="varchar(255)" columnName="property_type" tableName="audit_log_property_type"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-308">
        <dropDefaultValue columnDataType="varchar(255)" columnName="property_type" tableName="property_type"/>
    </changeSet>

    <changeSet author="postgres (generated)" id="**********061-312">
        <dropDefaultValue columnDataType="boolean" columnName="registered" tableName="audit_log_participant"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-313">
        <dropDefaultValue columnDataType="boolean" columnName="registered" tableName="participant"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-314">
        <modifyDataType columnName="revision_id" newDataType="int" tableName="audit_log_json_storage"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-315">
        <modifyDataType columnName="revision_id" newDataType="int" tableName="audit_log_merge_request"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-316">
        <modifyDataType columnName="revision_id" newDataType="int" tableName="audit_log_property_type"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-317">
        <dropNotNullConstraint columnDataType="smallint(5)" columnName="revision_type"
                               tableName="audit_log_json_storage"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-318">
        <dropNotNullConstraint columnDataType="smallint(5)" columnName="revision_type"
                               tableName="audit_log_merge_request"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-319">
        <dropNotNullConstraint columnDataType="smallint(5)" columnName="revision_type"
                               tableName="audit_log_property_type"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-321">
        <dropNotNullConstraint columnDataType="varchar(100)" columnName="table_name"
                               tableName="audit_log_property_type"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-322">
        <dropDefaultValue columnDataType="varchar(100)" columnName="table_name" tableName="audit_log_property_type"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-323">
        <dropDefaultValue columnDataType="varchar(100)" columnName="table_name" tableName="property_type"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-324">
        <modifyDataType columnName="valid_from_date" newDataType="timestamp" tableName="license"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-325">
        <modifyDataType columnName="valid_to_date" newDataType="timestamp" tableName="license"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-332">
        <dropUniqueConstraint constraintName="uk_5f6mcxp1qvs6jay0askudjg9f" tableName="contact_group"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-333">
        <addUniqueConstraint columnNames="g_name" constraintName="uk_5f6mcxp1qvs6jay0askudjg9f"
                             tableName="contact_group"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-334">
        <dropUniqueConstraint constraintName="uk_5ubo1b3ouyx89tpkoclne490u" tableName="table_type"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-335">
        <addUniqueConstraint columnNames="name" constraintName="uk_5ubo1b3ouyx89tpkoclne490u" tableName="table_type"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-336">
        <dropUniqueConstraint constraintName="uk_fmbw0loa3lbtb5kyb4dgg54av" tableName="participant_group"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-337">
        <addUniqueConstraint columnNames="name" constraintName="uk_fmbw0loa3lbtb5kyb4dgg54av"
                             tableName="participant_group"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-739">
        <dropColumn columnName="entity_id" tableName="address"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-740">
        <dropColumn columnName="entity_id" tableName="audit_log_address"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-741">
        <dropColumn columnName="entity_id" tableName="audit_log_custom_entity_instance"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-748">
        <dropColumn columnName="entity_id" tableName="custom_entity_instance"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-742">
        <dropColumn columnName="entity_id" tableName="audit_log_document"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-749">
        <dropColumn columnName="entity_id" tableName="document"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-743">
        <dropColumn columnName="entity_id" tableName="audit_log_license"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-750">
        <dropColumn columnName="entity_id" tableName="license"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-744">
        <dropColumn columnName="entity_id" tableName="audit_log_merge_request"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-751">
        <dropColumn columnName="entity_id" tableName="merge_request"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-745">
        <dropColumn columnName="entity_id" tableName="audit_log_multi_form_builder"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-752">
        <dropColumn columnName="entity_id" tableName="multi_form_builder"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-746">
        <dropColumn columnName="entity_id" tableName="audit_log_participant"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-753">
        <dropColumn columnName="entity_id" tableName="participant"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-747">
        <dropColumn columnName="entity_id" tableName="audit_log_tenant"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="**********061-754">
        <dropColumn columnName="entity_id" tableName="tenant"/>
    </changeSet>

    <changeSet author="postgres (generated)" id="1711668923265-410">
        <addColumn tableName="multi_form_response_error_codes">
            <column name="multi_form_response_error_id" type="int8" defaultOnNull="true" defaultValueNumeric="1">
                <constraints nullable="false"/>
            </column>
        </addColumn>
    </changeSet>
    <changeSet author="postgres (generated)" id="1711668923265-420">
        <sql>
            update multi_form_response_error_codes
            set multi_form_response_error_id = multi_form_response_error_multi_form_response_error_id
        </sql>
    </changeSet>
    <changeSet author="postgres (generated)" id="1711668923265-652">
        <addForeignKeyConstraint baseColumnNames="multi_form_response_error_id"
                                 baseTableName="multi_form_response_error_codes"
                                 constraintName="fk7h8y19kua0tat6kylhhqfem7w" deferrable="false"
                                 initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION"
                                 referencedColumnNames="multi_form_response_error_id"
                                 referencedTableName="multi_form_response_error" validate="true"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="1711668923265-667">
        <dropForeignKeyConstraint baseTableName="multi_form_response_error_codes"
                                  constraintName="fk2s0ta10xnhvwpkqgf0ur9twtg"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="1711668923265-755">
        <dropColumn columnName="multi_form_response_error_multi_form_response_error_id"
                    tableName="multi_form_response_error_codes"/>
    </changeSet>

    <changeSet author="postgres (generated)" id="1711668923265-413">
        <addColumn tableName="audit_log_multi_form_response_error_codes">
            <column name="multi_form_response_error_id" type="int8" defaultOnNull="true" defaultValueNumeric="1">
                <constraints nullable="false"/>
            </column>
        </addColumn>
    </changeSet>
    <changeSet author="postgres (generated)" id="1711668923265-423">
        <sql>
            update audit_log_multi_form_response_error_codes
            set multi_form_response_error_id = multi_form_response_error_multi_form_response_error_id
        </sql>
    </changeSet>
    <changeSet author="postgres (generated)" id="1711668923265-326">
        <dropPrimaryKey tableName="audit_log_multi_form_response_error_codes"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="1711668923265-327">
        <addPrimaryKey columnNames="multi_form_response_error_id, codes, revision_id"
                       constraintName="audit_log_multi_form_response_error_codes_pkey"
                       tableName="audit_log_multi_form_response_error_codes"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="1711668923265-758">
        <dropColumn columnName="multi_form_response_error_multi_form_response_error_id"
                    tableName="audit_log_multi_form_response_error_codes"/>
    </changeSet>
</databaseChangeLog>
