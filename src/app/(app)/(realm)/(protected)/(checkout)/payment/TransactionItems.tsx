"use client"
import { useState } from "react";
import PageContainer from "@/components/ui/Page/PageContainer";
import Toggle from "@/components/ui/buttons/Toggle";
import { useRouter } from "next/navigation";
import SummaryIcon from "@/components/cart/SummaryIcon";
import type { ActiveCartItems } from "@/types/CartType";
import { useMyProfile } from "@/hooks/providers/useMyProfile";
const previousAmount = '3.00';
const permissions = ['super-admin', 'city-admin']


const TransactionItems = ({ order }: { order: ActiveCartItems }) => {
  
  const { hasPermissions } = useMyProfile();

  const [isSplitPayment, setIsSplitPayment] = useState(false);
  const [partialPayment, setPartialPayment] = useState<string>('$10.00');
  const [isValidAmount, setIsValidAmount] = useState<boolean>(true);
  
  const router = useRouter();

  if(!order) return null

  return (
    <PageContainer className="w-full max-w-md">
      <h1 className="text-xl font-bold">Order Summary</h1>

      {/* List of items */}
      <div className="w-full">
        {order?.items.map((item, index) => {
          return (
            <div
              key={index}
              className="flex gap-4 items-center border-b border-neutral-100 py-4"
            >
              {/* Icon */}
              <SummaryIcon item={item.itemType} />

              {/* info */}
              <div className="flex flex-col gap-1 w-full">
                <span className="text-sm font-semibold line-clamp-1">{item.primaryDisplay}</span>
                <span className="text-xs text-neutral-700 line-clamp-1">{item.secondaryDisplay}</span>
              </div>
            </div>
          );
        })}
      </div>

      {/* <CartFees cart={order} /> */}
      
      {/* Total */}
      <div className="flex justify-between mt-2 py-2">
        <Toggle 
          state={isSplitPayment} 
          setState={setIsSplitPayment} 
          label='Partial Payment?'
        />
      </div>

      {/* Partial Payment */}
      { isSplitPayment && <>
          <div className="flex justify-between mt-2 text-sm py-2">
            <p>Previous Payments:</p>
            <div className="flex gap-2">
              <p>CASH</p>
              <p>${previousAmount}</p>
            </div>
          </div>

          {/* Partial Payment */}
          <div className="flex text-lg font-semibold mb-2">
            <p className="mr-auto">Partial:</p>
            $
            <input
              type="text"
              value={partialPayment}
              // onChange={handlePartial}
              className="w-20 text-right border-b border-black no-arrows"
              inputMode="decimal"
            />
          </div>
        </>
      }

      <button
        type="button"
        className={`
          w-full mt-4 text-white font-semibold py-2 rounded
          ${partialPayment && partialPayment !== '0.00'
            ? `bg-blue-600 hover:bg-blue-700` : `bg-gray-400 cursor-not-allowed`}
        `}
        onClick={(e)=>{
          e.preventDefault();
          const hasPermission = hasPermissions(permissions);
        if (hasPermission) {
          router.push(`/dashboard`); 
        } else {
          router.push(`/home`);
        }
        }}
        disabled={!isValidAmount}
      >
        {isSplitPayment ? 
          `Partial Payment ${( partialPayment ? `- ${parseFloat(partialPayment).toFixed(2)}` : '')}` :
          `Submit Payment - ${order.total}`
        }
      </button>
    </PageContainer>
  );
};

export default TransactionItems;
