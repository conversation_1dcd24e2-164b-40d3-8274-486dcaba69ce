import { useForm } from "react-hook-form";

import { useState } from "react";
import type { <PERSON> } from "@/types/DogType";
import type { Document } from "@/types/DocumentType";
import { useUpdateEntityDogProfile } from "@/hooks/api/useProfiles";
import { useQueryClient } from "@tanstack/react-query";
import { useAtom } from "jotai";
import { toastAtom } from "@/components/ui/toast/toast";
import { FormValues } from "@/types/DogType";
import {
  EditDialog,
  EditDialogFooter,
  createFormData,
} from "@/app/(app)/(realm)/(protected)/(resident)/(registered)/resident/[entityType]/[entityId]/components/modals/EditDialog";
import {
  Comment,
  DogRabiesVaccinationDocument,
  DogRabiesVaccinationExemptionDocument,
  ExemptVaccine,
  RabiesAdministeredDate,
  <PERSON><PERSON>Brand,
  <PERSON>biesDueDate,
  <PERSON>biesLotExpirationDate,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>roduce<PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  RabiesVeterinary,
} from "@/components/dialog/EditFields";

export const EditVaccines = ({
  dog,
  trigger,
  admin = true,
}: {
  dog: Dog;
  trigger: JSX.Element;
  admin?: boolean;
}) => {
  const documents = dog?.documents || [];

  // Find dogRabiesVaccinationExemptionDocument in the documents array
  const exemptionDocument = documents.find(
    (document: Document) =>
      document.key === "dogRabiesVaccinationExemptionDocument",
  );

  // Find dogRabiesVaccinationDocument in the documents array
  const vaccinationDocument = documents.find(
    (document: Document) => document.key === "dogRabiesVaccinationDocument",
  );

  const initialValues = {
    veterinaryName: dog?.veterinaryName || "",
    vaccineDatesExempt: dog?.vaccineDatesExempt || false,
    rabiesTagNumber: dog?.rabiesTagNumber || "",
    vaccineProducer: dog?.vaccineProducer || "",
    vaccineBrand: dog?.vaccineBrand || "",
    vaccineAdministeredDate: dog?.vaccineAdministeredDate
      ? new Date(dog?.vaccineAdministeredDate).toISOString().slice(0, 10)
      : "",
    vaccineDueDate: dog?.vaccineDueDate
      ? new Date(dog?.vaccineDueDate).toISOString().slice(0, 10)
      : "",
    vaccineLotNumber: dog?.vaccineLotNumber || "",
    vaccineLotExpirationDate: dog?.vaccineLotExpirationDate
      ? new Date(dog?.vaccineLotExpirationDate).toISOString().slice(0, 10)
      : "",
    comment: "",
    dogRabiesVaccinationExemptionDocument: exemptionDocument || "",
    dogRabiesVaccinationDocument: vaccinationDocument || "",
  };

  const updateEntityDogProfile = useUpdateEntityDogProfile();

  const {
    watch,
    reset,
    control,
    register,
    handleSubmit,
    formState: { errors, isDirty, isValid },
  } = useForm<FormValues>({
    defaultValues: initialValues,
    mode: "onChange",
  });

  const [isOpen, setIsOpen] = useState(false);
  const [, setToast] = useAtom(toastAtom);
  const queryClient = useQueryClient();

  const handleCancel = () => {
    reset(initialValues);
    setIsOpen(false);
  };

  const handleSave = (data: any) => {
    const formData = createFormData(data);

    updateEntityDogProfile.mutate(
      {
        entityId: dog.entityId,
        body: formData,
      },
      {
        onSuccess: () => {
          queryClient.invalidateQueries();
          reset(data);
          setToast({
            status: "success",
            label: "Dog Updated",
            message: "Successfully Updated Dog Identification",
          });
          setIsOpen(false);
        },
        onError: (error: any) => {
          console.log(error);
          setToast({
            status: "error",
            label: "Error Updating Profile",
            message: error?.response?.data?.message,
          });
        },
      },
    );
  };

  const watchedValues = watch();

  const isExempt = watchedValues.vaccineDatesExempt;

  console.log(exemptionDocument);

  return (
    <EditDialog
      title="Edit Dog Vaccine Information"
      isOpen={isOpen}
      setIsOpen={setIsOpen}
      trigger={trigger}
    >
      <form className="flex flex-col gap-4" onSubmit={handleSubmit(handleSave)}>
        <RabiesVeterinary register={register} errors={errors} />
        <ExemptVaccine control={control} />

        {isExempt ? (
          <DogRabiesVaccinationExemptionDocument
            control={control}
            required={false}
          />
        ) : (
          <>
            <RabiesTagNumber register={register} errors={errors} />
            <RabiesProducer control={control} errors={errors} />
            <RabiesBrand control={control} errors={errors} />
            <RabiesAdministeredDate register={register} errors={errors} />
            <RabiesDueDate register={register} errors={errors} />
            <DogRabiesVaccinationDocument control={control} required={false} />

            <p className="mt-10 text-xl text-neutral-800">
              Additional Vaccine Information
            </p>

            <RabiesLotNumber register={register} errors={errors} />
            <RabiesLotExpirationDate register={register} errors={errors} />
          </>
        )}
        <Comment register={register} />
        <EditDialogFooter
          handleCancel={handleCancel}
          disabled={!isDirty}
          loading={updateEntityDogProfile.isLoading}
        />
      </form>
    </EditDialog>
  );
};
