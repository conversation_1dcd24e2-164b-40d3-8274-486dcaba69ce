import { useGetLicenses } from "@/hooks/api/useLicense";
import { format } from "date-fns";
import React, { useEffect, useState } from "react";
import { License } from "@/types/LicenseType";
import { useParams, useRouter } from "next/navigation";

import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { FiExternalLink } from "react-icons/fi";
import Link from "next/link";
import { Button } from "@/components/ui/button";
import { currentRange } from "../dashboardHelper";
import LoadingSpinner from "@/components/ui/LoadingSpinner";
import { ListContent, ListLayout, ListTitle } from "./ListLayout";
import AvatarImage, { getAvatarBlob } from "@/components/AvatarImage";

export default function NewLicensesList() {
  
  const router = useRouter();

  const [page] = useState("0");
  const [size] = useState("5");
  const [initialEntityIds, setInitialEntityIds] = useState<any>(null);
  const { data, isLoading, isError } = useGetLicenses({
    size: size,
    page: page,
    status: "active",
    issuedDate: currentRange("month", new Date()),
    refetchIntervalDuration: 30000,
  });

  console.log("Data", data);

  const totalPages = data?.totalPages || 0;
  let startPage = Math.max(0, parseInt(page) - 2);
  let endPage = Math.min(startPage + 4, totalPages - 1);

  if (parseInt(page) < 2) {
    endPage = Math.min(4, totalPages - 1);
  }
  if (parseInt(page) > totalPages - 3) {
    startPage = Math.max(0, totalPages - 5);
  }

  useEffect(() => {
    if (data && !initialEntityIds) {
      const licenses = data?.content?.map((item) => item?.license?.entityId);
      setInitialEntityIds(licenses);
    }
  }, [data, initialEntityIds]);

  if (isLoading)
    return (
      <ListLayout>
        <ListTitle
          title={`
          Last ${size} New Licenses
        `}
        />
        <ListContent>
          <LoadingSpinner />
        </ListContent>
      </ListLayout>
    );

  if (isError) return <div>Error...</div>;

  return (
    <ListLayout>
      <ListTitle title={`Last ${size} New Licenses`}>
        <Button
          variant="primary"
          size="sm"
          disabled={isLoading || data?.content?.length === 0}
          onClick={() =>
            router.push(
              `/license/dogLicenses/dogLicenses?issuedDate=${currentRange(
                "month",
                new Date(),
              )}`,
            )
          }
        >
          View More
        </Button>
      </ListTitle>
      <ListContent>
        {data &&
          !isLoading &&
          (data.content?.length > 0 ? (
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead className="w-[120px]">Lic #</TableHead>
                  <TableHead>Type</TableHead>
                  <TableHead>Resident</TableHead>
                  <TableHead className="text-right">Issued</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {data?.content?.map((data) => {
                  if (!data?.license) return null;

                  const license = data?.license as License;
                  const licenseEntityId = license?.entityId;
                  const isEntityNew =
                    !initialEntityIds?.includes(licenseEntityId) &&
                    initialEntityIds?.size > 0;
                  const individual =
                    data &&
                    Array.isArray(data.individual) &&
                    data.individual.length > 0
                      ? data.individual[0]
                      : null;
                  const name =
                    `${individual?.firstName ?? "Unknown"} ${individual?.lastName ?? ""}`.trim();

                  const issued = license?.issuedDate
                    ? format(new Date(license?.issuedDate), "MMM d, yyyy")
                    : "No Date";

                  return (
                    <TableRow
                      key={license.entityId}
                      onClick={() => {
                        router.push(
                          `/entity/license/${license?.entityId}?tab=profile`,
                        );
                      }}
                      className={`
                  relative hover:bg-neutral-100
                    ${isEntityNew && "bg-blue-50"} 
                  `}
                    >
                      <TableCell className="font-medium">
                        <div className="flex items-center gap-1">
                          {license.licenseNumber}
                          <Link
                            href={`/entity/license/${license?.entityId}?tab=profile`}
                            target="_blank"
                            onClick={(e) => {
                              e.stopPropagation();
                            }}
                            className="flex items-center rounded p-1 hover:bg-blue-600 hover:text-blue-50"
                          >
                            <FiExternalLink className="" />
                          </Link>
                        </div>
                      </TableCell>

                      <TableCell>
                        {license.licenseType.name} ({license.activityType})
                      </TableCell>
                      <TableCell className="">
                        <Link
                          href={`/entity/individual/${individual?.entityId}?tab=profile`}
                          className={`
                        flex w-fit items-center gap-2 rounded px-2 py-1 hover:bg-blue-200
                      `}
                          onClick={(e) => {
                            e.stopPropagation();
                          }}
                        >
                          <AvatarImage
                            entityType="individual"
                            src={getAvatarBlob(individual?.documents) as string}
                            alt="user"
                            width={24}
                            height={24}
                            className="rounded-full"
                          />
                          {name}
                        </Link>
                      </TableCell>
                      <TableCell className="text-right">{issued}</TableCell>
                      {isEntityNew && (
                        <div className="absolute left-0.5 top-0.5 rounded bg-blue-600 px-0.5 text-[11px] text-white shadow">
                          New
                        </div>
                      )}
                    </TableRow>
                  );
                })}
              </TableBody>
            </Table>
          ) : (
            <div className="text-center">No Licenses Found</div>
          ))}
      </ListContent>
    </ListLayout>
  );
}
