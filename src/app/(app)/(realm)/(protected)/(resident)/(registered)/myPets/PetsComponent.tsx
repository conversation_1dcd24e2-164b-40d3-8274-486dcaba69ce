"use client";
import LoadingSpinner from "@/components/ui/LoadingSpinner";
import PageContainer from "@/components/ui/Page/PageContainer";
import { Dog } from "@/types/DogType";
import React from "react";
import Image from "next/image";
import { useGetMyProfile } from "@/hooks/api/useResident";
import AddPetLicenseRequest from "@/components/resident/AddPetLicenseRequest";
import PetCard from "./components/PetCard";
import { LuArchive, LuDog, LuPlus } from "react-icons/lu";
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";
import { motion } from "framer-motion";
import PetLicenseDisclaimerMessage from "@/components/banners/PetLicenseDisclaimerMessage";

export default function PetsComponent() {
  const {
    data: resident,
    isError: residentError,
    isLoading: residentLoading,
  } = useGetMyProfile();

  if (residentLoading)
    return (
      <div className="flex h-full w-full flex-col items-center justify-center gap-8 bg-neutral-100 p-6">
        <LoadingSpinner />
      </div>
    );

  if (residentError) return <div>Failed to load user data</div>;

  if (!resident) return null;

  const dogs: Dog[] = resident?.dog ?? null;

  // Filter dogs that are not active dogs.active === true
  const activeDogs =
    dogs
      ?.filter((dog) => dog?.active === true)
      ?.sort((a, b) => (a?.dogName > b?.dogName ? 1 : -1)) ?? null;
  const inactiveDogs =
    dogs
      ?.filter((dog) => dog?.active === false)
      ?.sort((a, b) => (a?.dogName > b?.dogName ? 1 : -1)) ?? null;

  // Animation variants
  const container = {
    hidden: { opacity: 0 },
    show: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
      },
    },
  };

  const item = {
    hidden: { y: 20, opacity: 0 },
    show: { y: 0, opacity: 1 },
  };

  return (
    <div className="flex flex-col overflow-y-auto scroll-smooth">
      <div className="flex w-full flex-col gap-10 md:container md:p-6">
        {/* <PetLicenseDisclaimerMessage /> */}
        <div className="flex flex-col gap-10 2xl:flex-row">
          <PageContainer className="flex w-full flex-col gap-10 rounded-lg border border-blue-100 bg-white/80 pt-6 shadow-sm backdrop-blur-sm">

            <div className="flex w-full flex-col gap-4">
              <h1 className="text-left text-2xl font-bold">My Pets</h1>
              <p className="text-left">
                Here are the furry friends registered to you.
              </p>
            </div>

            {/* Active Dog Cards */}
            <Accordion type="single" collapsible defaultValue="item-1">
              <AccordionItem value="item-1" className="border-b-0">
                <AccordionTrigger className="group mb-3 hover:no-underline">
                  <div className="flex items-center gap-3 rounded-full bg-blue-50 px-4 py-2 transition-colors group-hover:bg-blue-100">
                    <LuDog className="text-xl text-blue-600" />
                    <span className="text-xl font-semibold text-blue-800">
                      Dogs ({activeDogs?.length || 0})
                    </span>
                  </div>
                </AccordionTrigger>
                <AccordionContent>
                  <motion.div
                    variants={container}
                    initial="hidden"
                    animate="show"
                    className="mt-4 grid grid-cols-1 gap-6 p-0.5 sm:grid-cols-2 lg:grid-cols-3"
                  >
                    {activeDogs?.length > 0 ? (
                      activeDogs?.map((dog: Dog) => (
                        <motion.div key={dog.entityId} variants={item}>
                          <PetCard pet={dog} />
                        </motion.div>
                      ))
                    ) : (
                      <motion.div variants={item} className="col-span-full">
                        <PageContainer className="flex w-full flex-col items-center rounded-xl border border-blue-200 bg-blue-50 p-8">
                          <Image
                            src="/images/resident/saddog.png"
                            alt="Sad Dog"
                            width={180}
                            height={180}
                            className="mb-4"
                          />
                          <h3 className="mb-2 text-2xl font-medium text-blue-800">
                            No Pets Found
                          </h3>
                          <p className="max-w-md text-center text-blue-600">
                            You don&apos;t have any active pets registered yet.
                            Add your furry friend to get started!
                          </p>
                        </PageContainer>
                      </motion.div>
                    )}
                  </motion.div>
                </AccordionContent>
              </AccordionItem>
            </Accordion>

            {/* Inactive Dog Cards */}
            <Accordion type="single" collapsible>
              <AccordionItem value="item-1" className="border-b-0">
                <AccordionTrigger className="group mb-3 hover:no-underline">
                  <div className="flex items-center gap-3 rounded-full bg-gray-50 px-4 py-2 transition-colors group-hover:bg-gray-100">
                    <LuArchive className="text-xl text-gray-600" />
                    <span className="text-xl font-semibold text-gray-700">
                      Archived Pets ({inactiveDogs?.length || 0})
                    </span>
                  </div>
                </AccordionTrigger>
                <AccordionContent>
                  <motion.div
                    variants={container}
                    initial="hidden"
                    animate="show"
                    className="mt-4 grid grid-cols-1 gap-6 p-0.5 sm:grid-cols-2 lg:grid-cols-3"
                  >
                    {inactiveDogs?.length > 0 ? (
                      inactiveDogs?.map((dog: Dog) => (
                        <motion.div key={dog.entityId} variants={item}>
                          <PetCard pet={dog} />
                        </motion.div>
                      ))
                    ) : (
                      <motion.div variants={item} className="col-span-full">
                        <PageContainer className="flex w-full flex-col items-center rounded-xl border border-gray-200 bg-gray-50 p-8">
                          <Image
                            src="/images/resident/saddog.png"
                            alt="Sad Dog"
                            width={150}
                            height={150}
                            className="mb-4 opacity-70 grayscale"
                          />
                          <h3 className="mb-2 text-2xl font-medium text-gray-700">
                            No Archived Pets
                          </h3>
                          <p className="text-center text-gray-600">
                            You don&apos;t have any archived pets.
                          </p>
                        </PageContainer>
                      </motion.div>
                    )}
                  </motion.div>
                </AccordionContent>
              </AccordionItem>
            </Accordion>
          </PageContainer>

          {/* Request Form Section */}
          <motion.div
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ delay: 0.3 }}
          >
            <PageContainer className="h-fit max-w-lg shrink-0 rounded-lg border border-blue-100 bg-white/80 shadow-sm backdrop-blur-sm">
              <AddPetLicenseRequest />
            </PageContainer>
          </motion.div>
        </div>
      </div>
    </div>
  );
}
