import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  <PERSON><PERSON>,
  DialogContent,
  <PERSON><PERSON>Title,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Textarea } from "@/components/ui/textarea";
import { toastAtom } from "@/components/ui/toast/toast";
import { useAddFee } from "@/hooks/api/usePayment";
import { Individual } from "@/types/IndividualType";
import { useAtom } from "jotai";
import React, { useState } from "react";
import { Controller, useForm } from "react-hook-form";
import { Dog } from "../licenses/LicenseType";

interface AddFeeFormValues {
  feeCode: string;
  amount: number;
  entityId?: string;
  entityType?: string;
  dogEntityId?: string;
  dogEntityType?: string;
  individualEntityId?: string;
  individualEntityType?: string;
  comment: string;
}

export default function AddFeeButton({
  currentEntityType,
  entity,
  entityRefetch,
}: {
  currentEntityType: string;
  entity: any;
  entityRefetch: any;
}) {
  const addFeeMutation = useAddFee();
  const [open, setOpen] = useState(false);
  const [_, setToast] = useAtom(toastAtom);
  const currentEntity = entity[currentEntityType];

  const {
    register,
    handleSubmit,
    formState: { errors },
    watch,
    control,
    reset,
  } = useForm<AddFeeFormValues>({
    defaultValues: {
      feeCode: "DLD-M-LICENSE",
    },
  });

  const feeCode = watch("feeCode");

  const entityList = getList(entity, currentEntityType);

  const onSubmit = (data: AddFeeFormValues) => {
    const associations = [
      {
        entityId: currentEntity.entityId ?? null,
        entityType: currentEntityType ?? null,
      },
    ];

    if (data.feeCode === "DLD-M-LICENSE") {
      switch (currentEntityType) {
        case "dog":
          associations.push({
            entityId: data.individualEntityId ?? null,
            entityType: "individual",
          });
          break;
        case "individual":
          associations.push({
            entityId: data.dogEntityId ?? null,
            entityType: "dog",
          });
          break;
        default:
          break;
      }
    } 

    addFeeMutation.mutate(
      {
        body: {
          feeCode: data.feeCode,
          amount: data.amount,
          associations: associations,
          comment: data.comment,
        },
      },
      {
        onSuccess: () => {
          reset();
          setToast({
            label: "Success adding fee",
            status: "success",
            message: "Fee added successfully",
          });
          entityRefetch();
          setOpen(false);
        },
        onError: (error: any) => {
          setToast({
            label: "Error adding fee",
            status: "error",
            message: error?.message ?? "An error occurred",
          });
        },
      }
    );
  };

  return (
    <Dialog
      open={open}
      onOpenChange={(isOpen) => {
        if (!isOpen) {
          reset();
        }
        setOpen(isOpen);
      }}
    >
      <DialogTrigger asChild>
        <Button variant={"primary"} disabled={addFeeMutation.isLoading}>
          {addFeeMutation.isLoading ? "Adding Fee..." : "Add Fee"}
        </Button>
      </DialogTrigger>
      <DialogContent>
        <DialogTitle>Add a fee to a resident&apos;s account</DialogTitle>
        <form onSubmit={handleSubmit(onSubmit)}>
          <div className="flex flex-col gap-4">
            <div>
              <label htmlFor="feeCode" className="block text-sm font-medium text-gray-700">
                Fee Code
              </label>
              <Controller
                name="feeCode"
                control={control}
                rules={{ required: true }}
                render={({ field }) => (
                  <Select onValueChange={field.onChange} value={field.value}>
                    <SelectTrigger className="w-[180px]">
                      <SelectValue placeholder="Select Fee Code" />
                    </SelectTrigger>
                    <SelectContent className="z-[99999]">
                      <SelectItem value="DLD-M-LICENSE">Dangerous Dog</SelectItem>
                    </SelectContent>
                  </Select>
                )}
              />
              {errors.feeCode && <p className="text-sm text-red-500">Fee Code is required</p>}
            </div>

            {feeCode === "DLD-M-LICENSE" && (
              <>
                {/* Display list of entities depending on the current entity type */}
                {entityList?.length > 0 && currentEntityType === "dog" && (
                  <div>
                    <label htmlFor="individualEntityId" className="block text-sm font-medium text-gray-700">
                      Owner
                    </label>
                    <Controller
                      name="individualEntityId"
                      control={control}
                      rules={{ required: true }}
                      render={({ field }) => (
                        <Select onValueChange={field.onChange} value={field.value}>
                          <SelectTrigger className="w-[180px]">
                            <SelectValue placeholder="Select Owner" />
                          </SelectTrigger>
                          <SelectContent className="z-[99999]">
                            {entityList.map((individual: any) => (
                              <SelectItem key={individual.id} value={individual.id}>
                                {individual.label}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      )}
                    />
                    {errors.individualEntityId && (
                      <p className="text-sm text-red-500">Owner is required</p>
                    )}
                  </div>
                )}

                {entityList?.length > 0 && currentEntityType === "individual" && (
                  <div>
                    <label htmlFor="dogEntityId" className="block text-sm font-medium text-gray-700">
                      Dog
                    </label>
                    <Controller
                      name="dogEntityId"
                      control={control}
                      rules={{ required: true }}
                      render={({ field }) => (
                        <Select onValueChange={field.onChange} value={field.value}>
                          <SelectTrigger className="w-[180px]">
                            <SelectValue placeholder="Select Dog" />
                          </SelectTrigger>
                          <SelectContent className="z-[99999]">
                            {entityList.map((dog: any) => (
                              <SelectItem key={dog.id} value={dog.id}>
                                {dog.label}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      )}
                    />
                    {errors.dogEntityId && (
                      <p className="text-sm text-red-500">Dog is required</p>
                    )}
                  </div>
                )}
              </>
            )}

            <div>
              <label htmlFor="amount" className="block text-sm font-medium text-gray-700">
                Amount
              </label>
              <Input
                type="number"
                step="0.01"
                placeholder="Amount in dollars"
                {...register("amount", { required: true, min: 0 })}
                className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
              />
              {errors.amount && (
                <p className="text-sm text-red-500">Amount is required and must be greater than 0</p>
              )}
            </div>

            <div>
              <label htmlFor="comment" className="block text-sm font-medium text-gray-700">
                Comment (Resident will see this)
              </label>
              <Textarea {...register("comment")} />
            </div>
          </div>

          <div className="mt-4 flex justify-end gap-4">
            <Button type="submit" variant={"primary"} disabled={addFeeMutation.isLoading}>
              {addFeeMutation.isLoading ? "Adding Fee..." : "Add Fee"}
            </Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  );
}


const getList = (entity:any, entityType: string) => {
  switch (entityType) {
    case "dog":
      return entity["individual"]?.map((individual: Individual) => ({
        id: individual.entityId,
        label: individual.firstName + " " + individual.lastName,
      })) || [];
    case "individual":
      return entity["dog"]?.map((dog: Dog) => ({
        id: dog.entityId,
        label: `${dog.dogName}`,
      })) || [];
    default:
      return [];
  }
}