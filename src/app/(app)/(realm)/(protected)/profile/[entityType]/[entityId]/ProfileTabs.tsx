/* eslint-disable react-hooks/exhaustive-deps */
"use client";
import { useEffect, useRef, useState, useLayoutEffect } from "react";
import Link from "next/link";
import { usePathname, useSearchParams } from "next/navigation";
import { motion } from "framer-motion";
import { License } from "./licenses/LicenseType";
import { cn } from "@/lib/utils";
import { useEntity } from "@/hooks/providers/useEntity";
import { useMyProfile } from "@/hooks/providers/useMyProfile";

import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";

import { ChevronDown } from "lucide-react";

interface Tab {
  label: string;
  tab: string;
  notification?: number | string | null;
  permissions?: boolean;
}

const areTabArraysEqual = (a: Tab[], b: Tab[]) => {
  if (a.length !== b.length) return false;
  return a.every((tab, idx) => tab.tab === b[idx].tab);
};

export default function ProfileTabs() {
  const { hasPermissions } = useMyProfile();
  const { entityType, entity } = useEntity();
  const containerRef = useRef<HTMLDivElement>(null);
  const measureRef = useRef<HTMLDivElement>(null);
  const moreRef = useRef<HTMLDivElement>(null);
  const [tabWidths, setTabWidths] = useState<number[]>([]);
  const [tabsLayout, setTabsLayout] = useState<{ visible: Tab[]; overflow: Tab[] }>({
    visible: [],
    overflow: [],
  });

  const expiredLicenses = entity["license"]?.filter(
    (license: License) => license.status === "Expired",
  );
  const currentEntity = entity[entityType];
  const outstandingAmount = currentEntity?.fees
    ? currentEntity?.fees?.totals?.totalOutstandingAmount
    : 0;

  const tabs: { [key: string]: Tab } = {
    profile: {
      label: "Profile",
      tab: "profile",
      permissions: hasPermissions(["super-admin", "resident"]),
    },
    licenses: {
      label: "Licenses",
      tab: "licenses",
      notification: expiredLicenses?.length > 0 ? expiredLicenses.length : null,
      permissions: hasPermissions(["super-admin", "resident"]),
    },
    notes: {
      label: "Notes",
      tab: "notes",
      permissions: hasPermissions(["super-admin"]),
    },
    associations: {
      label: "Associations",
      tab: "associations",
      permissions: hasPermissions(["super-admin"]),
    },
    history: {
      label: "History",
      tab: "history",
      permissions: hasPermissions(["super-admin", "resident"]),
    },
    fees: {
      label: "Fees",
      tab: "fees",
      notification: outstandingAmount > 0 ? "$" : null,
      permissions: hasPermissions(["super-admin", "resident"]),
    },
    payments: {
      label: "Payments",
      tab: "payments",
      permissions: hasPermissions(["super-admin", "resident"]),
    },
    files: {
      label: "Files",
      tab: "files",
      permissions: hasPermissions(["super-admin", "resident"]),
    },
    settings: {
      label: "Settings",
      tab: "settings",
      permissions: hasPermissions(["super-admin", "resident"]),
    },
  };

  const entityTabs: { [key: string]: Tab[] } = {
    dog: [
      tabs.profile,
      tabs.licenses,
      tabs.notes,
      tabs.fees,
      tabs.files,
      tabs.associations,
      tabs.history,
      tabs.settings,
    ],
    individual: [
      tabs.profile,
      tabs.licenses,
      tabs.notes,
      tabs.fees,
      tabs.files,
      tabs.associations,
      tabs.history,
      tabs.settings,
    ],
  };

  const availableTabs = entityTabs[entityType]?.filter((tab) => tab.permissions) || [];

  useLayoutEffect(() => {
    if (!measureRef.current || availableTabs.length === 0) return;
    const allTabs = Array.from(measureRef.current.children) as HTMLDivElement[];
    if (allTabs.length !== availableTabs.length) return;
    const widths = allTabs.map((child) => child.getBoundingClientRect().width + 24);
    const changed =
      widths.length !== tabWidths.length || widths.some((w, i) => w !== tabWidths[i]);
    if (changed) {
      setTabWidths(widths);
    }
  }, [availableTabs, tabWidths]);

  useEffect(() => {
    if (tabWidths.length === 0) return;

    function recalcLayout() {
      if (!containerRef.current) return;
      const containerWidth = containerRef.current.getBoundingClientRect().width - 96
      const moreButtonWidth = moreRef.current
        ? moreRef.current.getBoundingClientRect().width
        : 80;
      let visibleTabs: Tab[] = [];
      let overflowTabs: Tab[] = [];
      let usedWidth = 0;

      for (let i = 0; i < availableTabs.length; i++) {
        if (
          i === 0 ||
          usedWidth + tabWidths[i] + (overflowTabs.length > 0 ? moreButtonWidth : 0) <=
            containerWidth
        ) {
          visibleTabs.push(availableTabs[i]);
          usedWidth += tabWidths[i];
        } else {
          overflowTabs.push(availableTabs[i]);
        }
      }

      setTabsLayout((prev) => {
        if (
          areTabArraysEqual(prev.visible, visibleTabs) &&
          areTabArraysEqual(prev.overflow, overflowTabs)
        ) {
          return prev;
        }
        return { visible: visibleTabs, overflow: overflowTabs };
      });
    }

    recalcLayout();
    window.addEventListener("resize", recalcLayout);
    return () => window.removeEventListener("resize", recalcLayout);
  }, [availableTabs, tabWidths]);

  return (
    <>
      <div
        ref={measureRef}
        style={{
          position: "absolute",
          visibility: "hidden",
          pointerEvents: "none",
          height: 0,
          overflow: "hidden",
        }}
      >
        {availableTabs.map((tab) => (
          <div key={tab.tab} style={{ display: "inline-block", whiteSpace: "nowrap" }}>
            <TabLabel tab={tab} />
          </div>
        ))}
      </div>

      <div className="shrink-0 border-b-2 bg-white px-6 py-1 text-base md:px-6">
        <div ref={containerRef} className="flex shrink-0 items-center gap-8 md:container md:mx-auto">
          {tabsLayout.visible.map((tab) => (
            <div key={tab.tab} className="tab-item">
              <TabLabel tab={tab} entityType={entityType} />
            </div>
          ))}

          {tabsLayout.overflow.length > 0 && (
            <div ref={moreRef} className="shrink-0">
              <MoreTabsDropdown tabs={tabsLayout.overflow} entityType={entityType} />
            </div>
          )}
        </div>
      </div>
    </>
  );
}

function TabLabel({ tab, entityType }: { tab: Tab; entityType?: string }) {
  const pathname = usePathname();
  const active = useSearchParams().get("tab") === tab.tab;
  if (!tab.permissions) return null;

  return (
    <div className="relative">
      <Link
        href={pathname + `?tab=${tab.tab}`}
        className={cn(
          "relative translate-y-0.5 cursor-pointer font-semibold text-stone-500 whitespace-nowrap",
          entityType ? entityStyling[entityType].hover : "",
          active && "translate-y-0.5 font-semibold text-black"
        )}
      >
        {tab.label}
        {active && (
          <motion.div
            className={cn(
              "absolute -bottom-2 left-0 h-1 w-full rounded-full",
              entityType ? entityStyling[entityType]?.background : ""
            )}
            layoutId="underline"
          />
        )}
      </Link>

      {tab.notification && (
        <div className="absolute -right-2 -top-2 z-10 w-fit rounded-full bg-red-500 px-1.5 py-0 text-xs font-semibold text-white">
          {tab.notification}
        </div>
      )}
    </div>
  );
}

function MoreTabsDropdown({ tabs, entityType }: { tabs: Tab[]; entityType?: string }) {
  const pathname = usePathname();
  const currentTab = useSearchParams().get("tab");
  const isAnyActive = tabs.some((tab) => tab.tab === currentTab);
  const notificationCount = tabs.reduce((count, tab) => {
    if (typeof tab.notification === "number") {
      return count + tab.notification;
    } else if (tab.notification) {
      return count + 1;
    }
    return count;
  }, 0);

  return (
    <DropdownMenu>
      <DropdownMenuTrigger
        className={cn(
          "relative flex items-center gap-1 font-semibold text-stone-500 cursor-pointer whitespace-nowrap shrink-0 h-fit",
          entityType ? entityStyling[entityType].hover : "",
          isAnyActive && "font-semibold text-black"
        )}
      >
        More <ChevronDown className="size-4" />
        {isAnyActive && (
          <motion.div
            className={cn(
              "absolute -bottom-2 left-0 h-1 w-full rounded-full",
              entityType ? entityStyling[entityType]?.background : ""
            )}
            layoutId="underline"
          />
        )}
        {notificationCount > 0 && (
          <div className="absolute -right-2 -top-2 z-10 flex h-5 min-w-5 items-center justify-center rounded-full bg-red-500 px-1.5 py-0 text-xs font-semibold text-white">
            {notificationCount}
          </div>
        )}
      </DropdownMenuTrigger>

      <DropdownMenuContent align="end" className="min-w-40">
        {tabs.map((tab) => (
          <DropdownMenuItem key={tab.tab} asChild>
            <Link
              href={pathname + `?tab=${tab.tab}`}
              className={cn(
                "flex w-full items-center justify-between px-2 py-1.5",
                tab.tab === currentTab && "bg-gray-100 font-semibold"
              )}
            >
              {tab.label}
              {tab.notification && (
                <span className="ml-2 rounded-full bg-red-500 px-1.5 py-0.5 text-xs font-semibold text-white">
                  {tab.notification}
                </span>
              )}
            </Link>
          </DropdownMenuItem>
        ))}
      </DropdownMenuContent>
    </DropdownMenu>
  );
}

const entityStyling: { [key: string]: { hover: string; background: string } } = {
  individual: {
    hover: "hover:text-blue-600",
    background: "bg-blue-500",
  },
  dog: {
    hover: "hover:text-orange-600",
    background: "bg-orange-500",
  },
  license: {
    hover: "hover:text-green-600",
    background: "bg-green-500",
  },
  permit: {
    hover: "hover:text-purple-600",
    background: "bg-purple-500",
  },
  organization: {
    hover: "hover:text-cyan-600",
    background: "bg-cyan-500",
  },
  address: {
    hover: "hover:text-yellow-600",
    background: "bg-yellow-500",
  },
};
