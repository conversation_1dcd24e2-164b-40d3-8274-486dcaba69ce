import { cn } from "@/lib/utils";
import React from "react";

const UnderlineSVG = ({ className }: { className?: string }) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    viewBox="0 0 357 12"
    className={cn(
      "absolute bottom-0 translate-y-1/2 left-0 fill-orange-300 stroke-orange-300 z-10 w-full h-auto object-contain",
      className
    )}
  >
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M25.5762 6.05574C28.1727 6.30134 31.2642 6.53872 34.6588 6.76177C48.4469 7.66775 66.8419 8.31466 76.0628 8.37054C142.421 8.77271 208.563 7.1933 274.79 4.43273C250.074 4.43248 225.358 4.44076 200.643 4.4735C146.477 4.90604 85.5414 5.45237 40.4894 5.85629C35.5467 5.90061 30.7952 5.94321 26.2648 5.98376C26.0352 6.00754 25.8056 6.03153 25.5762 6.05574ZM335.691 4.44172C342.127 4.44263 348.563 4.44322 354.999 4.44322C355.828 4.44322 356.499 3.77165 356.499 2.94322C356.499 2.11479 355.828 1.44322 354.999 1.44322C350.527 1.44322 346.055 1.44293 341.583 1.44246C341.575 1.43368 341.567 1.42518 341.559 1.41698C341.378 1.22463 341.19 1.12648 341.106 1.08623C340.845 0.960374 340.574 0.928669 340.514 0.921677L340.506 0.920733C340.388 0.90599 340.256 0.895942 340.121 0.888348C339.848 0.872936 339.479 0.863086 339.015 0.857908C323.074 0.680131 307.133 0.762895 291.207 0.84558L288.841 0.857834C265.795 0.976561 234.538 1.20274 200.629 1.47351C188.568 1.4895 176.507 1.5113 164.446 1.54079C151.385 1.57273 137.87 1.30511 124.146 1.03336C91.5502 0.387914 57.7756 -0.280873 26.0988 2.98512C17.1098 3.06558 8.99434 3.13792 1.98682 3.19949C1.15843 3.20677 0.49278 3.88422 0.500059 4.71262C0.507339 5.54101 1.18479 6.20666 2.01318 6.19938C6.1514 6.16301 10.6759 6.1229 15.5383 6.07957C15.5388 6.09033 15.5393 6.10114 15.5399 6.11199C15.5674 6.5725 15.7788 6.90915 15.9522 7.10919C16.2547 7.45807 16.6501 7.63562 16.8544 7.72006C17.3404 7.92091 18.0104 8.08021 18.7428 8.2181C21.7258 8.77972 27.5718 9.30258 34.4621 9.75532C48.3019 10.6647 66.7569 11.3142 76.0447 11.3705C156.591 11.8586 236.806 9.42979 317.216 5.51469C320.87 5.33676 328.17 4.94199 333.645 4.58121C334.36 4.53412 335.046 4.48744 335.691 4.44172Z"
    />
  </svg>
);

export default UnderlineSVG;
