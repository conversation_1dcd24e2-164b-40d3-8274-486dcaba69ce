import { useState } from 'react'
import { FiAlertCircle } from 'react-icons/fi'

const Description = ({
  description = 'test test test'
}:{
  description: string | null
}) => {
  const [isHovered, setIsHovered] = useState(false)
  return (
    <p className='relative w-auto'>
      <FiAlertCircle 
        onMouseEnter={() => setIsHovered(true)}
        onMouseLeave={() => setIsHovered(false)}
      />
      {isHovered && (
        <div className='
          absolute bottom-6 left-1/2 -translate-x-1/2 w-[50vw] text-center max-w-md h-auto bg-neutral-800 text-white
          shadow-lg rounded-lg px-4 py-2 text-sm

        '>
          {description || 'No description provided'}
        </div>
      )}
    </p>
  )
}

export default Description