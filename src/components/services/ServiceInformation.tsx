"use client"

import { BiInfoCircle } from 'react-icons/bi';
import Description from './Description';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/Tooltip";

type ServiceType = {
  name: string | null;
  isOnline: boolean;
  description?: string | null;
};

interface ServicesProps {
  services: ServiceType[];
}

const ServiceInformation = ({ services }:ServicesProps) => {
  if(!Array.isArray(services)) return (
    <div>
      <h3 className='text-lg mt-10 mb-4 text-red-400 font-bold'>⚠️ All Services are down! ⚠️</h3>
      <p>Our team is tracking the issue and is working on resolving it. If you require further assistance, please send us an email at <a className="text-blue-600" href="mailto:<EMAIL>"><EMAIL></a>
      </p>  
    </div>
  )

  const offlineServices = services.filter((service) => service.isOnline === false);
  const offlineServicesCount = offlineServices.length;
  const offlineServicesString = offlineServices.map((service:any) => service.name).join(', ');

  return (
    <div className='flex flex-col'>
      {services && services.map((service:ServiceType) => (
        <Service key={service.name} name={service.name} isOnline={service.isOnline} />
      ))}

      {offlineServicesCount > 0 && (
        <p className='text-sm mt-6'>
          We apologize for the inconvenience,  It looks like{' '}
          <span className='text-red-400 font-bold'>{offlineServicesString}</span>{' '} service
          {offlineServicesCount > 1 ? ' are' : ' is'} down. Our team is tracking
          the issue and is working on resolving it. If you require further
          assistance, please send us an email at <a className="text-blue-600" href="mailto:<EMAIL>"><EMAIL></a>
        </p>
      )}
    </div>
  );
};

export default ServiceInformation;

// Service
const Service = ({ name, isOnline, description = '' }:ServiceType) => {
  return (
    <div className='flex justify-between gap-2 items-center'>
      <p className='shrink-0'>{name}</p>
      <TooltipProvider>
        <Tooltip>
          <TooltipTrigger>
            <BiInfoCircle />
          </TooltipTrigger>
          <TooltipContent side="top">
            <p>{description || 'No description provided'}</p>
          </TooltipContent>
        </Tooltip>
      </TooltipProvider>
      <p className='w-full border-b border-black border-dashed translate-y-1.5 '></p>
      {isOnline ? (
        <p className='shrink-0 text-green-600'>Online ✅</p>
      ) : (
        <p className='shrink-0 text-red-500'>Offline ❌</p>
      )}
      <p>{description}</p>
    </div>
  );
}
