"use client";
import React, { useState } from "react";
import { FiRadio } from "react-icons/fi";
import ServiceInformation from "@/components/services/ServiceInformation";
import { useGetServices } from "@/hooks/api/useServices";

import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/Tooltip";
import {
  Dialog,
  DialogTrigger,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogClose,
} from "@/components/ui/dialog";

type ServiceType = {
  name: string | null;
  isOnline: boolean;
  description?: string | null;
};

const Services = ({ extended = false }: { extended?: boolean }) => {
  const { data, isLoading, isError } = useGetServices();

  if (isError) return <div>Error loading services</div>;
  if (isLoading) return <div>Loading Services...</div>;

  return (
    <>
      <TooltipProvider>
        <Dialog>
          <Tooltip>
            <TooltipTrigger asChild>
              <DialogTrigger asChild>
                <button
                  className="relative mb-2 mt-auto flex w-full items-center gap-2 rounded bg-neutral-100/10 px-4 py-2 text-neutral-100 shadow transition-all hover:bg-neutral-100 hover:text-neutral-900 z-[50]"
                  id="services-button"
                  aria-label="Service Status"
                >
                  <FiRadio className="text-xl" />
                  <span className={`${!extended ? "hidden" : ""}`}>
                    Service Status
                  </span>
                  {Array.isArray(data) &&
                    data.filter((service: ServiceType) => !service.isOnline)
                      .length > 0 && (
                    <div className="absolute -right-2 -top-2 flex h-4 w-4 min-w-4 items-center justify-center rounded-full bg-red-500 p-0.5 text-xs text-white">
                      {
                        data.filter((service: ServiceType) => !service.isOnline)
                          .length
                      }
                    </div>
                  )}

                  {!Array.isArray(data) && (
                    <div className="absolute -right-2 -top-2 flex h-4 w-auto items-center justify-center rounded-full bg-red-300/80 px-1 text-[6px] text-red-950">
                      ❌
                    </div>
                  )}
                </button>
              </DialogTrigger>
            </TooltipTrigger>
            <TooltipContent side="right">Service Status</TooltipContent>
          </Tooltip>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>Services Status 📡</DialogTitle>
              <DialogClose />
            </DialogHeader>
            <ServiceInformation services={data ?? []} />
          </DialogContent>
        </Dialog>
      </TooltipProvider>
    </>
  );
};

export default Services;
