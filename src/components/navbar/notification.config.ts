import { FiBook, FiAlertCircle, FiClipboard, FiRefreshCw } from 'react-icons/fi'
import { IconType } from 'react-icons/lib'

export type NotificationType = {
  icon: IconType,
  message: string,
  date: string,
  link: string,
  messageType: string,
  messageFrom: string,
  read: boolean,
}

export const notifications:NotificationType[] = [
  {
    icon: FiBook,
    message: 'New city ordinance regarding rabies vaccination requirements will take effect from July 1, 2023. Please ensure all applications comply with the new rules.',
    date: '2021-09-01T00:00:00.000Z',
    link: '',
    messageType: 'info',   
    messageFrom: 'Regulatory Update',
    read: false,
  },{
    icon: FiAlertCircle,
    message: 'Scheduled maintenance will take place this Sunday from 2 AM to 4 AM. The system will be unavailable during this time.',
    date: '2021-09-01T00:00:00.000Z',
    link: '',
    messageType: 'alert',
    messageFrom: 'System Alert',
    read: false,
  },{
    icon: FiClipboard,
    message: 'You have 12 dog license applications pending for your review.',
    date: '2021-09-01T00:00:00.000Z',
    link: '',
    messageType: 'normal',
    messageFrom: 'Pending Tasks',
    read: false,
  },{
    icon: FiRefreshCw,
    message: 'The dog license application for Max (Owner: Jane Doe) has been updated. Please review the new information.',
    date: '2021-09-01T00:00:00.000Z',
    link: '',
    messageType: 'normal',
    messageFrom: 'Application Update',
    read: true,
  }
]