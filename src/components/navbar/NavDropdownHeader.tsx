import React from 'react'

const NavDropdownHeader = ({
  children,
  label,
  onClick
}:{
  children: React.ReactNode
  label: string
  onClick: () => void
}) => {
  return (
    <div className='flex justify-between items-end py-3 px-4 gap-6 shrink-0'>
      <h3 className='font-bold text-xl'>{label}</h3>
      <button 
        type='button'
        className='flex gap-2 items-center text-blue-500 text-sm font-semibold'
        onClick={onClick}
      >{children}</button>
    </div>
  )
}

export default NavDropdownHeader