"use client";
import { atom, useAtom } from "jotai";
import { useMyProfile } from "@/hooks/providers/useMyProfile";
import { sidebarToggleAtom } from "../sidebar/atom/atoms";
import { FiMenu } from "react-icons/fi";
import Menu from "./Menu";
import SearchBar from "./SearchBar";
import DownloadContainer from "../ui/download/DownloadContainer";
import NavbarCart from "./NavbarCart";
import User from "../sidebar/main/User";

export const navbarSearch = atom(false);
const requiredPermissions = ["super-admin"];

const Navbar = () => {
  const [, setIsMobileOpen] = useAtom(sidebarToggleAtom);
  const { hasPermissions } = useMyProfile();

  return (
    <NavbarContainer>
      <div className="flex max-w-xs flex-1 flex-row items-center gap-2 ">
        <button
          className="flex lg:hidden"
          onClick={() => {
            setIsMobileOpen(true);
          }}
        >
          <FiMenu className="text-2xl hover:text-gray-600" />
        </button>
        <div className="hidden md:block">
          {hasPermissions(requiredPermissions) ? <Menu /> : null}
        </div>
      </div>
      <div className="hidden shrink-0 flex-grow md:flex z-50">
        {hasPermissions(requiredPermissions) ? <SearchBar /> : null}
      </div>
      <NavbarIcons>
        {hasPermissions(requiredPermissions) ? <DownloadContainer /> : null}
        <NavbarCart />
        <User />
      </NavbarIcons>
    </NavbarContainer>
  );
};

export default Navbar;

const NavbarContainer = ({ children }: { children: React.ReactNode }) => {
  return (
    <nav className="relative z-10 flex h-14 shrink-0 items-center justify-between gap-6 bg-white px-6 py-3 shadow-sm z-[40]">
      {children}
    </nav>
  );
};

const NavbarIcons = ({ children }: { children: React.ReactNode }) => {
  return (
    <div className="flex max-w-xs flex-1 shrink-0 items-center justify-end gap-6 text-neutral-700">
      {children}
    </div>
  );
};
