import {
  <PERSON>ubar<PERSON>ontent,
  Menubar<PERSON><PERSON>,
  <PERSON>ubar<PERSON>enu,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
} from "@/components/ui/menubar";
import useRecentItems from "../../sidebar/hooks/useRecentItems";
import { useRouter } from "next/navigation";
import { BsPinAngle, BsPinFill } from "react-icons/bs";
import { FiX } from "react-icons/fi";
import type { RecentItem } from "../../sidebar/hooks/useRecentItems";
import { MenubarLabel } from "@radix-ui/react-menubar";
import AvatarImage from "@/components/AvatarImage";

export default function RecentMenu() {
  const router = useRouter();
  const { recentItems, removeItem, handleBookmark } = useRecentItems();

  return (
    <MenubarMenu>
      <MenubarTrigger>Recent</MenubarTrigger>
      <MenubarContent>
        <MenubarLabel className="mt-1 px-1 text-xs text-neutral-700">
          Recent Activities
        </MenubarLabel>
        {!recentItems.length && (
          <MenubarItem disabled>No Recent Activities</MenubarItem>
        )}
        {recentItems.map((item: RecentItem) => {

          return (
            <MenubarItem
              key={item.entityId}
              className="flex w-full cursor-pointer items-center gap-1 p-0"
              onClick={() => {
                if (item.entityType === "license") {
                  router.push(`/a/${item.entityType}/${item.entityId}`);
                } else {
                  router.push(`/profile/${item.entityType}/${item.entityId}?tab=profile`);
                }
              }}
            >
              <button
                className="p-1 text-lg"
                onClick={(e) => {
                  e.stopPropagation();
                  handleBookmark(item.entityId);
                }}
              >
                {item.pinned ? (
                  <BsPinFill className="text-yellow-500" />
                ) : (
                  <BsPinAngle className="text-gray-500" />
                )}
              </button>
              <div className="flex w-full items-center gap-2 p-1 text-left">
                <AvatarImage
                  entityType={item.entityType}
                  src={item.avatarUrl as string}
                  alt="avatar"
                  width={24}
                  height={24}
                  className="border-1 rounded-full shadow shadow-neutral-400"
                />
                <span className="mr-auto line-clamp-1 flex-grow text-sm text-blue-700">
                  {item.displayName}
                </span>
              </div>
              <button
                className="p-1 text-lg"
                onClick={(e) => {
                  e.stopPropagation();
                  removeItem(item.entityId);
                }}
              >
                <FiX className="text-neutral-800 hover:text-red-600" />
              </button>
            </MenubarItem>
          );
        })}
      </MenubarContent>
    </MenubarMenu>
  );
}