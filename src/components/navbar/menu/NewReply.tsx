import Button from "@/components/ui/buttons/Button";
import { Controller, useForm } from "react-hook-form";
import { useCreateCommentOnTicket } from "@/hooks/api/useSupport";
import { useQueryClient } from "@tanstack/react-query";
import { useAtom } from "jotai";
import { toast<PERSON>tom } from "@/components/ui/toast/toast";
import ReactQuill from "react-quill";
import "react-quill/dist/quill.snow.css";
import Modal from "@/components/modal/Modal";

// const realmTable: { [key: string]: number } = {
//   schenectady: 338,
// };

export default function NewReply({
  issueId,
  newCommentModal,
  setNewCommentModal,
}: {
  issueId: number;
  newCommentModal: boolean;
  setNewCommentModal: (val: boolean) => void;
}) {
  
  const {
    handleSubmit,
    control,
    formState: { errors },
  } = useForm();

  const toolbarOptions = [
    ["bold", "italic", "underline", "strike"], // toggled buttons
    ["blockquote", "code-block"],

    [{ header: 1 }, { header: 2 }], // custom dropdown
    [{ list: "ordered" }, { list: "bullet" }],
    [{ script: "sub" }, { script: "super" }], // superscript/subscript
    [{ indent: "-1" }, { indent: "+1" }], // outdent/indent

    [{ size: ["small", false, "large", "huge"] }], // custom dropdown
    [{ header: [1, 2, 3, 4, 5, 6, false] }],

    [{ color: [] }, { background: [] }], // dropdown with defaults
    [{ font: [] }],
    [{ align: [] }],

    ["link", "image", "video"], // link and image, video
  ];
  const newComment = useCreateCommentOnTicket(issueId);


  const [, setToast] = useAtom(toastAtom);

  const queryClient = useQueryClient();

  const onSubmit = (data:any) => {
    newComment.mutate(
      data?.comment,
      {
        onSuccess: (data) => {
          console.log(data);
          queryClient.invalidateQueries(["projectIssue", issueId]);
          setToast({
            status: "success",
            label: "Success",
            message: "Comment Added to Ticket",
          });
          setNewCommentModal(false);
        },
        onError: (error) => {
          console.log(error);
        },
      }
    );
  };

  return (
    <>
      <Modal
        title={`New Comment to Ticket #${issueId}`}
        isOpen={newCommentModal}
        onClose={() => {
          setNewCommentModal(false);
        }}
      >
        <form
          onSubmit={handleSubmit(onSubmit)}
          className="flex flex-col gap-4 overflow-auto"
        >
          <div className="grid grid-cols-1 gap-10">
            <div className="flex flex-col gap-5">
              <div className="flex flex-col">
                <label>
                  Comment:{" "}
                  <span className="text-red-500 text-sm italic">*Required</span>
                </label>
                <Controller
                  name="comment"
                  control={control}
                  defaultValue=""
                  rules={{ required: "Comment Required" }}
                  render={({ field }) => (
                    <ReactQuill
                      className="w-full border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500 overflow-hidden min-h-[200px]"
                      value={field.value}
                      onChange={(val) => field.onChange(val)}
                      onBlur={() => field.onBlur()}
                      modules={{ toolbar: toolbarOptions }}
                    />
                  )}
                />
                {errors.comment && (
                  <span className="text-red-500">Comment Required</span>
                )}
              </div>
            </div>
          </div>
          <div className="flex justify-end gap-4">
            <Button type="submit">Submit</Button>
          </div>
        </form>
      </Modal>
    </>
  );
}
