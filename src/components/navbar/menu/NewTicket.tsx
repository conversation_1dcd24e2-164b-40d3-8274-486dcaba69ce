import Button from "@/components/ui/buttons/Button";
import { Controller, useForm } from "react-hook-form";
import {
  useCreateProjectIssue,
  useGetRedmineProjectId,
} from "@/hooks/api/useSupport";
import { useQueryClient } from "@tanstack/react-query";
import { useAtom } from "jotai";
import { toastAtom } from "@/components/ui/toast/toast";
import ReactQuill from "react-quill";
import "react-quill/dist/quill.snow.css";
import { useEffect, useState } from "react";

export default function NewTicket({
  onClose,
  subject,
  description,
  ticketType,
  priority,
}: {
  onClose: () => void;
  subject?: string;
  description?: string;
  ticketType?: number;
  priority?: number;
}) {
  const {
    register,
    handleSubmit,
    control,
    formState: { errors },
  } = useForm({
    defaultValues: {
      subject: subject || "",
      description: description || "",
      ticketType: ticketType || 3,
      priority: priority || 2,
    },
  });
  const {
    data: redmineProjectId,
    isLoading: isLoadingRedmineProjectId,
    isError: isErrorRedmineProjectId,
  } = useGetRedmineProjectId();
  const [projectId, setProjectId] = useState<number | null>(null);
  useEffect(() => {
    if (redmineProjectId?.value) {
      setProjectId(redmineProjectId?.value);
    }
  }, [redmineProjectId]);

  const toolbarOptions = [
    ["bold", "italic", "underline", "strike"], // toggled buttons
    ["blockquote", "code-block"],

    [{ header: 1 }, { header: 2 }], // custom dropdown
    [{ list: "ordered" }, { list: "bullet" }],
    [{ script: "sub" }, { script: "super" }], // superscript/subscript
    [{ indent: "-1" }, { indent: "+1" }], // outdent/indent

    [{ size: ["small", false, "large", "huge"] }], // custom dropdown
    [{ header: [1, 2, 3, 4, 5, 6, false] }],

    [{ color: [] }, { background: [] }], // dropdown with defaults
    [{ font: [] }],
    [{ align: [] }],

    ["link", "image", "video"], // link and image, video
  ];
  const newTicket = useCreateProjectIssue(projectId!);

  const [, setToast] = useAtom(toastAtom);

  const queryClient = useQueryClient();

  const onSubmit = (data: any) => {
    if (!projectId) return;
    newTicket.mutate(
      {
        projectId,
        data: {
          ticketType: data.ticketType,
          subject: data.subject,
          description: data.description,
          priority_id: data.priority,
        },
      },
      {
        onSuccess: (data) => {
          console.log(data);
          queryClient.invalidateQueries(["projectIssues"]);
          setToast({
            status: "success",
            label: "Success",
            message: "Ticket Created",
          });
          onClose();
        },
        onError: (error) => {
          console.log(error);
        },
      },
    );
  };

  const inputStyling =
    "border border-gray-300 rounded p-1 focus:outline-none focus:ring-1 focus:ring-blue-500";

  return (
    <form
      onSubmit={handleSubmit(onSubmit)}
      className="flex flex-col gap-4 overflow-auto"
    >
      <div className="grid grid-cols-1 gap-10">
        <div className="flex flex-col gap-5">
          <div className="flex flex-col">
            <label>
              Subject:{" "}
              <span className="text-sm italic text-red-500">*Required</span>
            </label>
            <input
              className={`${inputStyling}`}
              type="text"
              {...register("subject", { required: true })}
            />
            {errors.subject && (
              <span className="text-red-500">Subject Required</span>
            )}
          </div>
          <div className="flex flex-col">
            <label>
              Description:{" "}
              <span className="text-sm italic text-red-500">*Required</span>
            </label>
            <Controller
              name="description"
              control={control}
              defaultValue=""
              rules={{ required: "Description Required" }}
              render={({ field }) => (
                <ReactQuill
                  className="min-h-[200px] w-full overflow-hidden rounded border border-gray-300 focus:outline-none focus:ring-1 focus:ring-blue-500"
                  value={field.value}
                  onChange={(val) => field.onChange(val)}
                  onBlur={() => field.onBlur()}
                  modules={{ toolbar: toolbarOptions }}
                />
              )}
            />
            {errors.description && (
              <span className="text-red-500">Description Required</span>
            )}
          </div>
          <div className="flex flex-col">
            <label>
              Priority:{" "}
              <span className="text-sm italic text-red-500">*Required</span>
            </label>
            <Controller
              name="priority"
              control={control}
              defaultValue={2}
              render={({ field }) => (
                <select {...field} className={`${inputStyling}`}>
                  <option value={1}>Low</option>
                  <option value={2}>Normal</option>
                  <option value={3}>High</option>
                </select>
              )}
            />
          </div>
        </div>
      </div>
      <div className="flex justify-end gap-4">
        <Button type="submit">Submit</Button>
      </div>
    </form>
  );
}
