import {
  MenubarContent,
  MenubarItem,
  MenubarMenu,
  MenubarPortal,
  MenubarSub,
  MenubarSubContent,
  MenubarSubTrigger,
  MenubarTrigger,
} from "@/components/ui/menubar";

import { useGetAllReportBySection } from "@/hooks/api/useReport";
import Modal from "@/components/modal/Modal";
import NoCardReport from "@/components/reports/NoCardReport";
import { useModal } from "./useModal";

const reportSections = {
  dog: {
    key: "dogFormsAndReports",
    title: "Dog Licensing",
  },
  clerk: {
    key: "clerkFormsAndReports",
    title: "Clerk Reports",
  },
};

export default function ReportsMenu() {
  const { isModalOpen, closeModal, modalData, openModal } = useModal();
  return (
    <>
      <MenubarMenu>
        <MenubarTrigger>Reports</MenubarTrigger>
        <MenubarPortal>
          <MenubarContent>
            <LicenseReports
              section={reportSections.dog}
              openModal={openModal}
            />
            <LicenseReports
              section={reportSections.clerk}
              openModal={openModal}
            />
          </MenubarContent>
        </MenubarPortal>
      </MenubarMenu>
      {isModalOpen && (
        <Modal
          title={modalData.label}
          isOpen={isModalOpen}
          onClose={closeModal}
        >
          <NoCardReport card={modalData} closeModal={closeModal} />
        </Modal>
      )}
    </>
  );
}

const LicenseReports = ({ openModal, section }: any) => {
  const { data, isError } = useGetAllReportBySection(section.key);

  if (!data || isError) return null;

  return (
    <MenubarSub>
      <MenubarSubTrigger>{section.title}</MenubarSubTrigger>
      <MenubarSubContent>
        {data.reports.map((report: any) => (
          <MenubarItem
            className="cursor-pointer"
            key={report.reportId}
            onClick={() => openModal(report)}
          >
            {report.label}
          </MenubarItem>
        ))}
      </MenubarSubContent>
    </MenubarSub>
  );
};
