import {
  MenubarContent,
  MenubarItem,
  MenubarMenu,
  MenubarPortal,
  MenubarTrigger,
} from "@/components/ui/menubar";

import Modal from "@/components/modal/Modal";
import { useRouter } from "next/navigation";
import { useState } from "react";
import NewTicket from "./NewTicket";
import { userAtom } from "@/components/sidebar/main/User";
import { useAtom } from "jotai";

export default function SupportMenu() {
  
  const router = useRouter();
  const [supportModal, setSupportModal] = useState<boolean>(false);
  const [user] = useAtom(userAtom);
  const redmineApiKey = user?.redmine_api_key ?? null

  return (
    <>
      <MenubarMenu>
        <MenubarTrigger>Support</MenubarTrigger>
        <MenubarPortal>
          <MenubarContent>
            <MenubarItem
              className="bg-blue-500 text-white hover:!bg-blue-600 hover:!text-white cursor-pointer p-1 rounded"
              onClick={() => {
                setSupportModal(true);
              }}
              disabled={!redmineApiKey}
            >
              <span className="px-1">New Ticket {!redmineApiKey && <span>(Access Required)</span>} </span>
            </MenubarItem>
            <MenubarItem
              onClick={() => {
                router.push(`/support/issues`);
              }}
              className="cursor-pointer"
            >
              Support Tickets
            </MenubarItem>
          </MenubarContent>
        </MenubarPortal>
      </MenubarMenu>
      {supportModal && (
        <Modal
          title={"Create Support Ticket"}
          isOpen={supportModal}
          onClose={() => {
            setSupportModal(false);
          }}
          disableOutsideClick={true}
        >
          <NewTicket onClose={()=>{
            setSupportModal(false)
          }}/>
        </Modal>
      )}
    </>
  );
}