// TODO: Drop down & make a wrapper

import React, { useRef, useEffect, createContext, useContext } from "react";
import { IconType } from "react-icons/lib";
import { motion, AnimatePresence } from "framer-motion";
import { FiX } from "react-icons/fi";

export const DropdownContext = createContext({ closeDropdown: () => {} });

export const useDropdownContext = () => useContext(DropdownContext);

type Props = {
  Icon: IconType;
  iconSize?: string;
  children: React.ReactNode;
  position?: "left" | "right";
  notificationCount?: number;
  notificationColor?: "primary" | "success" | "warning" | "danger";
  size?: "sm" | "md" | "lg" | "default" | "full";
  isOpen: boolean;
  setIsOpen: React.Dispatch<React.SetStateAction<boolean>>;
  disabled?: boolean;
  error?: boolean;
};

const NavDropdown = ({
  Icon,
  iconSize = "text-xl",
  children,
  position = "left",
  notificationCount = 0,
  notificationColor = "primary",
  size = "default",
  isOpen,
  setIsOpen,
  disabled = false,
  error = false,
}: Props) => {
  const ref = useRef<HTMLDivElement>(null);

  const settings = {
    position: {
      left: "left-2",
      right: "right-6",
    },
    notificationColor: {
      primary: "bg-blue-500 !text-blue-50",
      success: "bg-green-500 !text-green-50",
      warning: "bg-yellow-500 !text-yellow-50",
      danger: "bg-red-500 !text-red-50",
    },
    size: {
      sm: "w-[200px]",
      md: "w-[300px]",
      lg: "w-[400px]",
      default: "w-auto",
      full: "w-full",
    },
  };

  const handleClickOutside = (event: { target: any }) => {
    if (ref.current && !ref.current.contains(event.target)) {
      setIsOpen(false);
    }
  };

  const closeDropdown = () => setIsOpen(false);

  useEffect(() => {
    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const ErrorIcon = () => (
    <div
      className={`
      absolute -top-2 -right-2 rounded-full px-1 border border-white
      text-white bg-red-600 text-xs flex items-center justify-center h-[18px] min-w-[18px]
    `}
    >
      <FiX />
    </div>
  )


  return (
    <DropdownContext.Provider value={{ closeDropdown }}>
      <div
        className="flex items-center justify-center transition-all"
        ref={ref}
      >
        {/* Button */}
        <button
          role="button"
          onClick={() => disabled ? setIsOpen(false) : setIsOpen(!isOpen)}
          className="relative hover:text-neutral-800 text-neutral-500"
        >
          <Icon className={`${iconSize}`} />
          {notificationCount > 0 && (
            <>
              <div
                className={`
                absolute -top-2 -right-2 rounded-full px-1 border border-white
                text-white text-xs flex items-center justify-center h-[18px] min-w-[18px]
                ${settings.notificationColor[notificationColor]}
              `}
              >
                {notificationCount}
              </div>

              <AnimatePresence>
                {isOpen && (
                  <motion.div
                    className="
                      rounded-lg shadow-xl shadow-neutral-500 border border-neutral-300
                      bg-white overflow-auto
                    "
                    id="cart-dropdown"
                    ref={ref}
                    initial={{ opacity: 0, y: -10 }}
                    animate={{ opacity: 1, y: 0 }}
                  >
                      <div
                        className={`
                          absolute top-[70px] min-w-[200px] shadow-lg border
                          bg-white rounded
                          ${settings.position[position]}
                          ${settings.size[size]}
                        `}
                      >
                        {children}
                      </div>
                  </motion.div>
                )}
              </AnimatePresence>
            </>
          )}
          {error && <ErrorIcon />}
        </button>
      </div>
    </DropdownContext.Provider>
  );
};

export default NavDropdown;
