"use client"

import { useState, useRef, MouseEvent as ReactMouseEvent  } from 'react'
import { BiCheckDouble } from 'react-icons/bi'
import { motion } from 'framer-motion'
import { format } from 'date-fns'
import { notifications } from './notification.config'
import NavDropdownHeader from './NavDropdownHeader'
import { FiBell, FiX } from 'react-icons/fi'
import Modal from '../modal/Modal'

const Notification = () => {
  const [active, setActive] = useState<string>('all')
  const [isModalOpen, setIsModalOpen] = useState<boolean>(false)
  const ref = useRef<HTMLDivElement>(null)

  type TabType = {
    label: string;
    value: string;
    notificationCount: number;
  }

  const tabs:TabType[] = [
    { 
      label: 'All',
      value: 'all', 
      notificationCount: 3
    },
    {
      label: 'Following',
      value: 'following',
      notificationCount: 2
    },
    {
      label: 'Archive',
      value: 'archive',
      notificationCount: 0
    }
  ]

  let unreadNotificationCount = notifications.filter(notification => !notification.read).length 

  if(unreadNotificationCount > 99) {
    unreadNotificationCount = 99
  }


  return (
    <>
    <button className='relative' 
      onClick={() => setIsModalOpen(true)}
    >
      <FiBell className='text-xl text-neutral-500' />
      {unreadNotificationCount > 0 && (
        <div className='absolute -top-2 -right-2 w-fit px-1 bg-[#ed115D] rounded-full text-white flex items-center justify-center font-semibold border border-white'>          <span className='sr-only'>Notification</span>
          <span className='text-xs'>{unreadNotificationCount}</span>
        </div>
      )}
    </button>
    <Modal
      isOpen={isModalOpen}
      onClose={() => setIsModalOpen(false)}
      title='Notifications'
      position='right'
    >  
      <div className='w-full max-w-sm bg-white z-10'>
        <motion.div
          className='flex gap-6 border-b border-neutral-100 transition-all duration-200 px-2 mb-2'
        >
          {tabs.map((tab, index) => (
            <motion.button 
              className={`
                flex items-center gap-2 text-sm border-b-2 py-1 px-2 font-bold transition-all duration-200
                ${active === tab.value ? 'border-neutral-800 text-neutral-700' : 'border-transparent text-neutral-400'}
              `}
              key={index}
              onClick={() => {
                setActive(tab.value)
              }}
            >
              <div>{tab.label}</div>
              {tab.notificationCount > 0 && (
                <div className={`
                  w-4 h-4 text-white rounded flex items-center justify-center
                  ${active === tab.value ? 'bg-red-500' : 'bg-white border border-neutral-400 !text-neutral-400'}
                `}>
                  {tab.notificationCount}
                </div>
              )}
            </motion.button>
          ))}
        </motion.div>

        <motion.div>
          {notifications.map((notification, index) => {
            const Icon = notification.icon
            return (
              <motion.div
                className={`
                  flex gap-4 items-start text-sm border-b border-neutral-300 px-4 py-2.5 transition-all duration-200 relative
                  hover:bg-green-50 hover:shadow-inner
                  ${notification.messageType === 'alert' && 'bg-red-50'}
                  ${notification.messageType === 'info' && 'bg-sky-50'}
                `}
                key={index}
              >
                <Icon className='text-xl text-neutral-500 shrink-0 mt-1'/>
                <div className='flex flex-col w-full text-left'>
                  <p className='text-neutral-700 font-semibold'>
                    {notification.message}
                  </p>
                  <small className='text-neutral-500 text-xs mt-1'>
                    {format(new Date(notification.date), 'MMM dd, yyyy')} - {notification.messageFrom}
                  </small>
                </div>
                {!notification.read && (
                  <div className='absolute top-2 right-2 h-2 w-2 shadow rounded-full bg-sky-600' />
                )}
              </motion.div>
            )
          })}
        </motion.div>
      </div>
    </Modal>
    </>
  )
}

export default Notification