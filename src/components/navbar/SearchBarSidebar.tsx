import { FiSearch } from "react-icons/fi";
import Link from 'next/link'

const SearchBar = () => {
  return (
    <Link href="/entity/search"className="w-full max-w-full relative cursor-pointer sm:hidden flex mb-2">
      <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
        <FiSearch />
      </div>
      <div className="w-full px-4 py-2 pl-10 text-sm text-gray-900 border border-gray-300 rounded bg-gray-50">
        <span className="w-full line-clamp-1">Search People, Licenses...</span>
      </div>
    </Link>
  );
};

export default SearchBar;
