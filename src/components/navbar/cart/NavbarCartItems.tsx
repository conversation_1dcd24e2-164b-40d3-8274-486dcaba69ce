"use client";

import React from "react";
import SummaryIcon from "@/components/cart/SummaryIcon";
import SummaryButtons from "@/components/cart/SummaryButtons";
import Link from "next/link";
import { realm } from "@/utils/realm";

const formatter = new Intl.NumberFormat("en-US", {
  style: "currency",
  currency: "USD",
});

const getLink = (itemType: string, itemId: string) => {
  switch (itemType) {
    case "tag":
      return `/store/replacements`;
    case "license":
      return `/a/${itemType}/${itemId}`;
    default:
      return null
  }
};

const NavbarCartItems = ({
  items,
  cartId,
  setIsModalOpen,
}: {
  items: any[];
  cartId: string;
  setIsModalOpen: React.Dispatch<React.SetStateAction<boolean>>;
}) => {
  return (
    <div className="w-full">
      {items?.map((item, index) => {
        const itemType = item?.itemType;
        const itemId = item?.itemId;
        const link = getLink(itemType, itemId);

        return (
          <Link
            href={link ?? ""}
            key={index}
            className="flex items-center gap-4 rounded border-b border-neutral-100 px-2 py-4 hover:bg-blue-100"
            onClick={(e) => {
              e.stopPropagation();
              setIsModalOpen(false);
            }}
          >
            <SummaryIcon item={itemType} />

            <div className="flex w-full flex-col gap-1">
              <span className="line-clamp-1 text-sm font-semibold">
                {item.primaryDisplay}
              </span>
              <span className="line-clamp-1 text-xs text-neutral-700">
                {item.secondaryDisplay}
              </span>
              <span className="line-clamp-1 text-xl font-bold italic">
                {formatter.format(item.total)}
              </span>
            </div>

            <SummaryButtons cartItemId={item.cartItemId} />
          </Link>
        );
      })}
    </div>
  );
};

export default NavbarCartItems;
