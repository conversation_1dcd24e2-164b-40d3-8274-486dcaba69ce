
import Image from "next/image";
import {
  AiFillBell,
} from "react-icons/ai";
import { FiCircle } from "react-icons/fi";
import { MdFlag } from "react-icons/md";
import { HiOutlineMail, HiOutlinePhone } from "react-icons/hi";

type SearchResultProps = {
  result: any;
  activeEntityId: string;
  setActiveEntityId: (entityId: string) => void;
};

const MapResult = ({ 
  result,
  activeEntityId,
  setActiveEntityId
}: SearchResultProps) => {

  if (!result) return null;

  const {
    primaryDisplay,
    secondaryDisplay,
    thirdDisplay,
    entityType,
    avatarUrl,
  } = result;

  type Icon = {
    [key: string]: string;
  };

  const icons: Icon = {
    individual: "/images/icons/user.png",
    dog: "/images/icons/dog.png",
    company: "/images/icons/organization.png",
    address: "/images/icons/address.png",
  };

  const typeToIconMap:any = {
    email: <HiOutlineMail className="inline-block mr-1 mt-[1px]" />,
    phone: <HiOutlinePhone className="inline-block mr-1 mt-[1px]" />,
    active: <FiCircle className="inline-block mr-1 text-green-500" />,
    inactive: <FiCircle className="inline-block mr-1 text-red-500" />,
  };

  const avatar = (avatarUrl && avatarUrl !== '') ? avatarUrl : icons[entityType.toLowerCase()];

  const formatPhoneNumber = (phoneNumber:string) => {
    return phoneNumber.replace(/(\d{3})(\d{3})(\d{4})/, "($1) $2-$3");
  };

  return (
    <div className={`
      border  flex flex-col items-center cursor-pointer rounded
      ${activeEntityId === result.entityId 
        ? 'bg-gradient-to-br from-indigo-50 to-indigo-50 border-indigo-500' 
        : 'hover:bg-gradient-to-br hover:from-neutral-100 hover:to-neutral-50 border-neutral-200'
      }
    `}>
      <button
        className="flex items-center gap-4 w-full p-4 text-left"
        onClick={() => setActiveEntityId(result.entityId)}
      >
        <div className="w-16 h-16 relative shrink-0">
          <Image
            src={avatar}
            alt="Picture of the user"
            className="rounded"
            fill
            style={{
              objectFit: "cover",
            }}
          />
        </div>
          
        <div className="w-full">
          <div className="font-bold text-lg flex justify-between items-center">
            <span>{primaryDisplay}</span>
            <div className="shrink-0 flex gap-2 text-neutral-500 text-sm">
              <button disabled><AiFillBell/></button>
              <button disabled><MdFlag/></button>
            </div>
          </div>
          <p className="text-neutral-500 text-sm">{secondaryDisplay}</p>
          {thirdDisplay && (
            <p className="text-base mt-auto flex gap-6 text-neutral-600 items-center">
              {thirdDisplay.map((item: any, index: number) => (
                <span key={index} className="flex items-center justify-center text-xs  text-neutral-800 line-clamp-1">
                  {typeToIconMap[item.type]}
                  {item.type === "phone" && formatPhoneNumber(item.value)}
                  {item.type !== "phone" && item.value}
                </span>
              ))}
            </p>
          )}
        </div>
      </button>

    </div>
  );
};

export default MapResult;