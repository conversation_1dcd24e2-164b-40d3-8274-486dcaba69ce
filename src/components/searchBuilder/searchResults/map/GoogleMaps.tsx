import GoogleMarkers from "./GoogleMarkers";
import { useSmoothPanMap } from "../../hooks/useSmoothPanMapProps";
import { useParams } from "next/navigation";

// TODO: import from API in the future
const initialMapOptions = {
  mapId: process.env.NEXT_PUBLIC_MAP_ID ?? '',
  center: {
    lat: 0,
    lng: 0
  },
  zoom: 8,
  disableDefaultUI: true
};


const GoogleMaps = ({
  results,
  activeEntityId,
}: {
  results: any[];
  activeEntityId?: string;
}) => {
  

  const { map, mapRef } = useSmoothPanMap({
    results,
    activeEntityId,
    initialMapOptions
  });

  console.log(map)

  return (
    <div ref={mapRef} className="w-full h-full relative">
      {map && <GoogleMarkers map={map} data={results} />}
    </div>
  );
};


export default GoogleMaps;