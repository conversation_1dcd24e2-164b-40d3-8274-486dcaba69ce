// hooks/useSmoothPanMap.ts
import { useState, useEffect, useRef } from 'react';
import { smoothPanTo } from "../utils/MapScripts";

type UseSmoothPanMapProps = {
  results: any[];
  activeEntityId?: string;
  initialMapOptions: {
    mapId: string;
    center: {
      lat: number;
      lng: number;
    };
    zoom: number;
    disableDefaultUI: boolean;
  };
};

export const useSmoothPanMap = ({ results, activeEntityId, initialMapOptions }: UseSmoothPanMapProps) => {
  const [map, setMap] = useState<any>();
  const ref = useRef<HTMLDivElement>(null);
  const initialRender = useRef(true);

  const activeEntityIndex = results.findIndex(
    (result) => result.entityId === activeEntityId
  );

  const activeEntity = activeEntityIndex !== -1 ? results[activeEntityIndex] : null;
  const activeEntityLat = activeEntity?.latitude;
  const activeEntityLng = activeEntity?.longitude;

  useEffect(() => {
    if (!map) {
      const initializedMap = new window.google.maps.Map(
        ref.current as HTMLDivElement,
        initialMapOptions
      );
      setMap(initializedMap);
    } else {
      if (activeEntityId && activeEntityLat && activeEntityLng) {
        const newCenter = new window.google.maps.LatLng(activeEntityLat, activeEntityLng);
  
        smoothPanTo({
          map,
          targetLat: activeEntityLat,
          targetLng: activeEntityLng,
          durationMs: initialRender.current ? 0 : 250, 
          targetZoom: 18 
        }); 
     
      }
    }
      
    initialRender.current = false;
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [activeEntityId, map, activeEntityLat, activeEntityLng]);
  
  return { map, mapRef: ref };
};
