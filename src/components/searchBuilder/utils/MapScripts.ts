type SmoothPanTo = {
  map: google.maps.Map;
  targetLat: number;
  targetLng: number;
  durationMs: number;
  targetZoom?: number;
};

export const smoothPanTo = ({
  map,
  targetLat,
  targetLng,
  durationMs,
  targetZoom
}: SmoothPanTo): void => {
  const startPos = map.getCenter();
  const startZoom = map.getZoom();

  if (!startPos || typeof startZoom === "undefined") {
    console.error("The map does not have a valid center or zoom defined.");
    return;
  }

  const endPos = new google.maps.LatLng(targetLat, targetLng);
  const startTime = performance.now();

  const animate = (currentTime: number): void => {
    const elapsedMs = currentTime - startTime;
    const progress = Math.min(elapsedMs / durationMs, 1);
    
    const lat = startPos.lat() + progress * (endPos.lat() - startPos.lat());
    const lng = startPos.lng() + progress * (endPos.lng() - startPos.lng());

    map.setCenter(new google.maps.LatLng(lat, lng));

    if (typeof targetZoom !== "undefined") {
      const zoom = startZoom + progress * (targetZoom - startZoom);
      map.setZoom(zoom);
    }
    
    if (progress < 1) {
      requestAnimationFrame(animate);
    }
  };

  requestAnimationFrame(animate);
};
