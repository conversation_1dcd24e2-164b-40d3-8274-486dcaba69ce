import React from 'react'
import { BiChevronDown, BiChevronUp } from 'react-icons/bi'

const HideSearchForm = ({
  searchCollapse,
  setSearchCollapse
}:{
  searchCollapse: boolean,
  setSearchCollapse: React.Dispatch<React.SetStateAction<boolean>>
}) => {
  return (
    <div className='absolute bottom-0 left-1/2 -translate-x-1/2 translate-y-1/3 '>
      <button 
        className='
          flex gap-1 items-center justify-center relative w-auto px-2 py-1 
          bg-white text-neutral-700 rounded text-sm transition duration-200 ease-in-out
          hover:font-semibold hover:scale-105
              
        '
        onClick={() => setSearchCollapse(!searchCollapse)}
      >
        
        {!searchCollapse ? 
          <>
            {/* <div className='absolute bottom-0 -translate-y-1/3 left-0 -translate-x-3 w-[120%] h-full bg-red-300'></div> */}
            <span className='z-20 flex shrink-0 gap-2 items-center'>
              <BiChevronUp /> Hide Search
            </span> 
          </>:<>
            <BiChevronDown />Expand Search
          </>
        }
      </button>
    </div>
  )
}

export default HideSearchForm