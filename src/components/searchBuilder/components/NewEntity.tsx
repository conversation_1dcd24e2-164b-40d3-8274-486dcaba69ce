import Link from "next/link";
import { FaDog } from "react-icons/fa";
import { FiUserPlus } from "react-icons/fi";

type NewEntityProps = {
  entity: Entity[];
};

type Entity = {
  icon: string;
  title: string;
  description: string;
  link: string;
};

const NewEntity = ({ entity }: NewEntityProps) => {
  return (
    <div className="grid grid-cols-2 gap-4">
      {entity &&
        entity.map((entity: Entity, idx: number) => {
          const { icon, title, description, link } = entity;
          const IconComponent =
            icon === "user" ? FiUserPlus : icon === "dog" ? FaDog : null;

          return (
            <Link key={`${icon}-${idx}`} href={link} className="">
              <div className="flex flex-col items-center justify-start gap-4 p-4 rounded-lg shadow-md bg-white hover:bg-gray-100 cursor-pointer">
                {/* Icon */}
                <div>{IconComponent && <IconComponent size={70} />}</div>

                {/* Title & Description */}
                <div>
                  {/* Title */}
                  <h3
                    className={`text-3xl font-bold text-center ${
                      icon === "user" ? "text-blue-500" : "text-green-500"
                    }`}
                  >
                    {title}
                  </h3>

                  {/* Description */}
                  <p className={`text-lg`}>{description}</p>
                </div>
              </div>
            </Link>
          );
        })}
    </div>
  );
};

export default NewEntity;
