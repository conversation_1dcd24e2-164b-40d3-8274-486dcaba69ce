import Image from "next/image"
import { poppins } from "@/styles/Fonts"

export const BrandMark = ({
  size = 40,
}: {
  size?: number;
}) => {
  return (
    <div className="relative" aria-label="Brand logo">
      <Image
        src="/logos/ClerkXpress.svg"
        alt="ClerkXpress logo"
        width={size}
        height={size}
      />
    </div>
  );
};

export const LogoType = ({
  position = 'left',
  color = 'default',
  size
}:{
  position?: 'left' | 'center' | 'right',
  size: 'sm' | 'md' | 'lg',
  color?: 'default' | 'white' | 'dark'
}) => {

  const positions:{
    [key: string]: string
  } = {
    left: 'text-left',
    center: 'text-center',
    right: 'text-right',
  }

  const sizes = {
    sm: {
      brandMark: 'text-xl',
      subText: 'text-xs'
    },
    md: {
      brandMark: 'text-3xl',
      subText: 'text-sm'
    },
    lg: {
      brandMark: 'text-4xl',
      subText: 'text-3xl'
    }
  }

  const colors:{
    [key: string]: {
      brandMark: string,
      subText: string
    }
  } = {
    default: {
      brandMark: 'text-neutral-800',
      subText: 'text-neutral-500'
    },
    white: {
      brandMark: 'text-white',
      subText: 'text-white'
    },
    dark: {
      brandMark: 'text-neutral-900',
      subText: 'text-neutral-900'
    }
  }
  
  return (
    <div
      className={`${positions[position]} ${poppins.className} flex flex-col justify-center ${colors[color].brandMark}`}
      aria-label="ClerkXpress Text Logo"
    >
      <div className={sizes[size].brandMark}>
        <span className="font-semibold tracking-wider uppercase">CLERK</span>
        <span className="font-thin uppercase">XPRESS</span>
      </div>
      <small className={`font-medium ${sizes[size].subText} ${colors[color].subText}`}>
        By [s]Cube
      </small>
    </div>
  );
};