"use client";
import React, { useEffect, useState } from "react";
import { AnimatePresence, motion } from "framer-motion";
import Image from "next/image";

const fadeInOutVariants = {
  hidden: { opacity: 0 },
  visible: { opacity: 1 },
  exit: { opacity: 0 },
};

export default function LoginSignupContainer({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <main className="flex h-[100svh] w-screen overflow-hidden">
      {/* Right panel: Brand gradient */}
      <div className="hidden lg:block relative h-full w-1/2 bg-gradient-to-br from-[#7ed957] via-[#43c6ac] to-[#1e90ff]">
        {/* Town Hall Illustration */}
        <Image
          src="/images/townhall.png"
          alt="Town Hall Illustration"
          fill
          className="object-cover"
        />
        <div className="absolute inset-0 bg-gradient-to-r from-transparent via-transparent to-black opacity-50"></div>
      </div>
      {/* Left panel: Login card */}
      <div className="flex h-full w-full flex-col items-center justify-center overflow-y-auto p-6 lg:w-1/2 bg-white">
        {children}
      </div>
    </main>
  );
}
