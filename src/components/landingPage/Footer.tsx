import Image from "next/image";
import Link from "next/link";
import React from "react";

export default function Footer() {
  return (
    <footer
      id="footer"
      className="flex flex-col items-center justify-center overflow-x-hidden border-t border-slate-200 bg-slate-800 py-12 text-white"
    >
      <div className="container mx-auto max-w-8xl px-6">
        <div className="grid grid-cols-2 gap-x-4 gap-y-8 sm:grid-cols-2 md:grid-cols-5 md:gap-8">
          {/* Logo and description */}
          <div className="col-span-2 md:col-span-2">
            <div className="mb-6 flex items-center space-x-2">
              <div className="flex">
                <Image
                  src="/logos/ClerkXpress.svg"
                  alt="ClerkXpress Logo"
                  width={50}
                  height={50}
                />
              </div>
              <div>
                <span className="text-xl font-semibold md:text-3xl">CLERK</span>
                <span className="text-xl font-light text-white md:text-3xl">
                  XPRESS
                </span>
              </div>
            </div>
            <p className="mb-6 max-w-xs text-slate-300">
              Modern permitting solutions for modern cities. Fast, efficient,
              and transparent.
            </p>
            <div className="flex space-x-4">
              <Link
                target="_blank"
                href="https://www.linkedin.com/company/-s-cube-inc/"
                className="text-slate-300 hover:text-white"
              >
                LinkedIn
              </Link>
            </div>
          </div>

          {/* Sales */}
          <div className="col-span-1">
            <h3 className="mb-4 text-lg font-semibold">Sales</h3>
            <ul className="space-y-2">
              <li>
                <Link href="/sales" className="text-slate-300 hover:text-white">
                  Sales Portal
                </Link>
              </li>
              <li>
                <Link
                  href="/sales#features"
                  className="text-slate-300 hover:text-white"
                >
                  Features
                </Link>
              </li>
              <li>
                <Link
                  href="/sales#solutions"
                  className="text-slate-300 hover:text-white"
                >
                  Solutions
                </Link>
              </li>
              <li>
                <Link href="/demo" className="text-slate-300 hover:text-white">
                  Request Demo
                </Link>
              </li>
            </ul>
          </div>

          {/* Company */}
          <div className="col-span-1">
            <h3 className="mb-4 text-lg font-semibold">Company</h3>
            <ul className="space-y-2">
              <li>
                <Link href="/" className="text-slate-300 hover:text-white">
                  Home
                </Link>
              </li>
              <li>
                <Link
                  href="/sales#about"
                  className="text-slate-300 hover:text-white"
                >
                  About
                </Link>
              </li>
              <li>
                <Link
                  target="_blank"
                  href="https://scubeenterprise.com/"
                  className="text-slate-300 hover:text-white"
                >
                  Partners
                </Link>
              </li>
            </ul>
          </div>

          {/* Contact Us */}
          <div className="col-span-2 sm:col-span-1 md:col-span-1">
            <h3 className="mb-4 text-lg font-semibold">Contact Us</h3>
            <ul className="space-y-2">
              <li>
                <p className="text-slate-300">
                  <span className="font-medium">Information</span>
                  <br />
                  <Link
                    href="mailto:<EMAIL>"
                    className="break-all text-sm hover:text-white md:text-base"
                  >
                    <EMAIL>
                  </Link>
                </p>
              </li>
              <li>
                <p className="text-slate-300">
                  <span className="font-medium">Support / Help Desk</span>
                  <br />
                  <Link
                    href="mailto:<EMAIL>"
                    className="break-all text-sm hover:text-white md:text-base"
                  >
                    <EMAIL>
                  </Link>
                </p>
              </li>
            </ul>
          </div>
        </div>

        {/* Legal links and copyright */}
        <div className="mt-12 border-t border-slate-700 pt-8">
          <div className="flex flex-col justify-between gap-4 text-sm sm:flex-row sm:items-center">
            <p className="text-center text-slate-300 sm:text-left">
              &copy; 2025 ClerkXpress. All rights reserved.
            </p>
            <div className="flex flex-wrap justify-center gap-4 sm:justify-end">
              <Link
                href="/termsofservice"
                className="text-slate-300 hover:text-white"
              >
                Terms of Service
              </Link>
              <Link
                href="/privacy/policy"
                className="text-slate-300 hover:text-white"
              >
                Privacy Policy
              </Link>
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
}
