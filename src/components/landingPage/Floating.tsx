import { CheckCircle, Clock } from "lucide-react";
import React from "react";

export default function Floating() {
  return (
    <div className="relative h-[500px] md:h-[400px]"
      id="floating"
    >
      {/* Left Sticky Note */}
      <div className="absolute left-0 top-0 z-20 -rotate-6 transform md:left-10 md:top-10">
        <div className="h-56 w-56 bg-yellow-100 p-5 shadow-lg">
          <div className="mx-auto -mt-3 mb-2 h-4 w-4 rounded-full bg-red-500"></div>
          <p className="font-handwriting text-sm leading-tight">
            Track all your building permits in one place, with easy status
            updates and automatic renewal alerts.
          </p>
        </div>
      </div>

      {/* Checklist Element */}
      <div className="absolute bottom-20 left-16 z-10 md:bottom-0 md:left-40">
        <div className="w-48 rounded-lg bg-white p-4 shadow-xl">
          <div className="mb-2 flex h-8 w-8 items-center justify-center rounded bg-blue-500 text-white">
            <CheckCircle size={20} />
          </div>
          <h3 className="mb-2 font-medium">Permit Checklist</h3>
          <div className="space-y-2">
            <div className="flex items-center">
              <div className="mr-2 flex h-4 w-4 items-center justify-center rounded-sm border border-blue-500 bg-blue-500">
                <CheckCircle size={10} className="text-white" />
              </div>
              <span className="text-sm">Application</span>
            </div>
            <div className="flex items-center">
              <div className="mr-2 flex h-4 w-4 items-center justify-center rounded-sm border border-blue-500 bg-blue-500">
                <CheckCircle size={10} className="text-white" />
              </div>
              <span className="text-sm">Document Upload</span>
            </div>
            <div className="flex items-center">
              <div className="mr-2 h-4 w-4 rounded-sm border border-slate-300"></div>
              <span className="text-sm">Review Process</span>
            </div>
          </div>
        </div>
      </div>

      {/* Reminder Card */}
      <div className="absolute right-0 top-0 z-30 md:right-10 md:top-10">
        <div className="w-64 rounded-lg bg-white p-4 shadow-xl">
          <div className="mb-1 flex justify-between">
            <h3 className="font-medium">Reminders</h3>
            <span className="text-xs text-slate-500">Monday</span>
          </div>
          <div className="mb-3 border-l-4 border-blue-500 py-2 pl-3">
            <p className="text-sm font-medium">License Renewal</p>
            <p className="text-xs text-slate-500">Due in 3 days</p>
          </div>
          <div className="mt-2 flex items-center text-xs text-slate-500">
            <Clock size={14} className="mr-1" />
            <span>10:00 - 11:30</span>
          </div>
        </div>
      </div>

      {/* Tasks Card */}
      <div className="absolute bottom-0 left-4 z-20 md:bottom-10 md:left-auto md:right-24">
        <div className="w-64 rounded-lg bg-white p-4 shadow-xl">
          <h3 className="mb-3 font-medium">Today&apos;s permits</h3>
          <div className="space-y-3">
            <div>
              <div className="flex items-center">
                <div className="mr-2 h-4 w-4 rounded-sm bg-red-500"></div>
                <p className="text-sm">New business license</p>
                <div className="ml-auto flex">
                  <div className="-mr-1 flex h-5 w-5 items-center justify-center rounded-full bg-slate-200 text-[10px] font-medium">
                    JD
                  </div>
                </div>
              </div>
              <div className="ml-6 mt-1">
                <div className="h-1.5 w-full rounded-full bg-slate-100">
                  <div
                    className="h-1.5 rounded-full bg-blue-500"
                    style={{ width: "60%" }}
                  ></div>
                </div>
                <div className="mt-1 flex justify-between text-xs text-slate-400">
                  <span>Apr 12</span>
                  <span>60%</span>
                </div>
              </div>
            </div>

            <div>
              <div className="flex items-center">
                <div className="mr-2 h-4 w-4 rounded-sm bg-green-500"></div>
                <p className="text-sm">Building inspection</p>
                <div className="ml-auto flex">
                  <div className="-mr-1 flex h-5 w-5 items-center justify-center rounded-full bg-slate-200 text-[10px] font-medium">
                    TS
                  </div>
                </div>
              </div>
              <div className="ml-6 mt-1">
                <div className="h-1.5 w-full rounded-full bg-slate-100">
                  <div
                    className="h-1.5 rounded-full bg-red-500"
                    style={{ width: "85%" }}
                  ></div>
                </div>
                <div className="mt-1 flex justify-between text-xs text-slate-400">
                  <span>Apr 14</span>
                  <span>85%</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Integrations Card */}
      <div className="absolute bottom-48 right-0 z-10 md:bottom-64 md:right-10">
        <div className="rounded-lg bg-white p-3 shadow-lg">
          <p className="mb-1 text-sm font-medium">100+ Integrations</p>
          <div className="flex space-x-2">
            <div className="flex h-10 w-10 items-center justify-center rounded bg-white p-1.5 shadow-sm">
              <svg viewBox="0 0 24 24" className="h-full w-full">
                <path
                  fill="#EA4335"
                  d="M5.266 9.804C6.221 7.337 8.732 5.5 11.699 5.5c1.816 0 3.451.812 4.668 2.095L19.3 4.662C17.445 2.801 14.755 1.5 11.699 1.5c-4.425 0-8.227 2.628-9.933 6.406l3.5 1.898z"
                />
                <path
                  fill="#4285F4"
                  d="M11.699 1.5C14.755 1.5 17.445 2.8 19.3 4.662l-2.933 2.933C15.15 6.312 13.515 5.5 11.699 5.5c-2.968 0-5.478 1.837-6.433 4.304L1.766 7.906C3.472 4.128 7.274 1.5 11.699 1.5z"
                />
                <path
                  fill="#FBBC05"
                  d="M2.908 12c0-.786.111-1.55.317-2.273L1.766 7.906A10.457 10.457 0 001.5 12c0 1.439.293 2.81.826 4.058l3.46-1.927A6.024 6.024 0 012.908 12z"
                />
                <path
                  fill="#34A853"
                  d="M11.699 17.5c-2.163 0-4.054-1.066-5.214-2.702L2.766 16.06C4.48 19.168 7.873 21.5 11.699 21.5c2.842 0 5.536-1.037 7.522-2.97l-2.799-2.892c-1.358 1.092-3.067 1.862-4.981 1.862z"
                />
                <path
                  fill="#EA4335"
                  d="M21.11 12c0-.594-.082-1.192-.236-1.769h-9.097v3.739h5.314c-.252 1.104-.95 2.047-1.862 2.632l2.799 2.892C20.278 17.421 21.11 14.786 21.11 12z"
                />
              </svg>
            </div>
            <div className="flex h-10 w-10 items-center justify-center rounded bg-white p-1.5 shadow-sm">
              <svg viewBox="0 0 24 24" className="h-full w-full">
                <path fill="#00A0FF" d="M1.5 1.5H9V9H1.5z" />
                <path fill="#00CFFF" d="M9 1.5h7.5V9H9z" />
                <path fill="#00AAF0" d="M1.5 9H9v7.5H1.5z" />
                <path fill="#00DDA5" d="M9 9h7.5v7.5H9z" />
                <path fill="#00D2B8" d="M16.5 1.5H24V9h-7.5z" />
                <path fill="#00C4BD" d="M16.5 9H24v7.5h-7.5z" />
                <path fill="#00BACA" d="M1.5 16.5H9V24H1.5z" />
                <path fill="#00ACAF" d="M9 16.5h7.5V24H9z" />
                <path fill="#009E97" d="M16.5 16.5H24V24h-7.5z" />
              </svg>
            </div>
            <div className="flex h-10 w-10 items-center justify-center rounded bg-white p-1.5 shadow-sm">
              <svg viewBox="0 0 24 24" className="h-full w-full">
                <path fill="#0F9D58" d="M12 13.5v9l7.5-4.5V9z" />
                <path fill="#4285F4" d="M12 13.5L4.5 18V9l7.5 4.5z" />
                <path fill="#188038" d="M19.5 9L12 13.5 19.5 18V9z" />
                <path fill="#EA4335" d="M12 1.5L4.5 6 12 10.5l7.5-4.5z" />
              </svg>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
