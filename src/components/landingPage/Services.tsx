import React from "react";
import { dmsans } from "@/styles/Fonts";
import Image from "next/image";
import { motion, useAnimation } from "framer-motion";
import { useRef, useEffect } from "react";
import { useInView } from "framer-motion";
import { Landing_Page } from "@/hooks/api/useCMS";

type CardType = {
  image_id: string;
  overview_header: string;
  overview_description: string;
};

const Header = ({
  mini,
  header,
  description,
}: {
  mini: string;
  header: string;
  description: string;
}) => {
  return (
    <header className="mb-20 pt-16 text-center">
      <small className="uppercase text-blue-600" role="text">
        {mini}
      </small>
      <h2
        className={`text-6xl font-bold text-clerk-primary ${dmsans.className}`}
      >
        {header}
      </h2>
      <p className="text-neutral-500">{description}</p>
    </header>
  );
};

// CMS Link URLS
const build: {
  [key: string]: string;
} = {
  development: "https://cms-dev.clerkxpress.com/assets/",
  production: "https://cms.clerkxpress.com/assets/",
  staging: "https://cms-staging.clerkxpress.com/assets/",
  default: "https://cms-dev.clerkxpress.com/assets/",
};

const Card = ({ card }: { card: CardType }) => {
  console.log(card);
  const controls = useAnimation();
  const ref = useRef(null);
  const isInView = useInView(ref);

  useEffect(() => {
    if (isInView) {
      controls.start({
        opacity: 1,
        y: 0,
      });
    }
  }, [controls, isInView]);

  const cmsLink = build[(process.env.APP_ENV as any) || "default"];
  const img =
    typeof card.image_id === "string"
      ? card.image_id
      : (card.image_id as any).image;
  const imgUrl = cmsLink + img;

  return (
    <motion.article
      ref={ref}
      initial={{ opacity: 0, y: 100 }}
      animate={controls}
      className="flex flex-col items-center gap-1 rounded p-3"
      role="listitem"
    >
      <Image
        src={imgUrl}
        alt={card.overview_header}
        width={64}
        height={64}
        priority
      />
      <h3 className="text-xl font-bold text-clerk-primary">
        {card.overview_header}
      </h3>
      <p className="text-center text-lg text-neutral-500">
        {card.overview_description}
      </p>
    </motion.article>
  );
};

const Services = ({ data }: { data: Landing_Page["services"] }) => {
  const { mini_text, header, description, overview } = data;
  return (
    <div className="container mx-auto max-w-8xl p-6" id="services">
      <Header mini={mini_text} header={header} description={description} />
      <div
        className="grid grid-cols-1 gap-10 sm:grid-cols-2 md:grid-cols-3"
        role="list"
      >
        {overview.map((card, index) => (
          <Card key={index} card={card} />
        ))}
      </div>
    </div>
  );
};

export default Services;
