"use client";

import React, { useEffect, useState } from "react";
import { AnimatePresence, motion } from "framer-motion";
import Link from "next/link";
import Image from "next/image";
import { Menu, X } from "lucide-react";
import { usePathname } from "next/navigation";
import { useActiveSection } from "@/hooks/useActiveSection";

import {
  NavigationMenu,
  NavigationMenuList,
  NavigationMenuItem,
} from "@/components/ui/navigation-menu";
import { Button } from "@/components/ui/button";

export default function Navbar() {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const pathname = usePathname();
  const activeSection = useActiveSection();

  useEffect(() => {
    if (isMenuOpen) {
      const scrollY = window.scrollY;
      document.body.style.overflow = "hidden";
      document.body.style.position = "fixed";
      document.body.style.top = `-${scrollY}px`;
      document.body.style.width = "100%";
    } else {
      document.body.style.overflow = "";
      document.body.style.position = "";
      document.body.style.top = "";
      document.body.style.width = "";
    }
  }, [isMenuOpen]);

  const toggleMenu = () => {
    setIsMenuOpen(!isMenuOpen);
  };

  function linkIsActive(linkHref: string): boolean {
    const [pathPart, hashPart] = linkHref.split("#");
    const routeToCompare = pathPart === "" ? "/" : pathPart;

    if (routeToCompare !== pathname) {
      return false;
    }

    if (hashPart) {
      return activeSection === hashPart;
    }

    if (linkHref === "/") {
      return activeSection === "" || activeSection === "home";
    }

    return activeSection === "";
  }

  // No navigation links needed for minimal resident-focused navbar
  const navLinks: { name: string; href: string }[] = [
    { name: "Home", href: "/" },
    { name: "For Municipalities", href: "/sales" },
  ];



  return (
    <header className="fixed top-0 left-0 right-0 z-50 w-full border-b border-gray-200 bg-white/95 backdrop-blur-sm ">
      <div className="container mx-auto flex h-16 items-center justify-between px-6 max-w-8xl w-full">
        {/* -- Logo -- */}
        <Link href="/" className="flex items-center gap-2">
          <Image
            src="/logos/ClerkXpress.svg"
            alt="ClerkXpress Logo"
            width={40}
            height={40}
          />
          <div>
            <span className="text-xl font-semibold md:text-2xl">CLERK</span>
            <span className="text-xl font-light text-clerk-background md:text-2xl">
              XPRESS
            </span>
          </div>
        </Link>

        {/* -- Desktop Navigation -- */}
        <div className="hidden md:flex">
          <NavigationMenu>
            <NavigationMenuList className="flex items-center gap-6">
              {navLinks?.map((link) => {
                const isActive = linkIsActive(link.href);
                return (
                  <NavigationMenuItem key={link.name}>
                    <Link
                      href={link.href}
                      className={`relative text-sm font-medium transition duration-150 hover:text-blue-600 ${
                        isActive ? "text-blue-600" : "text-gray-700"
                      }`}
                    >
                      {link.name}
                      {isActive && (
                        <motion.div
                          layoutId="activeLink"
                          className="absolute -bottom-1 left-0 h-0.5 w-full bg-blue-600"
                          transition={{
                            type: "spring",
                            stiffness: 380,
                            damping: 30,
                          }}
                        />
                      )}
                    </Link>
                  </NavigationMenuItem>
                );
              })}
            </NavigationMenuList>
          </NavigationMenu>
        </div>

        {/* -- Desktop CTA Buttons -- */}
        <div className="hidden items-center space-x-3 md:flex gap-2">
          <Link
            href="/login"
            className="text-base font-medium text-gray-700 hover:text-blue-600"
          >
            Login
          </Link>
          <Link
            href="/signup"
            className="rounded bg-blue-600 px-4 py-2 text-white hover:bg-blue-700"
          >
            Sign up
          </Link>
        </div>

        {/* -- Mobile Menu Button -- */}
        <button
          className="rounded-md p-2 text-gray-600 focus:outline-none md:hidden"
          onClick={toggleMenu}
          aria-label="Toggle navigation menu"
        >
          {isMenuOpen ? <X className="h-6 w-6" /> : <Menu className="h-6 w-6" />}
        </button>
      </div>

      {/* -- Mobile Menu -- */}
      <AnimatePresence>
        {isMenuOpen && (
          <motion.div
            initial={{ height: 0, opacity: 0 }}
            animate={{ height: "auto", opacity: 1 }}
            exit={{ height: 0, opacity: 0 }}
            transition={{ duration: 0.3 }}
            className="overflow-hidden border-b border-gray-200 bg-white md:hidden"
          >
            <div className="container mx-auto px-4 py-4">
              <nav className="flex flex-col space-y-4">
                {navLinks.map((link) => {
                  const isActive = linkIsActive(link.href);
                  return (
                    <Link
                      key={link.name}
                      href={link.href}
                      className={`relative py-2 text-base font-medium transition-colors duration-200 ${
                        isActive
                          ? "text-blue-600"
                          : "text-gray-700 hover:text-blue-600"
                      }`}
                      onClick={() => setIsMenuOpen(false)}
                    >
                      {link.name}
                      {isActive && (
                        <motion.div
                          initial={{ scale: 0, opacity: 0 }}
                          animate={{ scale: 1, opacity: 1 }}
                          className="absolute right-0 h-2 w-2 rounded-full bg-blue-600"
                        />
                      )}
                    </Link>
                  );
                })}

                <div className="mt-4 flex flex-col space-y-3 pt-4">
                  <Link
                    href="/login"
                    className="py-2 text-center text-base font-medium text-gray-700 hover:text-blue-600"
                    onClick={() => setIsMenuOpen(false)}
                  >
                    Sign in
                  </Link>
                  <Button
                    className="w-full bg-blue-600 hover:bg-blue-700"
                    onClick={() => setIsMenuOpen(false)}
                  >
                    Sign up
                  </Button>
                </div>
              </nav>
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </header>
  );
}
