"use client";
import React from "react";
import { motion } from "framer-motion";
import { Activity, Target, Users, Award, Clock, Map } from "lucide-react";
import { Button } from "@/components/ui/button";
import Image from "next/image";

const AboutSection = () => {
  const fadeIn = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: { duration: 0.6 },
    },
  };

  const staggerContainer = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
      },
    },
  };

  return (
    <>
      <section id="about" className="py-20">
        <div className="container mx-auto max-w-8xl px-6">
          {/* Section Header */}
          <div className="mb-16 text-center">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.5 }}
            >
              <span className="rounded-full bg-black px-4 py-1 text-sm font-medium text-white">
                About Us
              </span>
            </motion.div>
            <motion.h2
              className="mt-6 text-4xl font-bold md:text-5xl"
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.5, delay: 0.1 }}
            >
              Modernizing municipal services
            </motion.h2>
            <motion.p
              className="mx-auto mt-4 max-w-2xl text-lg text-gray-600"
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.5, delay: 0.2 }}
            >
              We&apos;re on a mission to transform how cities and residents
              interact through innovative, accessible technology.
            </motion.p>
          </div>

          {/* Story Section */}
          <div className="mb-20 grid gap-10 md:grid-cols-2 lg:gap-16">
            {/* Image Column */}
            <motion.div
              className="relative hidden rounded-2xl p-2  shadow-xl md:flex"
              variants={fadeIn}
              initial="hidden"
              whileInView="visible"
              viewport={{ once: true }}
            >
              <div className="relative h-full w-full overflow-hidden rounded-xl">
                <Image
                  src="/images/aboutus.jpg"
                  alt="ClerkXpress team"
                  className="rounded-lg"
                  layout="fill"
                  objectFit="cover"
                />
              </div>

              {/* Decorative elements */}
              <motion.div
                className="absolute -left-6 -top-6 h-24 w-24 rounded-full bg-blue-100"
                initial={{ scale: 0 }}
                whileInView={{ scale: 1 }}
                viewport={{ once: true }}
                transition={{ delay: 0.3, duration: 0.5 }}
              />
              <motion.div
                className="absolute -bottom-6 -right-6 h-24 w-24 rounded-full bg-gray-100"
                initial={{ scale: 0 }}
                whileInView={{ scale: 1 }}
                viewport={{ once: true }}
                transition={{ delay: 0.4, duration: 0.5 }}
              />
            </motion.div>

            {/* Content Column */}
            <motion.div
              className="flex flex-col justify-center"
              variants={fadeIn}
              initial="hidden"
              whileInView="visible"
              viewport={{ once: true }}
              transition={{ delay: 0.2 }}
            >
              <h3 className="mb-4 text-2xl font-bold md:text-3xl">
                Our journey
              </h3>
              <div className="space-y-4 text-gray-600">
                <p>
                  ClerkXpress began with a simple observation: municipal
                  services were stuck in the past, bound by paper processes and
                  in-person requirements that made life difficult for both
                  residents and city staff.
                </p>
                <p>
                  Founded by a team with backgrounds in both government and
                  technology, we set out to create a platform that would bring
                  municipal services fully online while remaining affordable for
                  cities of all sizes.
                </p>
                <p>
                  Today, ClerkXpress represents the future of municipal service
                  delivery, offering a platform that helps cities modernize
                  operations, reduce paperwork, and provide 24/7 service access
                  to their residents - all at a fraction of the cost of legacy
                  systems.
                </p>
              </div>

              <div className="mt-8 flex flex-wrap gap-6">
                {[
                  {
                    icon: <Activity className="size-6 text-blue-600" />,
                    title: "Innovation-Driven",
                    description:
                      "Built on modern cloud technology for reliability and security",
                  },
                  {
                    icon: <Users className="h-6 w-6 text-blue-600" />,
                    title: "Customer-Focused",
                    description:
                      "Designed with direct input from city clerks and residents",
                  },
                ].map((item, index) => (
                  <div key={index} className="flex max-w-xs items-center">
                    <div className="mr-3 flex size-12 shrink-0 items-center justify-center rounded-full bg-blue-100">
                      {item.icon}
                    </div>
                    <div>
                      <p className="font-bold">{item.title}</p>
                      <p className="text-sm text-gray-600">
                        {item.description}
                      </p>
                    </div>
                  </div>
                ))}
              </div>
            </motion.div>
          </div>

          {/* Mission & Values */}
          <div className="mb-20">
            <motion.div
              className="mb-12 text-center"
              variants={fadeIn}
              initial="hidden"
              whileInView="visible"
              viewport={{ once: true }}
            >
              <h3 className="text-2xl font-bold md:text-3xl">
                Our mission & values
              </h3>
              <p className="mx-auto mt-4 max-w-2xl text-lg text-gray-600">
                We&apos;re guided by core principles that drive everything we do
              </p>
            </motion.div>

            <motion.div
              className="grid gap-6 sm:grid-cols-2 lg:grid-cols-3"
              variants={staggerContainer}
              initial="hidden"
              whileInView="visible"
              viewport={{ once: true }}
            >
              {[
                {
                  title: "Accessibility for All",
                  description:
                    "We believe government services should be accessible to everyone, regardless of location, ability, or technology access.",
                  icon: <Users className="h-6 w-6" />,
                  color: "bg-blue-100 text-blue-600",
                },
                {
                  title: "Efficiency Through Innovation",
                  description:
                    "We constantly seek new ways to streamline processes, eliminate waste, and make municipal operations more efficient.",
                  icon: <Activity className="h-6 w-6" />,
                  color: "bg-green-100 text-green-600",
                },
                {
                  title: "Customer-Centered Design",
                  description:
                    "Every feature is designed based on real feedback from clerks and residents to ensure it solves actual problems.",
                  icon: <Target className="h-6 w-6" />,
                  color: "bg-purple-100 text-purple-600",
                },
                {
                  title: "Transparency",
                  description:
                    "We believe in clear, honest communication with both our municipal partners and their residents.",
                  icon: <Award className="h-6 w-6" />,
                  color: "bg-amber-100 text-amber-600",
                },
                {
                  title: "Affordability",
                  description:
                    "We're committed to providing premium technology at a price point that works for municipalities of all sizes.",
                  icon: <Award className="h-6 w-6" />,
                  color: "bg-red-100 text-red-600",
                },
                {
                  title: "Continuous Improvement",
                  description:
                    "We're never satisfied with the status quo, constantly evolving our platform based on feedback and emerging needs.",
                  icon: <Award className="h-6 w-6" />,
                  color: "bg-indigo-100 text-indigo-600",
                },
              ].map((value, index) => (
                <motion.div
                  key={index}
                  className="flex flex-col rounded-xl border border-gray-200 bg-white p-6 shadow-sm transition-all duration-300 hover:shadow-md"
                  variants={fadeIn}
                  whileHover={{ y: -5 }}
                >
                  <div
                    className={`mb-4 flex h-12 w-12 items-center justify-center rounded-full ${value.color}`}
                  >
                    {value.icon}
                  </div>
                  <h4 className="mb-2 text-xl font-bold">{value.title}</h4>
                  <p className="text-gray-600">{value.description}</p>
                </motion.div>
              ))}
            </motion.div>
          </div>

          {/* Team Section Placeholder */}
          <motion.div
            className="mb-20 rounded-xl bg-gray-50 p-8 text-center md:p-12"
            variants={fadeIn}
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true }}
          >
            <h3 className="mb-6 text-2xl font-bold md:text-3xl">
              Meet our team
            </h3>
            <p className="mx-auto mb-8 max-w-2xl text-lg text-gray-600">
              We&apos;re a passionate team of technologists, former municipal
              employees, and civic tech enthusiasts with deep expertise in both
              government operations and modern software development.
            </p>

            {/* Team member photos would go here */}
            <div className="flex justify-center">
              <Button className="bg-blue-600 hover:bg-blue-700">
                Learn more about our team (Coming soon!)
              </Button>
            </div>
          </motion.div>

          {/* Testimonial */}
          <motion.div
            className="rounded-xl bg-blue-600 p-8 text-white md:p-12"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.6 }}
          >
            <div className="mx-auto max-w-3xl text-center">
              <svg
                className="mx-auto mb-6 h-12 w-12 text-blue-300"
                fill="currentColor"
                viewBox="0 0 32 32"
              >
                <path d="M9.352 4C4.456 7.456 1 13.12 1 19.36c0 5.088 3.072 8.064 6.624 8.064 3.36 0 5.856-2.688 5.856-5.856 0-3.168-2.208-5.472-5.088-5.472-.576 0-1.344.096-1.536.192.48-3.264 3.552-7.104 6.624-9.024L9.352 4zm16.512 0c-4.8 3.456-8.256 9.12-8.256 15.36 0 5.088 3.072 8.064 6.624 8.064 3.264 0 5.856-2.688 5.856-5.856 0-3.168-2.304-5.472-5.184-5.472-.576 0-1.248.096-1.44.192.48-3.264 3.456-7.104 6.528-9.024L25.864 4z" />
              </svg>
              <p className="mb-6 text-xl font-medium md:text-2xl">
                &quot;ClerkXpress reflects our commitment to making local government more accessible, efficient, and resident-focused. This platform is a vital step forward in connecting our community with the services and information they need—quickly and easily.&quot;
              </p>
              <div>
                <p className="font-bold">Mayor Gary McCarthy </p>
                <p className="text-blue-200">Schenectady, NY</p>
              </div>
            </div>
          </motion.div>
        </div>
      </section>
    </>
  );
};

export default AboutSection;
