import {
  addDays,
  addWeeks,
  addMonths,
  addYears,
  format,
  parseISO,
} from "date-fns";
import { EditFeeCodeDateProp, EditFeeCodeProp, FeeType } from "./FeeTypes";

const daysOfWeek = ["<PERSON>", "Mon", "<PERSON><PERSON>", "Wed", "Thu", "Fri", "Sat"];
const cronWeek = {
  Sun: 0,
  Mon: 1,
  Tue: 2,
  Wed: 3,
  Thu: 4,
  Fri: 5,
  Sat: 6,
};

export const feeHelper = {
  daysOfWeek,
  cronWeek,
};

export type RecurrenceType =
  | "daily"
  | "weekly"
  | "monthly"
  | "yearly"
  | "custom";

export interface RecurrenceConfig {
  recurrenceType: RecurrenceType;

  // For weekly recurrence
  selectedDays?: string[]; // e.g. ["Mon", "Wed", "Fri"]

  // For monthly (or yearly) recurrence
  dayOfMonth?: number | "last"; // e.g. 15 or "last"

  // For yearly recurrence (requires a month)
  monthOfYear?: number; // 1–12

  // For custom recurrence, simply store the full cron expression.
  customCron?: string;

  // Optional time configuration. If not provided, defaults to midnight.
  time?: {
    hours: number;
    minutes: number;
    seconds: number;
  };

  // These are not part of the cron string but can be used in your logic.
  occurrenceCount?: number; // Optional number of occurrences
}

export const convertToDollar = (amount: number) => {
  return amount.toLocaleString("en-US", {
    style: "currency",
    currency: "USD",
  });
};

export const calculateDate = (date: EditFeeCodeDateProp): Date | null => {
  if (!date) return null;

  let baseDate: Date;

  if (date.type === "relative") {
    switch (date.date) {
      case "today":
        baseDate = new Date();
        break;
      case "tomorrow":
        baseDate = addDays(new Date(), 1);
        break;
      default:
        baseDate = parseISO(date.date);
        break;
    }
  } else {
    baseDate = new Date(date.date);
  }

  if (isNaN(baseDate.getTime())) return null; 

  let calculatedDate: Date = baseDate;

  switch (date.offsetBy) {
    case "days":
      calculatedDate = addDays(baseDate, date.offset);
      break;
    case "weeks":
      calculatedDate = addWeeks(baseDate, date.offset);
      break;
    case "months":
      calculatedDate = addMonths(baseDate, date.offset);
      break;
    case "years":
      calculatedDate = addYears(baseDate, date.offset);
      break;
  }

  return new Date(calculatedDate.setHours(0, 0, 0, 0));
};

export const generateCron = (config: RecurrenceConfig): string => {
  // Use provided time or default to midnight (0:00:00)
  const seconds =
    config.time?.seconds !== undefined ? String(config.time.seconds) : "0";
  const minutes =
    config.time?.minutes !== undefined ? String(config.time.minutes) : "0";
  const hours =
    config.time?.hours !== undefined ? String(config.time.hours) : "0";

  let dayOfMonth = "*";
  let month = "*";
  let dayOfWeek = "*";

  switch (config.recurrenceType) {
    case "daily":
      // Daily: every day at the specified time.
      break;

    case "weekly": {
      // Weekly: use the selected days.
      // Map day names to cron numbers (Sun=0, Mon=1, etc.)
      const cronWeekMap: { [key: string]: number } = {
        Sun: 0,
        Mon: 1,
        Tue: 2,
        Wed: 3,
        Thu: 4,
        Fri: 5,
        Sat: 6,
      };
      if (config.selectedDays && config.selectedDays.length > 0) {
        dayOfWeek = config.selectedDays
          .map((day) => cronWeekMap[day])
          .sort((a, b) => a - b)
          .join(",");
      }
      break;
    }

    case "monthly": {
      // Monthly: use the day of month (or "L" for last)
      if (config.dayOfMonth) {
        dayOfMonth =
          config.dayOfMonth === "last" ? "L" : String(config.dayOfMonth);
      }
      break;
    }

    case "yearly": {
      // Yearly: both day and month must be specified.
      if (config.dayOfMonth) {
        dayOfMonth =
          config.dayOfMonth === "last" ? "L" : String(config.dayOfMonth);
      }
      if (config.monthOfYear) {
        month = String(config.monthOfYear);
      }
      break;
    }

    case "custom":
      // For custom, simply return the provided cron expression.
      return config.customCron || "";

    default:
      break;
  }

  const cron = `${seconds} ${minutes} ${hours} ${dayOfMonth} ${month} ${dayOfWeek}`;
  return cron;
};

export const reverseCron = (
  cronExpression: string,
): RecurrenceConfig | null => {
  if (!cronExpression) return null;

  const parts = cronExpression.trim().split(" ");
  if (parts.length < 6) return null;

  const [seconds, minutes, hours, dayOfMonth, month, dayOfWeek] = parts;

  const time = {
    hours: parseInt(hours, 10),
    minutes: parseInt(minutes, 10),
    seconds: parseInt(seconds, 10),
  };

  // Determine the recurrence type based on cron parts.
  if (dayOfMonth === "*" && month === "*" && dayOfWeek === "*") {
    // Daily recurrence
    return { recurrenceType: "daily", time };
  }

  if (dayOfMonth === "*" && month === "*" && dayOfWeek !== "*") {
    // Weekly recurrence
    const cronWeekMapReverse: { [key: number]: string } = {
      0: "Sun",
      1: "Mon",
      2: "Tue",
      3: "Wed",
      4: "Thu",
      5: "Fri",
      6: "Sat",
    };

    const selectedDays = dayOfWeek.split(",").map((numStr) => {
      const num = parseInt(numStr, 10);
      return cronWeekMapReverse[num];
    });
    return { recurrenceType: "weekly", selectedDays, time };
  }

  if (dayOfMonth !== "*" && month === "*") {
    // Monthly recurrence
    const day = dayOfMonth === "L" ? "last" : parseInt(dayOfMonth, 10);
    return { recurrenceType: "monthly", dayOfMonth: day, time };
  }

  if (dayOfMonth !== "*" && month !== "*") {
    // Yearly recurrence
    const day = dayOfMonth === "L" ? "last" : parseInt(dayOfMonth, 10);
    const monthNum = parseInt(month, 10);
    return {
      recurrenceType: "yearly",
      dayOfMonth: day,
      monthOfYear: monthNum,
      time,
    };
  }

  return null;
};

export const convertGroupFees = (fees: EditFeeCodeProp[]): FeeType[] => {
  return fees.map((fee) => {
    const newFee: FeeType = {
      key: fee.key,
      feeName: fee.feeName,
      amount: fee.amount,
      operation: fee.operation,
    };

    if (fee.recurringAmount) {
      newFee.recurringAmount = fee.recurringAmount;
    }

    if (fee.startDate) {
      const calculatedDate = calculateDate(fee.startDate);
      newFee.startDate = calculatedDate ? format(calculatedDate, "yyyy-MM-dd") : undefined;
    }

    if (fee.endDate) {
      const calculatedDate = calculateDate(fee.endDate);
      newFee.endDate = calculatedDate ? format(calculatedDate, "yyyy-MM-dd") : undefined;
    }

    if (fee.isRecurring && fee.recurrence) {
      const recurrenceConfig:RecurrenceConfig = {
        recurrenceType: fee.recurrence.recurrenceType || "daily",
        customCron: fee.recurrence.customCron,
        dayOfMonth: fee.recurrence.dayOfMonth,
        monthOfYear: fee.recurrence.monthOfYear,
        selectedDays: fee.recurrence.selectedDays,
        time: fee.recurrence.time,
        occurrenceCount: fee.occurrenceCount,
      };
      newFee.cronExpression = generateCron(recurrenceConfig);
    }

    return newFee;
  });
};