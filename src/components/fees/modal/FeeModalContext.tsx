"use client";

import React, {
  create<PERSON>ontext,
  Dispatch,
  SetStateAction,
  useContext,
  useEffect,
  useState,
} from "react";
import { Dog } from "@/types/DogType";
import { Individual } from "@/types/IndividualType";
import { License } from "@/types/LicenseType";
import {
  useAddFeeSetToEntity,
  useGetAllFeeCodes,
  useGetAllFeeGroups,
} from "@/hooks/api/useFees";
import Loading from "@/app/(app)/loading";
import { EditFeeCodeProp, EditFeeSetProp } from "./FeeTypes";

/** Association interface for your selected/available entities */
interface Association {
  entityId: string;
  entityType: string;
  label: string;
  mandatory: boolean;
}

interface FeesDocumentsProp {
  [key: string]: File;
}

interface FeeModalContextType {
  /** Current selected fee group (optional) */
  group: EditFeeSetProp | null;
  setGroup: Dispatch<SetStateAction<EditFeeSetProp | null>>;
  /** All available fee groups for this entity type */
  groups: EditFeeSetProp[];

  /** Current fee being edited (optional) */
  editFee: EditFeeCodeProp | null;
  setEditFee: (fee: EditFeeCodeProp | null) => void;

  /** Whether user is adding a new fee */
  addFee: boolean;
  setAddFee: (addFee: boolean) => void;

  /** All available fees for this entity type */
  fees: EditFeeCodeProp[];

  /** Associations logic */
  associations: Association[];
  availableAssociations: Association[];
  initializeAssociations: () => void;
  addAssociation: (assoc: Association) => void;
  removeAssociation: (assoc: Association) => void;
  showAssociations: boolean;
  setShowAssociations: (showAssociations: boolean) => void;

  /** Current entity info */
  entity: Dog | Individual | License;
  entityType: string;

  /** Dialog state/controls */
  isOpen: boolean;
  onClose: () => void;

  /** Documents logic */
  showDocuments: boolean;
  setShowDocuments: (showDocuments: boolean) => void;
  feesDocuments: FeesDocumentsProp;
  setFeesDocuments: (feesDocuments: FeesDocumentsProp) => void;

  /** Mutation on entityfee */
  updateEntityFee: any;
}

/** Create the React Context */
const FeeModalContext = createContext<FeeModalContextType | undefined>(undefined);

interface FeeModalProviderProps {
  children: React.ReactNode;
  entity: Dog | Individual | License;
  entityType: string;
  isOpen: boolean;
  onClose: () => void;
  defaultGroup?: string;
}

/** Helper to extract a label from a Dog or Individual */
function getEntityLabel(entity: Dog | Individual): string {
  if ("dogName" in entity && entity.dogName) {
    return entity.dogName;
  }
  if ("firstName" in entity && entity.firstName) {
    return entity.firstName;
  }
  return "";
}

export function FeeModalProvider({
  children,
  entity,
  entityType,
  isOpen,
  onClose,
  defaultGroup
}: FeeModalProviderProps) {
  /** ------------------------------
   *  1) Queries / Data Fetching
   * ------------------------------ */
  const { data: feeSets, isLoading: feeSetsLoading } = useGetAllFeeGroups();
  const { data: feeCodes, isLoading: feeCodesLoading } = useGetAllFeeCodes();
  const updateEntityFee = useAddFeeSetToEntity();

  /** ------------------------------
   *  2) Local States
   * ------------------------------ */

  // Fee Sets
  const [groups, setGroups] = useState<EditFeeSetProp[]>([]);
  const [group, setGroup] = useState<EditFeeSetProp | null>(null);

  // Fees
  const [editFee, setEditFee] = useState<EditFeeCodeProp | null>(null);
  const [addFee, setAddFee] = useState<boolean>(false);
  const [fees, setFees] = useState<EditFeeCodeProp[]>([]);

  // Documents
  const [showDocuments, setShowDocuments] = useState<boolean>(false);
  const [feesDocuments, setFeesDocuments] = useState<FeesDocumentsProp>({});

  // Associations
  const [associations, setAssociations] = useState<Association[]>([]);
  const [availableAssociations, setAvailableAssociations] = useState<Association[]>([]);
  const [showAssociations, setShowAssociations] = useState<boolean>(false);

  /** ------------------------------
   *  3) Initialize Associations
   * ------------------------------ */

  const initializeAssociations = () => {
    // Clear out old data to avoid duplicates on re-init
    setAssociations([]);
    setAvailableAssociations([]);

    // Define your primary association types
    const primaryAssociations = ["dog", "individual", "organization"];

    // Loop through each primary type
    primaryAssociations.forEach((primaryAssociation) => {
      if (primaryAssociation === entityType) {
        // The current entity is mandatory
        const currentEntity = (entity as any)[entityType] as Dog | Individual;
        setAssociations((prev) => {
          if (prev.some((a) => a.entityId === currentEntity.entityId)) {
            return prev;
          }
          return [
            ...prev,
            {
              entityId: currentEntity.entityId,
              entityType: currentEntity.entityType,
              label: getEntityLabel(currentEntity),
              mandatory: true,
            },
          ];
        });
      } else {
        // Additional entities of this type in the entity object
        const otherEntities =
          ((entity as any)[primaryAssociation] as (Dog | Individual)[]) || [];
        otherEntities.forEach((assoc) => {
          setAvailableAssociations((prev) => {
            if (prev.some((a) => a.entityId === assoc.entityId)) {
              return prev;
            }
            return [
              ...prev,
              {
                entityId: assoc.entityId,
                entityType: assoc.entityType,
                label: getEntityLabel(assoc),
                mandatory: false,
              },
            ];
          });
        });
      }
    });
  };

  /** ------------------------------
   *  4) Add/Remove Association
   * ------------------------------ */

  const addAssociation = (assoc: Association) => {
    setAssociations((prev) => {
      if (prev.some((a) => a.entityId === assoc.entityId)) return prev;
      return [...prev, assoc];
    });
    setAvailableAssociations((prev) =>
      prev.filter((a) => a.entityId !== assoc.entityId),
    );
  };

  const removeAssociation = (assoc: Association) => {
    if (assoc.mandatory) return; // Do not remove if mandatory
    setAssociations((prev) => prev.filter((a) => a.entityId !== assoc.entityId));
    setAvailableAssociations((prev) => {
      if (prev.some((a) => a.entityId === assoc.entityId)) return prev;
      return [...prev, assoc];
    });
  };

  // Re-initialize if the entity changes
  useEffect(() => {
    if (entity) {
      initializeAssociations();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [entity]);

  /** ------------------------------
   *  5) FeeSets & FeeCodes Loading
   * ------------------------------ */

  useEffect(() => {
    if (feeSets) {
      const filteredGroups: EditFeeSetProp[] = feeSets.filter(
        (group: EditFeeSetProp) => group.entityTypes?.includes(entityType),
      );
      setGroups(filteredGroups);
    }
  }, [feeSets, entityType]);

  useEffect(() => {
    if (feeCodes) {
      const filteredFeeCodes = feeCodes
        .filter((fee: EditFeeCodeProp) => fee.entityTypes?.includes(entityType))
        .map((fee) => {
          // remove unneeded fields
          const {
            id,
            createdBy,
            createdDate,
            lastModifiedBy,
            lastModifiedDate,
            conversionReference,
            ...filteredFee
          } = fee;
          return filteredFee;
        });
      setFees(filteredFeeCodes);
    }
  }, [feeCodes, entityType]);

  useEffect(() => {
    if (defaultGroup) {
      const groupToSet = groups.find(g => g.groupName === defaultGroup);
      if (groupToSet) {
        setGroup(groupToSet);
      }
    }
  }, [defaultGroup, groups]);

  /** ------------------------------
   *  6) Render
   * ------------------------------ */

  return (
    <FeeModalContext.Provider
      value={{
        // Groups
        group,
        setGroup,
        groups,

        // Fee Edit
        editFee,
        setEditFee,
        addFee,
        setAddFee,
        fees,

        // Associations
        associations,
        availableAssociations,
        initializeAssociations,
        addAssociation,
        removeAssociation,
        showAssociations,
        setShowAssociations,

        // Entity Info
        entity,
        entityType,

        // Dialog
        isOpen,
        onClose,

        // Documents
        showDocuments,
        setShowDocuments,
        feesDocuments,
        setFeesDocuments,

        // Mutation
        updateEntityFee,
      }}
    >
      {feeCodesLoading && feeSetsLoading ? (
        <Loading text="Loading fees..." fixed={false} className="bg-white" />
      ) : (
        <>{children}</>
      )}
    </FeeModalContext.Provider>
  );
}

/** Custom Hook to Consume FeeModalContext */
export function useFeeModalContext() {
  const context = useContext(FeeModalContext);
  if (!context) {
    throw new Error("useFeeModalContext must be used within a FeeModalProvider");
  }
  return context;
}
