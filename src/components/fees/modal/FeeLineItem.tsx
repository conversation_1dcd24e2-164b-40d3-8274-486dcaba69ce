import React from "react";
import { EditFeeCodeProp } from "./FeeTypes";
import EditFee from "./EditFee";
import DeleteFee from "./DeleteFee";
import {
  format,
} from "date-fns";
import { calculateDate, convertToDollar } from "./feeHelper";

export default function FeeLineItem({
  fee,
  index,
}: {
  fee: EditFeeCodeProp;
  index: number;
}) {
  const amount = convertToDollar(fee.amount);
  const recurringAmount = fee?.recurringAmount
    ? convertToDollar(fee.recurringAmount)
    : null;
  const startDate = fee?.startDate ? calculateDate(fee.startDate) : null;
  const endDate = fee?.endDate ? calculateDate(fee.endDate) : null;

  return (
    <div className="flex w-full items-center gap-2 rounded px-2 py-1 hover:bg-blue-100">
      <div className="flex w-full flex-col">
        <div>{fee.feeName}</div>
        <div className="flex gap-4 text-xs text-neutral-700">
          {!recurringAmount && <span>Amount: {amount} </span>}
          {recurringAmount && <span>Recurring: {recurringAmount}</span>}
          {startDate && (
            <span>Start Date: {format(startDate, "MM/dd/yyyy")}</span>
          )}
          {endDate && <span>End Date: {format(endDate, "MM/dd/yyyy")}</span>}
        </div>
      </div>

      <div className="flex shrink-0 items-center gap-2">
        <EditFee fee={fee} />
        <DeleteFee fee={fee} index={index} />
      </div>
    </div>
  );
}
