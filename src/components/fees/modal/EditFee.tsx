import { Button } from "@/components/ui/button";
import React from "react";
import { EditIcon } from "lucide-react";
import { useFeeModalContext } from "./FeeModalContext";
import { EditFeeCodeProp } from "./FeeTypes";

export default function EditFee({
  fee,
}: {
  fee: EditFeeCodeProp;
}) {
  const { setEditFee } = useFeeModalContext();

  return (
    <Button
      size={"sm"}
      className="h-8 w-8 shrink-0 p-0"
      onClick={() => {
        setEditFee(fee);
      }}
    >
      <EditIcon size={14} />
    </Button>
  );
}
