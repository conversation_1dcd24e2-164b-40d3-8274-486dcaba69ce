export interface FeeType {
  key: string;
  feeName: string;
  amount: number;
  operation: "MANUAL" | "FLAT" | "PERCENTAGE";
  recurringAmount?: number;
  startDate?: string | null;
  endDate?: string | null;
  entityTypes?: string[];
  cronExpression?: string;
  dueDate?: string;
}


export interface FileWithLabel extends File {
  id: string;
  label: string;
}

export interface EditFeeSetProp {
  groupName: string;
  label: string;
  description: string;
  feeCodes: EditFeeCodeProp[];
  entityTypes: string[];
  dueDate?: string;
}

export interface EditFeeCodeProp {
  key: string;
  feeName: string;
  amount: number;
  operation: "MANUAL" | "FLAT" | "PERCENTAGE";
  recurrence?: {
    recurrenceType?: "daily" | "weekly" | "monthly" | "yearly" | "custom";
    customCron?: string;
    dayOfMonth?: number | "last";
    monthOfYear?: number;
    selectedDays?: string[];
    time?: {
      hours: number;
      minutes: number;
      seconds: number;
    };

  }
  entityTypes?: string[];
  occurrenceCount?: number;
  recurringAmount?: number;
  startDate?: EditFeeCodeDateProp;
  endDate?: EditFeeCodeDateProp;
  isRecurring?: boolean;
}

export interface EditFeeCodeDateProp {
  date: string;
  type: "relative" | "fixed";
  offset: number;
  offsetBy: "days" | "weeks" | "months" | "years";
}