"use client";
import React from "react";
import { useFeeModalContext } from "./FeeModalContext";
import { AlertDialogFooter } from "@/components/ui/alert-dialog";
import { Button } from "@/components/ui/button";

export default function FeeAssociations() {
  const {
    associations,
    availableAssociations,
    addAssociation,
    removeAssociation,
    showAssociations,
    setShowAssociations,
  } = useFeeModalContext();

  if (!showAssociations) return null; 

  return (
    <div className="p-4">
      <h2 className="text-2xl font-bold mb-4">Manage Associations</h2>
      <div className="grid grid-cols-2 gap-6">
        {/* Available Associations */}
        <div>
          <h3 className="text-lg font-semibold mb-2">Available Associations</h3>
          {availableAssociations.length === 0 ? (
            <p className="text-gray-500">No available associations</p>
          ) : (
            <ul className="space-y-2">
              {availableAssociations.map((assoc) => (
                <li
                  key={assoc.entityId}
                  className="flex items-center justify-between border p-2 rounded hover:bg-gray-100"
                >
                  <div>
                    <span className="block font-medium">{assoc.label}</span>
                    <span className="block text-sm text-gray-500">
                      {assoc.entityType}
                    </span>
                  </div>
                  <button
                    onClick={() => addAssociation(assoc)}
                    className="text-green-600 hover:text-green-800 font-semibold"
                  >
                    Add
                  </button>
                </li>
              ))}
            </ul>
          )}
        </div>
        {/* Current Associations */}
        <div>
          <h3 className="text-lg font-semibold mb-2">Current Associations</h3>
          {associations.length === 0 ? (
            <p className="text-gray-500">No associations selected</p>
          ) : (
            <ul className="space-y-2">
              {associations.map((assoc) => (
                <li
                  key={assoc.entityId}
                  className="flex items-center justify-between border p-2 rounded hover:bg-gray-100"
                >
                  <div>
                    <span className="block font-medium">{assoc.label}</span>
                    <span className="block text-sm text-gray-500">
                      {assoc.entityType}
                    </span>
                  </div>
                  {assoc.mandatory ? (
                    <span className="text-gray-400 font-semibold">Mandatory</span>
                  ) : (
                    <button
                      onClick={() => removeAssociation(assoc)}
                      className="text-red-600 hover:text-red-800 font-semibold"
                    >
                      Remove
                    </button>
                  )}
                </li>
              ))}
            </ul>
          )}
        </div>
      </div>

      <AlertDialogFooter className="mt-6">
        <Button
          variant="secondary"
          type="button"
          onClick={() => setShowAssociations(false)}
        >
          Close
        </Button>
      </AlertDialogFooter>
    </div>
  );
}