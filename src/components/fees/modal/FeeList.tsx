// FeeList.tsx
import AddFeeSection from "./AddFeeSection";
import { Button } from "@/components/ui/button";
import { useFeeModalContext } from "./FeeModalContext";
import FeeLineItem from "./FeeLineItem";
import { ArrowLeft, Plus } from "lucide-react";
import {
  AlertDialogCancel,
  AlertDialogFooter,
} from "@/components/ui/alert-dialog";
import FeeDocuments from "./FeeDocuments";
import { FeeForm } from "@/app/(app)/(realm)/(protected)/(admin)/templateBuilder/feeBuilder/FeeForm";
import { EditFeeCodeProp } from "./FeeTypes";
import { convertGroupFees } from "./feeHelper";
import { useToast } from "@/hooks/useToast";
import FeeAssociations from "./FeeAssociations";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { useState } from "react";
import { Switch } from "@/components/ui/switch";

const FeeList = ({ groupLock }: { groupLock: boolean }) => {
  const { toast } = useToast();

  const {
    group,
    editFee,
    setEditFee,
    addFee,
    onClose,
    showDocuments,
    showAssociations,
    feesDocuments,
    setGroup,
    setAddFee,
    associations,
    updateEntityFee,
  } = useFeeModalContext();

  const fees = group?.feeCodes || [];

  const handleSubmitEditFee = (updatedFee: EditFeeCodeProp) => {
    if (!group) return;

    const updatedFees = group.feeCodes.map((fee) =>
      fee.key === updatedFee.key ? updatedFee : fee,
    );

    setGroup({ ...group, feeCodes: updatedFees });
    setEditFee(null);
  };

  const handleCancelEditFee = () => {
    setEditFee(null);
  };

  if (!group) return null;
  if (addFee) return <AddFeeSection />;
  if (editFee)
    return (
      <FeeForm
        fee={editFee}
        onCancel={handleCancelEditFee}
        onSubmit={handleSubmitEditFee}
        className={"p-0"}
      />
    );
  if (showDocuments) return <FeeDocuments />;
  if (showAssociations) return <FeeAssociations />;

  const handleSubmit = async () => {
    if (!group) {
      console.error("No fee group selected.");
      return;
    }

    const convertedFees = convertGroupFees(group.feeCodes);

    const strippedAssociations = associations.map(
      ({ entityId, entityType }) => ({
        entityId,
        entityType,
      }),
    );

    const { feeCodes, ...rest } = group;
    const updatedGroup = {
      ...rest,
      fees: convertedFees,
      associations: strippedAssociations,
    };

    const formData = new FormData();
    formData.append("json", JSON.stringify(updatedGroup));

    Object.entries(feesDocuments).forEach(([key, file]) => {
      formData.append(key, file);
    });

    console.log(updatedGroup);

    updateEntityFee.mutate(formData, {
      onSuccess: () => {
        console.log("Fee Added");
        onClose();
        toast.success({
          label: "Fee Added",
          message: "The fee has been successfully added.",
        });
      },
      onError: (error: any) => {
        console.error("Error adding fee:", error);
        toast.error({
          label: "Error adding fee",
          message:
            "An error occurred while adding the fee. Please try again later.",
        });
      },
    });
  };

  return (
    <>
      <div className="flex flex-col gap-3">
        {!groupLock && (
          <button
            className="flex w-fit items-center justify-center gap-2 text-blue-600 hover:text-blue-500"
            onClick={() => setGroup(null)}
          >
            <ArrowLeft size={16} /> Back to Group Selection
          </button>
        )}
        <p className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70">
          Fees:
        </p>
        {fees.map((fee, index) => (
          <FeeLineItem key={fee.key} fee={fee} index={index} />
        ))}
        <Button
          onClick={() => setAddFee(true)}
          size={"sm"}
          variant={"outline"}
          className="mb-4 flex w-full cursor-pointer items-center gap-2"
        >
          <Plus /> Add Fee
        </Button>

        <DueDate />
      </div>
      <AlertDialogFooter>
        <div className="mr-auto flex gap-2">
          <AssociationsButton />
          <FeeDocumentsButton />
        </div>
        <AlertDialogCancel onClick={onClose} className="ml-auto"
          disabled={updateEntityFee.isLoading}
        >
          {updateEntityFee.isLoading ? "Canceling..." : "Cancel"}
        </AlertDialogCancel>
        <Button onClick={handleSubmit} variant="primary"
          disabled={updateEntityFee.isLoading}
        >
          {updateEntityFee.isLoading ? "Submiting..." : "Submit"}
        </Button>
      </AlertDialogFooter>
    </>
  );
};

export default FeeList;

const AssociationsButton = () => {
  const { setShowAssociations, associations } = useFeeModalContext();
  const associationCount = associations.length || 0;

  return (
    <Button onClick={() => setShowAssociations(true)} className="relative">
      {associationCount > 0 && (
        <span className="absolute right-0 top-0 flex h-5 w-fit min-w-5 -translate-y-1/2 translate-x-1/3 items-center justify-center rounded-full bg-blue-600 px-1 text-xs text-white">
          {associationCount}
        </span>
      )}
      Associations
    </Button>
  );
};

const FeeDocumentsButton = () => {
  const { setShowDocuments, feesDocuments } = useFeeModalContext();
  const documentCount = Object.keys(feesDocuments).length;

  return (
    <Button onClick={() => setShowDocuments(true)} className="relative mr-auto">
      {documentCount > 0 && (
        <span className="absolute right-0 top-0 flex h-5 w-fit min-w-5 -translate-y-1/2 translate-x-1/3 items-center justify-center rounded-full bg-blue-600 px-1 text-xs text-white">
          {documentCount}
        </span>
      )}
      Documents
    </Button>
  );
};

const DueDate = () => {
  const { group, setGroup } = useFeeModalContext();
  const [error, setError] = useState<string | null>(null);
  const [hasDueDate, setHasDueDate] = useState<boolean>(false);

  const handleDueDateChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newDueDate = e.target.value;
    if (!group) return;

    // If the field is cleared, remove the dueDate property completely.
    if (!newDueDate) {
      setError(null);
      setGroup((prev) => {
        if (!prev) return null;
        const { dueDate, ...rest } = prev;
        return rest;
      });
      return;
    }

    // Validate the date format by attempting to create a Date object.
    const selectedDate = new Date(newDueDate);
    if (isNaN(selectedDate.getTime())) {
      setError("Please enter a complete and valid date.");
      return;
    }

    // Ensure the date is in the future.
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    if (selectedDate <= today) {
      setError("Due date must be in the future.");
      return;
    }

    // If all validations pass, clear any errors and update the group.
    setError(null);
    setGroup((prev) => (prev ? { ...prev, dueDate: newDueDate } : null));
  };

  return (
    <div className="mb-6 flex flex-col gap-2">
      <div className="flex items-center gap-4">
        <Switch
          id="hasDueDate"
          checked={hasDueDate}
          onCheckedChange={setHasDueDate}
        />
        <Label htmlFor="hasDueDate" className="text-sm">
          Has Due Date
        </Label>
      </div>
      {hasDueDate && (
        <>
          <Input
            type="date"
            id="dueDate"
            value={group?.dueDate || ""}
            onChange={handleDueDateChange}
          />
          {error && <p className="text-xs text-red-500">{error}</p>}
        </>
      )}
    </div>
  );
};
