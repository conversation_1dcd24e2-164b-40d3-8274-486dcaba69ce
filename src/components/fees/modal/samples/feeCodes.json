[{"key": "DOG-M-IMPOUNDMENT-BASE", "feeName": "Dog Impoundment Fee", "operation": "MANUAL", "amount": 80, "entityTypes": ["dog"]}, {"key": "DOG-M-IMPOUNDMENT-RECURRING", "feeName": "Dog Impoundment Recurring Fee", "operation": "MANUAL", "amount": 0, "recurringAmount": 80, "startDate": {"type": "relative", "date": "today", "offset": 0, "offsetBy": "days"}, "endDate": {"type": "relative", "date": "today", "offset": 5, "offsetBy": "days"}, "cronExpression": "0 0 0 * * *", "entityTypes": ["dog"]}, {"key": "DOG-M-OTHER", "feeName": "Dog Other Fee", "amount": 0, "operation": "MANUAL", "entityTypes": ["dog"]}, {"key": "CUSTOM-FEE", "feeName": "Custom Fee", "operation": "MANUAL", "amount": 0, "entityTypes": ["individual", "dog", "license"]}]