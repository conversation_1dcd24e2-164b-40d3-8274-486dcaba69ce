import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
} from "@/components/ui/select";
import { useFeeModalContext } from "./FeeModalContext";
import { AlertDialogFooter } from "@/components/ui/alert-dialog";
import { Button } from "@/components/ui/button";
import { Label } from "@/components/ui/label";

export default function GroupSelect() {
  const { group, setGroup, groups, onClose } =
    useFeeModalContext();

  return (
    <>
      <Label>Fee Type</Label>
      <Select
        defaultValue={group?.groupName}
        value={group?.groupName}
        onValueChange={(value) => {
          const foundGroup = groups.find((g) => g.groupName === value) || null;
          if (foundGroup) {
            setGroup(foundGroup);
            return;
          }
        }}
      >
        <SelectTrigger className="z-[9999999]">
          {group?.label || "Select a Fee Type"}
        </SelectTrigger>
        <SelectContent className="z-[9999999]">
          {groups.map((group) => (
            <SelectItem key={group.groupName} value={group.groupName}>
              {group.label}
            </SelectItem>
          ))}
        </SelectContent>
      </Select>
      <AlertDialogFooter>
        <Button variant={"outline"} onClick={onClose}>
          Cancel
        </Button>
      </AlertDialogFooter>
    </>
  );
}
