import React, { useRef, useState, useEffect } from "react";
import LoadingSpinner from "@/components/ui/LoadingSpinner";
import { useGetOrder } from "@/hooks/api/useOrder";
import { useGetReceipt } from "@/hooks/api/usePayment";
import { requests } from "@/utils/agent";
import { useMyProfile } from "@/hooks/providers/useMyProfile";

const REFETCH_INTERVAL = 5000;
const TIMEOUT_DURATION = 30000;

const DownloadReceipt = ({ url }: { url: string; }) => {
  console.log(url);
  let removePartOfUrl = "https://dev.clerkxpress.com/";
  let realUrl = url.replace(removePartOfUrl, "");

  const { hasPermissions } = useMyProfile();
  const permitted = hasPermissions(["super-admin"]);

  // If the user is not an admin, add "/me/" to the second / in the url
  if (!permitted) {
    const secondSlash = realUrl.indexOf("/", realUrl.indexOf("/") + 1);
    realUrl = realUrl.slice(0, secondSlash) + "/me" + realUrl.slice(secondSlash);
  }

  const downloadPdf = (blob: any) => {
    const link = document.createElement("a");
    link.href = URL.createObjectURL(blob);
    link.target = "_blank";
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  return (
    <button
      type="button"
      className={` 
        mt-2 !w-full rounded
        border bg-blue-600 px-4 py-1 text-center font-semibold text-blue-50 transition-colors hover:bg-blue-700  hover:font-medium sm:w-fit
      `}
      onClick={async () => {
        const file = await requests.get(realUrl, {
          responseType: "blob",
        });
        downloadPdf(file);
      }}
    >
      Download Receipt
    </button>
  );
};

export default function FeeReceipt({ orderId }: { orderId: string }) {
  const [timeoutExceeded, setTimeoutExceeded] = useState(false);
  const { hasPermissions } = useMyProfile();
  const permitted = hasPermissions(["super-admin"]);


  const { data, isLoading, isError } = useGetOrder(orderId);
  const {
    data: receiptData,
    isError: receiptError,
  } = useGetReceipt(
    orderId,
    {
      refetchInterval: timeoutExceeded ? false : REFETCH_INTERVAL,
    },
  );

  const timerRef = useRef<null | NodeJS.Timeout>(null);

  useEffect(() => {
    if (data?.licenseDocumentUrl) {
      if (timerRef.current) {
        clearTimeout(timerRef.current);
        timerRef.current = null;
      }
      setTimeoutExceeded(false);
      return;
    }

    timerRef.current = setTimeout(() => {
      setTimeoutExceeded(true);
    }, TIMEOUT_DURATION);

    return () => {
      if (timerRef.current) {
        clearTimeout(timerRef.current);
      }
    };
  }, [data?.licenseDocumentUrl]);

  if (isLoading)
    return (
      <div className="mt-2 flex items-center justify-center gap-3 rounded border bg-neutral-100 py-1 text-center text-sm">
        <LoadingSpinner className="my-0 h-6 w-6 " /> Loading Receipt
      </div>
    );
  if (isError)
    return (
      <div className="mt-2 rounded border bg-red-100 py-1 text-center text-sm text-red-600">
        Error Loading Receipt
      </div>
    );

  if (data) {
    const receipts = receiptData?.receipts ?? null;

    console.log(receiptData);

    console.log(data);
    return (
      <div className="">
        {receipts?.length === 0 && !timeoutExceeded && (
          <p className="text-blue-500">Loading receipt...</p>
        )}
        {receiptError && <p className="text-red-500">Error loading receipt</p>}
        {timeoutExceeded && receipts?.length === 0 && (
          <p className="text-red-500">No Receipt Found</p>
        )}
        {receipts &&
          receipts.map((payment) => {
            return (
              <DownloadReceipt
                key={payment.paymentId}
                url={payment.receiptUrl}
              />
            );
          })}
      </div>
    );
  }
  return null;
}
