import {
  Drawer,
  Drawer<PERSON><PERSON>,
  <PERSON><PERSON><PERSON>onte<PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
} from "@/components/ui/drawer";
import { Button } from "../ui/button";
import { LucideSlidersHorizontal } from "lucide-react";

type HistoryDrawerProps = {
  children: React.ReactNode;
};

const HistoryDrawer = ({ children }: HistoryDrawerProps) => {
  return (
    <Drawer>
      <DrawerTrigger>
        <Button variant="outline" className="shrink-0">
          <span>Options</span>
          <LucideSlidersHorizontal className="w-5 h-5 ml-2" />
        </Button>
      </DrawerTrigger>
      <DrawerContent className="h-1/2">
        <DrawerHeader></DrawerHeader>
        <div className="flex flex-col h-full w-full">{children}</div>
        <DrawerFooter>
          <DrawerClose>
            <Button variant="outline">Close</Button>
          </DrawerClose>
        </DrawerFooter>
      </DrawerContent>
    </Drawer>
  );
};

export default HistoryDrawer;
