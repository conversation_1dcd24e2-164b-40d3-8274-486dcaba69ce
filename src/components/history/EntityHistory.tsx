import {
  useGetEvents
} from "@/hooks/api/useProfiles";
import { Event } from "@/types/EventType";
import PageContainer from "../ui/Page/PageContainer";
import { useMemo, useState } from "react";
import { DateRange } from "react-day-picker";
import { addDays, format } from "date-fns";
import {
  DatePickerWithRangeComponent,
  FilterComponent,
  MutationComponent,
  PageMutationComponent,
  SortComponent,
} from "./mutation-components";
import { cn } from "@/lib/utils";
import {
  History,
  HistoryResults,
} from "@/components/history/history-components";

export type MutationType = "filter" | "sort" | "search" | "dateRange";
export type MutationInput =
  | string[]
  | "asc"
  | "desc"
  | DateRange
  | string
  | null;

export const EntityHistory = ({
  entity,
  entityType,
  sheet,
  tab,
}: {
  entity: any;
  entityType: string;
  sheet?: boolean;
  tab?: boolean;
}) => {

  console.log(entity)
  
  const events = entity?.events;

  console.log(events)
  
  if (!events) {
    return (
      <PageContainer className="container min-h-[120px] rounded-none md:rounded">
        <h1>No History Found</h1>
      </PageContainer>
    );
  }

  return (
    <EntityHistoryWithEvents
      sheet={sheet}
      tab={tab}
      events={events}
      entityType={entityType}
    />
  );
};

const EntityHistoryWithEvents = ({
  events,
  entityType,
  sheet,
  tab,
}: {
  events: Event[];
  entityType: string;
  sheet?: boolean;
  tab?: boolean;
}) => {
  const eventsList = useGetEvents();

  // Mutation States
  const [results, setResults] = useState<Event[] | null>([]);
  const [resultsFound, setResultsFound] = useState<boolean>(false);

  // Filter by code list
  const filteredList = eventsList
    ? eventsList.data?.filter((event: any) => event.profileType === entityType)
    : [];

  if (sheet) {
    return (
      <SheetHistory
        filteredList={filteredList}
        events={events}
        entityType={entityType}
        results={results}
        resultsFound={resultsFound}
        setResults={setResults}
        setResultsFound={setResultsFound}
      />
    );
  }

  return (
    <PageHistory
      filteredList={filteredList}
      events={events}
      entityType={entityType}
      results={results}
      resultsFound={resultsFound}
      setResults={setResults}
      setResultsFound={setResultsFound}
      container={!tab}
    />
  );
};

const SheetHistory = ({
  filteredList,
  events,
  entityType,
  results,
  resultsFound,
  setResults,
  setResultsFound,
}: {
  filteredList: any[] | undefined;
  events: any;
  entityType: string;
  results: any;
  resultsFound: boolean;
  setResults: React.Dispatch<React.SetStateAction<any>>;
  setResultsFound: React.Dispatch<React.SetStateAction<boolean>>;
}) => {
  // Sort history into bins
  const historyBins = useMemo(() => {
    const sortedData = [...(events ?? [])].sort(
      (a, b) =>
        new Date(b.createdDate).getTime() - new Date(a.createdDate).getTime(),
    );

    // Group events by date
    const groupedData = sortedData.reduce(
      (acc, event) => {
        const date = new Date(event.createdDate);
        const key = format(date, "PPP");
        if (!acc[key]) {
          acc[key] = [];
        }
        acc[key].push(event);
        return acc;
      },

      {},
    );

    return groupedData;
  }, [events]);

  return (
    <div className="flex w-full flex-col gap-4">
      <MutationComponent
        data={events ?? []}
        results={results}
        resultsTrigger={resultsFound}
        setResultsTrigger={setResultsFound}
        setResults={setResults}
        entityType={entityType}
        filteredList={filteredList}
      />
      <div className="mt-8 flex flex-col gap-16">
        {resultsFound ? (
          <HistoryResults
            entityType={entityType}
            data={results ?? []}
            title="Results"
            customError="No results matching filter"
          />
        ) : (
          <History data={historyBins} entityType={entityType} sheet />
        )}
      </div>
    </div>
  );
};

const TabHistory = ({}: {}) => {
  return <div>Tab</div>;
};

const PageHistory = ({
  filteredList,
  events,
  entityType,
  results,
  resultsFound,
  setResults,
  setResultsFound,
  container,
}: {
  filteredList: any[] | undefined;
  events: any;
  entityType: string;
  results: any;
  resultsFound: boolean;
  setResults: React.Dispatch<React.SetStateAction<any>>;
  setResultsFound: React.Dispatch<React.SetStateAction<boolean>>;
  container?: boolean;
}) => {
  // Mutation Control States
  const [options, setOptions] = useState<MutationType[]>([]);
  const [date, setDate] = useState<DateRange | undefined>({
    from: undefined,
    to: undefined,
  });
  const [selectedValues, setSelectedValues] = useState<Set<string>>(
    new Set<string>(),
  );
  const [sortOrder, setSortOrder] = useState<"asc" | "desc" | null>(null);
  const [searchQuery, setSearchQuery] = useState<string>("");

  // Sort history into bins
  const historyBins = useMemo(() => {
    const sortedData = Array.isArray(events)
      ? [...events].sort((a, b) => {
          return (
            new Date(b.createdDate).getTime() -
            new Date(a.createdDate).getTime()
          );
        })
      : [];

    // Group events by date
    const groupedData = sortedData.reduce(
      (acc, event) => {
        const date = new Date(event.createdDate);
        const key = format(date, "PPP");
        if (!acc[key]) {
          acc[key] = [];
        }
        acc[key].push(event);
        return acc;
      },

      {},
    );

    return groupedData;
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [history]);

  // Remove option from options
  const removeOption = (option: MutationType) => {
    // Remove the option
    setOptions(options.filter((opt) => opt !== option));

    // Reset the state based on the option
    switch (option) {
      case "filter":
        setSelectedValues(new Set<string>());
        break;
      case "dateRange":
        setDate({ from: undefined, to: undefined });
        break;
      case "sort":
        setSortOrder(null);
        break;
    }
  };

  return (
    <div className={cn("flex flex-row gap-10", container ? "container" : "")}>
      <div className="flex w-full flex-col gap-4">
        <div className="flex w-full items-center justify-between">
          <PageMutationComponent
            data={events ?? []}
            setResults={setResults}
            setResultsTrigger={setResultsFound}
            entityType={entityType}
            eventsList={filteredList}
            options={options}
            setOptions={setOptions}
            date={date}
            setDate={setDate}
            selectedValues={selectedValues}
            setSelectedValues={setSelectedValues}
            sortOrder={sortOrder}
            setSortOrder={setSortOrder}
            searchQuery={searchQuery}
            setSearchQuery={setSearchQuery}
          />
        </div>
        <div className="flex w-full ">
          <div className="flex-grow"></div>
          <div className="flex w-1/4 flex-col gap-6">
            <div className={cn("", options.includes("filter") ? "" : "hidden")}>
              <FilterComponent
                filteredList={filteredList}
                selectedValues={selectedValues}
                setSelectedValues={setSelectedValues}
                removeOption={removeOption}
              />
            </div>
            <div
              className={cn("", options.includes("dateRange") ? "" : "hidden")}
            >
              <DatePickerWithRangeComponent
                date={date}
                setDate={setDate}
                months={2}
                className="w-full"
                removeOption={removeOption}
              />
            </div>
            <div className={cn("", options.includes("sort") ? "" : "hidden")}>
              <SortComponent
                sortOrder={sortOrder}
                setSortOrder={setSortOrder}
                removeOption={removeOption}
              />
            </div>
          </div>
        </div>
        {/* Body */}
        <div className="mt-8 flex flex-col gap-16">
          {resultsFound ? (
            <HistoryResults
              entityType={entityType}
              data={results ?? []}
              title="Results"
              customError="No results matching filter"
            />
          ) : (
            <History data={historyBins} entityType={entityType} />
          )}
        </div>
      </div>
    </div>
  );
};

// Sort Date in Ascending or Descending Order
export const sortHistory = (mode: "asc" | "desc" | null, baseData: any[]) => {
  if (baseData === null || baseData === undefined || mode === null) {
    return;
  }
  const results = [...baseData].sort((a, b) => {
    switch (mode) {
      case "asc":
        return (
          new Date(a.createdDate).getTime() - new Date(b.createdDate).getTime()
        );
      case "desc":
        return (
          new Date(b.createdDate).getTime() - new Date(a.createdDate).getTime()
        );
      default:
        return 0;
    }
  });

  return results;
};

// Filter history by code
export const filterHistory = (filters: string[], baseData: any[]) => {
  if (baseData === null || baseData === undefined) {
    return;
  }

  if (filters.length === 0) {
    return baseData;
  } else {
    const results = baseData.filter((item) => filters.includes(item.code));

    return results;
  }
};

// Sort history by date
export const dateSort = (
  value: DateRange,
  baseData: any[],
  setDate: React.Dispatch<React.SetStateAction<DateRange | undefined>>,
) => {
  console.log("Sorting history by date: ", value);

  setDate(value);

  if (baseData === null || baseData === undefined) {
    return;
  }

  const results = baseData.filter((item) => {
    const eventDate = new Date(item.createdDate);
    const withinDateRange =
      (!value?.from || eventDate >= value.from) &&
      (!value?.to || eventDate <= addDays(value.to, 1));

    return withinDateRange;
  });

  return results;
};

// Define a search function
export const searchHistory = (query: any, data: any[]) => {
  if (data === null || data === undefined) {
    return;
  }

  const results = data.filter((item: any) => {
    return Object.values(item).some(
      (value) =>
        typeof value === "string" &&
        value.toLowerCase().includes(query.toLowerCase()),
    );
  });

  return results;
};
