import { DateRange } from "react-day-picker";
import {
  dateSort,
  filterHistory,
  MutationInput,
  MutationType,
  searchHistory,
  sortHistory,
} from "./EntityHistory";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/Tooltip";

import { useEffect, useState } from "react";
import { Input } from "@/components/ui/input";
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
  CommandSeparator,
} from "@/components/ui/command";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Badge } from "@/components/ui/badge";
import { cn } from "@/lib/utils";
import { But<PERSON> } from "../ui/button";
import { Separator } from "../ui/separator";
import { Ban, CheckIcon } from "lucide-react";
import { DatePickerWithRange } from "@/app/(app)/(realm)/(protected)/(app)/entity/[entitytype]/[entityId]/history/DatePickerWithRange";
import { FiFilter, FiX } from "react-icons/fi";
import { useAtom } from "jotai";
import { toastAtom } from "../ui/toast/toast";

type FilterComponentProps = {
  filteredList: any[] | undefined;
  selectedValues: Set<string>;
  setSelectedValues: React.Dispatch<React.SetStateAction<Set<string>>>;
  removeOption: (option: MutationType) => void;
};

type DateRangeComponentProps = {
  date: DateRange | undefined;
  setDate: React.Dispatch<React.SetStateAction<DateRange | undefined>>;
  months: number;
  className: string;
  removeOption: (option: MutationType) => void;
};

type SortComponentProps = {
  sortOrder: "asc" | "desc" | null;
  setSortOrder: React.Dispatch<React.SetStateAction<"asc" | "desc" | null>>;
  removeOption: (option: MutationType) => void;
};

type PageMutationComponentProps = {
  data: Event[];
  setResults: React.Dispatch<React.SetStateAction<Event[] | null>>;
  setResultsTrigger: React.Dispatch<React.SetStateAction<boolean>>;
  entityType: string;
  eventsList: any;
  options: MutationType[];
  setOptions: React.Dispatch<React.SetStateAction<MutationType[]>>;
  date: DateRange | undefined;
  setDate: React.Dispatch<React.SetStateAction<DateRange | undefined>>;
  selectedValues: Set<string>;
  setSelectedValues: React.Dispatch<React.SetStateAction<Set<string>>>;
  sortOrder: "asc" | "desc" | null;
  setSortOrder: React.Dispatch<React.SetStateAction<"asc" | "desc" | null>>;
  searchQuery: string;
  setSearchQuery: React.Dispatch<React.SetStateAction<string>>;
};

export const PageMutationComponent = ({
  data,
  setResultsTrigger,
  setResults,
  options,
  setOptions,
  date,
  setDate,
  selectedValues,
  setSelectedValues,
  sortOrder,
  setSortOrder,
  searchQuery,
  setSearchQuery,
}: PageMutationComponentProps) => {
  // Apply Mutations to data
  const applyMutation = () => {
    // If the input states are not set, return
    if (options.length === 0) {
      setResults(null);
      setResultsTrigger(false);
      return;
    }

    // Start with the original data
    let mutatedData = [...data];

    // Iterate over each option and apply the corresponding mutation
    options.forEach((option) => {
      const inputMap: { [key: string]: MutationInput } = {
        filter: Array.from(selectedValues),
        sort: sortOrder,
        dateRange: date ?? { from: undefined, to: undefined },
        search: searchQuery,
      };

      const operations: {
        [key: string]: Function;
      } = {
        filter: filterHistory,
        sort: sortHistory,
        dateRange: dateSort,
        search: searchHistory,
      };

      // Apply the mutation based on the current option
      mutatedData = operations[option](inputMap[option], mutatedData, setDate);
    });

    // Set the results with the final mutated data
    setResults(mutatedData);
    setResultsTrigger(true);
  };

  useEffect(() => {
    applyMutation();
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [selectedValues, date, sortOrder, searchQuery]);

  // Add option to options
  const addOption = (option: MutationType) => {
    setOptions([...options, option]);
  };

  // Remove option from options
  const removeOption = (option: MutationType) => {
    // Remove the option
    setOptions(options.filter((opt) => opt !== option));

    // Reset the state based on the option
    switch (option) {
      case "filter":
        setSelectedValues(new Set<string>());
        break;
      case "dateRange":
        setDate({ from: undefined, to: undefined });
        break;
      case "sort":
        setSortOrder(null);
        break;
    }
  };

  return (
    <div className="flex w-full flex-row gap-4">
      {/* Search */}
      <div className="w-full">
        <Input
          type="text"
          placeholder="Search History"
          className="w-full"
          onChange={(e) => {
            if (!options.includes("search")) {
              addOption("search");
            }

            console.log("Search query: ", e.target.value);

            // If the search query is empty, remove the search option
            if (e.target.value === "") {
              removeOption("search");
            }

            setSearchQuery(e.target.value);
          }}
        />
      </div>

      <div className="flex flex-row gap-4">
        {/* Select Options */}
        <div>
          <DropdownMenu>
            <DropdownMenuTrigger asChild className="w-full">
              <Button variant="outline" className="w-full">
                <FiFilter className="mr-2" />
                Sort & Filter
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent className="">
              <DropdownMenuLabel>Sort & Filter</DropdownMenuLabel>
              <DropdownMenuSeparator />
              <DropdownMenuItem onClick={() => addOption("dateRange")}>
                Date Range
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => addOption("filter")}>
                Filter
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => addOption("sort")}>
                Sort
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </div>
    </div>
  );
};

export const FilterComponent = ({
  filteredList,
  selectedValues,
  setSelectedValues,
  removeOption,
}: FilterComponentProps) => {
  if (!filteredList) {
    return null;
  }

  return (
    <TooltipProvider>
      <div className="flex w-full flex-row items-center justify-between gap-2">
        <Popover>
          <PopoverTrigger asChild className="w-full">
            <Button
              variant="outline"
              size="lg"
              className="h-10 w-full border-dashed"
            >
              Filter by Code
              {selectedValues?.size > 0 && (
                <>
                  <Separator orientation="vertical" className="mx-2 h-4" />
                  <Badge
                    variant="secondary"
                    className="rounded-sm px-1 font-normal lg:hidden"
                  >
                    {selectedValues.size}
                  </Badge>
                  <div className="hidden space-x-1 lg:flex">
                    {selectedValues.size > 1 ? (
                      <Badge
                        variant="secondary"
                        className="rounded-sm px-1 font-normal"
                      >
                        {selectedValues.size} selected
                      </Badge>
                    ) : (
                      filteredList
                        ?.filter((option) => selectedValues.has(option.code))
                        .map((option) => {
                          console.log("option", option);
                          return (
                            <Badge
                              variant="secondary"
                              key={option.code}
                              className="rounded-sm px-1 font-normal"
                            >
                              {option.name}
                            </Badge>
                          );
                        })
                    )}
                  </div>
                </>
              )}
            </Button>
          </PopoverTrigger>
          <PopoverContent
            side="bottom"
            className="w-[260px] p-0"
            align="center"
          >
            <Command>
              <CommandInput placeholder="Filter by Code" />
              <CommandList>
                <CommandEmpty>No results found.</CommandEmpty>
                <CommandGroup>
                  {filteredList?.map((option) => {
                    const isSelected = selectedValues.has(option.code);
                    return (
                      <CommandItem
                        key={option.code}
                        onSelect={() => {
                          let newSelectedValues;
                          if (isSelected) {
                            newSelectedValues = new Set<string>(
                              Array.from(selectedValues).filter(
                                (value) => value !== option.code,
                              ),
                            );
                          } else {
                            newSelectedValues = new Set<string>([
                              ...Array.from(selectedValues),
                              option.code,
                            ]);
                          }
                          setSelectedValues(newSelectedValues);
                        }}
                      >
                        <div
                          className={cn(
                            "border-primary mr-2 flex h-4 w-4 items-center justify-center rounded-sm border",
                            isSelected
                              ? "bg-primary text-primary-foreground"
                              : "opacity-50 [&_svg]:invisible",
                          )}
                        >
                          <CheckIcon className={cn("h-4 w-4")} />
                        </div>
                        <span className="w-full">{option.name}</span>
                      </CommandItem>
                    );
                  })}
                </CommandGroup>
                {selectedValues.size > 0 && (
                  <>
                    <CommandSeparator />
                    <CommandGroup>
                      <CommandItem
                        onSelect={() => setSelectedValues(new Set<string>())}
                        className="justify-center text-center"
                      >
                        Clear filters
                      </CommandItem>
                    </CommandGroup>
                  </>
                )}
              </CommandList>
            </Command>
          </PopoverContent>
        </Popover>
        <Tooltip>
          <TooltipTrigger>
            <Button
              size="sm"
              onClick={() => {
                removeOption("filter");
              }}
              variant="ghost"
            >
              <Ban className="text-red-500" />
            </Button>
          </TooltipTrigger>
          <TooltipContent side="bottom">
            <p>Remove Option</p>
          </TooltipContent>
        </Tooltip>
      </div>
    </TooltipProvider>
  );
};

export const DatePickerWithRangeComponent = ({
  date,
  setDate,
  months,
  className,
  removeOption,
}: DateRangeComponentProps) => {
  return (
    <TooltipProvider>
      <div className="flex w-full flex-row items-center justify-between gap-2">
        <DatePickerWithRange
          months={months}
          date={date}
          setDate={setDate}
          className="w-full"
        />
        <Tooltip>
          <TooltipTrigger>
            <Button
              size="sm"
              onClick={() => {
                removeOption("dateRange");
              }}
              variant="ghost"
            >
              <Ban className="text-red-500" />
            </Button>
          </TooltipTrigger>
          <TooltipContent side="bottom">
            <p>Remove Option</p>
          </TooltipContent>
        </Tooltip>
      </div>
    </TooltipProvider>
  );
};

export const SortComponent = ({
  sortOrder,
  setSortOrder,
  removeOption,
}: SortComponentProps) => {
  return (
    <TooltipProvider>
      <div className="flex w-full flex-row items-center justify-between gap-2">
        <Button
          className="w-full"
          onClick={() => {
            determineSortFunction(sortOrder, setSortOrder);
          }}
          variant="outline"
        >
          {determineSortTitle(sortOrder)}
        </Button>
        <Tooltip>
          <TooltipTrigger>
            <Button
              size="sm"
              onClick={() => {
                removeOption("sort");
              }}
              variant="ghost"
            >
              <Ban className="text-red-500" />
            </Button>
          </TooltipTrigger>
          <TooltipContent side="bottom">
            <p>Remove Option</p>
          </TooltipContent>
        </Tooltip>
      </div>
    </TooltipProvider>
  );
};

type MutationComponentProps = {
  data: Event[];
  results: Event[] | null;
  resultsTrigger: boolean;
  setResults: React.Dispatch<React.SetStateAction<Event[] | null>>;
  setResultsTrigger: React.Dispatch<React.SetStateAction<boolean>>;
  entityType: string;
  filteredList: any[] | undefined;
};

export const MutationComponent = ({
  data,
  setResultsTrigger,
  setResults,
  entityType,
  filteredList,
}: MutationComponentProps) => {
  const [, setToast] = useAtom(toastAtom);
  const [options, setOptions] = useState<MutationType[]>([]);
  const [date, setDate] = useState<DateRange | undefined>({
    from: undefined,
    to: undefined,
  });
  const [selectedValues, setSelectedValues] = useState<Set<string>>(
    new Set<string>(),
  );
  const [sortOrder, setSortOrder] = useState<"asc" | "desc" | null>(null);
  const [searchQuery, setSearchQuery] = useState<string>("");

  // Apply Mutations to data
  const applyMutation = () => {
    if (options.length === 0) {
      console.log("Empty state");
      setResults(null);
      setResultsTrigger(false);
      return;
    }

    let mutatedData = [...data];

    options.forEach((option) => {
      console.log("Applying mutation: ", option);

      const inputMap: { [key: string]: MutationInput } = {
        filter: Array.from(selectedValues),
        sort: sortOrder,
        dateRange: date ?? { from: undefined, to: undefined },
        search: searchQuery,
      };

      const operations: {
        [key: string]: Function;
      } = {
        filter: filterHistory,
        sort: sortHistory,
        dateRange: dateSort,
        search: searchHistory,
      };

      // Apply the mutation based on the current option
      mutatedData = operations[option](inputMap[option], mutatedData, setDate);
    });

    // Set the results with the final mutated data
    setResults(mutatedData);
    setResultsTrigger(true);
  };

  useEffect(() => {
    applyMutation();
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [selectedValues, date, sortOrder, searchQuery]);

  // Add option to options
  const addOption = (option: MutationType) => {
    setOptions([...options, option]);
  };

  // Remove option from options
  const removeOption = (option: MutationType) => {
    // Remove the option
    setOptions(options.filter((opt) => opt !== option));

    // Reset the state based on the option
    switch (option) {
      case "filter":
        setSelectedValues(new Set<string>());
        break;
      case "dateRange":
        setDate({ from: undefined, to: undefined });
        break;
      case "sort":
        setSortOrder(null);
        break;
    }
  };

  return (
    <div className="flex w-full flex-col gap-4">
      <div className="flex flex-row gap-4">
        {/* Search */}
        <div className="w-full">
          <Input
            type="text"
            placeholder="Search History"
            className="w-full"
            onChange={(e) => {
              if (!options.includes("search")) {
                addOption("search");
              }

              console.log("Search query: ", e.target.value);

              // If the search query is empty, remove the search option
              if (e.target.value === "") {
                removeOption("search");
              }

              setSearchQuery(e.target.value);
            }}
          />
        </div>
        {/* Select Options */}
        <div>
          <DropdownMenu>
            <DropdownMenuTrigger asChild className="w-full">
              <Button variant="outline" className="w-full">
                <FiFilter className="mr-2" />
                Sort & Filter
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent className="">
              <DropdownMenuLabel>Sort & Filter</DropdownMenuLabel>
              <DropdownMenuSeparator />
              <DropdownMenuItem onClick={() => addOption("dateRange")}>
                Date Range
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => addOption("filter")}>
                Filter
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => addOption("sort")}>
                Sort
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </div>

      <div className="flex flex-col gap-4">
        {/* Filter */}
        <div className={cn("", options.includes("filter") ? "" : "hidden")}>
          <div className="flex w-full flex-row items-center justify-between gap-2">
            <Popover>
              <PopoverTrigger asChild className="w-full">
                <Button
                  variant="outline"
                  size="lg"
                  className="h-10 w-full border-dashed"
                >
                  Filter by Code
                  {selectedValues?.size > 0 && (
                    <>
                      <Separator orientation="vertical" className="mx-2 h-4" />
                      <Badge
                        variant="secondary"
                        className="rounded-sm px-1 font-normal lg:hidden"
                      >
                        {selectedValues.size}
                      </Badge>
                      <div className="hidden space-x-1 lg:flex">
                        {selectedValues.size > 1 ? (
                          <Badge
                            variant="secondary"
                            className="rounded-sm px-1 font-normal"
                          >
                            {selectedValues.size} selected
                          </Badge>
                        ) : (
                          filteredList
                            ?.filter((option) =>
                              selectedValues.has(option.code),
                            )
                            .map((option) => {
                              console.log("option", option);
                              return (
                                <Badge
                                  variant="secondary"
                                  key={option.code}
                                  className="rounded-sm px-1 font-normal"
                                >
                                  {option.name}
                                </Badge>
                              );
                            })
                        )}
                      </div>
                    </>
                  )}
                </Button>
              </PopoverTrigger>
              <PopoverContent
                side="bottom"
                className="w-[260px] p-0"
                align="center"
              >
                <Command>
                  <CommandInput placeholder="Filter by Code" />
                  <CommandList>
                    <CommandEmpty>No results found.</CommandEmpty>
                    <CommandGroup>
                      {filteredList?.map((option) => {
                        const isSelected = selectedValues.has(option.code);
                        return (
                          <CommandItem
                            key={option.code}
                            onSelect={() => {
                              let newSelectedValues;
                              if (isSelected) {
                                newSelectedValues = new Set<string>(
                                  Array.from(selectedValues).filter(
                                    (value) => value !== option.code,
                                  ),
                                );
                              } else {
                                newSelectedValues = new Set<string>([
                                  ...Array.from(selectedValues),
                                  option.code,
                                ]);
                              }
                              setSelectedValues(newSelectedValues);
                            }}
                          >
                            <div
                              className={cn(
                                "border-primary mr-2 flex h-4 w-4 items-center justify-center rounded-sm border",
                                isSelected
                                  ? "bg-primary text-primary-foreground"
                                  : "opacity-50 [&_svg]:invisible",
                              )}
                            >
                              <CheckIcon className={cn("h-4 w-4")} />
                            </div>
                            <span className="w-full">{option.name}</span>
                          </CommandItem>
                        );
                      })}
                    </CommandGroup>
                    {selectedValues.size > 0 && (
                      <>
                        <CommandSeparator />
                        <CommandGroup>
                          <CommandItem
                            onSelect={() =>
                              setSelectedValues(new Set<string>())
                            }
                            className="justify-center text-center"
                          >
                            Clear filters
                          </CommandItem>
                        </CommandGroup>
                      </>
                    )}
                  </CommandList>
                </Command>
              </PopoverContent>
            </Popover>
            <Button
              onClick={() => {
                console.log("Reset filter");
                removeOption("filter");
              }}
              variant="ghost"
            >
              <Ban className="text-red-500" />
            </Button>
          </div>
        </div>

        {/* Date Range */}
        <div className={cn("", options.includes("dateRange") ? "" : "hidden")}>
          <div className="flex w-full flex-row items-center justify-between gap-2">
            <DatePickerWithRange
              months={1}
              date={date}
              setDate={setDate}
              className="w-full"
            />
            <Button
              onClick={() => {
                console.log("Reset dateRange");
                removeOption("dateRange");
              }}
              variant="ghost"
            >
              <Ban className="text-red-500" />
            </Button>
          </div>
        </div>

        {/* Sort */}
        <div className={cn("", options.includes("sort") ? "" : "hidden")}>
          <div className="flex w-full flex-row items-center justify-between gap-2">
            <Button
              className="w-full"
              variant="outline"
              onClick={() => {
                determineSortFunction(sortOrder, setSortOrder);
              }}
            >
              {determineSortTitle(sortOrder)}
            </Button>
            <Button
              onClick={() => {
                console.log("Reset sort");
                removeOption("sort");
              }}
              variant="ghost"
            >
              <Ban className="text-red-500" />
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
};

// Display Sort Title
const determineSortTitle = (value: "asc" | "desc" | null) => {
  switch (value) {
    case "asc":
      return "Ascending";
    case "desc":
      return "Descending";
    default:
      return "Sort";
  }
};

// Determine Sort Function
const determineSortFunction = (
  value: "asc" | "desc" | null,
  setSortOrder: React.Dispatch<React.SetStateAction<"asc" | "desc" | null>>,
) => {
  switch (value) {
    case "asc":
      setSortOrder("desc");
      break;
    case "desc":
      setSortOrder("asc");
      break;
    default:
      setSortOrder("asc");
  }
};
