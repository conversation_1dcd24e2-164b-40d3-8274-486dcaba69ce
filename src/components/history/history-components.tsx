import { LuActivitySquare } from "react-icons/lu";
import { FaDog, FaUser } from "react-icons/fa";
import { TbLicense } from "react-icons/tb";
import { format } from "date-fns";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";

import { FiInfo, FiMessageSquare } from "react-icons/fi";
import { cn } from "@/lib/utils";
import { Event } from "@/types/EventType";
import { Separator } from "../ui/separator";
import { useRef } from "react";
import { useMyProfile } from "@/hooks/providers/useMyProfile";

const HistoryItem = ({
  data,
  entityType,
}: {
  data: Event;
  entityType: string;
}) => {
  const Icon = IconMap[entityType] ?? IconMap["default"];
  const commentIsEmpty =
    data?.comment?.length === 0 || data?.comment?.length === undefined;
  const uuid = data.uuid;
  console.log(data);

  // Event Data
  const name = data.name ?? "Unknown Event";
  const comment = commentIsEmpty ? "No comment" : data.comment;
  const description = data.action ?? "No description";
  const createdBy = data.createdBy;
  const createdDate = format(new Date(data.createdDate), "Pp");
  const { hasPermissions } = useMyProfile();

  return (
    <div className="flex w-full flex-col  flex-wrap gap-2 rounded border bg-white p-3 hover:border-neutral-700 sm:w-full md:max-w-md">
      <div className="flex flex-row items-center justify-between">
        <div className="flex flex-row flex-wrap items-center justify-start gap-2">
          <Icon />
          <span className="font-semibold ">{name}</span>
        </div>

        {!commentIsEmpty && (
          <Popover>
            <PopoverTrigger>
              <FiMessageSquare />
            </PopoverTrigger>
            <PopoverContent align="end">{comment}</PopoverContent>
          </Popover>
        )}
      </div>
      <div>
        <span className="shrink text-xs text-neutral-500">
          Description: {description}
        </span>
      </div>
      {hasPermissions(["super-admin"]) && (
        <div className="flex w-full flex-col flex-wrap items-start justify-start gap-2 md:flex-row md:items-center md:justify-between">
          <span className="shrink text-xs text-neutral-500">
            Created By: {createdBy}
          </span>
          <span className="shrink text-xs text-neutral-500">{createdDate}</span>
        </div>
      )}
    </div>
  );
};

const HistoryList = ({
  data,
  entityType,
  title,
}: {
  data: Event[];
  entityType: string;
  title?: string;
}) => {
  const viewRef = useRef<HTMLDivElement>(null);

  return (
    <div className="flex w-full flex-col items-center">
      <div className="flex w-full  flex-row items-center justify-start gap-2">
        <LuActivitySquare className="h-5 w-5 text-neutral-500" />
        <span className="text-neutral-500">{title}</span>
      </div>
      <div className="w-full">
        <div className="mb-4 flex flex-row">
          <div className="flex w-5 flex-col items-center justify-center">
            <Separator
              className={cn("w-[1.5px] bg-neutral-300")}
              orientation="vertical"
            />
          </div>
          <div ref={viewRef} className="mt-8 flex w-full flex-col gap-2">
            {data?.map((event: Event) => (
              <HistoryItem
                key={event.uuid}
                data={event}
                entityType={entityType}
              />
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};

const SheetHistoryList = ({
  data,
  entityType,
  title,
}: {
  data: Event[];
  entityType: string;
  title?: string;
}) => {
  return (
    <div className="flex w-full flex-col gap-2">
      {data?.map((event: Event) => (
        <HistoryItem key={event.uuid} data={event} entityType={entityType} />
      ))}
    </div>
  );
};

export const History = ({
  data,
  entityType,
  sheet,
}: {
  data: {
    [key: string]: Event[];
  };
  entityType: string;
  sheet?: boolean;
}) => {
  const view = findFirstAndLast(data);

  if (Object.keys(data).length === 0) {
    return <span className="text-neutral-600">No history</span>;
  }
  return (
    <div className="">
      {sheet ? (
        <>
          <div>
            <HistoryDivider
              title={view.first}
              view={{ isTopLineVisible: false, isBottomLineVisible: true }}
            />
            <SheetHistoryList data={data[view.first]} entityType={entityType} />
          </div>

          <div>
            {Object.keys(data).map((key) => {
              if (key === view.first || key === view.last) return null;
              return (
                <div key={key}>
                  <HistoryDivider
                    title={key}
                    view={{ isTopLineVisible: true, isBottomLineVisible: true }}
                  />
                  <SheetHistoryList data={data[key]} entityType={entityType} />
                </div>
              );
            })}
          </div>

          <div className={cn("", view.last === view.first ? "hidden" : "")}>
            <HistoryDivider
              title={view.last}
              view={{ isTopLineVisible: true, isBottomLineVisible: true }}
            />
            <SheetHistoryList data={data[view.last]} entityType={entityType} />
          </div>
        </>
      ) : (
        <div className="flex flex-col gap-6">
          {Object.keys(data).map((key) => {
            return (
              <div key={key}>
                <HistoryList
                  data={data[key]}
                  entityType={entityType}
                  title={key}
                />
              </div>
            );
          })}
        </div>
      )}
    </div>
  );
};

export const HistoryResults = ({
  data,
  entityType,
  customError,
  title,
}: {
  data: Event[];
  entityType: string;
  customError?: string;
  title: string;
}) => {
  return (
    <div className="flex flex-col gap-4">
      <span className="font-bold text-black">{title}</span>

      {data && data.length > 0 ? (
        data.map((event: Event) => (
          <HistoryItem key={event.uuid} data={event} entityType={entityType} />
        ))
      ) : (
        <span className="text-neutral-600">{customError}</span>
      )}
    </div>
  );
};

export const HistoryDivider = ({
  title,
  view: { isTopLineVisible, isBottomLineVisible },
}: {
  title: string;
  view: {
    isTopLineVisible: boolean;
    isBottomLineVisible: boolean;
  };
}) => {
  return (
    <div className="flex translate-x-6 flex-row items-center justify-start gap-2">
      <div>
        {isTopLineVisible ? (
          <div className="h-8 w-0.5 translate-x-2 bg-neutral-200" />
        ) : (
          <div className="h-8 w-0.5 translate-x-2 " />
        )}
        <LuActivitySquare className="h-5 w-5 text-neutral-500" />
        {isBottomLineVisible && (
          <div className="h-8 w-0.5 translate-x-2 bg-neutral-200" />
        )}
      </div>

      <span className="text-neutral-500">{title}</span>
    </div>
  );
};

type Input = { [key: string]: Event[] };

// Function to find the key of the last and first element in events
const findFirstAndLast = (events: Input) => {
  // Find the first and last key in the object
  const keys = Object.keys(events);
  const first = keys[0];
  const last = keys[keys.length - 1];

  return { first, last };
};

const IconMap: { [key: string]: any } = {
  license: TbLicense,
  dog: FaDog,
  individual: FaUser,
  default: FiInfo,
};
