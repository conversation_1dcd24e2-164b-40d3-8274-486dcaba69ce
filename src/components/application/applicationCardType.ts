interface BulletPointInfo {
  items: string[];
  label: string;
}

interface BulletPointItem {
  icon: string;
  label: string;
  info?: BulletPointInfo[];
}

interface BulletPoint {
  items: BulletPointItem[];
  label: string;
}

interface FormField {
  image?: string;
  label: string;
  type?: string;
  href?: string;
  bulletPoints?: BulletPoint[];
}

interface FormFields {
  title: FormField;
  active?: FormField;
  button: {
    type: "primary" | "destructive";
    label: string;
    href: string;
    disabled?: boolean;
  };
  bulletPoints?: BulletPoint[];
}

export interface Form {
  fields: FormFields;
  formName: string;
  requirements?: string[];
}

export interface FormListEntry {
  form: string;
  permissions: string[];
  hidden?: boolean;
}

export interface FormType {
  forms: FormListEntry[];
  category: string;
}

export interface FormCategory {
  types: FormType[];
  category: string;
}

export interface FormsConfig {
  forms: {
    [key: string]: Form;
  };
  formList: FormCategory[];
}