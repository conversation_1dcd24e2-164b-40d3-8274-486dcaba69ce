"use client";
import Loading from "@/app/(app)/loading";
import ErrorPage from "@/components/error/ErrorPage";
import UnderlineSVG from "@/components/graphics/Underline";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/Tooltip";
import { Button } from "@/components/ui/button";
import { useGetJSONStorage } from "@/hooks/api/useAdmin";
import { useMyProfile } from "@/hooks/providers/useMyProfile";
import Image from "next/image";
import { useParams, useRouter, useSearchParams } from "next/navigation";
import React from "react";
import { BiSolidInfoCircle } from "react-icons/bi";
import {
  Form,
  FormCategory,
  FormListEntry,
  FormType,
} from "./applicationCardType";
import { useAtom } from "jotai";
import { toastAtom } from "../ui/toast/toast";

export const Licenses = () => {
  const { data, isLoading, isError } = useGetJSONStorage("list", "onlineForms");
  const { hasPermissions } = useMyProfile();

  if (isLoading) return <Loading text="Getting License List" />;
  if (isError)
    return <ErrorPage message="Failed to load license list" fixed={false} />;

  const licenseForms =
    data.formList.find((form: FormCategory) => form.category === "Licenses")
      ?.types ?? [];

  return (
    <>
      {licenseForms.map((formType: FormType) => (
        <div key={formType.category} className="flex flex-col gap-6">
          <LicenseGroup label={formType.category} />
          <div className="flex flex-wrap gap-10">
            {formType.forms
              .filter((form: FormListEntry) => {
                const formData = data.forms[form.form];
                return hasPermissions(form.permissions) && formData?.fields;
              })
              .map((form: FormListEntry) => {
                return (
                  <LicenseCard key={form.form} form={data.forms[form.form]} />
                );
              })}
          </div>
        </div>
      ))}
    </>
  );
};

const LicenseGroup = ({ label }: { label: string }) => {
  return (
    <h3 className="relative z-20 mb-10 w-fit text-2xl  font-medium ">
      <span className="relative z-30 text-green-950">{label}</span>
      <UnderlineSVG className="fill-teal-500 stroke-teal-500" />
    </h3>
  );
};

const imageList: {
  [key: string]: string;
} = {
  happyDog: "/images/resident/doghappy.png",
  multiDog: "/images/resident/dogpurebred.png",
};

const LicenseCard = ({ form }: { form: Form }) => {
  const router = useRouter();
  const [_, setToast] = useAtom(toastAtom);
  const searchParams = useSearchParams();
  const params = useParams();
  console.log(params)
  
  const getLink = (link: string) => {
    const paramMatches = link.match(/{{param:(.*?)}}/g);
    if (paramMatches) {
      paramMatches.forEach((match) => {
        const paramName = match.match(/{{param:(.*?)}}/)?.[1];
        if (paramName && params[paramName]) {
          const paramValue = Array.isArray(params[paramName])
            ? params[paramName].join(",")
            : params[paramName] ?? "";
          link = link.replace(match, paramValue);
        }
      });
    }
  
    const queryMatches = link.match(/{{query:(.*?)}}/g);
    if (queryMatches) {
      queryMatches.forEach((match) => {
        const queryName = match.match(/{{query:(.*?)}}/)?.[1];
        if (queryName) {
          const queryValue = searchParams.get(queryName) || '';
          link = link.replace(match, queryValue);
        }
      });
    }
  
    if (link.startsWith("")) {
      return link;
    }
    return link;
  };
  
  if(!form?.fields) return null;

  return (
    <TooltipProvider delayDuration={0}>
      <div className="relative flex w-full max-w-[360px] flex-col rounded-xl border border-blue-600 p-6 hover:bg-gradient-to-br hover:from-blue-100 hover:to-white">
        {form?.fields?.active && (
          <div className="absolute right-6 top-0 flex -translate-y-1/2 items-center justify-center gap-1 rounded bg-blue-600 px-2 py-1 text-sm text-white shadow">
            {form.fields.active.label}
          </div>
        )}
        <Image
          src={imageList[form.fields?.title.image ?? "happyDog"]}
          width={100}
          height={100}
          alt={form.fields?.title.label + " Image"}
        />
        <div className="mb-2">
          <div className="mb-2 flex gap-2">
            <h3 className="text-lg font-bold">{form.fields?.title.label}</h3>
          </div>
        </div>

        {form.fields?.bulletPoints?.map((bulletPoint) => {
          return (
            <div key={bulletPoint.label} className="mb-4">
              <div className="mb-2 flex gap-2">
                <h3 className="text-base font-medium">{bulletPoint.label}</h3>
              </div>
              {bulletPoint.items.map((item) => {
                return (
                  <ul key={item.label} className="flex flex-col gap-1 text-sm">
                    <Li icon={item?.icon} iconLabel={item.label}>
                      {item.label}
                      {item?.info && (
                        <Tooltip>
                          <TooltipTrigger asChild>
                            <button
                              className="inline-flex items-center"
                              aria-label="Tooltip: More information about service dog paperwork"
                              aria-describedby="tooltip-service"
                            >
                              <BiSolidInfoCircle className="text-lg text-neutral-700" />
                            </button>
                          </TooltipTrigger>
                          <TooltipContent id="tooltip-service" role="tooltip">
                            {item.info.map((info) => (
                              <div
                                key={info.label}
                                className="flex flex-col gap-1 p-2"
                              >
                                <div className="text-base font-semibold text-neutral-950">
                                  {info.label}
                                </div>
                                <ul className="list-inside list-disc">
                                  {info.items.map((item) => (
                                    <li key={item}>{item}</li>
                                  ))}
                                </ul>
                              </div>
                            ))}
                          </TooltipContent>
                        </Tooltip>
                      )}
                    </Li>
                  </ul>
                );
              })}
            </div>
          );
        })}

        <div className="mt-auto flex flex-col gap-2">
          {form.fields?.button.href && (
            <Button
              className="w-full"
              variant={form.fields.button.type ?? "primary"}
              onClick={() => {
                const link = getLink(form.fields.button.href);
                link
                  ? router.push(link)
                  : setToast({ 
                    message: "Link not found", 
                    status: "error" 
                  });
              }}
              aria-label={form.fields.button.label}
            >
              {form.fields?.button?.label}
            </Button>
          )}
        </div>
      </div>
    </TooltipProvider>
  );
};

const Li = ({
  children,
  icon,
  iconLabel,
}: {
  children: React.ReactNode;
  icon: string;
  iconLabel: string;
}) => {
  console.log(icon);

  return (
    <li className="flex shrink-0 items-center gap-2">
      <span aria-hidden="true">{icon}</span>
      <span className="sr-only">{iconLabel}</span>
      {children}
    </li>
  );
};