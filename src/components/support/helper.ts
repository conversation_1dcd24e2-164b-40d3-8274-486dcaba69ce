"use client"
import { useCreateProjectIssue, useGetRedmineProjectId } from "@/hooks/api/useSupport";
import { useState } from "react";


export const useRedmineAlert = () => {
  const { data: redmineProjectId } = useGetRedmineProjectId();
  const newTicketIssue = useCreateProjectIssue(redmineProjectId?.value);
  const [statusMessage, setStatusMessage] = useState("");

  const reportBug = ({
    subject,
    description,
    projectId,      
  }: {
    subject: string;
    description: string;
    projectId: number;
  }) => {
    newTicketIssue.mutate(
      {
        projectId,
        data: {
          ticketType: 1,
          subject,
          description,
          priority_id: 2,
        },
      },
      {
        onSuccess: () => setStatusMessage("Bug reported successfully."),
        onError: () => setStatusMessage("Error reporting bug."),
      },
    );
  };

  return { reportBug, statusMessage };
};
