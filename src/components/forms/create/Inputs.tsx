import React from "react";
import { DynamicControl } from "../controls/DynamicControl2";

const FormInputs = ({ inputs }: { inputs: any }) => {
  return inputs.map((d: any, i: number) => {
    return (
      <div
        key={i}
        className={`
          p-2 flex text-sm
          ${
            d.inputType === "checkbox"
              ? "flex-row-reverse justify-end items-start"
              : "flex-col justify-start"
          }
          ${d.size === "xxs" && "basis-full md:basis-1/12"}
          ${d.size === "xs" && "basis-full md:basis-2/12"}
          ${d.size === "sm" && "basis-full md:basis-3/12"}
          ${d.size === "md" && "basis-full md:basis-4/12"}
          ${d.size === "lg" && "basis-full md:basis-6/12"}
          ${d.size === "xl" && "basis-full md:basis-8/12"}
          ${d.size === "xxl" && "basis-full md:basis-10/12"}
          ${d.size === "full" && "basis-full"}
        `}
      >
        <label htmlFor={d.fieldName} className="">
          {d.label} {d.required.value && <span className="text-red-500">*</span>}
        </label>
        
    
        
        <DynamicControl inputData={d} />
      </div>
    );
  });
};

export default FormInputs;
