
const FormTitleLink = ({ 
  onClick, 
  label 
}:{
  onClick:()=>void
  label:string
}) => {
  
  return (
    <button 
      onClick={onClick}
      className='
        px-4 py-1 ml-auto flex items-center 
        justify-center shadow-lg rounded bg-indigo-500 hover:bg-indigo-600 text-white
        transform transition-all duration-200 ease-in-out hover:scale-105
      '
    >
      {label}
    </button>
  )
}

export default FormTitleLink