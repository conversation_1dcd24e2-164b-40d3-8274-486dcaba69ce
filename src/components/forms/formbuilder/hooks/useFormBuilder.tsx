import { use<PERSON><PERSON> } from "jotai";
import {
  JSXElementConstructor,
  ReactElement,
  ReactFragment,
  ReactPortal,
} from "react";
import { FieldValues, useFormContext } from "react-hook-form";
import { toast<PERSON>tom } from "@/components/ui/toast/toast";
import { usePathname, useSearchParams, useRouter } from "next/navigation";
import { useBuilder } from "./useBuilder";
import { useMyCart } from "@/hooks/useMyCart";
import { getToken } from "@/utils/keycloakUtils";

type Section = {
  conditionallyDisplay: {
    values: string[];
    fieldName: string;
  }[];
  elements: any[];
  title:
    | string
    | number
    | boolean
    | ReactElement<any, string | JSXElementConstructor<any>>
    | ReactFragment
    | ReactPortal
    | null
    | undefined;
};

// Function to clean Query String
const cleanQueryString = (queryString: string) => {
  // Form: {string}
  // Returns: string

  // Only return the string inside the curly braces
  return queryString.replace("{", "").replace("}", "");
};

export const useFormBuilder = (form?: any) => {
  // Builder
  const {
    delFileList,
    hasNextPage,
    changeCurrentPage,
    getCurrentPage,
    getPages,
    getCurrentStep,
    createFormDataBody,
    createJsonBody,
    getAllInputs,
    processInputs,
    triggerLoading,
    loading: apiLoading,
    isLastPage,
    getProgressBarSteps,
  } = useBuilder(form);

  

  // Cart Management
  const { addToCart } = useMyCart();

  // Get page
  const page = getCurrentStep();

  // Return current page
  const getPage = () => page;

  // Return Api Loading state
  const getApiLoading = () => apiLoading;
  // Return is last page
  const getIsLastPage = () => isLastPage();

  const { getValues, setValue, trigger } = useFormContext();

  const values = getValues();

  // Atoms
  const [, setToast] = useAtom(toastAtom);

  // Next router
  const router = useRouter();
  const pathname = usePathname();

  // Query Params
  const qParams = useSearchParams();

  // Function to check if section has element
  const getSectionHasElement = (predicate: any) =>
    page?.sections?.some(({ elements = [] }) => elements.some(predicate));

  // Function to return an array of files based on what is in the form values
  const getFilesArray = (formValues: any) => {
    const files: File[] = [];

    // Get all the filelists and add each file in the list to the files array
    Object.values(formValues)
      .filter(
        (value): value is FileList =>
          value instanceof FileList && value.length > 0
      )
      .forEach((value: FileList) =>
        Array.from(value).forEach((file) => files.push(file))
      );

    // Get all the files and add each file to the files array
    Object.values(formValues)
      .filter((value): value is File => value instanceof File)
      .forEach((file) => files.push(file));

    return files;
  };

  // Get all files from form
  const filesArray: File[] = getFilesArray(values);

  // Pre-calculate section predicates
  const hasFileDisplay = getSectionHasElement(
    (element: any) => element.type === "fileDisplay"
  );
  const hasSearchBuilder = getSectionHasElement(
    (element: any) => element.type === "searchBuilder"
  );
  const hasFieldDisplay = getSectionHasElement(
    (element: any) => element.type === "fieldDisplay"
  );
  const hasFileOrConditionalSelect = getSectionHasElement(
    (element: any) =>
      element.type === "file" ||
      element.type === "conditionalSelect" ||
      element.type === "fileOptions"
  );

  // Function to return div styles
  const divStyles = () => {
    const baseStyles = "overflow-auto h-full px-6 py-10";

    if (hasFileDisplay) {
      const layoutStyle =
        filesArray.length > 0
          ? "w-full mx-auto px-10 items-center justify-center"
          : "max-w-3xl mx-auto px-10 items-center justify-center";
      return `flex flex-col md:flex-row pt-4 ${layoutStyle}`;
    }

    if (hasFieldDisplay) {
      return `mx-auto container max-w-3xl items-start justify-start pb-10`;
    }

    if (hasFileOrConditionalSelect) {
      return baseStyles;
    }

    if (hasSearchBuilder) {
      return `flex items-center justify-center w-full max-w-3xl mx-auto px-10`;
    }

    return `${baseStyles} container mx-auto max-w-3xl`;
  };

  const sectionHasElementOfType = (
    section: Section,
    elementType: string,
    sections?: Section[]
  ) =>
    sections
      ? sections.some((section: Section) =>
          section.elements?.some(
            (element: { type: string }) => element.type === elementType
          )
        )
      : section.elements?.some(
          (element: { type: string }) => element.type === elementType
        );

  const sectionHasElementWithFieldName = (sections: any[], fieldName: string) =>
    sections?.some((section: { elements: any[] }) =>
      section.elements?.some(
        (element: { fieldName: any }) => element.fieldName === fieldName
      )
    );

  const sectionHasFileDisplay = (sections: any) => {
    let section: any = [];
    return sectionHasElementOfType(section, "fileDisplay", sections);
  };

  const sectionHasFieldDisplay = (sections: any) => {
    let section: any = [];
    return sectionHasElementOfType(section, "fieldDisplay", sections);
  };

  // Function to help hide form sections
  const evaluateDisplayCondition = (
    conditionallyDisplay: any,
    formValues: FieldValues
  ) => {
    for (let condition of conditionallyDisplay) {
      if (condition.values.includes(String(formValues[condition.fieldName]))) {
        return false;
      }
    }

    return true;
  };

  const updateFormValues = async (files: any) => {
    for (const key in files) {
      await setValue(key, files[key]);
    }
  };

  // Function to handle next button click
  const nextStep = async () => {
    const validate = await trigger();

    if (validate) {
      if (page.onPageNext && Array.isArray(page.onPageNext)) {
        triggerLoading(true);

        try {
          const formValues = getValues();
          // Call Del FileList
          const d = delFileList(formValues);
          const data = {
            ...d,
            participantId: qParams.get("entityId"),
            licenseType: qParams.get("entityType"),
          };

          const apiCalls = page.onPageNext.map((apiCallConfig: any) =>
            executeAPICall(apiCallConfig, data)
          );

          await Promise.all(apiCalls);
        } catch (error) {
          setToast({
            status: "error",
            label: "Error",
            message: (error as Error).message,
            position: "top-right",
            duration: 5000,
          });
        } finally {
          triggerLoading(false);
        }
      }

      // Check if there is a next page and only increment currentPage if there is
      if (hasNextPage()) {
        changeCurrentPage("next");
      } else {
        setToast({
          status: "warning",
          label: "Warning",
          message: "This is the last page",
          position: "top-right",
          duration: 5000,
        });
      }
    } else {
      setToast({
        status: "error",
        label: "Error",
        message: "Please fill all required fields",
        position: "top-right",
        duration: 5000,
      });
    }
  };

  // Function to handle back button click
  const previousStep = () => {
    if (getCurrentPage() > 0) {
      const returnTo = qParams.get("returnTo");
      const entityId = qParams.get("entityId");

      const pages = getPages();

      // If the new current page has a pending endpoint, then we need to remove the licenseId from the url
      if (
        pages[getCurrentPage() - 1].onPageNext?.some((apiCallConfig: any) =>
          apiCallConfig.api.includes("pending")
        )
      ) {
        const newUrl = `${pathname}?entityId=${entityId}&returnTo=${returnTo}`;

        router.push(newUrl);
      }

      changeCurrentPage("prev");
    } else {
      router.back();
    }
  };

  // Function to handle Form Submit
  const onSubmit = async (data: any) => {
    const pages = getPages();
    if (getCurrentPage() !== pages.length - 1) return;

    triggerLoading(true);

    try {
      const apiCallPromises = getCurrentStep().onFormSubmit.map(
        (apiCallConfig: any) => executeAPICall(apiCallConfig, delFileList(data))
      );
      await Promise.all(apiCallPromises);
    } catch (error) {
      setToast({
        status: "error",
        label: "Error",
        message: (error as Error).message,
        position: "top-right",
        duration: 5000,
      });
      console.log("Error submitting form", error);
    } finally {
      triggerLoading(false);
    }
  };

  const formatPhoneNumber = (value: any) => {
    if (!value) return value;

    const phoneNumber = value.replace(/[^\d]/g, "");
    const phoneNumberLength = phoneNumber.length;

    if (phoneNumberLength < 4) return phoneNumber;

    if (phoneNumberLength < 7) {
      return `(${phoneNumber.slice(0, 3)}) ${phoneNumber.slice(3)}`;
    }

    return `(${phoneNumber.slice(0, 3)}) ${phoneNumber.slice(
      3,
      6
    )}-${phoneNumber.slice(6, 10)}`;
  };

  // Function to execute API call
  const executeAPICall = async (
    apiCallConfig: {
      body: { fields: any[]; sendAllFormDataField: any; type: string };
      api: string | string[];
      requestSlug: any[];
      queryString: any[];
      method: any;
      response: { success: any; error: any };
    },
    formValues: { [x: string]: string }
  ) => {
    let response;
    try {
      const purgedFileList = delFileList(formValues);

      const fields = apiCallConfig.body?.fields?.map(
        (field: { key: any }) => field.key
      );

      // build the base URL
      let url = process.env.NEXT_PUBLIC_API_BACKEND_HOST! + apiCallConfig.api;

      // Handle requestSlug if any
      if (apiCallConfig.requestSlug?.length > 0) {
        apiCallConfig.requestSlug.forEach(
          (slug: { valueLocation: string; key: string; valueName: string }) => {
            // Get the value of valueName from page url
            let value = "";
            if (slug.valueLocation === "queryString") {
              value = qParams.get(slug.valueName) ?? "";
              console.log("Slug - QuerySTR", value);
            } else if (slug.valueLocation === "form") {
              value = formValues[slug.valueName] ?? "";
            }

            url = url.replace(`${slug.key}`, value);
            console.log("Slug - URL", url);
          }
        );
      }

      // Handle query string if any
      if (apiCallConfig.queryString?.length > 0) {
        const params = new URLSearchParams();
        apiCallConfig.queryString.forEach(
          (query: {
            key: string;
            valueLocation: string;
            valueName: string;
          }) => {
            // let value = qParams.get(query.valueName) ?? "";
            // console.log("QuerySTR - VALUE", value);
            // if (query.valueLocation === "queryString") {
            //   params.append(query.key, value);
            // } else if (query.valueLocation === "form") {
            //   let formDataValue = formValues[query.valueName] ?? "";
            //   params.append(query.key, formDataValue);
            // }
            // console.log("QuerySTR - PARAMS", params.toString());
            // // Based on if there is already a query string or not, append the query string if there is, use ? else use &
            // url = url + (url.includes("?") ? "&" : "?") + params.toString();
            // console.log("QuerySTR - URL", url);

            let value;
            if (query.valueLocation === "queryString") {
              value = qParams.get(query.valueName) ?? "";
            } else if (query.valueLocation === "form") {
              value = formValues[query.valueName] ?? "";
            }

            if (value) {
              params.append(cleanQueryString(query.key), value);
            }
          }
        );
        console.log("QuerySTR - PARAMS", params.toString());
        // Only append the complete query string once, after the loop
        url = url + (url.includes("?") ? "&" : "?") + params.toString();
        console.log("QuerySTR - URL", url);
      }
      console.log("URL", url);
      // Get only the value of all fields in the form from formValues into an array
      const sendAllValues = Object.values(formValues);

      let bodyData;
      if (apiCallConfig.body) {
        if (apiCallConfig?.body?.sendAllFormDataField) {
          if (apiCallConfig.body.type.includes("ignore")) {
            bodyData =
              apiCallConfig.body.type === "json"
                ? createJsonBody(sendAllValues, purgedFileList)
                : createFormDataBody(formValues, "send All", undefined, true);

            console.log("bodyData", sendAllValues);
          } else
            bodyData =
              apiCallConfig.body.type === "json"
                ? createJsonBody(sendAllValues, purgedFileList)
                : createFormDataBody(formValues, "send All");

          console.log("bodyData", sendAllValues);
        }

        if (fields?.length > 0) {
          const dataToSend: { [key: string]: string } = {};
          fields.forEach((field: string | number) => {
            const fieldStr = String(field);
            dataToSend[fieldStr] = formValues[fieldStr];
          });

          bodyData =
            apiCallConfig.body.type === "json"
              ? createJsonBody(fields, dataToSend)
              : createFormDataBody(dataToSend, "fields", fields);
        }
      }

      const token = getToken();

      response = await fetch(url, {
        method: apiCallConfig.method,
        body: bodyData,
        headers: {
          ...(apiCallConfig.body?.type === "json"
            ? { "Content-Type": "application/json" }
            : {}),
          Authorization: `Bearer ${token}`,
        },
      });

      console.log("Response-1", response);

      if (!response.ok) {
        console.log("Response " + response);
      }

      let data: any;

      // Check if response is not empty
      if (response.status !== 204) {
        data = await response.json();
        console.log("Response-2", data);
      }

      console.log("data", data);

      const { success, error } = apiCallConfig.response;

      if (data || response.status === success.code) {
        console.log("in here");
        if (success.navigate) {
          const entityId = data?.entityId ?? qParams.get("entityId");
          let entityType = data?.entityType ?? qParams.get("entityType");

          if (entityType.toLowerCase().includes("license")) {
            entityType = "license";
          }

          const navigationMap: { [key: string]: string } = {
            cart: `/cart?entityId=${entityId ?? ""}&entityType=${
              entityType ?? ""
            }`,
            individualProfile: `/entity/individual/${entityId}?tab=profile`,
            licenseProfile: `/entity/${entityType}/${entityId}?tab=profile`,
            profile: `/entity/${entityType}/${entityId}?tab=profile`,
          };

          // Split the success.navigate string into an array
          const actions = success.navigate.split(",");

          // Execute each action
          actions.forEach((action: any) => {
            const pushURL =
              navigationMap[action] ??
              "/dashboard?error=Invalid Navigation after completion of form";

            if (action === "addToCart") {
              // console.log("Entity ID", entityId);

              addToCart(entityId, "license");
            } else {
              router.push(pushURL);
            }
          });
        }

        if (success.setAllToForm) {
          // Get all inputs from form
          const formInputs = getAllInputs(getPages());
          console.log(formInputs);
          // Process inputs
          const extractedFileValues = processInputs(formInputs, data);

          // Edit the phone number field in extractedFileValues
          if (extractedFileValues.phone) {
            extractedFileValues.phone = formatPhoneNumber(
              extractedFileValues.phone
            );
          }

          updateFormValues(extractedFileValues);
        }

        if (success.fields) {
          success.fields.forEach((field: any) => {
            if (field.valueLocation === "form-data") {
              setValue(field.key, data[field.valueName]);
            }
          });
        }
      }

      if (apiCallConfig.api.includes("pending")) {
        if (data) {
          const returnTo = qParams.get("returnTo");
          const entityId = qParams.get("entityId");
          const entityType = qParams.get("entityType");

          const newUrl = `${pathname}?entityId=${entityId}&entityType=${entityType}&licenseId=${data.entityId}&returnTo=${returnTo}`;

          router.push(newUrl);
        }
      }

      if (error.code.includes(response.status)) {
        console.log(error.message);
      }
    } catch (error) {
      setToast({
        status: "error",
        label: "Error",
        message: (error as Error).message,
        position: "top-right",
        duration: 5000,
      });
      console.log("Error trying to form", error);
    }
    return response;
  };

  // Find a conditional checkbox element with a "trigger" property
  const conditionalCheckbox = page?.sections?.flatMap((section: any) =>
    section.elements?.filter(
      (element: any) =>
        element.type === "conditionalCheckbox" && element.trigger
    )
  )[0];

  // Function to trigger values for conditional checkboxes
  const triggerConditionalCheckbox = (fieldName: string) => {
    const fieldValue = getValues(fieldName);

    if (!fieldValue) return false;

    return fieldValue.trim() !== "";
  };

  return {
    values,
    divStyles,
    filesArray,
    sectionHasFileDisplay,
    sectionHasElementOfType,
    evaluateDisplayCondition,
    nextStep,
    previousStep,
    onSubmit,
    getPage,
    getApiLoading,
    getIsLastPage,
    getCurrentPage,
    getCurrentStep,
    getProgressBarSteps,
    changeCurrentPage,
    sectionHasElementWithFieldName,
    sectionHasFieldDisplay,
    triggerConditionalCheckbox,
    conditionalCheckbox,
  };
};
