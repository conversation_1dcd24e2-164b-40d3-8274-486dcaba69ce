import { useQuery, useMutation } from "@tanstack/react-query";
import { requests } from "@/utils/agent";
import { useCallback } from "react";
import { usePathname, useRouter, useSearchParams } from "next/navigation";

const useExecuteApiCall = (
  apiCallConfig: {
    body: { fields: any[]; sendAllFormDataField: any; type: string };
    api: string | string[];
    requestSlug: any[];
    queryString: any[];
    method: any;
    response: { success: any; error: any };
  },
  formValues: { [x: string]: string }
) => {
  // Next router
  const router = useRouter();
  const pathname = usePathname();

  // Query Params
  const qParams = useSearchParams();

  // Function To remove file from File list and set it to the same key
  const delFileList = useCallback((data: any) => {
    const newData = { ...data };
    for (const key in newData) {
      if (newData[key] instanceof FileList) {
        newData[key] = newData[key][0];
      }
    }
    return newData;
  }, []);

  // Function to create JSON body for API call
  const createJsonBody = (fields: any, d: any) => {
    const fieldValues = fields.map((field: any) => d[field]);

    const json = Object.fromEntries(
      fields.map((_: any, i: string | number) => [fields[i], fieldValues[i]])
    );

    return JSON.stringify(json);
  };

  // Function to create form data body for API call
  const createFormDataBody = (
    d: any,
    type: "send All" | "fields",
    fields?: any
  ) => {
    let fd = new FormData();

    switch (type) {
      case "send All":
        for (const key in d) {
          if (d[key] !== undefined) {
            if (d[key] instanceof File) {
              fd.append(key, d[key]);
            } else {
              fd.append(key, String(d[key]));
            }
          }
        }
        break;

      case "fields":
        fields.forEach((field: any) => {
          if (d.hasOwnProperty(field)) {
            fd.append(field, d[field] as string);
          }
        });
        break;

      default:
        break;
    }

    return fd;
  };

  // Build body data
  const mutation = useMutation(async () => {
    const { post, put, patch, get, del } = requests;

    // Purge File List
    const purgedFileList = delFileList(formValues);

    // Get all the fields from the body
    const fields = apiCallConfig.body?.fields?.map(
      (field: { key: any }) => field.key
    );

    // Build Base URL
    let url = process.env.NEXT_PUBLIC_API_BACKEND_HOST! + apiCallConfig.api;

    // Handle requestSlug if any
    // if (apiCallConfig.requestSlug) {
    //   apiCallConfig.requestSlug.forEach((slug: { valueName: string }) => {
    //     // Get the value of valueName from page url
    //     const value = qParams.get(slug.valueName);

    //     // url = url + "/" + value;

    //     // Append the value to the url
    //     url = url.replace(`{${slug.valueName}}`, value ?? "");
    //   });
    // }

    // Handle requestSlug if any
    if (apiCallConfig.requestSlug?.length > 0) {
      apiCallConfig.requestSlug.forEach(
        (slug: { valueLocation: string; key: string; valueName: string }) => {
          // Get the value of valueName from page url
          let value = "";
          if (slug.valueLocation === "queryString") {
            value = qParams.get(slug.valueName) ?? "";
          } else if (slug.valueLocation === "form") {
            value = formValues[slug.valueName] ?? "";
          }

          url = url.replace(`${slug.key}`, value);
        }
      );
    }

    // Handle query string if any
    if (apiCallConfig.queryString?.length > 0) {
      const params = new URLSearchParams();
      apiCallConfig.queryString.forEach(
        (query: { key: string; valueLocation: string; valueName: string }) => {
          // Get the value of valueName from page url
          // const value = qParams.get(query.key);

          // Append the value to the query string
          // params.append(query.key, value ?? "");

          // Get the value of valueName from page url
          let value = params.get(query.valueName) ?? "";
          if (query.valueLocation === "queryString") {
            params.append(query.key, value);
          } else if (query.valueLocation === "form") {
            let formDataValue = formValues[query.valueName] ?? "";
            params.append(query.key, formDataValue);
          }

          // Based on if there is already a query string or not, append the query string if there is, use ? else use &
          url = url + (url.includes("?") ? "&" : "?") + params.toString();
        }
      );
    }
    console.log("URL", url);

    // Get only the value of all fields in the form from formValues into an array
    const sendAllValues = Object.values(formValues);

    let bodyData;
    if (apiCallConfig.body) {
      if (apiCallConfig?.body?.sendAllFormDataField) {
        bodyData =
          apiCallConfig.body.type === "json"
            ? createJsonBody(sendAllValues, purgedFileList)
            : createFormDataBody(formValues, "send All");
      }

      if (fields?.length > 0) {
        const dataToSend: { [key: string]: string } = {};
        fields.forEach((field: string | number) => {
          const fieldStr = String(field);
          dataToSend[fieldStr] = formValues[fieldStr];
        });

        bodyData =
          apiCallConfig.body.type === "json"
            ? createJsonBody(fields, dataToSend)
            : createFormDataBody(dataToSend, "fields", fields);
      }
    }

    let response;
    switch (apiCallConfig.method) {
      case "POST":
        response = await post(url, bodyData);
        break;
      case "PUT":
        response = await put(url, bodyData);
        break;
      case "PATCH":
        response = await patch(url, bodyData);
        break;
      default:
        throw new Error("Invalid method");
    }

    // Success and error handling

    return response;
  });

  return mutation;
};
