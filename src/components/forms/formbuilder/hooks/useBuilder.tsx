import { useCallback, useMemo, useState } from "react";

type Direction = "next" | "prev";

type FormInput = {
  fieldName: string;
  type: string;
  label: string;
};

type ExtractedFileValue = {
  [key: string]: string | boolean | number | null;
};

type FieldInfo = {
  fieldName: string;
  type: string;
  label: string;
};

type DataType = {
  [key: string]: any;
};

export const useBuilder = (form?: any) => {
  const [pages] = useState<any[]>(form?.pages ?? []); // Pages
  const [currentPage, setCurrentPage] = useState<number>(0); // Current Page Index
  const [loading, setLoading] = useState<boolean>(false); // Loading

  const currentStep = useMemo(() => pages[currentPage], [currentPage, pages]); // Current Page Object
  const progressBarSteps = useMemo(
    () =>
      pages.map((page: any, index: number) => ({
        title: page.title,
        step: index + 1,
      })),
    [pages]
  ); // Progress Bar Steps

  // Change the current page
  const changeCurrentPage = useCallback(
    (direction: Direction) => {
      setCurrentPage((prevPage) =>
        direction === "next" && prevPage < pages.length - 1
          ? prevPage + 1
          : direction === "prev" && prevPage > 0
          ? prevPage - 1
          : prevPage
      );
    },
    [pages.length]
  );

  const getCurrentPage = useCallback(() => currentPage, [currentPage]); // Getters for variables
  const getCurrentStep = useCallback(() => currentStep, [currentStep]); // Getters for variables
  const getProgressBarSteps = useCallback(
    () => progressBarSteps,
    [progressBarSteps]
  ); // Getters for variables
  const getPages = useCallback(() => pages, [pages]); // Getters for variables

  // Check if current page is the last page
  const isLastPage = useCallback(
    () => currentPage === pages.length - 1,
    [currentPage, pages.length]
  );

  // Check if there is a next page
  const hasNextPage = useCallback(
    () => currentPage < pages.length - 1,
    [currentPage, pages.length]
  );

  // Function To remove file from File list and set it to the same key
  const delFileList = useCallback((data: any) => {
    const newData = { ...data };
    for (const key in newData) {
      if (newData[key] instanceof FileList) {
        newData[key] = newData[key][0];
      }
    }
    return newData;
  }, []);

  // Function to extract values from api response
  const processInputs = (
    formInputs: FormInput[],
    extractedFileValues: ExtractedFileValue
  ) => {
    let updatedFormInputs: ExtractedFileValue = { ...extractedFileValues }; // create a shallow copy

    formInputs.forEach((item) => {
      if (
        item.type === "select" &&
        updatedFormInputs.hasOwnProperty(item.fieldName)
      ) {
        let value = extractedFileValues[item.fieldName];
        if (typeof value === "string") {
          updatedFormInputs[item.fieldName] = value.toLowerCase();
        }

        // If the value is for a select input that is a state in an address, don't convert to lowercase
        if (item.fieldName.includes("state")) {
          updatedFormInputs[item.fieldName] = value;
        }
      }
    });

    return updatedFormInputs;
  };

  // Function to extract form inputs from form
  const getAllInputs = (
    data: DataType,
    fields: FieldInfo[] = []
  ): FieldInfo[] => {
    for (let key in data) {
      if (key === "fieldName" && "type" in data) {
        fields.push({
          fieldName: data.fieldName,
          type: data.type,
          label: data.label,
        });
      } else if (typeof data[key] === "object" && data[key] !== null) {
        getAllInputs(data[key], fields);
      }
    }
    return fields;
  };

  // Function to create JSON body for API call
  const createJsonBody = (fields: any, d: any) => {
    const fieldValues = fields.map((field: any) => d[field]);

    const json = Object.fromEntries(
      fields.map((_: any, i: string | number) => [fields[i], fieldValues[i]])
    );

    return JSON.stringify(json);
  };

  // Function to create form data body for API call
  // const createFormDataBody = (
  //   d: any,
  //   type: "send All" | "fields",
  //   fields?: any,
  //   ignoreNull?: boolean
  // ) => {
  //   let fd = new FormData();

  //   switch (type) {
  //     case "send All":
  //       for (const key in d) {
  //         if (d[key] !== undefined) {
  //           if (d[key] instanceof File) {
  //             fd.append(key, d[key]);
  //           } else {
  //             fd.append(key, String(d[key]));
  //           }
  //         }
  //       }
  //       break;

  //     case "fields":
  //       fields.forEach((field: any) => {
  //         if (d.hasOwnProperty(field)) {
  //           fd.append(field, d[field] as string);
  //         }
  //       });
  //       break;

  //     default:
  //       break;
  //   }

  //   return fd;
  // };

  const createFormDataBody = (
    d: any,
    type: "send All" | "fields",
    fields?: any,
    ignoreNull: boolean = false // Default value set to false
  ) => {
    let fd = new FormData();

    switch (type) {
      case "send All":
        for (const key in d) {
          if (
            d[key] !== undefined &&
            !(ignoreNull && d[key] === null) &&
            d[key] !== ""
          ) {
            if (d[key] instanceof File) {
              fd.append(key, d[key]);
            } else {
              fd.append(key, String(d[key]));
            }
          }
        }
        break;

      case "fields":
        fields.forEach((field: any) => {
          if (
            d.hasOwnProperty(field) &&
            !(ignoreNull && d[field] === null) &&
            d[field] !== ""
          ) {
            fd.append(field, d[field] as string);
          }
        });
        break;

      default:
        break;
    }

    return fd;
  };

  // Function to trigger loading spinner
  const triggerLoading = (set: boolean) => {
    setLoading(set);
  };

  return {
    form,
    pages,
    getCurrentPage,
    setCurrentPage,
    getCurrentStep,
    getProgressBarSteps,
    changeCurrentPage,
    loading,
    setLoading,
    delFileList,
    processInputs,
    getAllInputs,
    createJsonBody,
    createFormDataBody,
    isLastPage,
    hasNextPage,
    getPages,
    triggerLoading,
  };
};
