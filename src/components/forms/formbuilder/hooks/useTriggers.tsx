import { useSearchParams } from "next/navigation";
import { useFormContext } from "react-hook-form";

export const useTriggers = (elements: any[]) => {
  const { getValues } = useFormContext();
  const urlParams = useSearchParams();

  // Get elements with triggers
  const isArrayWithElements = (arr: any[]) => {
    return Array.isArray(arr) && arr.length > 0;
  };

  const triggerElements = elements.filter((element: any) => {
    if (isArrayWithElements(element.triggers)) {
      return element.triggers;
    }
  });

  const yearsFromToday = (givenYear: any): number => {
    const currentYear = new Date().getFullYear();
    const yearDifference = givenYear - currentYear;

    if (yearDifference > 3) {
      return -1;
    } else if (yearDifference > 0 && yearDifference <= 3) {
      return yearDifference;
    } else {
      return -1;
    }
  };

  const decisionMap = (val1: any, val2: any, condition: string) => {
    switch (condition) {
      case "equals":
        return val1 === val2;
      case "notEquals":
        return val1 !== val2;
      case "greaterThan":
        return val1 > val2;
      case "lessThan":
        return val1 < val2;
      case "greaterThanOrEqual":
        return val1 >= val2;
      case "lessThanOrEqual":
        return val1 <= val2;
      default:
        return false;
    }
  };

  // Checkbox Triggers
  // Radio Triggers
  // Select Triggers
  // Input Triggers
  // Date Triggers
  const dateTriggers = (element: any) => {
    const decisionArray: any[] = [];

    element.triggers.forEach((trigger: any) => {
      const { fieldName, condition, compareTo } = trigger;

      // Get Value of field name
      const comparisonValue = urlParams.get(compareTo) ?? getValues(compareTo);
      const fieldValue = getValues(fieldName);

      // Cast to date and then get full year
      const date = new Date(fieldValue);
      const year = date.getFullYear();
      const newDuration = yearsFromToday(year);

      // Compare the years
      const decisionCalculation = decisionMap(
        newDuration,
        comparisonValue,
        condition
      );

      const decision = {
        fieldName: fieldName,
        condition: condition,
        compareTo: compareTo,
        decision: decisionCalculation,
      };

      // add to decision array
      decisionArray.push(decision);
    });

    return decisionArray;
  };

  return {
    dateTriggers,
    triggerElements,
  };
};
