import { useEffect, useState } from "react";
import { pdfjs, Document, Page } from "react-pdf";
import "react-pdf/dist/esm/Page/AnnotationLayer.css";
import "react-pdf/dist/esm/Page/TextLayer.css";
import Image from "next/image";
import { FiArrowLeft, FiArrowRight } from "react-icons/fi";

const options = {
  cMapUrl: "cmaps/",
  standardFontDataUrl: "standard_fonts/",
};

pdfjs.GlobalWorkerOptions.workerSrc = `//cdnjs.cloudflare.com/ajax/libs/pdf.js/${pdfjs.version}/pdf.worker.min.js`;

type FileState = {
  data: string | ArrayBuffer | null;
  type: "pdf" | "image" | string;
};

type FileDisplayProps = {
  files: File[];
};

type ConvertedFile = {
  data: string | ArrayBuffer | null;
  type: "pdf" | "image" | string;
};

const FileDisplay = ({ files }: FileDisplayProps) => {
  // console.log(files);

  // Local States
  const [numPages, setNumPages] = useState<number | null>(null);
  const [currentFileIndex, setCurrentFileIndex] = useState(0);
  const [currentPage, setCurrentPage] = useState<any>(1);
  const [fileState, setFileState] = useState<FileState[] | null>(null);

  // Convert File to Base64
  const fileToBase64 = (file: File): Promise<string> =>
    new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.readAsDataURL(file);
      reader.onload = () => resolve(reader.result as string);
      reader.onerror = (error) => reject(error);
    });

  // Convert Base64 to URL
  const base64ToBlobUrl = (base64: string, type: string) => {
    const binaryString = window.atob(base64.split(",")[1]);
    const len = binaryString.length;
    const bytes = new Uint8Array(len);
    for (let i = 0; i < len; i++) {
      bytes[i] = binaryString.charCodeAt(i);
    }
    const blob = new Blob([bytes], { type: type });
    const url = URL.createObjectURL(blob);
    return url;
  };

  // On mount, convert files to base64
  useEffect(() => {
    const convertFilesToBase64 = async () => {
      const convertedFiles: ConvertedFile[] = await Promise.all(
        files.map(async (file) => {
          const data: string = await fileToBase64(file); // The type of data is now string
          const type = file.type.includes("pdf") ? "pdf" : "image";
          return { data, type };
        })
      );
      setFileState(convertedFiles);
    };

    if (files) convertFilesToBase64();
  }, [files]);

  // On mount, set the number of pages
  function onDocumentLoadSuccess({ numPages: nextNumPages }: any) {
    setNumPages(nextNumPages);
    if (currentPage === null) {
      setCurrentPage(nextNumPages);
    } else {
      setCurrentPage(1);
    }
  }

  // Change page
  const changePage = (offset: number) => {
    const nextPageNumber = (currentPage || 1) + offset;
    if (nextPageNumber < 1) {
      if (currentFileIndex > 0) {
        setCurrentFileIndex(currentFileIndex - 1);
        setCurrentPage(null);
      }
    } else if (nextPageNumber > numPages!) {
      if (currentFileIndex < files.length - 1) {
        setCurrentFileIndex(currentFileIndex + 1);
        setCurrentPage(1);
      }
    } else {
      setCurrentPage(nextPageNumber);
    }
  };

  // Next and previous page
  const nextPage = () => changePage(1);
  const previousPage = () => changePage(-1);

  return (
    <div className="flex flex-col items-center justify-between p-4 max-h-full ">
      <div
        className="
        h-full
        flex flex-col
        justify-center
        items-center
         mb-4
         border-4 border-gray-200 border-dashed p-4 mx-4
      "
      >
        {fileState &&
          fileState[currentFileIndex] && // Display PDF if file type is pdf
          fileState[currentFileIndex].type === "pdf" && (
            <Document
              file={fileState[currentFileIndex].data}
              onLoadSuccess={onDocumentLoadSuccess}
              options={options}
            >
              <Page pageNumber={currentPage} />
            </Document>
          )}
        {fileState &&
          fileState[currentFileIndex] && // Display Image if file type is image
          fileState[currentFileIndex].type === "image" && (
            <Image
              src={fileState[currentFileIndex].data as string}
              width={500}
              height={500}
              className="object-contain"
              alt="Image"
            />
          )}
      </div>
      {fileState && fileState[currentFileIndex] && (
        <div className={`flex flex-col justify-center items-center`}>
          <div className={`flex flex-row justify-center items-center`}>
            <div className="right-0 top-1/2">
              <button
                type="button"
                disabled={currentPage <= 1 && currentFileIndex === 0}
                onClick={previousPage}
                className="bg-gray-200 p-2 rounded-l-lg"
              >
                <FiArrowLeft />
              </button>
            </div>
            <div className="right-0 top-1/2">
              <button
                type="button"
                disabled={
                  currentPage >= numPages! &&
                  currentFileIndex === files.length - 1
                }
                onClick={nextPage}
                className="bg-gray-200 p-2 rounded-r-lg"
              >
                <FiArrowRight />
              </button>
            </div>
          </div>
          <div className="text-center mt-4 text-sm text-gray-500 hover:text-gray-700 cursor-pointer bg-gray-200 p-2 rounded-lg">
            <a
              href={base64ToBlobUrl(
                fileState![currentFileIndex].data as string,
                fileState![currentFileIndex].type === "pdf"
                  ? "application/pdf"
                  : "image/jpeg"
              )}
              target="_blank"
            >
              Open File
            </a>
          </div>
        </div>
      )}
    </div>
  );
};

export default FileDisplay;
