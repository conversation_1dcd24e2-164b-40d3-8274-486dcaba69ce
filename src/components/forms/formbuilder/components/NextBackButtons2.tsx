"use client";

import { useState } from "react";
import Link from "next/link";
import { useAtom } from "jotai";
import { stepsAtom } from "@/atoms/CreateFormAtoms";
import ProgressBar from "./ProgressBar";

interface NextBackButtonsProps {
  backLink?: string;
  backText?: string;
  nextText?: string;
  step: number;
  loading?: boolean;
  progressBar?: boolean;
}

const NextBackButtons = ({
  backLink,
  backText,
  nextText,
  step,
  loading = false,
  progressBar = false,
}: NextBackButtonsProps) => {
  const [steps] = useAtom(stepsAtom);
  const [progress] = useState(progressBar);

  return (
    <>
      {/* { progress && <ProgressBar steps={steps}/> } */}

      <div
        className={`
      w-full flex items-center py-4 px-6 justify-end
    `}
      >
        {step > 1 && (
          <Link href={backLink ?? ""} className="mr-auto">
            <button
              type="button"
              className="px-2 py-1 rounded bg-neutral-500 hover:bg-neutral-600 text-neutral-50 w-20"
            >
              {backText ?? "Back"}
            </button>
          </Link>
        )}
        <button
          disabled={loading}
          type="submit"
          className="px-2 py-1 rounded bg-blue-500 hover:bg-blue-600 text-blue-50 w-20"
        >
          {loading ? "Loading..." : nextText ?? "Next"}
        </button>
      </div>
    </>
  );
};

export default NextBackButtons;
