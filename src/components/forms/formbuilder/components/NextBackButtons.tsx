"use client";

import { useState } from "react";
import Link from "next/link";
import { useAtom } from "jotai";
import { stepsAtom } from "@/atoms/CreateFormAtoms";
import ProgressBar from "./ProgressBar";
import { useFormContext } from "react-hook-form";

interface NextBackButtonsProps {
  backButton?: (() => void) | null;
  nextButton?: () => void;
  backText?: string;
  nextText?: string;
  loading?: boolean;
  progressBar?: boolean;
  submit?: boolean;
  steps?: any;
  currentStep: number;
}

const NextBackButtons = ({
  nextButton,
  backText,
  nextText,
  steps,
  currentStep,
  submit = false,
  backButton = null,
  loading = false,
  progressBar = false,
}: NextBackButtonsProps) => {
  // const [steps] = useAtom(stepsAtom);
  const [progress] = useState(progressBar);

  const { trigger } = useFormContext();

  return (
    <>
      {progress && <ProgressBar currentStep={currentStep + 1} steps={steps} />}

      <div
        className={`
      w-full flex items-center py-4 px-6 justify-end bg-white
    `}
      >
        {backButton && (
          <button
            type="button"
            onClick={backButton}
            className="px-2 py-1 rounded bg-neutral-500 hover:bg-neutral-600 text-neutral-50 w-20"
          >
            {backText ?? "Back"}
          </button>
        )}
        {submit ? (
          <button
            disabled={loading}
            type="submit"
            className="px-2 py-1 rounded bg-blue-500 hover:bg-blue-600 text-blue-50 w-20 ml-auto"
          >
            {loading ? "Loading..." : nextText ?? "Submit"}
          </button>
        ) : (
          <button
            disabled={loading}
            type="button"
            onClick={nextButton}
            className="px-2 py-1 rounded bg-blue-500 hover:bg-blue-600 text-blue-50 w-20 ml-auto"
          >
            {loading ? "Loading..." : nextText ?? "Next"}
          </button>
        )}
      </div>
    </>
  );
};

export default NextBackButtons;
