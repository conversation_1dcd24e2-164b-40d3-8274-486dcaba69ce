export const FormInputElementStyles = (element: any) => {
  const main = ` p-2 flex text-sm shrink-0 w-full ${
    element.type === "checkbox"
      ? "flex-row-reverse justify-end items-start"
      : "flex-col justify-start"
  } ${element.size === "xxs" && "basis-full md:basis-1/12"} ${
    element.size === "xs" && "basis-full md:basis-2/12"
  } ${element.size === "sm" && "basis-full md:basis-3/12"} ${
    element.size === "md" && "basis-full md:basis-4/12"
  } ${element.size === "lg" && "basis-full md:basis-6/12"} ${
    element.size === "xl" && "basis-full md:basis-8/12"
  } ${element.size === "xxl" && "basis-full md:basis-10/12"} ${
    element.size === "full" && "basis-full"
  } `;

  const conditionalDisplay = `flex items-start ${
    element.size === "xxs" && "justify-end md:justify-start"
  } ${element.size === "xs" && "justify-end md:justify-start"} ${
    element.size === "sm" && "justify-end md:justify-start"
  } ${element.size === "md" && "justify-end md:justify-start"} ${
    element.size === "lg" && "justify-end md:justify-start"
  } ${element.size === "xl" && "justify-end md:justify-start"} ${
    element.size === "xxl" && "justify-end md:justify-start"
  } ${element.size === "full" && "justify-end md:justify-start"} `;

  return {
    main,
    conditionalDisplay,
  };
};
