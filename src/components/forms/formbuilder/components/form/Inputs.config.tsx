import { DynamicControl } from "@/components/forms/controls/DynamicControl";
import FileInput from "../fileInput/FileInput";
import React, { useState, useEffect, useMemo } from "react";
import { ControlType } from "@/components/forms/controls/dynamicControlTypes";
import { useFormContext } from "react-hook-form";
import { FormInputElementStyles } from "../styles/FormInput";
import MultipleFileInput from "../fileInput/MultipleFileInput";
import { useSearchParams } from "next/navigation";
import { useTriggers } from "../../hooks/useTriggers";
import { dogOwnerTransferSearchForm } from "@/lib/configuration/profile-events/dogOwnerTransferSearchForm";
import { ErrorMessage } from "@hookform/error-message";

const stringToBoolean = (string: string) => {
  switch (string.toLowerCase().trim()) {
    case "true":
      return true;
    case "false":
      return false;
    default:
      return false;
  }
};

type FormElementsProps = {
  section: any;
  optionalPage: boolean | undefined;
  defaultOption: {
    label: string;
    value: string;
  };
  fileType: string | null;
  setFileType: React.Dispatch<React.SetStateAction<string | null>>;
  watchedValues: {
    [key: string]: string | boolean | number | null | undefined;
  };
  inputConstraints: ControlType[];
  evaluateDisplayCondition: (
    condition: any,
    values: { [key: string]: any }
  ) => boolean;
};

const SectionTitle = ({
  section,
  sectionHasElementOfType,
}: {
  section: any;
  sectionHasElementOfType: (section: any, type: string) => boolean;
}) => (
  <h2
    className={`text-xl font-bold mb-2 mt-10 ${
      sectionHasElementOfType(section, "fieldDisplay")
        ? "text-left mx-auto max-w-md text-neutral-600"
        : "text-center"
    }`}
  >
    {section.title}
  </h2>
);

const camel2title = (camelCase: string) =>
  camelCase
    .replace(/([A-Z])/g, (match) => ` ${match}`)
    .replace(/^./, (match) => match.toUpperCase())
    .trim();

const FormElements = ({
  section,
  optionalPage,
  defaultOption,
  fileType,
  setFileType,
  watchedValues,
  inputConstraints,
  evaluateDisplayCondition,
}: FormElementsProps) => {
  const {
    register,
    getValues,
    setValue,
    watch,
    formState: { errors },
  } = useFormContext();

  const urlParams = useSearchParams();
  const maxDuration = urlParams.get("maxDuration");

  // Function to trigger values for conditional checkboxes
  const triggerConditionalCheckbox = (
    conditionalCheckboxFieldName: string,
    conditionalCheckboxTriggerFieldName: string
  ) => {
    if (!conditionalCheckboxFieldName || !conditionalCheckboxTriggerFieldName)
      return null;
    const fieldValue = getValues(conditionalCheckboxTriggerFieldName);
    let val = fieldValue ? true : false;

    return {
      conditionalCheckboxFieldName: conditionalCheckboxFieldName,
      conditionalCheckboxTriggerFieldName: conditionalCheckboxTriggerFieldName,
      value: val,
    };
  };

  const conditionalCheckbox = section.elements?.find(
    (element: any) =>
      element.type === "conditionalCheckbox" && element.triggers?.length > 0
  );
  const triggers = conditionalCheckbox?.triggers;
  const triggerFieldNames = triggers?.map((trigger: any) => trigger.fieldName);

  const newTriggerValues = triggerFieldNames?.map((fieldName: string) =>
    triggerConditionalCheckbox(conditionalCheckbox.fieldName, fieldName)
  );

  const conditionalSelect = section.elements?.find(
    (element: any) =>
      element.type === "conditionalSelect" && element.triggers?.length > 0
  );

  const selectTriggers = conditionalSelect?.triggers;
  const selectTriggerFieldNames = selectTriggers?.map(
    (trigger: any) => trigger.fieldName
  );

  const [isInitialRender, setIsInitialRender] = useState(true);

  const { dateTriggers, triggerElements } = useTriggers(section?.elements);

  // Set in form

  const yearsFromToday = (givenYear: any): number => {
    const currentYear = new Date().getFullYear();
    const yearDifference = givenYear - currentYear;

    if (yearDifference > 3) {
      return -1;
    } else if (yearDifference > 0 && yearDifference <= 3) {
      return yearDifference;
    } else {
      return -1;
    }
  };

  const shouldDisableOption = (maxDuration: any, conditionallyDisplay: any) => {
    // Get year from maxDuration
    const mDuration = new Date(maxDuration).getFullYear();

    let newDuration: string | null = String(yearsFromToday(mDuration));

    // Check if maxDuration is "-1" (all options should be enabled)
    if (newDuration === "-1") return false;

    return !conditionallyDisplay.some((condition: any) => {
      return condition.values.includes(String(newDuration));
    });
  };

  // Address Type
  const addressType = watch("addressType");
  const mailingAddressType = watch("mailingAddressType");
  const workAddressType = watch("workAddressType");

  // Watch "Home" and "Watch"
  const dbHomeAddress = watch("Home");
  const dbMailingAddress = watch("Mailing");
  const dbWorkAddress = watch("Work");

  useEffect(() => {
    const setAddressId = () => {
      if (!addressType && !mailingAddressType && !workAddressType) return;

      if (
        addressType.toLowerCase() === "home" &&
        (dbHomeAddress !== null || dbHomeAddress !== undefined)
      ) {
        setValue("participantAddressId", dbHomeAddress?.participantAddressId);
      } else if (
        mailingAddressType.toLowerCase() === "mailing" &&
        (dbMailingAddress !== null || dbMailingAddress !== undefined)
      ) {
        setValue(
          "participantAddressId",
          dbMailingAddress?.participantAddressId
        );
      } else if (
        workAddressType?.toLowerCase() === "work" &&
        (dbWorkAddress !== null || dbWorkAddress !== undefined)
      ) {
        setValue("participantAddressId", dbWorkAddress?.participantAddressId);
      } else {
        setValue("participantAddressId", null);
      }
    };

    setAddressId();
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [
    addressType,
    mailingAddressType,
    workAddressType,
    dbHomeAddress,
    dbMailingAddress,
    dbWorkAddress,
  ]);

  return (
    <div
      className={`${
        section.elements?.some(
          (element: any) =>
            inputConstraints.includes(element.type as ControlType) &&
            element.type !== "conditionalSelect"
        ) && "flex flex-wrap"
      }`}
    >
      {section.elements?.map((element: any, index: number) => {
        const { main, conditionalDisplay } = FormInputElementStyles(element);

        if (element.type === "conditionalCheckbox") {
          watch(element.fieldName, false);
        }

        // Set in form
        if (element.type === "set-in-form") {
          // setAddressId();
        }

        // Hide Element based on conditional display
        if (
          element.conditionallyDisplay &&
          !evaluateDisplayCondition(element.conditionallyDisplay, getValues())
        ) {
          const currentValue = getValues(element.fieldName);
          if (currentValue !== null) {
            setValue(element.fieldName, null);
          }
          return null;
        }

        return (
          <React.Fragment key={`element-${element.fieldName}`}>
            {element.options && element.type === "conditionalSelect" && (
              <div className="text-center text-xl">
                <div className="flex flex-row justify-center items-center">
                  <select
                    className="border border-gray-300 rounded px-2 py-1 w-full max-w-3xl"
                    // name={element.fieldName}
                    id={element.fieldName}
                    // required={
                    //   getValues(element.fieldName) === "" &&
                    //   element.required?.value
                    // }
                    // onChange={(e) => {
                    //   setFileType(e.target.value);
                    //   setValue(element.fieldName, e.target.value, {
                    //     shouldValidate: true,
                    //   });

                    //   // Reset the conditional checkboxes
                    //   setIsInitialRender(true);
                    // }}
                    value={
                      getValues(element.fieldName) ?? defaultOption?.value ?? ""
                    }
                    {...register(element.fieldName, {
                      required: true,
                      onChange(event) {
                        setFileType(event.target.value);
                        setValue(element.fieldName, event.target.value, {
                          shouldValidate: true,
                        });

                        // Reset the conditional checkboxes
                        setIsInitialRender(true);
                      },
                    })}
                  >
                    {element.options.map((conditional: any, index2: number) => {
                      console.log("Conditional: ", conditional);
                      return (
                        <option
                          value={conditional.value}
                          key={conditional.sortOrder}
                          disabled={selectTriggerFieldNames.some(
                            (fieldName: string) =>
                              shouldDisableOption(
                                watch(fieldName),
                                conditional.conditionallyDisplay
                              )
                          )}
                        >
                          {conditional.label}
                        </option>
                      );
                    })}
                  </select>
                  <p className="text-red-500 ml-2">
                    {element.required?.value && "*"}
                  </p>
                </div>
                <ErrorMessage errors={errors} name={element.fieldName} />
              </div>
            )}

            {/* Search Builder */}
            {element.type === "searchBuilder" && (
              <div className="overflow-y-auto min-h-full overflow-x-hidden space-y-4">
                <ErrorMessage
                  errors={errors}
                  name={element.fieldName}
                  render={({ message }) => (
                    <p className="text-red-500 text-center font-bold my-2">
                      {message}
                    </p>
                  )}
                />

                <div
                  className={`flex flex-row w-full justify-center items-start `}
                >
                  <p className="text-red-500 ml-2">
                    {element.required?.value && "*"}
                  </p>
                </div>
              </div>
            )}

            {/* File Input */}
            <div className="flex flex-col flex-wrap items-center justify-center">
              {(fileType || defaultOption) &&
                element.conditionallyDisplay?.some(
                  (conditional: {
                    fieldName: string;
                    values: string | string[];
                  }) =>
                    conditional.values.includes(
                      fileType ?? defaultOption.value
                    ) ||
                    conditional.values.includes(
                      String(getValues(conditional.fieldName))
                    )
                ) && (
                  <FileInput
                    type="file"
                    label={element.label}
                    fieldName={element.fieldName}
                  />
                )}
              {element.type === "file" &&
                element.conditionallyDisplay?.some(
                  (conditional: {
                    fieldName: string;
                    values: string | string[];
                  }) =>
                    conditional.values.includes(
                      String(getValues(conditional.fieldName))
                    )
                ) && (
                  <FileInput
                    type="file"
                    label={element.label}
                    fieldName={element.fieldName}
                  />
                )}
              {element.type === "file" &&
                element.conditionallyDisplay.length === 0 &&
                (element.options === null ||
                  element.options === undefined ||
                  element.options.length < 1) && (
                  <FileInput
                    type="file"
                    label={element.label}
                    fieldName={element.fieldName}
                  />
                )}
              {element.type === "fileOptions" &&
                element.conditionallyDisplay.length === 0 &&
                element.options.length > 1 && (
                  <MultipleFileInput
                    type="file"
                    label={element.label}
                    fieldName={element.fieldName}
                    options={element.options}
                    // multiple={true}
                  />
                )}
            </div>

            {/* Form Input */}
            {inputConstraints.includes(element.type as ControlType) &&
              element.type !== "conditionalSelect" &&
              !watchedValues[element.fieldName] && (
                <div className={main}>
                  {/* Conditional Display Toggle */}
                  {element.type === "conditionalCheckbox" && (
                    <div className={conditionalDisplay}>
                      <div className="py-0.5 mr-1">
                        <input
                          type="checkbox"
                          id={element.fieldName}
                          {...register(element.fieldName)}
                          defaultChecked={
                            newTriggerValues?.find(
                              (trigger: any) =>
                                trigger?.conditionalCheckboxFieldName ===
                                element.fieldName
                            )?.value ?? stringToBoolean(element.defaultValue)
                          }
                        />
                      </div>

                      <label
                        className=" cursor-pointer"
                        htmlFor={element.fieldName}
                      >
                        {camel2title(element.label)}
                      </label>
                    </div>
                  )}

                  {/* Inputs */}
                  {element.type !== "conditionalCheckbox" && (
                    <div>
                      <p>
                        <label htmlFor={element.fieldName}>
                          {element.label}{" "}
                        </label>
                        <label
                          className="text-red-500"
                          htmlFor={element.fieldName}
                        >
                          {element.required &&
                            element.required.value === true &&
                            optionalPage === false &&
                            "*"}
                        </label>
                      </p>
                      <DynamicControl
                        optionalPage={optionalPage}
                        inputData={element}
                      />
                    </div>
                  )}
                </div>
              )}

            {/* Form Confirmation */}
            <div className="flex gap-2 items-center max-w-md mx-auto">
              {element.type === "fieldDisplay" && (
                <>
                  <p className="shrink-0">{element.label}</p>
                  <p className="w-full border-b border-black border-dashed translate-y-1.5 "></p>
                  <p className="shrink-0 text-left">
                    {getValues(element.fieldName) ? (
                      getValues(element.fieldName)
                    ) : (
                      <span className="text-red-500 shrink-0">
                        Not Provided
                      </span>
                    )}
                  </p>
                </>
              )}
            </div>
          </React.Fragment>
        );
      })}
    </div>
  );
};

export { SectionTitle, FormElements };
