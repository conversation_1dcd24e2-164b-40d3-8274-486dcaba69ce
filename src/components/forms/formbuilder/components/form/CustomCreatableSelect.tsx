import { useState, useRef, useEffect } from 'react';
import { IoCloseCircleOutline, IoCloseOutline } from 'react-icons/io5';
import { BsChevronDown } from 'react-icons/bs';
import Image from 'next/image';

interface CustomCreatableSelectProps {
  options: OptionType[];
  value: OptionType[];
  onChange: (value: OptionType[]) => void;
  lock: boolean;
}

type OptionType = {
  value: string;
  label: string;
  image?: string;
  color?: string;
};

const CustomCreatableSelect = ({ options, value, onChange, lock }: CustomCreatableSelectProps) => {
  const [inputValue, setInputValue] = useState('');
  const [currentOptions, setCurrentOptions] = useState<OptionType[]>([]);
  const [showDropdown, setShowDropdown] = useState(false);
  const [focusedIndex, setFocusedIndex] = useState(-1);
  const wrapperRef = useRef<HTMLDivElement | null>(null); // Add the type annotation here

  const handleOptionClick = (option: OptionType) => {
    setCurrentOptions([...currentOptions, option]);
    onChange([...value, option]);
    setShowDropdown(false);
    setInputValue("");
  };

  const filteredOptions = options.filter(
    (option) =>
      !currentOptions.some((selected) => selected.value === option.value) &&
      option.label.toLowerCase().includes(inputValue.toLowerCase()) // Filter based on inputValue
  );
    
  const handleInputChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setInputValue(event.target.value);
    setShowDropdown(true);
    setFocusedIndex(-1);
  };

  const handleClickOutside = (event: MouseEvent) => {
    if (wrapperRef.current && !wrapperRef.current.contains(event.target as Node)) {
      setShowDropdown(false);
    }
  };
  
  useEffect(() => {
    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  const handleKeyDown = (event: React.KeyboardEvent<HTMLInputElement>) => {
    if (event.key === "Enter") {
      event.preventDefault(); 
      const option = filteredOptions[focusedIndex];
      if (!lock && inputValue.trim() !== "" && option === undefined) {
        const newOption = { value: inputValue, label: inputValue, color: "#a8a8a8" };
        setCurrentOptions([...currentOptions, newOption]);
        onChange([...value, newOption]);
        setInputValue("");
      } else if (showDropdown && option !== undefined) {
        handleOptionClick(option);
      }
    } else if (event.key === "ArrowDown") {
      event.preventDefault();
      setFocusedIndex((prevIndex) =>
        prevIndex === filteredOptions.length - 1 ? 0 : prevIndex + 1
      );
    } else if (event.key === "ArrowUp") {
      event.preventDefault();
      setFocusedIndex((prevIndex) =>
        prevIndex === 0 ? filteredOptions.length - 1 : prevIndex - 1
      );
    } else if (event.key === "Tab") {
      setShowDropdown(false);
    }
  };

  const handleRemoveClick = (optionToRemove:OptionType) => {
    const updatedOptions = currentOptions.filter((option) => option.value !== optionToRemove.value);
    setCurrentOptions(updatedOptions);
    onChange(updatedOptions); // Update the parent component with the new value
  };

  return (
    <div ref={wrapperRef} className="w-full">
      {/* Input Box */}
      <div onClick={()=>{setShowDropdown(true)}} className='w-full border border-neutral-600 bg-stone-800 rounded mb-2 mt-1 px-1 py-1 text-neutral-300 flex gap-2 items-stretch'>
        <div className='flex flex-wrap gap-1 mr-auto flex-grow'>
          {currentOptions.map((option) => (
            <span 
              key={option.value} 
              className='
                 border border-neutral-700 bg-neutral-900 text-sm rounded 
                flex items-center px-2 py-1 gap-1.5 whitespace-nowrap shrink-0
              '
            >
              {option.color && <div style={{backgroundColor: option.color}} className='w-1.5 h-1.5 rounded-full'></div>}
              {option.image && <Image src={option.image} width={4} height={4} className='w-4 h-4 rounded-full shrink-0' alt='profile image'/>}
              {option.label} 
              <IoCloseCircleOutline 
                className='text-sm flex-shrink-0'
                onClick={() => {
                  handleRemoveClick(option);
                }}
              />
            </span>
          ))}
          
          <input
            type="text"
            value={inputValue}
            onChange={handleInputChange}
            onKeyDown={handleKeyDown}
            onFocus={() => setShowDropdown(true)} 
            placeholder="Type and press Enter to add"
            className='bg-transparent flex-grow px-1 outline-none'
          />
        </div>

        {/* Cancel All Items Buttons */}
        <div className='border-r border-neutral-500 pr-1'>
          <IoCloseOutline 
            className='text-neutral-300 shrink-0 hover:text-red-400 h-full'
            onClick={() => {
              setCurrentOptions([]);
              onChange([]);
            }}
          />
        </div>

        {/* Dropdown Button */}
        <div>
          <BsChevronDown 
            className='text-neutral-300 shrink-0 h-full'
            onClick={() => setShowDropdown(!showDropdown)}
          />
        </div>
      </div>

      {/* Dropdown Box */}
      {showDropdown && (
        <div
          className="w-full text-neutral-300 absolute z-10 right-0 px-4"
        >
          <div className='border border-neutral-600 bg-stone-800 rounded shadow shadow-black'>
          {filteredOptions.map((option, index) => (
            <div
              key={option.value}
              className={`p-1 hover:bg-neutral-700 cursor-pointer${
                focusedIndex === index ? ' bg-neutral-700' : ''
              }`}
              onClick={() => {
                setCurrentOptions([...currentOptions, option]);
                onChange([...value, option]);
                setShowDropdown(false);
              }}
            >
              <div 
                className='flex items-center gap-1'
                style={{
                  color: option.color ? option.color : 'inherit',
                }}
              >
                {option.image && <Image src={option.image} alt="image" width={4} height={4} className='w-4 h-4 rounded-full' />}
                {option.label}
              </div>
            </div>
          ))}
          </div>
        </div>
      )}



    </div>
  );
};

export default CustomCreatableSelect;