import { useState, useRef, useEffect } from "react";
import { IoCloseCircleOutline, IoCloseOutline } from "react-icons/io5";
import { BsChevronDown } from "react-icons/bs";
import Image from "next/image";

interface CustomCreatableSelectProps {
  options: OptionType[];
  value: OptionType | undefined;
  onChange: (value: OptionType | null) => void;
  lock: boolean;
}

type OptionType = {
  value: string;
  label: string;
  image?: string;
  color?: string;
};

const CustomCreatableSelectSingle = ({
  options,
  value,
  onChange,
  lock,
}: CustomCreatableSelectProps) => {
  const [inputValue, setInputValue] = useState("");
  const [selectedOption, setSelectedOption] = useState<OptionType | null>(null);
  const [showDropdown, setShowDropdown] = useState(false);
  const [focusedIndex, setFocusedIndex] = useState(-1);
  const wrapperRef = useRef<HTMLDivElement | null>(null);

  const handleOptionClick = (option: OptionType) => {
    setSelectedOption(option);
    onChange(option);
    setShowDropdown(false);
    setInputValue("");
  };

  const filteredOptions = options.filter(
    (option) =>
      option.label.toLowerCase().includes(inputValue.toLowerCase()) &&
      (!selectedOption || option.value !== selectedOption.value)
  );

  const handleInputChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setInputValue(event.target.value);
    setShowDropdown(true);
    setFocusedIndex(-1);
  };

  const handleClickOutside = (event: MouseEvent) => {
    if (
      wrapperRef.current &&
      !wrapperRef.current.contains(event.target as Node)
    ) {
      setShowDropdown(false);
    }
  };

  useEffect(() => {
    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);

  const handleKeyDown = (event: React.KeyboardEvent<HTMLInputElement>) => {
    if (event.key === "Enter") {
      event.preventDefault();
      const option = filteredOptions[focusedIndex];
      if (!lock && inputValue.trim() !== "" && option === undefined) {
        const newOption = {
          value: inputValue,
          label: inputValue,
          color: "#a8a8a8",
        };
        setSelectedOption(newOption);
        onChange(newOption);
        setInputValue("");
      } else if (showDropdown && option !== undefined) {
        handleOptionClick(option);
      }
    } else if (event.key === "ArrowDown") {
      event.preventDefault();
      setFocusedIndex((prevIndex) =>
        prevIndex === filteredOptions.length - 1 ? 0 : prevIndex + 1
      );
    } else if (event.key === "ArrowUp") {
      event.preventDefault();
      setFocusedIndex((prevIndex) =>
        prevIndex === 0 ? filteredOptions.length - 1 : prevIndex - 1
      );
    } else if (event.key === "Tab") {
      setShowDropdown(false);
    }
  };

  return (
    <div ref={wrapperRef} className="w-full">
      {/* Input Box */}
      <div
        onClick={() => {
          setShowDropdown(true);
        }}
        className="w-full border border-neutral-600 bg-stone-800 rounded mb-2 mt-1 px-1 py-1 text-neutral-300 flex gap-2 items-stretch"
      >
        {selectedOption && (
          <span className="border border-neutral-700 bg-neutral-900 text-sm rounded flex items-center px-2 py-1 gap-1.5 whitespace-nowrap shrink-0">
            {selectedOption.color && (
              <div
                style={{ backgroundColor: selectedOption.color }}
                className="w-1.5 h-1.5 rounded-full"
              ></div>
            )}
            {selectedOption.image && (
              <Image
                src={selectedOption.image}
                width={4}
                height={4}
                className="w-4 h-4 rounded-full shrink-0"
                alt="profile image"
              />
            )}
            {selectedOption.label}
            <IoCloseCircleOutline
              className="text-sm flex-shrink-0"
              onClick={() => {
                setSelectedOption(null);
                onChange(null);
              }}
            />
          </span>
        )}

        {!selectedOption && (
          <input
            type="text"
            value={inputValue}
            onChange={handleInputChange}
            onKeyDown={handleKeyDown}
            onFocus={() => setShowDropdown(true)}
            placeholder="Type and press Enter to add"
            className="bg-transparent flex-grow px-1 outline-none"
          />
        )}

        {/* Dropdown Button */}
        <div>
          <BsChevronDown
            className="text-neutral-300 shrink-0 h-full"
            onClick={() => setShowDropdown(!showDropdown)}
          />
        </div>
      </div>

      {/* Dropdown Box */}
      {showDropdown && (
        <div className="w-full text-neutral-300 absolute z-10 right-0 px-4">
          <div className="border border-neutral-600 bg-stone-800 rounded shadow shadow-black">
            {filteredOptions.map((option, index) => (
              <div
                key={option.value}
                className={`p-1 hover:bg-neutral-700 cursor-pointer${
                  focusedIndex === index ? " bg-neutral-700" : ""
                }`}
                onClick={() => handleOptionClick(option)}
              >
                <div
                  className="flex items-center gap-1"
                  style={{ color: option.color ? option.color : "inherit" }}
                >
                  {option.image && (
                    <Image
                      src={option.image}
                      alt="image"
                      width={4}
                      height={4}
                      className="w-4 h-4 rounded-full"
                    />
                  )}
                  {option.label}
                </div>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
};

export default CustomCreatableSelectSingle;
