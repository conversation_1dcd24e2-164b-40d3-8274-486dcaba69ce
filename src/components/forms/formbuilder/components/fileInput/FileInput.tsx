"use client";

import { useState, useRef } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { useFormContext } from "react-hook-form";
import Preview from "./Preview";
import { FiUpload } from "react-icons/fi";
import FileData from "./FileData";
import { useAtom } from "jotai";
import { toastAtom } from "@/components/ui/toast/toast";

type FileInputProps = {
  type: string;
  label: string;
  fieldName: string;
};

const isAcceptedFileType = (type: string) => {
  return !!type.match(/image\/(jpeg|png)/) || !!type.match(/application\/pdf/);
};

const FileInput = ({ type, label, fieldName }: FileInputProps) => {
  const { register, setValue, getValues, resetField, watch } = useFormContext();
  const watchedFieldValue = watch(fieldName);
  const [dragging, setDragging] = useState<boolean>(false);
  const [fileError, setFileError] = useState<boolean>(false);
  const [shake, setShake] = useState<boolean>(false);
  const [, setToast] = useAtom(toastAtom);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const shakeAnimation = {
    start: {
      x: 0,
      rotate: 0,
    },
    end: {
      x: [0, -3, 3, -3, 3, 0],
      rotate: [0, -2, 2, -2, 2, 0],
      transition: {
        x: {
          type: "keyframes",
          duration: 0.3,
          times: [0, 0.15, 0.3, 0.45, 0.6, 1],
        },
        rotate: {
          type: "keyframes",
          duration: 0.3,
          times: [0, 0.15, 0.3, 0.45, 0.6, 1],
        },
      },
    },
  };

  const handleDragOver = (event: React.DragEvent<HTMLDivElement>) => {
    event.preventDefault();
    setDragging(true);
    const file = event.dataTransfer.items?.[0];
    if (!file) return;
    const fileType = isAcceptedFileType(file.type);
    fileType ? setFileError(false) : setFileError(true);
  };

  const handleDragLeave = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    setDragging(false);
  };

  const handleDrop = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    setDragging(false);

    const file = e.dataTransfer.items[0].getAsFile();
    const fileInArray = e.dataTransfer.files;
    if (!file && !fileInArray) return;
    fileUpload(file, fileInArray);
  };

  const fileUpload = async (file: any, files: any) => {
    if (!file) return;
    const fileType = isAcceptedFileType(file.type);
    if (!fileType) {
      setShake(true);
      setTimeout(() => {
        setShake(false);
      }, 300);
      setToast({
        status: "error",
        label: "Error Submitting Identification",
        message: "Please Try Again or Fill Out Form Manually",
        position: "bottom",
        duration: 5000,
      });
      return;
    }
    setValue(fieldName, files);
  };

  const handleClick = () => {
    if (fileInputRef.current) {
      fileInputRef.current.click();

      const file = fileInputRef.current.files?.[0];
      const files = fileInputRef.current.files;
      if (!file && !files) return;
      fileUpload(file, files);
    }
  };

  const reset = () => {
    resetField(fieldName);
    if (fileInputRef.current) {
      fileInputRef.current.value = "";
    }
  };

  const handleInputChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    const files = event.target.files;
    if (!file && !files) return;
    fileUpload(file, files);
  };

  return (
    <div className="w-full mb-10 max-w-xl">
      <div className="mb-2 text-center font-bold text-xl">{label}</div>
      <label htmlFor={fieldName} className="block text-center w-full">
        <motion.div
          initial={false}
          animate={shake ? "end" : "start"}
          variants={shakeAnimation}
          className={`
            border-2 p-4 mx-4 rounded-lg border-dashed border-gray-300 cursor-pointer 
            transition-colors duration-200 ease-in-out 
            hover:border-gray-400 hover:bg-gray-100
            ${
              dragging &&
              (!fileError
                ? "border-green-500 border-solid bg-green-100"
                : "border-red-500 border-solid bg-red-100")
            }
          `}
          onDragOver={handleDragOver}
          onDragLeave={handleDragLeave}
          onDrop={handleDrop}
          onClick={handleClick}
        >
          <div>
            <div className="flex flex-col items-center justify-center">
              {watchedFieldValue ? (
                <Preview file={getValues(fieldName)} />
              ) : (
                <div
                  className={`flex flex-row items-center justify-center text-center`}
                >
                  <FiUpload size={24} />
                </div>
              )}
              <div className="w-full">
                <div className="flex flex-col items-center justify-center">
                  <div className="w-full">
                    {!watchedFieldValue && (
                      <>
                        <div
                          className={`flex flex-row items-center justify-center text-center my-4`}
                        >
                          Drag & Drop or&nbsp;
                          <h1 className="cursor-pointer text-blue-500">
                            Choose&nbsp;
                          </h1>
                          file to upload
                        </div>

                        <span className="text-xs text-gray-500">
                          IMAGE or PDF
                        </span>
                      </>
                    )}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </motion.div>
      </label>

      {watchedFieldValue && (
        <div className="flex flex-row justify-center items-center my-4 mx-5">
          <FileData file={getValues(fieldName)} reset={reset} />
        </div>
      )}

      <input
        type={type}
        accept="image/*, application/pdf"
        className="hidden"
        {...register(fieldName, { onChange: handleInputChange })}
        ref={fileInputRef}
      />
    </div>
  );
};

export default FileInput;
