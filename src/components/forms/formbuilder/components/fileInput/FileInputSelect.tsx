"use client";
import { ErrorMessage } from "@hookform/error-message";
import { useContext, useEffect, useMemo, useRef, useState } from "react";
import { RegisterOptions, useFormContext } from "react-hook-form";
import { SelectedGroupContext } from "./MultipleFileInput";

type FileInputSelect = {
  options: {
    label: string;
    value: string;
    sortValue: number;
    default: boolean;
    description: string;
    group: string;
  }[];
  onChange: (file: any, value: string) => void;
  file: any;
};

const FileInputSelect = ({ options, onChange, file }: FileInputSelect) => {
  const {
    setValue,
    getValues,
    register,
    formState: { errors },
    watch,
  } = useFormContext();

  const { setErrorMessage, selectedGroup, setSelectedGroup, selectRef } =
    useContext(SelectedGroupContext);

  const fieldName = `${file.name}-Select`;

  // On mount, set the default value
  useEffect(() => {
    setErrorMessage(fieldName);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  // Find the select element and its default option
  const defaultOption = useMemo(() => {
    return options.find((option: any) => option.default === true);
  }, [options]);

  const tailwindClasses = `border border-gray-300 rounded px-2 py-1 w-full ${
    errors[fieldName] && "border-red-500"
  }`;

  // Grouping options by their group property
  const groupedOptions = options.reduce((acc, option) => {
    const groupName =
      option.group !== null && option.group !== undefined
        ? option.group
        : "No Group";
    if (!acc[groupName]) {
      acc[groupName] = [];
    }
    acc[groupName].push(option);
    return acc;
  }, {} as { [key: string]: typeof options });

  const validationOptions: RegisterOptions = {
    required: {
      value: true,
      message: "Please select an option",
    },
    validate: () => {
      const values = getValues();
      if (selectedGroup.length === 2 && selectedGroup[0] !== selectedGroup[1]) {
        // Wipe the selected group
        setSelectedGroup([]);

        return "Please select the same type of ID";
      }
      if (values.idFront && !values.idBack) {
        return "Please upload the back of your ID";
      }
      if (values.idBack && !values.idFront) {
        return "Please upload the front of your ID";
      }
    },
  };

  return (
    <div className="w-full my-2 p-2">
      <select
        {...register(fieldName, validationOptions)}
        name={fieldName}
        id={fieldName}
        className={tailwindClasses}
        defaultValue={defaultOption?.value}
        onChange={(e) => {
          const selectedOption = e.target.options[e.target.selectedIndex];
          const groupLabel = selectedOption.getAttribute("data-group");

          setSelectedGroup((prev: any) => [...prev, groupLabel]);
          onChange(file, e.target.value);
        }}
      >
        {Object.entries(groupedOptions).map(
          ([group, groupOptions], groupIdx) => {
            if (group === "No Group") {
              return groupOptions.map((option, idx) => (
                <option key={idx} value={option.value} data-group="undefined">
                  {option.label}
                </option>
              ));
            }
            return (
              <optgroup key={groupIdx} label={group}>
                {groupOptions.map((option, idx) => (
                  <option
                    key={idx}
                    value={option.value}
                    defaultValue={option.default ? option.value : undefined}
                    data-group={option.group}
                  >
                    {option.label}
                  </option>
                ))}
              </optgroup>
            );
          }
        )}
      </select>
    </div>
  );
};

export default FileInputSelect;
