import Image from "next/image";
import { useEffect, useState } from "react";
import { pdfjs, Document, Page } from "react-pdf";
import "react-pdf/dist/esm/Page/AnnotationLayer.css";
import "react-pdf/dist/esm/Page/TextLayer.css";

const options = {
  cMapUrl: "cmaps/",
  standardFontDataUrl: "standard_fonts/",
};

pdfjs.GlobalWorkerOptions.workerSrc = `//cdnjs.cloudflare.com/ajax/libs/pdf.js/${pdfjs.version}/pdf.worker.min.js`;

type PreviewProps = {
  file: FileList;
};

type ImagePreviewProps = {
  file: {
    data: string | ArrayBuffer | null;
    type: "pdf" | "image" | string;
  };
};

type PdfPreviewProps = {
  file: {
    data: string | ArrayBuffer | null;
    type: "pdf" | "image" | string;
  };
};

type FileState = {
  data: string | ArrayBuffer | null;
  type: "pdf" | "image" | string;
};

type ConvertedFile = {
  data: string | ArrayBuffer | null;
  type: "pdf" | "image" | string;
};

// Convert File to Base64
const fileToBase64 = (file: File): Promise<string> =>
  new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.readAsDataURL(file);
    reader.onload = () => resolve(reader.result as string);
    reader.onerror = (error) => reject(error);
  });

// Convert Base64 to URL
const base64ToBlobUrl = (base64: string, type: string) => {
  const binaryString = window.atob(base64.split(",")[1]);
  const len = binaryString.length;
  const bytes = new Uint8Array(len);
  for (let i = 0; i < len; i++) {
    bytes[i] = binaryString.charCodeAt(i);
  }
  const blob = new Blob([bytes], { type: type });
  const url = URL.createObjectURL(blob);
  return url;
};

const Preview = ({ file }: PreviewProps) => {
  const f = file[0];
  const [fileState, setFileState] = useState<FileState | null>(null);

  // Log out file size
  // console.log("File size: ", f.size);

  // On mount, convert files to base64
  useEffect(() => {
    const convertFilesToBase64 = async () => {
      // Convert File to Base64 to match with the type ConvertedFile expects
      const convertedFile: ConvertedFile = {
        data: await fileToBase64(f),
        type: f.type === "application/pdf" ? "pdf" : "image",
      };
      setFileState(convertedFile);
    };
    convertFilesToBase64();
  }, [f]);

  return fileState ? (
    fileState.type === "pdf" ? (
      <PdfPreview file={fileState} />
    ) : (
      <ImagePreview file={fileState} />
    )
  ) : null;
};

// Image Preview
const ImagePreview = ({ file }: ImagePreviewProps) => {
  return (
    <div className="flex flex-col">
      <Image
        src={file.data as string}
        alt="image preview"
        width={300}
        height={300}
      />
    </div>
  );
};

// PDF Preview
const PdfPreview = ({ file }: PdfPreviewProps) => {
  return (
    <div className="flex flex-col">
      {/* Display only first page of PDF */}
      <Document file={file.data} options={options}>
        <Page pageNumber={1} height={300} scale={1} />
      </Document>
    </div>
  );
};

export default Preview;
