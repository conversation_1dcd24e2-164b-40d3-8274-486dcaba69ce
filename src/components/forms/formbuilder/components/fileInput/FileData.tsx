import { useState } from "react";
import { useFormContext } from "react-hook-form";
import { BsFilePdf, BsImage } from "react-icons/bs";
import { FiX } from "react-icons/fi";

type FileDataProps = {
  file: FileList;
  reset: () => void;
};

// format file size
const formatBytes = (bytes: number, decimals = 2) => {
  if (bytes === 0) return "0 Bytes";

  const k = 1024;
  const dm = decimals < 0 ? 0 : decimals;
  const sizes = ["Bytes", "KB", "MB"];
  const i = Math.floor(Math.log(bytes) / Math.log(k));

  return parseFloat((bytes / Math.pow(k, i)).toFixed(dm)) + " " + sizes[i];
};

const FileIcon = ({ type }: { type: string }) => {
  return type === "PDF" ? (
    <BsFilePdf className={`p-2 h-12 w-12 text-neutral-700`} />
  ) : (
    <BsImage className={`p-2  h-12 w-12 text-neutral-700`} />
  );
};

const FileData = ({ file, reset }: FileDataProps) => {
  console.log("File: ", file);
  const f = file[0];
  const name = f.name;
  const size = formatBytes(f.size);
  const type = f.type === "application/pdf" ? "PDF" : "Image";

  return (
    <div className="flex flex-row w-full justify-between  bg-gray-100 rounded shadow p-2">
      <div className="flex flex-row justify-center items-center text-left">
        <FileIcon type={type} />
        <div className="flex flex-col justify-center text-left">
          <h1>{name}</h1>
          <div className="flex flex-row w-full">
            <p className="text-gray-500 text-xs">{size}</p>
            <p className="mx-2 text-gray-500 text-xs">&middot;</p>
            <p className="text-gray-500 text-xs">{type}</p>
          </div>
        </div>
      </div>
      {/* Close Button */}
      <div className="flex flex-row justify-center items-center cursor-pointer mr-4">
        <FiX
          className="h-6 w-6 text-red-500 hover:text-red-400 hover:rotate-180 transition-all"
          onClick={reset}
        />
      </div>
    </div>
  );
};

export default FileData;
