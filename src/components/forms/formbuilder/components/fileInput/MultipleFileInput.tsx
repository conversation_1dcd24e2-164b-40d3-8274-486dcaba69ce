import Button from "@/components/ui/buttons/Button";
import { toast<PERSON><PERSON> } from "@/components/ui/toast/toast";
import { motion } from "framer-motion";
import { useAtom } from "jotai";
import { useRef, useState } from "react";
import { useFormContext } from "react-hook-form";
import { FiFilePlus, FiUpload } from "react-icons/fi";
import FileData from "./FileData";
import MultipleFileData from "@/components/universal/input/MFileData";
import FileInputSelect from "./FileInputSelect";
import { ErrorMessage } from "@hookform/error-message";
import React from "react";

type MultipleFileInputProps = {
  type: string;
  label: string;
  fieldName: string;
  options: {
    label: string;
    value: string;
    sortValue: number;
    default: boolean;
    description: string;
    group: string;
  }[];
};

const shakeAnimation = {
  start: {
    x: 0,
    rotate: 0,
  },
  end: {
    x: [0, -3, 3, -3, 3, 0],
    rotate: [0, -2, 2, -2, 2, 0],
    transition: {
      x: {
        type: "keyframes",
        duration: 0.3,
        times: [0, 0.15, 0.3, 0.45, 0.6, 1],
      },
      rotate: {
        type: "keyframes",
        duration: 0.3,
        times: [0, 0.15, 0.3, 0.45, 0.6, 1],
      },
    },
  },
};

const isAcceptedFileType = (type: string) => {
  return !!type.match(/image\/(jpeg|png)/) || !!type.match(/application\/pdf/);
};

// Option Group Context
export const SelectedGroupContext = React.createContext<any>(null);

const MultipleFileInput = ({
  type,
  label,
  fieldName,
  options,
}: MultipleFileInputProps) => {
  const {
    register,
    setValue,
    getValues,
    resetField,
    watch,
    unregister,
    formState: { errors },
  } = useFormContext();
  const watchedFieldValue = watch(fieldName);
  const [filesUploaded, setFiles] = useState<any>();
  const [documentUploadedType, setDocumentUploadedType] = useState<string>();
  const [shake, setShake] = useState<boolean>(false);
  const [fileError, setFileError] = useState<boolean>(false);
  const [dragging, setDragging] = useState<boolean>(false);
  const [, setToast] = useAtom(toastAtom);
  const [errorMessage, setErrorMessage] = useState<string>("");
  const [selectedGroup, setSelectedGroup] = useState<string[]>([]);
  const selectRef = useRef<any>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);

  // Map document uploaded type and filesUpload
  const uploadMap = (file: any, docType: string) => {
    // Map document type
    const doc = {
      name: file.name,
      type: docType,
    };

    return doc;
  };

  // Handle Drag over for multiple files
  const handleDragOver = (event: React.DragEvent<HTMLDivElement>) => {
    event.preventDefault();
    setDragging(true);
    const files = event.dataTransfer.items;
    if (!files) return;
    const fileTypes = Array.from(files).map((file) =>
      isAcceptedFileType(file.type)
    );
    fileTypes.includes(false) ? setFileError(true) : setFileError(false);
  };

  // Handle Drag leave for multiple files
  const handleDragLeave = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    setDragging(false);
  };

  // Upload multiple files
  const filesUpload = (files: any) => {
    if (!files) return;
    const fileTypes = Array.from(files).map((file: any) =>
      isAcceptedFileType(file.type)
    );
    if (fileTypes.includes(false)) {
      setShake(true);
      setTimeout(() => {
        setShake(false);
      }, 300);
      setToast({
        status: "error",
        label: "Error Submitting Identification",
        message: "Please Try Again or Fill Out Form Manually",
        position: "bottom",
        duration: 5000,
      });
      return;
    }

    // Check if uploaded files are in a FileList
    const isFileList = files instanceof FileList;

    // If it is a FileList, convert to array
    const filesArray = isFileList ? Array.from(files) : files;

    const newFiles = filesUploaded
      ? [...filesUploaded, ...filesArray]
      : filesArray;
    setFiles(newFiles);
    uploadFiles(newFiles);
  };

  // Handle Drop for multiple files
  const handleDrop = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    setDragging(false);

    const files = e.dataTransfer.files;
    if (!files) return;
    filesUpload(files);
  };

  // Handle Click for multiple files
  const handleClick = () => {
    if (fileInputRef.current) {
      fileInputRef.current.value = "";
      fileInputRef.current.click();

      const files = fileInputRef.current.files;
      if (!files) return;
      filesUpload(files);
    }
  };

  // Reset
  const handleReset = () => {
    setFiles(null);
    resetField(fieldName);
    if (fileInputRef.current) {
      fileInputRef.current.value = "";
    }
  };

  // Handle multiple input change
  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files;
    if (!files) return;
    filesUpload(files);
  };

  // Remove file from array
  const removeFile = (file: any) => {
    setFiles((prevFiles: any) => prevFiles.filter((f: any) => f !== file));
    // Remove file from react hook form
    const hookValueFiles = getValues(fieldName);
    const newHookValueFiles = hookValueFiles.filter(
      (f: any) => f.name !== file.name
    );
    setValue(fieldName, newHookValueFiles);
  };

  // Function to upload files to react hook form
  const uploadFiles = (filesToBeUploaded: any) => {
    if (!filesToBeUploaded) return;
    setValue(fieldName, filesToBeUploaded);
  };

  const groupSets = new Set();

  // Populate groupSets with unique groups
  options.forEach((option) => {
    if (option.group === "Default") return;
    groupSets.add(option.group);
  });

  return (
    <div className="w-full flex flex-col items-center mb-10 max-w-7xl">
      {/* Header */}
      <div className="text-left font-bold text-xl">{label}</div>

      {/* What to upload */}
      <div
        className={`flex flex-col md:flex-row w-full justify-center items-center my-5`}
      >
        <p className="text-lg text-black text-center font-semibold">
          What you can upload:&nbsp;
        </p>
        <p className="text-lg text-black text-center ">
          {groupSets.size > 1
            ? Array.from(groupSets)
                .join(", ")
                .replace(/,(?!.*,)/gim, " and")
            : options
                .filter((option) => option.label.toLowerCase() !== "select")
                .map((option) => option.label)
                .join(", ")
                .replace(/,(?!.*,)/gim, " and")}
        </p>
      </div>

      {/* File Input */}
      <div
        className={`flex ${
          filesUploaded?.length > 0 ? "md:justify-between" : "md:justify-center"
        } md:flex-row flex-col w-full items-center md:items-start`}
      >
        {/* Upload */}
        <motion.div
          initial={false}
          animate={shake ? "end" : "start"}
          variants={shakeAnimation}
          className={`
            border-2 p-4 mt-4  rounded-lg border-dashed border-gray-300 cursor-pointer 
            transition-colors duration-200 ease-in-out 
            hover:border-gray-400 hover:bg-gray-100
            h-80 w-1/2
            ${
              dragging &&
              (!fileError
                ? "border-green-500 border-solid bg-green-100"
                : "border-red-500 border-solid bg-red-100")
            }
          `}
          onDragOver={handleDragOver}
          onDragLeave={handleDragLeave}
          onDrop={handleDrop}
          onClick={handleClick}
        >
          <div className="flex flex-col items-center justify-center w-full h-full">
            <div
              className={`flex flex-row items-center justify-center text-center`}
            >
              <FiFilePlus size={50} />
            </div>
            <>
              <div
                className={`flex flex-col md:flex-row items-center justify-center text-center my-4`}
              >
                Drag & Drop or&nbsp;
                <h1 className="cursor-pointer text-blue-500">Choose&nbsp;</h1>
                file to upload
              </div>

              <div className="text-xs text-gray-500 text-center">
                IMAGE or PDF
              </div>
            </>
          </div>
        </motion.div>

        {/* File List */}
        {Array.isArray(filesUploaded) && filesUploaded.length > 0 && (
          <div
            className={`flex flex-col w-1/2 justify-start items-center ml-5 ${
              filesUploaded?.length > 0 ? "" : "hidden"
            }`}
          >
            <SelectedGroupContext.Provider
              value={{
                selectedGroup,
                setSelectedGroup,
                setErrorMessage,
                selectRef,
              }}
            >
              <MultipleFileData
                files={filesUploaded}
                reset={removeFile}
                options={options}
              />
            </SelectedGroupContext.Provider>
          </div>
        )}
      </div>

      <div>
        <ErrorMessage
          errors={errors}
          name={errorMessage}
          render={({ message }) => (
            <p className="text-red-500 mt-2 w-full">{message}</p>
          )}
        />
      </div>

      {/* Hidden File Input */}
      <input
        multiple
        type={type}
        className="hidden"
        accept="image/*, application/pdf"
        {...register(fieldName, { onChange: handleChange })}
        ref={fileInputRef}
      />
    </div>
  );
};

export default MultipleFileInput;
