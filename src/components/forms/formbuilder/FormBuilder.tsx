"use client";
import { FormProvider, useForm } from "react-hook-form";
import FormView from "./components/form/FormView";
import Button from "@/components/ui/buttons/Button";
import { useEffect } from "react";
import Loading from "@/app/(app)/loading";

type FormBuilderProps = {
  form: any;
  prevData?: any;
  prevData2?: any;
};

const FormError = ({ error }: { error: any }) => {
  return (
    <div className="p-6">
      <div className="flex flex-col items-center justify-center">
        <div className="text-3xl font-bold text-red-600">
          Error Loading Form
        </div>
        <div className="text-sm font-bold text-red-500 italic mb-10">
          {error.message}
        </div>
        <Button variant="secondary" onClick={() => window.history.back()}>
          Go Back
        </Button>
      </div>
    </div>
  );
};

const FormBuilder = ({ form, prevData }: FormBuilderProps) => {
  const methods = useForm();

  useEffect(() => {
    if (Array.isArray(prevData) && prevData.length > 0) {
      console.log("Prev Data (Form Builder): ", prevData);
      const dog = prevData?.[0] ?? prevData;
      console.log("Dog: ", dog);
      const keys = Object.keys(dog);
      const values = Object.values(dog);
      for (let i = 0; i < keys.length; i++) {
        methods.setValue(keys[i], values[i]);
      }
    } else {
      if (prevData === undefined || prevData === null) return;

      if (prevData !== undefined) {
        const keys = Object.keys(prevData);
        const values = Object.values(prevData);
        for (let i = 0; i < keys.length; i++) {
          methods.setValue(keys[i], values[i]);
        }
      }
    }
  }, [prevData, methods]);

  if (!form) return <div className="p-6">Form is null or undefined</div>;
  if (form.isLoading) return <Loading text="Loading Form" />;
  if (form.isError)
    return (
      <div className="p-6">
        <FormError error={form.error} />
      </div>
    );

  return (
    <FormProvider {...methods}>
      <FormView form={form.data ? form.data : form} />
    </FormProvider>
  );
};

export default FormBuilder;
