import { useState } from "react";
import { useController } from "react-hook-form";
import { Accept, useDropzone } from "react-dropzone";
import { cn } from "@/lib/utils";
import Image from "next/image";
import { BiImageAdd } from "react-icons/bi";
import { Documents } from "@/components/license/cards/LicenseType";
import RenderDownloadedFile from "@/app/(app)/(realm)/(protected)/(resident)/(registered)/resident/[entityType]/[entityId]/components/RenderDownloadedFile";

type FileUploadProps = {
  input: any;
  required: boolean;
  control: any;
  onChange?: (name: string, value: any) => void;
  setIsDirty?: (value: boolean) => void;
};

const FileUpload = ({
  input,
  required,
  control,
  setIsDirty,
}: FileUploadProps) => {
  const {
    field,
    fieldState: { error, isDirty },
  } = useController({
    name: input.id,
    control,
    rules: { required: required ? "This field is required" : false },
  });

  const [file, setFile] = useState<File | null>(field.value || null);

  // Check type of file
  const isDocObject = (file: Documents) => file?.documentUuid !== undefined;
  const customRender = isDocObject(field.value as Documents);
  console.log("customRender", customRender);

  const acceptFiles: Accept = {
    "image/*": [".jpeg", ".png"],
    // "application/pdf": [".pdf"],
  };

  const onDrop = (acceptedFiles: File[]) => {
    console.log("acceptedFiles", acceptedFiles);
    setFile(acceptedFiles[0]); // Only take the first file
    field.onChange(acceptedFiles[0]);
    setIsDirty && setIsDirty(true);
  };

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    accept: acceptFiles,
    onDrop,
    multiple: false,
  });

  const removeFile = () => {
    setFile(null);
    field.onChange(null);
  };

  if (!file) {
    setIsDirty && setIsDirty(isDirty);
  }

  const renderPreview = () => {
    if (!file) return null;

    const fileUrl = URL.createObjectURL(file);
    if (file.type.includes("image")) {
      return (
        <div className="flex items-center justify-center">
          <Image width={300} height={300} src={fileUrl} alt="Preview" />
        </div>
      );
    } else if (file.type.includes("pdf")) {
      return (
        <div className="flex items-center justify-center">
          <object
            data={fileUrl}
            type="application/pdf"
            style={{ maxWidth: "auto", height: "300px" }}
          >
            <p>
              Your browser does not support PDFs.{" "}
              <a href={fileUrl}>Download the PDF</a>.
            </p>
          </object>
        </div>
      );
    }
  };

  return (
    <div className={cn("max-w-lg", input?.className)}>
      <p className="line-clamp-1">{input?.label}</p>
      <div
        {...getRootProps()}
        className={`dropzone cursor-pointer rounded-md border-2 border-dashed p-4 text-center ${
          isDragActive
            ? "border-blue-500 bg-blue-100"
            : "border-gray-300 bg-gray-50"
        }`}
      >
        <input {...getInputProps()} />
        {isDragActive ? (
          <p className="text-gray-700">Drop the file here ...</p>
        ) : file ? (
          customRender ? (
            <RenderDownloadedFile downloadObject={field.value as Documents} />
          ) : (
            <div className="preview">{renderPreview()}</div>
          )
        ) : (
          <div className="flex flex-col items-center text-gray-500">
            <BiImageAdd className="text-2xl" />
            Drag &apos;n&apos; drop a file here, or click to select a file
            <br />
            <span className="text-xs text-gray-400">
              Only .jpeg and .png files are allowed
            </span>
          </div>
        )}
      </div>
      {file && (
        <div className="mt-2 flex items-center justify-between rounded border border-neutral-900 p-1">
          <span className="line-clamp-1 text-gray-800">{file.name}</span>
          <button
            onClick={removeFile}
            className="text-sm text-red-500 hover:text-red-700"
          >
            Remove
          </button>
        </div>
      )}
      {error && <p className="text-red-500">{error.message}</p>}
    </div>
  );
};

export default FileUpload;
