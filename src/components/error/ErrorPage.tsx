"use client";
import { cn } from "@/lib/utils";
import { motion } from "framer-motion";
import Image from "next/image";
import { Button } from "../ui/button";
import { logout } from "@/utils/keycloakUtils";

export default function ErrorPage({
  message,
  fixed = false,
}: {
  message?: string;
  fixed?: boolean;
}) {
  return (
    <div
      className={cn(
        "flex flex-col items-center justify-center p-6",
        fixed ? "fixed left-0 top-0 z-50 h-screen w-screen" : "h-full w-full",
      )}
    >
      <Image
        src={"/images/resident/saddog.png"}
        alt="Sad Dog"
        width={200}
        height={200}
      />
      <h3 className="mt-4 text-3xl text-red-600">
        {message ?? "Something went wrong!"}
      </h3>
      <motion.button
        className="mt-6 rounded bg-red-700 px-4 py-2 text-white"
        whileHover={{ scale: 1.05 }}
        whileTap={{ scale: 0.95 }}
        onClick={() => window.location.reload()}
      >
        Retry
      </motion.button>
      <Button
        className="mt-6"
        variant={"ghost"}
        onClick={() => {
          logout();
        }}
      >
        Logout
      </Button>
    </div>
  );
}
