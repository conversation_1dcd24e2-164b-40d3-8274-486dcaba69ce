"use client"

import { useRouter } from 'next/navigation'
import { usePathname, useParams } from 'next/navigation'
import type { License } from '@/types/LicenseType'
import SearchTitle from '../searchBuilder/components/SearchTitle'
import { useState } from 'react'
import SearchTabBar from '../searchBuilder/SearchTabBar'
import TabsBar from '../ui/Tabs/TabsBar'

type LicenseProps = {
  licenses: License[];
  entityId: string;
  entityType: string;
}

type Options = {
  section: string;
  value: string;
}

const LicenseProfile = ({ licenses }:LicenseProps) => {
  const options:Options[] = [
    {
      section: 'Active',
      value: 'active'
    },
    {
      section: 'Expired',
      value: 'expired'
    },
    {
      section: 'All',
      value: 'all'
    },
  ]

  const [activeRadio, setActiveRadio] = useState<string | null>(options[0].value);

  console.log(licenses)
  const router = useRouter()
  const pathname = usePathname()
  const params = useParams()

  const entityId = params?.entityId ?? null


  // Active licenses
  const activeLicenses = licenses.filter((license: any) => license.licenseStatus === 'Active') ?? null
  const expiredLicenses = licenses.filter((license: any) => license.licenseStatus === 'Expired') ?? null
  
  // Go to Create New Dog License
  const handleNewDogLicense = () => {
    if(entityId){
      const link = `/license/dogLicenses/create?individualId=${entityId}&returnTo=${pathname}`
      router.push(link)
    }
  }

  return (
    <div>
      <TabsBar
        options={options}
        activeRadio={activeRadio}
        setActiveRadio={setActiveRadio}
      />
      
    
    </div>
  )
}

export default LicenseProfile