"use client";
import { IconType } from "react-icons";
import {
  <PERSON><PERSON><PERSON>,
  Toolt<PERSON><PERSON>ontent,
  TooltipProvider,
  TooltipTrigger,
} from "@radix-ui/react-tooltip";
import Link from "next/link";
import { ReactElement, useEffect, useState } from "react";
import Button from "../ui/buttons/Button";

export const Title = ({ children }: { children: React.ReactNode }) => {
  return (
    <h3 className="text-xl font-semibold text-neutral-700 mb-6 border-b pb-2">{children}</h3>
  );
};

export const IconCount = ({
  Icon,
  count,
  label,
  link,
}: {
  Icon: IconType;
  count: number;
  label: string;
  link: string;
}) => {
  return (
    <TooltipProvider>
      <Tooltip>
        <TooltipTrigger>
          <span>
            <Link
              href={link}
              className="flex items-center justify-center gap-2 border border-neutral-300 rounded py-1 px-2 hover:bg-clerk-primary/10"
            >
              <Icon className="text-neutral-600" /> {count}
            </Link>
          </span>
        </TooltipTrigger>
        <TooltipContent side="top" sideOffset={4}>
          <p className="p-1 text-sm text-clerk-accent rounded bg-clerk-background border">
            {label}
          </p>
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  );
};

interface ButtonProps {
  size: string;
  variant: "secondary" | "primary" | "danger";
  onClick: () => void;
}

interface EditableFieldProps {
  value: string;
  onValueChange: (value: string) => void;
  InputComponent: (props: {
    value: string;
    onChange: (event: React.ChangeEvent<HTMLInputElement>) => void;
    disabled: boolean;
  }) => ReactElement;
  className?: string;
}

const EditableField: React.FC<EditableFieldProps> = ({
  value,
  onValueChange,
  InputComponent,
  className,
}) => {
  const [edit, setEdit] = useState(false);
  const [inputValue, setInputValue] = useState(value ?? "");

  useEffect(() => {
    setInputValue(value);
  }, [value]);

  return (
    <div className={`flex gap-2 items-center w-full ${className}`}>
      <InputComponent
        value={inputValue}
        disabled={!edit}
        onChange={(e) => setInputValue(e.target.value)}
      />

      {!edit && (
        <Button size="xs" variant="secondary" onClick={() => setEdit(!edit)}>
          Edit
        </Button>
      )}

      {edit && (
        <>
          <Button
            size="xs"
            variant="primary"
            onClick={() => {
              setEdit(false);
              onValueChange(inputValue);
            }}
          >
            Save
          </Button>
          <Button
            size="xs"
            variant="danger"
            onClick={() => {
              setEdit(false);
              setInputValue(value);
            }}
          >
            Cancel
          </Button>
        </>
      )}
    </div>
  );
};

interface InputProps {
  value: string;
  onClick: (value: string) => void;
  error?: boolean;
  className?: string;
}

export const InputTextField: React.FC<InputProps> = ({
  value,
  onClick,
  className,
}) => {
  return (
    <EditableField
      value={value}
      onValueChange={onClick}
      className={className}
      InputComponent={({ value, onChange, disabled }) => (
        <input
          type="text"
          className="font-semibold border border-neutral-300 rounded py-1 px-2"
          value={value}
          disabled={disabled}
          onChange={onChange}
        />
      )}
    />
  );
};

export const InputDateField: React.FC<InputProps> = ({
  value,
  onClick,
  className,
}) => {
  return (
    <EditableField
      value={value}
      onValueChange={onClick}
      className={className}
      InputComponent={({ value, onChange, disabled }) => (
        <input
          type="date"
          className="font-semibold border border-neutral-300 rounded py-1 px-2"
          value={value}
          disabled={disabled}
          onChange={onChange}
        />
      )}
    />
  );
};
