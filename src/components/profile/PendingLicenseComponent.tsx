"use client";
import React, { useState } from "react";
import AddLicense from "@/components/license/AddLicense";
import { cn } from "@/lib/utils";
import AllLicenses from "./license/AllLicenses";
import Link from "next/link";
import { useMyProfile } from "@/hooks/providers/useMyProfile";
import PageContainer from "@/components/ui/Page/PageContainer";
import AddPetLicenseRequest from "@/components/resident/AddPetLicenseRequest";
import PetLicenseDisclaimerMessage from "../banners/PetLicenseDisclaimerMessage";
import PendingLicenses from "./license/PendingLicenses";

const PendingLicenseComponent = ({
  entity,
  entityType,
}: {
  entity: any;
  entityType: string;
}) => {
  const { hasPermissions } = useMyProfile();
  const [modalIsOpen, setModalIsOpen] = useState(false);

  if (entity) {
    const active = entity[entityType].active;
    return (
      <div className="mx-auto flex w-full flex-col items-start justify-start gap-10 py-10 lg:container lg:py-0">
        {/* Alert */}
        {/* {hasPermissions(["resident"]) && <PetLicenseDisclaimerMessage />} */}

        {/* Add New License */}
        {active ? (
          <NewLicenseButton
            setModalIsOpen={setModalIsOpen}
            entityId={entity.entityId}
            entityType={entityType}
          />
        ) : (
          <p className="w-fit rounded border border-red-300 bg-red-50 px-2 font-bold text-red-600 shadow">
            Account Inactive. Cannot make changes at this time.
          </p>
        )}

        <div className="flex w-full flex-col gap-10 2xl:flex-row">
          {/* License List */}
          <div className="flex w-full flex-col gap-12">
            <PendingLicenses licenses={entity?.license ?? []} />
          </div>

          {/* Add Pet License Request */}
          {hasPermissions(["resident"]) && (
            <PageContainer className="h-fit w-full rounded-sm lg:max-w-sm">
              <AddPetLicenseRequest
                title="Not seeing your license?"
                description="Request to add your existing pet license to your account."
              />
            </PageContainer>
          )}
        </div>

        {/* Modal */}
        {modalIsOpen && (
          <AddLicense
            isOpen={modalIsOpen}
            onClose={() => setModalIsOpen(false)}
            entityId={entity[entityType].entityId}
          />
        )}
      </div>
    );
  }

  return null;
};

export default PendingLicenseComponent;

const NewLicenseButton = ({
  setModalIsOpen,
  entityId,
  entityType,
}: {
  setModalIsOpen: (value: boolean) => void;
  entityId: string;
  entityType: string;
}) => {
  const { hasPermissions } = useMyProfile();

  if (!entityType.includes("individual")) return null;

  if (hasPermissions(["resident"])) {
    return (
      <Link
        href={`/licenses/newApplication`}
        className="block w-full rounded border bg-blue-600 px-4 py-1 text-center font-semibold text-blue-50 transition-colors hover:bg-blue-400 hover:font-medium  sm:w-fit"
      >
        New License
      </Link>
    );
  }

  if (hasPermissions(["super-admin"])) {
    return (
      <div className={cn("flex w-full flex-col gap-2")}>
        <button
          className={cn(
            "block w-full rounded border bg-blue-600 px-4 py-1 text-center font-semibold text-blue-50 transition-colors hover:bg-blue-200 hover:font-medium hover:text-blue-900 sm:w-fit",
          )}
          onClick={() => setModalIsOpen(true)}
        >
          New License
        </button>
      </div>
    );
  }

  return null;
};
