import Accordion from '@/components/ui/Accordion2';

interface ContactProps {
  contacts: any[]
}

const ContactResults = ({ contacts }:ContactProps) => {
  console.log(contacts)
  return (
    <Accordion title='Contacts'>
      {contacts && contacts.map((contact) => {
        return (
          <div key={contact.fieldName} className='grid grid-cols-[120px_1fr] text-sm'>
            <p>{contact.label}</p>
            <p className="font-semibold text-neutral-800">{contact.value}</p>
          </div>
        )
      })}
    </Accordion>
  )
}

export default ContactResults