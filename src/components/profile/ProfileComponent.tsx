"use client";
import PageContainer from "@/components/ui/Page/PageContainer";
import { useEntity } from "@/hooks/providers/useEntity";
import IndividualQuickView from "./IndividualQuickView";
import { useMyProfile } from "@/hooks/providers/useMyProfile";
import DogQuickView from "./DogQuickView";
import Link from "next/link";
import { useParams } from "next/navigation";
import { NoteData } from "@/hooks/api/useProfiles";
import { useProfileNotes } from "./notes/useProfileNotes";
import ProfileBuilderFactory from "../builders/profileBuilder/ProfileBuilderFactory";

type EntityType = "dog" | "individual";

export default function ProfileComponent() {
  const { entity, entityType, entityIsFetching, entityRefetch } = useEntity();
  const { hasPermissions } = useMyProfile();


  if (entity) {
    return (
      <div className="flex flex-col gap-6 lg:container lg:mx-auto xl:flex-row">
        <PageContainer className="w-full">
          <ProfileBuilderFactory entity={entity[entityType]} profileType={entityType as EntityType} entityIsFetching={entityIsFetching} entityRefetch={entityRefetch} />
        </PageContainer>

        {hasPermissions(["super-admin"]) && (
          <PageContainer className=" mb-20 flex h-fit shrink-0 flex-col gap-10 xl:mb-0 xl:w-full xl:max-w-[300px]">
            <RecentNotes />

            <div>
              <div className="text-xl font-semibold text-neutral-700">
                Quick Actions
              </div>
              {entityType === "individual" && <IndividualQuickView />}
              {entityType === "dog" && <DogQuickView />}
            </div>
          </PageContainer>
        )}
      </div>
    );
  }

  return (
    <div className="container mx-auto ">No Profile Information Available</div>
  );
}

const RecentNotes = () => {
  const { notes } = useProfileNotes();
  const { entityId, entityType } = useParams();
  const recentNotes: NoteData[] = notes ? notes.slice(0, 5) : [];

  return (
    <div className="flex flex-col gap-2">
      <div className="text-xl font-semibold text-neutral-700">Recent Notes</div>
      {recentNotes.length > 0 ? (
        recentNotes.map((note) => (
          <Link
            key={note.entityId}
            href={`/profile/${entityType}/${entityId}?tab=notes&noteId=${note.entityId}`}
            className="border-y py-1 text-sm"
          >
            <p className="font-medium text-neutral-800">
              {note.note ?? "No Content"}
            </p>
            <small className="line-clamp-1 text-xs text-neutral-500">
              by {note.createdBy ?? "N/A"}
            </small>
          </Link>
        ))
      ) : (
        <p className="text-sm text-neutral-500">No Notes Available</p>
      )}
      <Link
        href={`/profile/${entityType}/${entityId}?tab=notes`}
        className="text-sm italic text-blue-600"
      >
        ...Go To Notes
      </Link>
    </div>
  );
};
