import Image from "next/image";
import Accordion from "../ui/Accordion2";
import { AiFillHome } from "react-icons/ai";
import { Address } from "@/types/AddressType";
import Link from "next/link";

type PropertyResultsProps = {
  addresses: Address[];
};

const PropertyResults = ({ addresses }: PropertyResultsProps) => {
  return (
    <Accordion title="Addresses">
      {addresses &&
        addresses.map((address) => {

          console.log(address)

          return (
            <div
              className="flex items-center mb-10 last-of-type:mb-0"
              key={address.entityId}
            >
              <div className="flex items-center justify-center shadow bg-red-300 rounded relative w-20 h-20 shrink-0">
                <Image
                  src="/images/icons/address.png"
                  alt="home"
                  fill
                  className="p-2"
                />
              </div>
              <div className="flex flex-col ml-4 w-full h-20">
                <p className="font-semibold text-neutral-800 text-xl flex items-center gap-4">
                  {/* {address.address} {address.city}{" "}
                  {address.unit && address.unit}, {address.state} {address.zip}
                  <span className="text-sm text-neutral">
                    ({address.addressType})
                  </span> */}
                </p>
                <p className="text-sm text-neutral-400">
                  Section: null | Parcel: null
                </p>
                <p className="text-sm mt-auto">More Details Coming Soon</p>
              </div>

              {/* Button */}
              <Link
                href={`/property/${address.entityId}`}
                className="whitespace-nowrap shrink-0 bg-sky-200 rounded py-2 px-2 flex items-center gap-2"
              >
                <AiFillHome /> View Property
              </Link>
            </div>
          );
        })}
    </Accordion>
  );
};

export default PropertyResults;
