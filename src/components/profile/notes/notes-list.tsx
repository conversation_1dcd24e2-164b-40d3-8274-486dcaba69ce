import { NoteData } from "@/hooks/api/useProfiles";
import { useProfileNotes } from "./useProfileNotes";
import {
  <PERSON>,
  <PERSON><PERSON><PERSON>nt,
  <PERSON>Footer,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { cn } from "@/lib/utils";
import { Edit, Pin, Trash2 } from "lucide-react";
import { Badge } from "@/components/ui/badge";

const NotesList = () => {
  const {
    notes,
    setEditingNote,
    setIsEditModalOpen,
    setNoteToDelete,
    setIsDeleteDialogOpen,
    handlePinNote,
    focusedNoteId,
  } = useProfileNotes();

  return (
    <div className="space-y-8">
      {notes?.map((note) => (
        <Card
          key={note.entityId}
          id={"note-" + note.entityId}
          className={cn(
            "bg-card hover:bg-accent relative border bg-white transition-colors duration-200",
            focusedNoteId === note.entityId
              ? "border-blue-500 bg-blue-50"
              : "border-transparent",
          )}
        >
          <Button
            className={cn(
              "absolute left-0 top-0 flex h-8 w-8 -translate-x-1/3 -translate-y-1/3 transform items-center justify-center rounded-full p-2 shadow",
              note?.pinned === "true"
                ? " bg-blue-600 text-white"
                : "bg-white text-neutral-400",
            )}
            onClick={(e) => {
              e.stopPropagation();
              handlePinNote(note.entityId);
            }}
          >
            <Pin className="size-full" />
          </Button>
          <CardHeader>
            <CardTitle className="flex items-center justify-between text-sm font-medium">
              <span>{new Date(note.createdDate).toLocaleString()}</span>
              <span className="text-muted-foreground">{note.createdBy}</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="mb-2">{note.note}</p>
            <div className="flex flex-wrap gap-2">
              {(Array.isArray(note.noteTags) ? note.noteTags : []).map(
                (tag: string) => (
                  <Badge key={tag} variant="outline" className="text-xs">
                    {tag}
                  </Badge>
                ),
              )}
            </div>
          </CardContent>
          <CardFooter className="flex justify-end space-x-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => {
                const noteBeforeEdit = {
                  ...note,
                  noteTags: Array.isArray(note.noteTags)
                    ? note.noteTags.join(", ")
                    : note.noteTags || "",
                };
                setEditingNote(noteBeforeEdit);
                setIsEditModalOpen(true);
              }}
            >
              <Edit className="mr-2 h-4 w-4" />
              Edit
            </Button>

            <Button
              variant="secondary"
              size="sm"
              onClick={() => {
                setNoteToDelete(note.entityId);
                setIsDeleteDialogOpen(true);
              }}
            >
              <Trash2 className="mr-2 h-4 w-4" />
              Delete
            </Button>
          </CardFooter>
        </Card>
      ))}
    </div>
  );
};

export default NotesList;
