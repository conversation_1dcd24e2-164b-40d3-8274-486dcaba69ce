"use client";
import { createContext, useContext, useEffect, useState } from "react";
import type { ReactNode } from "react";
import {
  NoteData,
  useAddNote,
  useDeleteNote,
  useUpdateNote,
} from "@/hooks/api/useProfiles";
import { DateRange } from "react-day-picker";
import { sortNotes } from "./notesHelper";
import { useAtom } from "jotai";
import { toastAtom } from "@/components/ui/toast/toast";
import { useEntity } from "@/hooks/providers/useEntity";
import { Dog } from "@/components/license/cards/LicenseType";
import { useSearchParams } from "next/navigation";

type NewNoteType = {
  note: string;
  noteTags: string;
  pinned: boolean;
  associations: {
    entityId: string;
    entityType: string;
  }[];
};

type NoteDataEdit = {
  entityId: string;
  entityType: string;
  events: any[];
  note: string;
  createdBy: string;
  createdDate: string;
  lastModifiedBy: string;
  lastModifiedDate: string;
  rejectedFields: any[];
  noteTags: string;
  pinned: boolean | string;
};

type ProfileNotesContextType = {
  notes: NoteData[];
  setNotes: any;
  addNote: any;
  possibleAssociations: {
    entityId: string;
    entityType: string;
    label: string;
  }[];

  // Add Note
  newNote: NewNoteType;
  setNewNote: (note: NewNoteType) => void;
  handleAddNote: () => void;

  // Editing Note
  editingNote: NoteDataEdit | null;
  setEditingNote: (note: NoteDataEdit | null) => void;
  isEditModalOpen: boolean;
  setIsEditModalOpen: (isOpen: boolean) => void;
  handleEditNote: () => void;
  handlePinNote: (noteId: string) => void;
  updateNote: any;

  // Deleting Note
  noteToDelete: string | null;
  setNoteToDelete: (noteId: string | null) => void;
  isDeleteDialogOpen: boolean;
  setIsDeleteDialogOpen: (isOpen: boolean) => void;
  handleDeleteNote: () => void;
  deleteNote: any;

  // Filters
  filter: FilterProp;
  setFilter: (filter: FilterProp) => void;

  // Focused Note
  focusedNoteId: string | null;
  setFocusedNoteId: (noteId: string | null) => void;
};

const ProfileNotesContext = createContext<ProfileNotesContextType | undefined>(
  undefined,
);

type FilterProp = {
  search: string;
  tag: string | null;
  selectedAuthor: string | null;
  selectedDate: DateRange | undefined;
};

export const ProfileNotesProvider = ({ children }: { children: ReactNode }) => {
  // Pull in existing entity data
  const { entity, entityRefetch, entityType } = useEntity();

  // Search Params
  const paramfocusedNoteId = useSearchParams().get("noteId");

  // Toast Error
  const [_, setToast] = useAtom(toastAtom);

  // States
  const [notes, setNotes] = useState<NoteData[]>(entity?.entityNote ?? []);
  const [filter, setFilter] = useState<FilterProp>({
    search: "",
    tag: null,
    selectedAuthor: null,
    selectedDate: {
      from: undefined,
      to: undefined,
    },
  });

  const [newNote, setNewNote] = useState<NewNoteType>({
    note: "",
    noteTags: "",
    pinned: false,
    associations: [],
  });
  const [editingNote, setEditingNote] = useState<any | null>(null);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [noteToDelete, setNoteToDelete] = useState<string | null>(null);
  const [focusedNoteId, setFocusedNoteId] = useState<string | null>(null);

  // Mutations
  const addNote = useAddNote();
  const updateNote = useUpdateNote();
  const deleteNote = useDeleteNote();

  const handleAddNote = () => {
    const tagsArray = newNote.noteTags
      .split(",")
      .map((tag) => tag.trim())
      .filter((tag) => tag !== "");

    const newNoteObject = {
      note: newNote.note,
      noteTags: tagsArray,
      associations: [
        {
          entityId: entity[entityType].entityId as string,
          entityType: entityType as string,
        },
        ...newNote.associations,
      ],
      pinned: newNote.pinned ? "true" : "false",
    };

    addNote.mutate(newNoteObject, {
      onSuccess: (data: NoteData) => {
        const updatedNotes = sortNotes([...notes, data]);
        setNotes(updatedNotes);
        setFocusedNoteId(data.entityId);
        setNewNote({
          note: "",
          noteTags: "",
          pinned: false,
          associations: [],
        });
        setToast({
          label: "Note added successfully",
          message: "The note has been added successfully.",
          status: "success",
        });
      },
      onError: () => {
        setToast({
          label: "Failed to add note",
          message:
            "An error occurred while adding the note. Please try again later.",
          status: "error",
        });
      },
    });
  };

  const handleEditNote = () => {
    if (editingNote) {
      const updatedTags = (editingNote.noteTags || "")
        .split(",")
        .map((tag: string) => tag.trim())
        .filter((tag: string) => tag !== "");

      updateNote.mutate(
        {
          entityId: editingNote.entityId,
          body: {
            note: editingNote.note,
            noteTags: updatedTags,
            pinned: editingNote.pinned === "true" ? true : false,
          },
        },
        {
          onSuccess: (data: NoteData) => {
            const updatedNotes = sortNotes(
              notes.map((note) =>
                note.entityId === data.entityId ? data : note,
              ),
            );

            setNotes(updatedNotes);
            setEditingNote(null);
            setFocusedNoteId(editingNote.entityId);
            setIsEditModalOpen(false);
            setToast({
              label: "Note updated successfully",
              message: "The note has been updated successfully.",
              status: "success",
            });
            setFocusedNoteId(data.entityId);
          },
          onError: () => {
            setToast({
              label: "Failed to update note",
              message:
                "An error occurred while updating the note. Please try again later.",
              status: "error",
            });
          },
        },
      );
    }
  };

  const handleDeleteNote = () => {
    if (noteToDelete !== null) {
      deleteNote.mutate(noteToDelete, {
        onSuccess: () => {
          const updatedNotes = notes.filter(
            (note) => note.entityId !== noteToDelete,
          );
          setNotes(updatedNotes);
          setIsDeleteDialogOpen(false);
          setNoteToDelete(null);
          setToast({
            label: "Note deleted successfully",
            message: "The note has been deleted successfully.",
            status: "success",
          });
        },
        onError: (error: any) => {
          setToast({
            label: "Failed to delete note",
            message:
              "An error occurred while deleting the note. Please try again later.",
            status: "error",
          });
        },
      });
    }
  };

  const handlePinNote = (noteId: string) => {
    const note = notes.find((note) => note.entityId === noteId);
    if (note) {
      updateNote.mutate(
        {
          entityId: noteId,
          body: {
            pinned: note.pinned === "true" ? false : true,
          },
        },
        {
          onSuccess: () => {
            const updatedNotes = notes.map((note) =>
              note.entityId === noteId
                ? { ...note, pinned: note.pinned === "true" ? "false" : "true" }
                : note,
            );
            setNotes(sortNotes(updatedNotes));
            setFocusedNoteId(noteId);
            setToast({
              label: "Note updated successfully",
              message: "The note has been updated successfully.",
              status: "success",
            });
          },
          onError: () => {
            setToast({
              label: "Failed to update note",
              message:
                "An error occurred while updating the note. Please try again later.",
              status: "error",
            });
          },
        },
      );
    }
  };

  const possibleAssociations: {
    entityId: string;
    entityType: string;
    label: string;
  }[] = (() => {
    switch (entityType) {
      case "dog":
        return (
          entity["individual"]?.map((individual: any) => ({
            entityId: individual.entityId,
            entityType: "individual",
            label: `${individual.firstName} ${individual.lastName} (Owner)`,
          })) || []
        );
      case "individual":
        return (
          entity["dog"]?.map((dog: Dog) => ({
            entityId: dog.entityId,
            entityType: "dog",
            label: `${dog.dogName} (Dog)`,
          })) || []
        );
      default:
        return [];
    }
  })();

  // Update notes when entity changes.
  useEffect(() => {
    const sortedNotes = sortNotes(
      (entity["entityNote"] || []).map((note: any) => ({
        ...note,
        noteTags: Array.isArray(note.noteTags) ? note.noteTags : [],
      })),
    );
    setNotes(sortedNotes);
  }, [entity]);

  // Focus on note when noteId is in the URL
  useEffect(() => {
    if (paramfocusedNoteId) {
      setFocusedNoteId(paramfocusedNoteId);
    }
  }, [paramfocusedNoteId]);

  // Scrool to focusedNoteId
  useEffect(() => {
    if (focusedNoteId) {
      console.log(focusedNoteId);
      const noteElement = document.getElementById("note-" + focusedNoteId);
      if (noteElement) {
        noteElement.scrollIntoView({
          behavior: "smooth",
          block: "center",
          inline: "nearest",
        });
      }
    }
  }, [focusedNoteId]);

  return (
    <ProfileNotesContext.Provider
      value={{
        notes,
        setNotes,
        possibleAssociations,

        // New Notes
        newNote,
        setNewNote,
        addNote,

        // Edit Notes
        updateNote,
        editingNote,
        setEditingNote,
        isEditModalOpen,
        setIsEditModalOpen,

        // Delete Notes
        deleteNote,
        isDeleteDialogOpen,
        setIsDeleteDialogOpen,
        noteToDelete,
        setNoteToDelete,

        // Filters
        filter,
        setFilter,

        focusedNoteId,
        setFocusedNoteId,

        // Functions
        handleAddNote,
        handleEditNote,
        handleDeleteNote,
        handlePinNote,
      }}
    >
      {children}
    </ProfileNotesContext.Provider>
  );
};

export const useProfileNotes = () => {
  const context = useContext(ProfileNotesContext);
  if (!context) {
    throw new Error(
      "useProfileNotes must be used within a ProfileNotes Provider",
    );
  }
  return context;
};
