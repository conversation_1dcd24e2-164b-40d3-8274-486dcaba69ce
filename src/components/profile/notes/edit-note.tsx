import React from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>onte<PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  Di<PERSON>Footer,
} from "@/components/ui/dialog";
import { Textarea } from "@/components/ui/textarea";
import { Input } from "@/components/ui/input";
import { Pin, Tag } from "lucide-react";
import { Checkbox } from "@/components/ui/checkbox";
import { useProfileNotes } from "./useProfileNotes";
import { Button } from "@/components/ui/button";

export default function EditNote() {
  const {
    editingNote,
    setEditingNote,
    isEditModalOpen,
    setIsEditModalOpen,
    handleEditNote,
  } = useProfileNotes();

  return (
    <Dialog open={isEditModalOpen} onOpenChange={setIsEditModalOpen}>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>Edit Note</DialogTitle>
        </DialogHeader>
        <div className="grid gap-4 py-4">
          <Textarea
            placeholder="Enter note content..."
            value={editingNote?.note || ""}
            onChange={(e) =>
              setEditingNote(
                editingNote ? { ...editingNote, note: e.target.value } : null,
              )
            }
            className="mb-2"
          />
          <div className="flex items-center">
            <Tag className="mr-2 h-4 w-4" />
            <Input
              type="text"
              placeholder="Enter tags (comma-separated)"
              value={editingNote?.noteTags || ""} // Use string directly
              onChange={(e) =>
                setEditingNote(
                  editingNote
                    ? { ...editingNote, noteTags: e.target.value }
                    : null,
                )
              }
            />
          </div>
        </div>
        {/* Pinned */}
        <div className="flex items-center gap-3">
          <Pin className="size-4" />
          <div className="flex items-center gap-2">
            <Checkbox
              id="pinned"
              checked={editingNote?.pinned === "true" || false}
              onCheckedChange={(checked: boolean) =>
                setEditingNote(
                  editingNote
                    ? {
                        ...editingNote,
                        pinned: checked === true ? "true" : "false",
                      }
                    : null,
                )
              }
            />

            <label htmlFor="pinned">Pin this note</label>
          </div>
        </div>
        <DialogFooter>
          <Button variant="outline" onClick={() => setIsEditModalOpen(false)}>
            Cancel
          </Button>
          <Button onClick={handleEditNote}>Save Changes</Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
