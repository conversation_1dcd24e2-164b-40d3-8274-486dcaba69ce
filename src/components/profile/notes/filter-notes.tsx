import React from "react";
import { useProfileNotes } from "./useProfileNotes";
import { extractUniqueAuthors, extractUniqueTags } from "./notesHelper";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { Button } from "@/components/ui/button";
import { format } from "date-fns";
import { CalendarIcon } from "lucide-react";
import { Calendar } from "@/components/ui/calendar";

export default function FilterNotes() {
  const { notes, filter, setFilter } = useProfileNotes();

  const uniqueTags = extractUniqueTags(notes);
  const uniqueAuthors = extractUniqueAuthors(notes);

  return (
    <div className="mb-6 flex flex-wrap gap-4">
      <div className="flex-grow">
        <Input
          type="text"
          placeholder="Search notes..."
          value={filter.search}
          onChange={(e) => {
            setFilter({
              ...filter,
              search: e.target.value,
            });
          }}
          className="w-full"
        />
      </div>
      <Select
        onValueChange={(value) =>
          setFilter({
            ...filter,
            tag: value === "all" ? null : value,
          })
        }
      >
        <SelectTrigger className="line-clamp-1 md:w-[180px]">
          <SelectValue placeholder="Filter by tag" />
        </SelectTrigger>
        <SelectContent>
          <SelectItem value="all">All Tags</SelectItem>
          {uniqueTags.map((tag) => (
            <SelectItem key={tag} value={tag}>
              {tag}
            </SelectItem>
          ))}
        </SelectContent>
      </Select>
      <Select
        onValueChange={(value) =>
          setFilter({
            ...filter,
            selectedAuthor: value === "all" ? null : value,
          })
        }
      >
        <SelectTrigger className="line-clamp-1 md:w-[180px]">
          <SelectValue placeholder="Filter by author" />
        </SelectTrigger>
        <SelectContent>
          <SelectItem value="all">All Authors</SelectItem>
          {uniqueAuthors?.map((author) => (
            <SelectItem key={author} value={author}>
              {author}
            </SelectItem>
          ))}
        </SelectContent>
      </Select>
      <Popover>
        <PopoverTrigger asChild className="md:w-[280px] w-full">
          <Button
            variant="outline"
            className="line-clamp-1 pl-3 text-left font-normal"
          >
            {filter.selectedDate?.from ? (
              filter.selectedDate.to ? (
                `${format(filter.selectedDate.from, "PPP")} - ${format(
                  filter.selectedDate.to,
                  "PPP",
                )}`
              ) : (
                `${format(filter.selectedDate.from, "PPP")} - No Expiration`
              )
            ) : (
              <span>Pick a date</span>
            )}
            <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
          </Button>
        </PopoverTrigger>
        <PopoverContent className="w-auto p-0" align="start">
          <Calendar
            mode="range"
            selected={filter.selectedDate}
            onSelect={(range) => {
              setFilter({
                ...filter,
                selectedDate: {
                  from: range?.from || undefined,
                  to: range?.to || undefined,
                },
              });
            }}
            initialFocus
            numberOfMonths={2}
          />
        </PopoverContent>
      </Popover>

      <Button
        className="ml-auto"
        variant="outline"
        onClick={() => {
          setFilter({
            search: "",
            tag: null,
            selectedAuthor: null,
            selectedDate: {
              from: undefined,
              to: undefined,
            },
          });
        }}
      >
        Clear Filters
      </Button>
    </div>
  );
}
