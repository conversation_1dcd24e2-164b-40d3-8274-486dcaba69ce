import { useForm } from "react-hook-form";

import { Dog } from "@/types/DogType";
import {
  EditDial<PERSON>,
  EditDialogFooter,
  createFormData,
} from "@/app/(app)/(realm)/(protected)/(resident)/(registered)/resident/[entityType]/[entityId]/components/modals/EditDialog";
import { useUpdateEntityDogProfile } from "@/hooks/api/useProfiles";
import { useState } from "react";
import { useAtom } from "jotai";
import { toastAtom } from "@/components/ui/toast/toast";
import { useQueryClient } from "@tanstack/react-query";
import {
  Comment,
  DogRabiesVaccinationDocument,
  DogRabiesVaccinationExemptionDocument,
  ExemptVaccine,
  RabiesAdministeredDate,
  RabiesBrand,
  RabiesDueDate,
  RabiesLotExpirationDate,
  RabiesLotNumber,
  RabiesProducer,
  RabiesTagNumber,
  RabiesVeterinary,
} from "@/components/dialog/EditFields";
import { format, parseISO } from "date-fns";
import { useMyProfile } from "@/hooks/providers/useMyProfile";

export const EditDogVaccines = ({
  dog,
  admin = true,
}: {
  dog: Dog;
  admin?: boolean;
}) => {
  
  function formatDate(date: string) {
    return format(parseISO(date), "yyyy-MM-dd");
  }

  const { hasPermissions } = useMyProfile();
  const permitted = hasPermissions(['super-admin']);

  type FormValues = {
    veterinaryName: string;
    vaccineDatesExempt: boolean;
    rabiesTagNumber: string;
    vaccineProducer: string;
    vaccineBrand: string;
    vaccineAdministeredDate: string;
    vaccineDueDate: string;
    vaccineLotNumber: string;
    vaccineLotExpirationDate: string;
    dogRabiesVaccinationExemptionDocument: any;
    dogRabiesVaccinationDocument: any;
    comment?: string;
  };

  const initialValues: FormValues = {
    veterinaryName: dog?.veterinaryName || "",
    vaccineDatesExempt: dog?.vaccineDatesExempt ?? false,
    rabiesTagNumber: dog?.rabiesTagNumber || "",
    vaccineProducer: dog?.vaccineProducer || "",
    vaccineBrand: dog?.vaccineBrand || "",
    vaccineAdministeredDate: dog?.vaccineAdministeredDate
      ? formatDate(dog?.vaccineAdministeredDate)
      : "",
    vaccineDueDate: dog?.vaccineDueDate ? formatDate(dog?.vaccineDueDate) : "",
    vaccineLotNumber: dog?.vaccineLotNumber || "",
    vaccineLotExpirationDate: dog?.vaccineLotExpirationDate
      ? formatDate(dog?.vaccineLotExpirationDate)
      : "",
    dogRabiesVaccinationExemptionDocument:
      dog?.documents?.find(
        (doc) => doc.key === "dogRabiesVaccinationExemptionDocument",
      ) || null,
    dogRabiesVaccinationDocument:
      dog?.documents?.find(
        (doc) => doc.key === "dogRabiesVaccinationDocument",
      ) || null,
    comment: "",
  };

  console.log(initialValues);
  console.log(dog);

  const updateDogProfile = useUpdateEntityDogProfile();

  const {
    watch,
    reset,
    control,
    register,
    handleSubmit,
    formState: { errors, isDirty, isValid },
    setError
  } = useForm<FormValues>({
    defaultValues: initialValues,
    mode: "onChange",
  });

  const watchedValues = watch();
  const isVaccineExempt = watchedValues.vaccineDatesExempt;

  const [isOpen, setIsOpen] = useState(false);
  const [, setToast] = useAtom(toastAtom);
  const queryClient = useQueryClient();

  const handleCancel = () => {
    reset(initialValues);
    setIsOpen(false);
  };

  const handleSave = (data: any) => {
    console.log(data);
    const filterOnlyChanged: any = Object.keys(data).reduce(
      (acc: any, key: any) => {
        if (data[key] !== (initialValues as any)[key] || key === "comment") {
          acc[key] = data[key];
        }
        return acc;
      },
      {},
    );

    console.log(filterOnlyChanged);

    const formData = createFormData(filterOnlyChanged);

    updateDogProfile.mutate(
      {
        entityId: dog.entityId,
        body: formData,
      },
      {
        onSuccess: () => {
          queryClient.invalidateQueries();
          reset(data);
          setToast({
            status: "success",
            label: "Dog Updated",
            message: "Successfully Updated Dog Identification",
          });
          setIsOpen(false);
        },
        onError: (error: any) => {
          console.log(error);
          setToast({
            status: "error",
            label: "Error Updating Profile",
            message: error?.response?.data?.message,
          });
        },
      },
    );
  };

  const containsField = (field: string) =>
    dog?.rejectedFields?.includes(field) || false;

  return (
    <EditDialog
      title="Edit Dog Rabies Information"
      isOpen={isOpen}
      setIsOpen={setIsOpen}
    >
      <form className="flex flex-col gap-4" onSubmit={handleSubmit(handleSave)}>
        <RabiesVeterinary
          register={register}
          errors={errors}
          flag={containsField("veterinaryName")}
        />
        <ExemptVaccine control={control} />
        {isVaccineExempt ? (
          <DogRabiesVaccinationExemptionDocument
            control={control}
            required={permitted ? false : true}
            flag={containsField("dogRabiesVaccinationExemptionDocument")}
          />
        ) : (
          <>
            <RabiesTagNumber
              register={register}
              errors={errors}
              flag={containsField("rabiesTagNumber")}
            />
            <RabiesProducer
              control={control as any}
              errors={errors}
              flag={containsField("vaccineProducer")}
            />
            <RabiesBrand
              control={control as any}
              errors={errors}
              flag={containsField("vaccineBrand")}
            />
            <RabiesAdministeredDate
              register={register}
              errors={errors}
              flag={containsField("vaccineAdministeredDate")}
            />
            <RabiesDueDate
              register={register}
              errors={errors}
              flag={containsField("vaccineDueDate")}
            />
            <DogRabiesVaccinationDocument
              control={control}
              required={permitted ? false : true}
              flag={containsField("dogRabiesVaccinationDocument")}
            />
            <RabiesLotNumber
              register={register}
              errors={errors}
              flag={containsField("vaccineLotNumber")}
            />
            <RabiesLotExpirationDate
              register={register}
              errors={errors}
              flag={containsField("vaccineLotExpirationDate")}
            />
          </>
        )}

        <Comment register={register} />
        <EditDialogFooter handleCancel={handleCancel} disabled={!isDirty} />
      </form>
    </EditDialog>
  );
};

export default EditDogVaccines;
