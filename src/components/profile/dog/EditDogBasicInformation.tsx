import { useForm } from "react-hook-form";

import { Dog } from "@/types/DogType";
import {
  Edit<PERSON>ial<PERSON>,
  EditDialogFooter,
  createFormData,
} from "@/app/(app)/(realm)/(protected)/(resident)/(registered)/resident/[entityType]/[entityId]/components/modals/EditDialog";
import { useUpdateEntityDogProfile } from "@/hooks/api/useProfiles";
import { useState } from "react";
import { useAtom } from "jotai";
import { toastAtom } from "@/components/ui/toast/toast";
import { useQueryClient } from "@tanstack/react-query";
import { Document } from "@/types/DocumentType";
import {
  Comment,
  DogAlteration,
  DogName,
  DogSex,
  DogSpayedOrNeuteredDocument,
  DoxBirthDate,
  ExemptLicense,
  ServiceDogLicenseExemptionDocument,
} from "@/components/dialog/EditFields";

export const EditDogBasicInformation = ({
  dog,
  admin = true,
}: {
  dog: Dog;
  admin?: boolean;
}) => {
  const documents = dog?.documents || [];

  const rabiesExemptionDocument = documents.find(
    (document: Document) =>
      document.key === "dogRabiesVaccinationExemptionDocument",
  );

  const dogSpayedOrNeuteredDocument = documents.find(
    (document: Document) =>
      document.key === "dogSpayedOrNeuteredDocument",
  );

  type FormValues = {
    dogBirthDate: string;
    dogSex: string;
    dogSpayedOrNeutered: string;
    dogName: string;
    licenseExempt: boolean;
    dogRabiesVaccinationExemptionDocument: any;
    dogSpayedOrNeuteredDocument: any;
    comment: string;
  };

  const initialValues: FormValues = {
    dogBirthDate: dog?.dogBirthDate || "",
    dogSex: dog?.dogSex || "",
    dogSpayedOrNeutered: dog?.dogSpayedOrNeutered || "",
    dogName: dog?.dogName || "",
    licenseExempt: dog?.licenseExempt || false,
    dogRabiesVaccinationExemptionDocument: rabiesExemptionDocument || "",
    dogSpayedOrNeuteredDocument: dogSpayedOrNeuteredDocument || "",
    comment: "",
  };

  const updateDogProfile = useUpdateEntityDogProfile();

  const {
    watch,
    reset,
    control,
    register,
    handleSubmit,
    formState: { errors, isDirty, isValid },
  } = useForm<FormValues>({
    defaultValues: initialValues,
    mode: "onChange",
  });

  const watchedValues = watch();
  const isLicenseExempt = watchedValues.licenseExempt;
  console.log(watchedValues?.dogSpayedOrNeutered)

  const [isOpen, setIsOpen] = useState(false);
  const [, setToast] = useAtom(toastAtom);
  const queryClient = useQueryClient();

  const handleCancel = () => {
    reset(initialValues);
    setIsOpen(false);
  };

  const handleSave = (data: any) => {
    const filterOnlyChanged: any = Object.keys(data).reduce(
      (acc: any, key: any) => {
        if (data[key] !== (initialValues as any)[key]) {
          acc[key] = data[key];
        }
        return acc;
      },
      {},
    );

    const formData = createFormData(filterOnlyChanged);

    updateDogProfile.mutate(
      {
        entityId: dog.entityId,
        body: formData,
      },
      {
        onSuccess: () => {
          queryClient.invalidateQueries();
          reset(data);
          setToast({
            status: "success",
            label: "Dog Updated",
            message: "Successfully Updated Dog Identification",
          });
          setIsOpen(false);
        },
        onError: (error: any) => {
          console.log(error);
          setToast({
            status: "error",
            label: "Error Updating Profile",
            message: error?.response?.data?.message,
          });
        },
      },
    );
  };

  const containsField = (field: string) =>
    dog?.rejectedFields?.includes(field) || false;

  return (
    <EditDialog
      title="Edit Dog Basic Information"
      isOpen={isOpen}
      setIsOpen={setIsOpen}
    >
      <form className="flex flex-col gap-4" onSubmit={handleSubmit(handleSave)}>
        <DogName
          register={register}
          errors={errors}
          flag={containsField("dogName")}
        />
        <DoxBirthDate
          register={register}
          errors={errors}
          flag={containsField("dogBirthDate")}
        />
        <DogSex
          control={control}
          errors={errors}
          flag={containsField("dogSex")}
        />
        <DogAlteration
          control={control}
          dogSex={watchedValues?.dogSex as any}
          flag={containsField("dogSpayedOrNeutered")}
        />
        {watchedValues?.dogSpayedOrNeutered === "yes" && (
            <DogSpayedOrNeuteredDocument
              control={control}
              flag={containsField("dogSpayedOrNeuteredDocument")}
              required={
                admin 
                  ? false
                  : true
              }
            />
        )}
        <ExemptLicense control={control} />
        {isLicenseExempt && (
          <ServiceDogLicenseExemptionDocument
            control={control}
            flag={containsField("dogRabiesVaccinationExemptionDocument")}
            required={
              admin
                ? false
                : true
            }
          />
        )}
        <Comment register={register} />
        <EditDialogFooter handleCancel={handleCancel} disabled={!isDirty} />
      </form>
    </EditDialog>
  );
};

export default EditDogBasicInformation;
