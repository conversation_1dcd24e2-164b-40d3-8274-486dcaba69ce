import React, { useState } from "react";
import { formatDate, isVaccineValid } from "@/components/license/licenseHelper";
import { Button } from "@/components/ui/button";
import { cn } from "@/lib/utils";
import { BiEditAlt } from "react-icons/bi";
import { EditVaccines } from "./EditVaccines";
import type { Dog } from "@/types/DogType";

interface VaccinationInfoProps {
  title: string;
  children: React.ReactNode;
}

const VaccinationInfo = ({ title, children }: VaccinationInfoProps) => (
  <li className="flex flex-row items-center gap-4">
    <div className="w-[100px] text-neutral-600">{title}</div>
    <div>{children}</div>
  </li>
);

interface DogVaccinesProps {
  entityId: string;
  dog: Dog;
  admin?: boolean;
}

export default function DogVaccines({
  entityId,
  dog,
  admin = false,
}: DogVaccinesProps) {
  const [canEdit, setCanEdit] = useState(false);

  const {
    vaccineProducer = "N/A",
    vaccineAdministeredDate: unformattedVaccineAdministeredDate = null,
    vaccineDueDate: unformattedVaccineDueDate = "N/A",
    vaccineLotNumber = "N/A",
    vaccineLotExpirationDate: unformattedVaccineLotExpirationDate = null,
    vaccineDatesExempt: vaccineExempt = false,
  } = dog || {};

  console.log("vaccineDueDate", unformattedVaccineDueDate);

  const vaccineAdministeredDate = unformattedVaccineAdministeredDate
    ? formatDate(unformattedVaccineAdministeredDate)
    : "N/A";
  const vaccineDueDate = formatDate(unformattedVaccineDueDate) || "N/A";
  const vaccineLotExpirationDate = unformattedVaccineLotExpirationDate
    ? formatDate(unformattedVaccineLotExpirationDate)
    : "N/A";

  console.log("vaccineDueDate", vaccineDueDate);

  const validRabies = isVaccineValid(unformattedVaccineDueDate);
  const valid = vaccineExempt || validRabies;
  const statusClasses = cn(
    "mt-2 flex flex-col md:flex-row gap-10 rounded-lg border p-2",
    valid ? "border-green-400 bg-green-50" : "border-red-400 bg-red-50",
  );
  const buttonClasses = cn(
    "p-2 text-2xl size-6 w-full",
    valid
      ? "bg-green-500 hover:bg-green-500/70"
      : "bg-red-500 hover:bg-red-500/70",
  );
  const updateClasses = cn(
    "text-sm font-medium hidden md:block",
    valid ? "text-green-500" : "text-red-500",
  );
  const updateClasses2 = cn("text-sm font-medium block md:hidden text-white");

  return (
    <div className="flex max-w-lg flex-col gap-6 text-xs md:text-base">
      <div className={statusClasses}>
        <div className="w-full px-2 py-1">
          <h3 className="py-1 text-base font-medium">
            Rabies {!valid && <span className="text-red-900">(Expired)</span>}
          </h3>
          <ul>
            {vaccineExempt ? (
              <li>Exempt from Vaccination</li>
            ) : (
              <>
                <VaccinationInfo title="Producer:">
                  {vaccineProducer}
                </VaccinationInfo>
                <VaccinationInfo title="Admin Date:">
                  {vaccineAdministeredDate}
                </VaccinationInfo>
                <VaccinationInfo title="Due Date:">
                  {vaccineDueDate}
                </VaccinationInfo>
                <VaccinationInfo title="Lot Number:">
                  {vaccineLotNumber}
                </VaccinationInfo>
                <VaccinationInfo title="Lot Exp Date:">
                  {vaccineLotExpirationDate}
                </VaccinationInfo>
              </>
            )}
          </ul>
        </div>
        <EditVaccines
          admin={admin}
          dog={dog}
          trigger={
            <div className="my-auto flex h-full flex-col items-center justify-center gap-1 px-2">
              <Button
                size="icon"
                onClick={() => setCanEdit(!canEdit)}
                className={buttonClasses}
              >
                <BiEditAlt />
                <span className={updateClasses2}>Update</span>
              </Button>
              <span className={updateClasses}>Update</span>
            </div>
          }
        />
      </div>
    </div>
  );
}
