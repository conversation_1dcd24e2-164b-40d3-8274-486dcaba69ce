"use client";
import React from "react";
import {
  InputDateField,
  InputTextField,
} from "@/components/profile/ProfileComponents";
import { BiInjection } from "react-icons/bi";
import { Dog } from "@/types/DogType";
import { format, parseISO } from "date-fns";

type VaccineInfo = {
  Icon: any;
  input: string;
  label: string;
  value: string;
  type: string;
}

export default function Vaccinations({ dog, dogUpdate }: { dog: Dog, dogUpdate: any }) {
  const vaccineAdministeredDate = dog?.vaccineAdministeredDate ? format(parseISO(dog?.vaccineAdministeredDate), 'yyyy-MM-dd') : ''

  console.log(dog)
  
  const vaccineInfo: VaccineInfo[] = {
  } = [
    {
      Icon: BiInjection,
      input: "veterinaryName",
      label: "Veterinary Name",
      value: dog.veterinaryName || "",
      type: "text",
    },
    {
      Icon: BiInjection,
      input: "rabiesTagNumber",
      label: "Rabies Tag Number",
      value: dog?.rabiesTagNumber || "",
      type: "text",
    },
    {
      Icon: BiInjection,
      input: "vaccineProducer",
      label: "Vaccine Producer",
      value: dog?.vaccineProducer || "",
      type: "text",
    },
    {
      Icon: BiInjection,
      input: "vaccineAdministeredDate",
      label: "Vaccine Administered Date",
      value: vaccineAdministeredDate,
      type: "date",
    },
    {
      Icon: BiInjection,
      input: "vaccineDueDate",
      label: "Vaccine Due Date",
      value: dog.vaccineDueDate || "",
      type: "date",
    },
  ]
  return (
    <div className="flex flex-col gap-2">
      {vaccineInfo && vaccineInfo.map((vaccine) => {
          return (
            <div
              className="flex gap-3 items-center py-2 border-t justify-between"
              key={vaccine.input}
            >
              <div className="flex gap-2 items-center justify-between w-full">
                <div className="flex gap-4 items-center shrink-0 w-[200px]">
                  <vaccine.Icon />
                  <div className="">{vaccine.label}:</div>
                </div>
                {vaccine.type === "date" && (
                  <InputDateField
                    value={vaccine.value}
                    onClick={(value) => {
                      dogUpdate.mutate({
                        form: "newDogLicenseForm",
                        body: {
                          [vaccine.input]: value,
                        },
                      });
                    }}
                    error={dogUpdate.isError}
                  />
                )}
                {vaccine.type === "text" && (
                  <InputTextField
                    value={vaccine.value}
                    onClick={(value) => {
                      dogUpdate.mutate({
                        form: "newDogLicenseForm",
                        body: {
                          [vaccine.input]: value,
                        },
                      });
                    }}
                    error={dogUpdate.isError}
                  />
                )}
              </div>
            </div>
          );
        }
      )}
    </div>
  );
}
