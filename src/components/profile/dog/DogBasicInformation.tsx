import { useEntity } from "@/hooks/providers/useEntity";
import {
  ProfileGrid,
  ProfileGridItem,
  ProfileInput,
  ProfileSection,
  ProfileDateInput,
  createFormData,
  ProfileCustomSelectInput,
  ProfileSelectInput,
  flagCheck,
  ProfileOTPInput,
  ProfileDocument,
  ProfileTextarea,
} from "@/components/profile/helpers/Setup";
import { useUpdateEntityDogProfile } from "@/hooks/api/useProfiles";
import { Dog } from "@/types/DogType";
import { differenceInMonths, differenceInYears } from "date-fns";
import { useGetSettingsByOption } from "@/hooks/api/useAdmin";
import { EditProvider, useEditContext } from "../context/EditContext";
import { useMyProfile } from "@/hooks/providers/useMyProfile";

const DogBasicInformation = ({
  dog,
  entityRefetch,
  entityIsFetching,
}: {
  dog: Dog;
  entityRefetch: () => void;
  entityIsFetching: boolean;
}) => {
  const currentEntity: Dog = dog;
  const updateEntity = useUpdateEntityDogProfile();

  interface DogFormData {
    dogName: string;
    microchipNumber: string;
    dogBirthDate: string | Date;
    dogBio: string;
    dogBreed: string;
    dogPrimaryColor: string;
    dogSecondaryColor: string;
    licenseExempt: boolean;
    serviceDogType: string | null;
    dogSpayedOrNeutered: string;
    dogSpayedOrNeuteredDocument: any;
    isAlteredExempt: boolean;
    dogSpayedOrNeuteredExemptionDocument: any;
    dogSex: string;
    tagNumber: string;
  }

  const defaultValues: DogFormData = {
    tagNumber: currentEntity?.tagNumber || "",
    microchipNumber: currentEntity?.microchipNumber || "",
    dogName: currentEntity?.dogName || "",
    dogBirthDate: currentEntity?.dogBirthDate || "",
    dogBio: currentEntity?.dogBio || "",
    dogBreed: currentEntity?.dogBreed || "",
    dogPrimaryColor: currentEntity?.dogPrimaryColor || "",
    dogSecondaryColor: currentEntity?.dogSecondaryColor || "",
    licenseExempt: currentEntity?.licenseExempt || false,
    serviceDogType: currentEntity?.serviceDogType || null,
    dogSpayedOrNeutered: currentEntity?.dogSpayedOrNeutered || "",
    dogSpayedOrNeuteredDocument:
      currentEntity?.documents?.find(
        (doc) => doc.key === "dogSpayedOrNeuteredDocument",
      ) || null,
    isAlteredExempt: currentEntity?.isAlteredExempt || false,
    dogSpayedOrNeuteredExemptionDocument:
      currentEntity?.documents?.find(
        (doc) => doc.key === "dogSpayedOrNeuteredExemptionDocument",
      ) || null,

    dogSex:
      currentEntity?.dogSex.charAt(0).toUpperCase() +
        currentEntity?.dogSex.slice(1) || "",
  };
  console.log(dog)

  return (
    <EditProvider
      entity={dog}
      entityId={dog.entityId}
      entityType={dog.entityType}
      entityRefetch={entityRefetch}
      entityIsFetching={entityIsFetching}
      defaultValues={defaultValues}
      updateEntity={updateEntity}
    >
      <Content updateEntity={updateEntity} />
    </EditProvider>
  );
};


export default DogBasicInformation;

const Content = ({ updateEntity }: { updateEntity: any }) => {
  const { entity } = useEditContext();
  const currentEntity: Dog = entity;

  // Select Options
  const { data: dogSexOptions, isLoading: dogSexOptionsIsLoading } =
    useGetSettingsByOption("entity", "dog", "sex");

  const {
    data: dogColors,
    error: dogColorsError,
    isLoading: dogColorsIsLoading,
  } = useGetSettingsByOption("entity", "dog", "colors");

  const {
    data: dogBreeds,
    error: dogBreedsError,
    isLoading: dogBreedsIsLoading,
  } = useGetSettingsByOption("entity", "dog", "breeds");

  const onSubmit = (data: any) => {
    console.log(data);

    const newData: any = {
      ...data,
      dogBirthDate: data.dogBirthDate
        ? data.dogBirthDate
        : currentEntity.dogBirthDate,
    };

    const formData = createFormData(newData);
    updateEntity.mutate({
      entityId: currentEntity.entityId,
      body: formData,
    });
  };
  const { watchField } = useEditContext();

  const rejectedFields = currentEntity?.rejectedFields || [];
  const dogSpayedOrNeuteredWatch =
    watchField("dogSpayedOrNeutered").toLowerCase() === "yes";
  const isAlteredExemptWatch = watchField("isAlteredExempt");
  const { hasPermissions } = useMyProfile();

  return (
    <ProfileSection
      label="Basic Information"
      onSubmit={onSubmit}
      saving={updateEntity.isLoading}
      hideEdit={!currentEntity.active}
    >
      <ProfileGrid cols={2}>
        <ProfileGridItem
          label="Dog Tag Number"
          flagged={flagCheck("dogTagNumber", rejectedFields)}
          field={"dogTagNumber"}
          required
        >
          <ProfileOTPInput
            placeholder="No Tag Number Assigned"
            name="tagNumber"
            length={6}
            required
            permissions={["super-admin"]}
          />
        </ProfileGridItem>
        <ProfileGridItem
          label="Microchip Number"
          flagged={flagCheck("microchipNumber", rejectedFields)}
          field={"microchipNumber"}
        >
          <ProfileInput name="microchipNumber" />
        </ProfileGridItem>
      </ProfileGrid>

      <ProfileGrid cols={2}>
        <ProfileGridItem
          label="Dog Name"
          flagged={flagCheck("dogName", rejectedFields)}
          field={"dogName"}
          required
        >
          <ProfileInput name="dogName" required />
        </ProfileGridItem>

        <ProfileGridItem
          label="Breed"
          flagged={flagCheck("dogBreed", rejectedFields)}
          field={"dogBreed"}
          required
        >
          {dogBreedsIsLoading ? (
            "Loading..."
          ) : dogBreeds ? (
            <ProfileCustomSelectInput
              name="dogBreed"
              options={dogBreeds}
              required
            />
          ) : (
            "No Options"
          )}
        </ProfileGridItem>
      </ProfileGrid>

      <ProfileGrid cols={2}>
        <ProfileGridItem
          label="Date of Birth"
          flagged={flagCheck("dogBirthDate", rejectedFields)}
          field={"dogBirthDate"}
          required
        >
          <ProfileDateInput
            name="dogBirthDate"
            required
            formatPattern="
            MMMM yyyy"
          />
        </ProfileGridItem>
        <ProfileGridItem label="Age" noFlag>
          <div className="px-2 py-1">
            {calculateAgeInMonthsOrYears(currentEntity.dogBirthDate)}
          </div>
        </ProfileGridItem>
      </ProfileGrid>

      <ProfileGrid cols={2}>
        <ProfileGridItem label="Sex" required>
          {dogSexOptionsIsLoading ? (
            "Loading..."
          ) : dogSexOptions ? (
            <ProfileCustomSelectInput
              name="dogSex"
              options={dogSexOptions}
              required
            />
          ) : (
            "No Options"
          )}
        </ProfileGridItem>
        <ProfileGridItem
          required
          label={
            currentEntity.dogSex.toLowerCase() === "male"
              ? "Neutered"
              : "Spayed"
          }
        >
          <ProfileSelectInput
            name="dogSpayedOrNeutered"
            options={[
              { value: "yes", label: "Yes" },
              { value: "no", label: "No" },
            ]}
            required
          />
        </ProfileGridItem>
      </ProfileGrid>

      <ProfileGrid cols={2}>
        <ProfileGridItem label="Primary Color" required>
          {dogColorsIsLoading ? (
            "Loading..."
          ) : dogColors ? (
            <ProfileCustomSelectInput
              name="dogPrimaryColor"
              options={dogColors}
              required
            />
          ) : (
            "No Options"
          )}
        </ProfileGridItem>
        <ProfileGridItem label="Secondary Color">
          {dogColorsIsLoading ? (
            "Loading..."
          ) : dogColors ? (
            <ProfileCustomSelectInput
              name="dogSecondaryColor"
              options={dogColors}
            />
          ) : (
            "No Options"
          )}
        </ProfileGridItem>
      </ProfileGrid>

      <ProfileGrid cols={1}>
        <ProfileGridItem label="Bio">
          <ProfileTextarea name="dogBio" placeholder="No Bio" />
        </ProfileGridItem>
      </ProfileGrid>

      <ProfileGrid cols={2}>
        {/* Spay or Neuter Document */}
        {dogSpayedOrNeuteredWatch && !isAlteredExemptWatch && (
          <ProfileGridItem
            label="Spayed or Neutered Document"
            flagged={rejectedFields.includes("dogSpayedOrNeuteredDocument")}
            field={"dogSpayedOrNeuteredDocument"}
            required={!hasPermissions(["super-admin"])}
          >
            <ProfileDocument
              name="dogSpayedOrNeuteredDocument"
              accept={[".pdf", ".jpeg", ".png", ".jpg"]}
              required={!hasPermissions(["super-admin"])}
            />
          </ProfileGridItem>
        )}

        {/* Spay or Neuter  Exemption */}
        {!dogSpayedOrNeuteredWatch && isAlteredExemptWatch && (
          <ProfileGridItem
            label="Spayed or Neutered Exemption Document"
            flagged={rejectedFields.includes(
              "dogSpayedOrNeuteredExemptionDocument",
            )}
            field={"dogSpayedOrNeuteredExemptionDocument"}
            required={!hasPermissions(["super-admin"])}
          >
            <ProfileDocument
              name="dogSpayedOrNeuteredExemptionDocument"
              accept={[".pdf", ".jpeg", ".png", ".jpg"]}
              required={!hasPermissions(["super-admin"])}
            />
          </ProfileGridItem>
        )}
      </ProfileGrid>
    </ProfileSection>
  );
};

function calculateAgeInMonthsOrYears(startDateString: string) {
  const startDate = new Date(startDateString);
  const currentDate = new Date();

  const months = differenceInMonths(currentDate, startDate);
  const years = differenceInYears(currentDate, startDate);

  if (years < 1) {
    return `${months} months`;
  }
  return `${years} years`;
}
