import { formatDate } from "@/components/license/licenseHelper";
import { Title } from "@/components/profile/ProfileComponents";
import { Section } from "../helpers/Dialogs";
import { Badge } from "@/components/ui/badge";
import EditDogInsuranceInfo from "./EditInsuranceInfo";
import { EditDogBasicInformation } from "./EditDogBasicInformation";
import { EditDogPhysicalCharacteristics } from "./EditDogPhysicalCharacteristics";
import { EditDogIdentification } from "./EditDogIdentification";
import { Dog } from "@/types/DogType";
import FlagButton from "../helpers/FlagButton";
import { EditDogVaccines } from "./EditDogVaccines";
import { useState } from "react";
import { useMyProfile } from "@/hooks/providers/useMyProfile";

const RedAlert = ({ label }: { label: string }) => {
  return (
    <Badge className="animate-pulse" variant="destructive">
      {label}
    </Badge>
  );
};

export default function DogProfile({ dog }: { dog: Dog }) {
  const { hasPermissions } = useMyProfile();
  const permitted = hasPermissions(['super-admin']);
  
  console.log(dog)

  const {
    dogName = "",
    dogBirthDate: unformattedDogBirthDate = "",
    dogBreed = "",
    dogSex = "",
    dogPrimaryColor = "",
    dogSecondaryColor = "",
    dogSpayedOrNeutered = false,
    tagNumber = "",
    licenseExempt = false,
    entityId = "",
    status = "",
    isDangerous = false,
    insurancePolicyNumber = "",
    insuranceCompany = "",
    insuranceStartDate = "",
    insuranceEndDate = "",
    veterinaryName = "",
    vaccineDatesExempt = false,
    rabiesTagNumber = "",
    vaccineProducer = "",
    vaccineBrand = "",
    vaccineAdministeredDate = "",
    vaccineDueDate = "",
    vaccineLotExpirationDate = "",
  } = dog || {};

  const dogBirthDate = formatDate(unformattedDogBirthDate);

  // Statuses
  const lost = status === "Lost";
  const deceased = status === "Deceased";
  const transferred = status === "Transferred";
  const alertOn = isDangerous || lost || deceased || transferred;

  const rejectedFields = dog?.rejectedFields || [];

  const containsField = (field: string) => rejectedFields.includes(field);

  const Flag = ({ field }: { field: string }) => {
    const [isFlagged, setIsFlagged] = useState(containsField(field));

    if (!permitted) {
      if (isFlagged)
        return <Badge variant={"destructive"}>Needs Correction</Badge>;
      return null;
    }

    return (
      <FlagButton
        entityId={entityId}
        entityType="dog"
        flagged={isFlagged}
        setFlag={setIsFlagged}
        field={field}
      />
    );
  };

  console.log(dog)

  return (
    <div className="">
      <div className="flex flex-col gap-10 xl:flex-row">
        <div className="flex w-full flex-col gap-20">
          {alertOn && (
            <div className="flex gap-6">
              {isDangerous && <RedAlert label="Dangerous Dog" />}
              {lost && <RedAlert label="Lost Dog" />}
              {deceased && <RedAlert label="Deceased Dog" />}
              {transferred && (
                <RedAlert label="Dog Transferred - All actions disabled" />
              )}
            </div>
          )}

          {/* Identification*/}
          <div>
            <Title>
              <div className="flex items-center gap-4">
                <div className="flex items-center gap-3">Identification</div>
                {!transferred && permitted && <EditDogIdentification dog={dog} />}
              </div>
            </Title>

            <div className="flex flex-col gap-4 ">
              <Section label="Dog Tag:">
                {tagNumber 
                  ? tagNumber
                  : !permitted
                  ? <span>No Tag Number Assigned</span> 
                  : <span className="text-red-700 flex items-center gap-2">
                    No Tag Number Assigned 
                    <Badge variant={"destructive"}>Required</Badge>
                  </span>}
              </Section>
            </div>
          </div>

          {/* Vaccinations */}
          <div>
            <Title>
              <div className="flex items-center gap-4">
                Rabies Vaccine
                <EditDogVaccines dog={dog} />
              </div>
            </Title>

            <div className="flex flex-col gap-4 ">
              <Section label="Veterinary Name:">
                {veterinaryName?.length ? veterinaryName : "N/A"}
                <Flag field="veterinaryName" />
              </Section>
              <Section label="Rabies Exempt:">
                {vaccineDatesExempt ? "Yes" : "No"}
                <Flag field="vaccineDatesExempt" />
              </Section>
              <Section label="Rabies Tag Number:">
                {rabiesTagNumber?.length ? rabiesTagNumber : "N/A"}
                <Flag field="rabiesTagNumber" />
              </Section>
              <Section label="Administered Date:">
                {vaccineAdministeredDate?.length
                  ? formatDate(vaccineAdministeredDate)
                  : "N/A"}
                <Flag field="vaccineAdministeredDate" />
              </Section>
              <Section label="Due Date:">
                {vaccineDueDate?.length ? formatDate(vaccineDueDate) : "N/A"}
                {/* check if vaccine due date is expired */}

                <Flag field="vaccineDueDate" />
                {
                  new Date(vaccineDueDate) < new Date() && <Badge variant="destructive">Expired</Badge>
                }
              </Section>
              <Section label="Lot Expiration Date:">
                {vaccineLotExpirationDate?.length
                  ? formatDate(vaccineLotExpirationDate)
                  : "N/A"}
                <Flag field="vaccineLotExpirationDate" />
              </Section>
              <Section label="Producer:">
                {vaccineProducer?.length ? vaccineProducer : "N/A"}
                <Flag field="vaccineProducer" />
              </Section>
              <Section label="Brand:">
                {vaccineBrand?.length ? vaccineBrand : "N/A"}
                <Flag field="vaccineBrand" />
              </Section>
            </div>
          </div>

          {/* Basic Information */}
          <div>
            <Title>
              <div className="flex items-center gap-4">
                <div className="flex items-center gap-3">Basic Information</div>
                {!transferred && <EditDogBasicInformation dog={dog} />}
              </div>
            </Title>

            <div className="flex flex-col gap-4  ">
              <Section label="Name:">
                {dogName}
                <Flag field="dogName" />
              </Section>
              <Section label="Date of Birth:">
                {dogBirthDate}
                <Flag field="dogBirthDate" />
              </Section>
              <Section label="Sex:">
                <span className="capitalize">{dogSex}</span>
                <Flag field="dogSex" />
              </Section>
              <Section label="Spayed / Neutered:">
                <span className="capitalize">{dogSpayedOrNeutered}</span>
                <Flag field="dogSpayedOrNeutered" />
              </Section>
              <Section label="Service Animal:">
                {licenseExempt ? "Yes" : "No"}
                <Flag field="licenseExempt" />
              </Section>
            </div>
          </div>

          {/* Physical Characteristics */}
          <div>
            <Title>
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  Physical Characteristics{" "}
                  {!transferred && <EditDogPhysicalCharacteristics dog={dog} />}
                </div>
              </div>
            </Title>

            <div className="flex flex-col gap-4 ">
              <Section label="Breed:">
                {dogBreed} <Flag field="dogBreed" />
              </Section>

              <Section label="Primary Color">
                {dogPrimaryColor} <Flag field="dogPrimaryColor" />
              </Section>
              <Section label="Secondary Color">
                {dogSecondaryColor ?? "N/A"} <Flag field="dogSecondaryColor" />
              </Section>
            </div>
          </div>

          {/* Behavioral */}
          <div>
            <Title>
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  <span>Behavioral</span>
                </div>
              </div>
            </Title>

            <div className="flex flex-col gap-4 ">
              <Section label="Dangerous Dog:">
                {isDangerous ? "Yes" : "No"}
                <Flag field="isDangerous" />
              </Section>
            </div>
          </div>

          {/* Insurance */}
          <div>
            <Title>
              <div className="flex items-center gap-4">
                <div className="flex items-center gap-3">
                  <span>Insurance</span>
                </div>
                {!transferred && <EditDogInsuranceInfo dog={dog} />}
              </div>
            </Title>

            <div className="flex flex-col gap-4 ">
              <Section label="Insurance Provider:">
                {insuranceCompany ?? "N/A"}
                <Flag field="insuranceCompany" />
              </Section>
              <Section label="Policy Number:">
                {insurancePolicyNumber ?? "N/A"}
                <Flag field="insurancePolicyNumber" />
              </Section>
              <Section label="Policy Start Date:">
                {insuranceStartDate ? formatDate(insuranceStartDate) : "N/A"}
                <Flag field="insuranceStartDate" />
              </Section>
              <Section label="Policy End Date:">
                {insuranceEndDate ? formatDate(insuranceEndDate) : "N/A"}
                <Flag field="insuranceEndDate" />
              </Section>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
