import DogBasicInformation from "./DogBasicInformation";
import RabiesDogInformation from "./RabiesDogInformation";
import ServiceDogInformation from "./ServiceDogInformation";
import { Refreshing } from "../helpers/Setup";
import { Dog } from "@/types/DogType";

export default function DogProfile3({ 
  dog,
  entityIsFetching, 
  entityRefetch
} : { 
  dog: Dog;
  entityIsFetching: boolean;
  entityRefetch: () => void;
}) {

  return (
    <div className="flex flex-col gap-6">
      <div className="flex items-center gap-2 text-xl font-semibold text-neutral-700">
        Dog Profile
        {entityIsFetching && <Refreshing />}
      </div>
      <div className="flex flex-col gap-16">
        <DogBasicInformation
          dog={dog}
          entityIsFetching={entityIsFetching}
          entityRefetch={entityRefetch}
        />
        <RabiesDogInformation
          dog={dog}
          entityIsFetching={entityIsFetching}
          entityRefetch={entityRefetch}
        />
        <ServiceDogInformation
          dog={dog}
          entityIsFetching={entityIsFetching}
          entityRefetch={entityRefetch}
        />
      </div>
    </div>
  );
}
