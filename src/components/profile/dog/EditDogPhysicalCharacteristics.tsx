import { useForm } from "react-hook-form";
import { Dog } from "@/types/DogType";
import {
  useUpdateEntityDogProfile,
  useUpdateResidentDogProfile,
} from "@/hooks/api/useProfiles";
import { useState } from "react";
import { useAtom } from "jotai";
import { toast<PERSON>tom } from "@/components/ui/toast/toast";
import { useQueryClient } from "@tanstack/react-query";
import {
  EditDialog,
  EditDialogFooter,
  createFormData,
} from "@/app/(app)/(realm)/(protected)/(resident)/(registered)/resident/[entityType]/[entityId]/components/modals/EditDialog";
import {
  Comment,
  DogBreed,
  DogPrimaryColor,
  DogSecondaryColor,
} from "@/components/dialog/EditFields";

type FormValues = {
  [key: string]: any;
};

export const EditDogPhysicalCharacteristics = ({
  dog,
  admin = true,
}: {
  dog: Dog;
  admin?: boolean;
}) => {
  const initialValues = {
    dogBreed: dog?.dogBreed || "",
    dogPrimaryColor: dog?.dogPrimaryColor || "",
    dogSecondaryColor: dog?.dogSecondaryColor || "",
  };

  const updateDogProfile = useUpdateEntityDogProfile();

  const {
    reset,
    control,
    register,
    getValues,
    handleSubmit,
    trigger: formTrigger,
    formState: { errors, isDirty },
  } = useForm<FormValues>({
    defaultValues: initialValues,
    mode: "onChange",
  });

  const [isOpen, setIsOpen] = useState(false);
  const [, setToast] = useAtom(toastAtom);
  const queryClient = useQueryClient();

  const handleCancel = () => {
    reset(initialValues);
    setIsOpen(false);
  };

  const handleSave = () => {
    formTrigger();
    

    const data = getValues();

    const filterOnlyChanged: any = Object.keys(data).reduce(
      (acc: any, key: any) => {
        if (data[key] !== (initialValues as any)[key]) {
          acc[key] = data[key];
        }
        return acc;
      },
      {},
    );


    
    const formData = createFormData(filterOnlyChanged);

    updateDogProfile.mutate(
      {
        entityId: dog?.entityId,
        body: formData,
      },
      {
        onSuccess: () => {
          queryClient.invalidateQueries();
          reset(data);
          setToast({
            status: "success",
            label: "Dog Updated",
            message: "Successfully Updated Dog Information",
          });
          setIsOpen(false);
        },
        onError: (error: any) => {
          console.log(error);
          setToast({
            status: "error",
            label: "Error Updating Profile",
            message: error?.response?.data?.message,
          });
        },
      },
    );
  };

  const containsField = (field: string) =>
    dog?.rejectedFields?.includes(field) || false;

  return (
    <EditDialog
      title="Edit Physical Characteristics"
      isOpen={isOpen}
      setIsOpen={setIsOpen}
    >
      <form className="flex flex-col gap-4" onSubmit={handleSubmit(handleSave)}>
        <DogBreed
          control={control}
          errors={errors}
          flag={containsField("dogBreed")}
        />
        <DogPrimaryColor
          control={control}
          errors={errors}
          flag={containsField("dogPrimaryColor")}
        />
        <DogSecondaryColor
          control={control}
          errors={errors}
          flag={containsField("dogSecondaryColor")}
        />
        <Comment register={register} />
        <EditDialogFooter
          handleCancel={handleCancel}
          disabled={!isDirty}
          loading={updateDogProfile.isLoading}
        />
      </form>
    </EditDialog>
  );
};

export default EditDogPhysicalCharacteristics;
