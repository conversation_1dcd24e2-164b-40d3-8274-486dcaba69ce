import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Controller, useForm } from "react-hook-form";
import { useState } from "react";
import type { Dog } from "@/types/DogType";
import FileUpload from "@/components/universal/input/SimpleFileUpload";
import {
  EditDialog,
  EditDialogFooter,
  createFormData,
} from "@/app/(app)/(realm)/(protected)/(resident)/(registered)/resident/[entityType]/[entityId]/components/modals/EditDialog";
import { useQueryClient } from "@tanstack/react-query";
import { useAtom } from "jotai";
import { toastAtom } from "@/components/ui/toast/toast";
import { FormValues } from "@/types/DogType";
import {
  useUpdateEntityDogProfile,
  useUpdateResidentDogProfile,
} from "@/hooks/api/useProfiles";
import { Comment, ErrorMessage } from "@/components/dialog/EditFields";

const EditDogInsuranceInfo = ({
  dog,
  admin = true,
}: {
  dog: Dog;
  admin?: boolean;
}) => {
  const documents = dog?.documents || [];

  // Find the insurance document in the documents array
  const insuranceDocument = documents.find(
    (document) => document.key === "dogProofOfInsuranceDocument",
  );

  const initialValues = {
    insurancePolicyNumber: dog?.insurancePolicyNumber || "",
    insuranceCompany: dog?.insuranceCompany || "",
    insuranceEndDate: dog?.insuranceEndDate || "",
    insuranceStartDate: dog?.insuranceStartDate || "",
    dogProofOfInsuranceDocument: insuranceDocument,
  };

  const updateDogProfile = useUpdateEntityDogProfile();

  const {
    reset,
    control,
    register,
    getValues,
    handleSubmit,
    trigger: formTrigger,
    formState: { errors, isDirty, isValid },
  } = useForm<FormValues>({
    defaultValues: initialValues,
    mode: "onChange",
  });

  const [isOpen, setIsOpen] = useState(false);
  const [, setToast] = useAtom(toastAtom);
  const queryClient = useQueryClient();

  const handleCancel = () => {
    reset(initialValues);
    setIsOpen(false);
  };

  const handleSave = () => {
    formTrigger();

    const data = getValues();
    const formData = createFormData(data);

    updateDogProfile.mutate(
      {
        entityId: dog.entityId,
        body: formData,
      },
      {
        onSuccess: () => {
          queryClient.invalidateQueries();
          reset(data);
          setToast({
            status: "success",
            label: "Dog Updated",
            message: "Successfully Updated Dog Insurance",
          });
          setIsOpen(false);
        },
        onError: (error: any) => {
          console.log(error);
          setToast({
            status: "error",
            label: "Error Updating Profile",
            message: error?.response?.data?.message,
          });
        },
      },
    );
  };

  return (
    <EditDialog
      title="Edit Dog Insurance Information"
      isOpen={isOpen}
      setIsOpen={setIsOpen}
    >
      <form className="flex flex-col gap-4" onSubmit={handleSubmit(handleSave)}>
        <div className="flex flex-col gap-2">
          <Label htmlFor="dogProofOfInsuranceDocument">Insurance Records</Label>
          <Controller
            name="dogProofOfInsuranceDocument"
            control={control}
            rules={{
              required: true,
            }}
            render={({ field: { onChange, value } }) => {
              return (
                <FileUpload
                  input={{
                    className: "",
                    id: "dogProofOfInsuranceDocument",
                    label: "",
                  }}
                  required={true}
                  control={control}
                />
              );
            }}
          />
        </div>
        <div className="flex flex-col gap-2">
          <Label htmlFor="insurancePolicyNumber">Policy Number</Label>
          <Input autoComplete="off" {...register("insurancePolicyNumber")} />
          {errors.insurancePolicyNumber && (
            <ErrorMessage>Invalid Policy Number</ErrorMessage>
          )}
        </div>
        <div className="flex flex-col gap-2">
          <Label htmlFor="insuranceCompany">Insurance Company</Label>
          <Input autoComplete="off" {...register("insuranceCompany")} />
          {errors.insuranceCompany && (
            <ErrorMessage>Invalid Insurance Company</ErrorMessage>
          )}
        </div>
        <div className="flex flex-col gap-2">
          <Label htmlFor="insuranceStartDate">Start Date</Label>
          <Input
            autoComplete="off"
            type="date"
            {...register("insuranceStartDate")}
          />
          {errors.insuranceStartDate && (
            <ErrorMessage>Invalid Start Date</ErrorMessage>
          )}
        </div>
        <div className="flex flex-col gap-2">
          <Label htmlFor="insuranceEndDate">End Date</Label>
          <Input
            autoComplete="off"
            type="date"
            {...register("insuranceEndDate")}
          />
          {errors.insuranceEndDate && (
            <ErrorMessage>Invalid End Date</ErrorMessage>
          )}
        </div>
        <Comment register={register} />
        <EditDialogFooter
          handleCancel={handleCancel}
          disabled={!isDirty}
          loading={updateDogProfile.isLoading}
        />
      </form>
    </EditDialog>
  );
};

export default EditDogInsuranceInfo;
