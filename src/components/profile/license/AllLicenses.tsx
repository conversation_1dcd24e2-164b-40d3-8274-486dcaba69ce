import LicenseList from "@/components/license/LicenseList";
import PageContainer from "@/components/ui/Page/PageContainer";
import { License } from "@/types/LicenseType";
import React from "react";

export default function AllLicenses({ licenses }: { licenses: License[] }) {
  const filterLicenses = (statuses: string[]) =>
    licenses?.filter((license) => statuses.includes(license.status)) ?? [];

  const licenseGroups = [
    { label: "Rejected Licenses", statuses: ["Rejected"] },
    { label: "Expired Licenses", statuses: ["Expired"] },
    {
      label: "Pending Licenses",
      statuses: ["Pending Payment", "Pending", "Pending Approval"],
    },
    { label: "Current Licenses", statuses: ["Active"] },
    // { label: "Draft Licenses", statuses: ["Draft"] },
    { label: "Canceled Licenses", statuses: ["Canceled"] },
  ];

  console.log(licenses)
  console.log(licenseGroups)

  return (
    <PageContainer className="w-full rounded-none  md:rounded md:pt-6">
      <div className="flex w-full flex-col gap-20">
        {licenses.length === 0 && (
          <div className="flex w-full flex-col gap-4">
            <h1 className="text-left text-2xl font-bold">
              No Licenses Found
            </h1>
            <p className="text-left">You do not have any licenses.</p>
          </div>
        )}
        {licenseGroups.map(({ label, statuses }) => {
          const filteredLicenses = filterLicenses(statuses);
          return filteredLicenses.length > 0 ? (
            <LicenseList
              key={label}
              label={label}
              licenses={filteredLicenses}
              column={false}
            />
          ) : null;
        })}
      </div>
    </PageContainer>
  );
}
