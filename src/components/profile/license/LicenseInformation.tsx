import { useEntity } from "@/hooks/providers/useEntity";
import {
  ProfileGrid,
  ProfileGridItem,
  ProfileSection,
  createFormData,
  flagCheck,
} from "@/components/profile/helpers/Setup";
import { useUpdateEntityProfile2 } from "@/hooks/api/useProfiles";
import { License } from "@/types/LicenseType";
import { useGetSettingsByOption } from "@/hooks/api/useAdmin";
import { EditProvider } from "../context/EditContext";
import { format } from "date-fns";
import { cn } from "@/lib/utils";

const LicenseInformation = ({ license }: { license: License }) => {
  const { entityRefetch, entityIsFetching } = useEntity();
  const currentEntity: License = license;
  const updateEntity = useUpdateEntityProfile2();

  interface LicenseFormData {
    licenseNumber: string;
    licenseType: string;
    applicationDate: string | Date | null;
    validFromDate: string | Date;
    validToDate: string | Date;
    status: string;
    description: string;
    licenseExempt: boolean;
    expirationDate: string | Date | null;
  }

  const defaultValues: LicenseFormData = {
    licenseNumber: currentEntity?.licenseNumber || "",
    licenseType: currentEntity?.licenseType?.code || "",
    applicationDate: currentEntity?.applicationDate || null,
    validFromDate: currentEntity?.validFromDate || "",
    validToDate: currentEntity?.validToDate || "",
    status: currentEntity?.status || "",
    description: currentEntity?.description || "",
    licenseExempt: currentEntity?.licenseExempt || false,
    expirationDate: currentEntity?.expirationDate || null,
  };

  // Select Options
  const { data: licenseTypes, isLoading: licenseTypesIsLoading } =
    useGetSettingsByOption("entity", "license", "types");

  const onSubmit = (data: any) => {
    console.log(data);

    const newData: any = {
      ...data,
      validFromDate: data.validFromDate
        ? data.validFromDate
        : currentEntity.validFromDate,
    };

    const formData = createFormData(newData);
    console.log("running");
    updateEntity.mutate({
      entityId: currentEntity.entityId,
      body: formData,
    });
  };

  const rejectedFields = currentEntity?.rejectedFields || [];

  return (
    <EditProvider
      entity={license}
      entityId={license.entityId}
      entityType="license"
      entityRefetch={entityRefetch}
      entityIsFetching={entityIsFetching}
      defaultValues={defaultValues}
      updateEntity={updateEntity}
    >
      <ProfileSection
        label="License Information"
        onSubmit={onSubmit}
        saving={updateEntity.isLoading}
        hideEdit={true}
        showHideMessage={false}
      >
        <ProfileGrid cols={2}>
          <ProfileGridItem
            label="License Number"
            flagged={flagCheck("licenseNumber", rejectedFields)}
            field={"licenseNumber"}
          >
            <div className="px-2">{license?.licenseNumber ?? "No Number"}</div>
          </ProfileGridItem>

          <ProfileGridItem
            label="License Type"
            flagged={flagCheck("licenseType", rejectedFields)}
            field={"licenseType"}
          >
            <div className="px-2">
              {license?.licenseType?.name ?? "No Type"}
            </div>
          </ProfileGridItem>
        </ProfileGrid>

        {/* Validity Dates */}
        <ProfileGrid cols={2}>
          <ProfileGridItem
            label="Issued on"
            flagged={flagCheck("validFromDate", rejectedFields)}
            field={"validFromDate"}
          >
            <div className="px-2">
              {license?.validFromDate
                ? format(new Date(license?.validFromDate), "MMMM yyyy")
                : "No Date"}
            </div>
          </ProfileGridItem>
          <ProfileGridItem
            label="Expires"
            flagged={flagCheck("validToDate", rejectedFields)}
            field={"validToDate"}
          >
            <div className="px-2">
              {license?.validToDate
                ? format(new Date(license?.validToDate), "MMMM yyyy")
                : "No Date"}
            </div>
          </ProfileGridItem>
        </ProfileGrid>

        {/* Application Date and Status */}
        <ProfileGrid cols={2}>
          <ProfileGridItem
            label="Application Date"
            flagged={flagCheck("applicationDate", rejectedFields)}
            field={"applicationDate"}
          >
            <div className="px-2">
              {license?.applicationDate
                ? format(new Date(license?.applicationDate), "MMMM yyyy")
                : "No Date"}
            </div>
          </ProfileGridItem>
          <ProfileGridItem
            label="Status"
            flagged={flagCheck("status", rejectedFields)}
            field={"status"}
          >
            <div className={cn(
              "px-2 w-fit rounded ml-2",
              license?.status === "Active" ? "bg-green-600 text-white" : "bg-red-600 text-white"
            )}>{license?.status ?? "No Status"}</div>
          </ProfileGridItem>
        </ProfileGrid>
      </ProfileSection>
    </EditProvider>
  );
};

export default LicenseInformation;
