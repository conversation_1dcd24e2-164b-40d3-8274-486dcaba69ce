import { cn } from "@/lib/utils";
import Image from "next/image";
import { TbPhotoEdit } from "react-icons/tb";
import UpdateImageTrigger from "./UpdateImageModal";

export default function ProfileAvatar({
  avatarUrl,
  isActive,
  entityId,
  entityType,
  canEdit,
  blob,
  profile,
  type = "fancy",
}: {
  avatarUrl: any;
  isActive?: boolean;
  entityId: string | null | undefined;
  entityType: string | null | undefined;
  canEdit?: boolean;
  blob?: any;
  profile?: boolean;
  type?: "fancy" | "rounded" | "rounded-small" | "normal" | "normal-small"
}) {
  
  if (!entityId || !entityType) return null;

  // rounded version - simple circular avatar
  if (type === "rounded") {
    return (
      <div className="group relative size-20 flex-shrink-0">
        <Image
          src={avatarUrl ?? profileMap[entityType ?? "default"]?.avatar}
          alt="avatar"
          fill
          className={cn(
            "rounded-full border border-neutral-200 bg-white object-cover shadow-sm transition-opacity",
            !isActive && "grayscale opacity-60",
            canEdit && isActive && "group-hover:opacity-70"
          )}
        />
        {!isActive && (
          <div className="absolute inset-0 flex items-center justify-center rounded-full bg-red-500/70">
            <div className="h-1 w-1 rounded-full bg-white" />
          </div>
        )}
        
        {/* Edit overlay for rounded */}
        {canEdit && isActive && (
          <UpdateImageTrigger
            entityId={entityId}
            entityType={entityType}
            currentImageUrl={
              avatarUrl ?? profileMap[entityType ?? "default"]?.avatar
            }
            imageType={"avatar"}
            role="resident"
            blob={blob}
          >
            <div className="absolute inset-0 flex items-center justify-center rounded-full bg-black/40 opacity-0 transition-opacity group-hover:opacity-100 cursor-pointer">
              <TbPhotoEdit className="text-white text-xl" />
              <span className="sr-only">Edit Avatar</span>
            </div>
          </UpdateImageTrigger>
        )}
      </div>
    );
  }

  // rounded-small version
  if (type === "rounded-small") {
    return (
      <div className="group relative h-8 w-8 flex-shrink-0">
        <Image
          src={avatarUrl ?? profileMap[entityType ?? "default"]?.avatar}
          alt="avatar"
          fill
          className={cn(
            "rounded-full border border-neutral-200 bg-white object-cover shadow-sm transition-opacity",
            !isActive && "grayscale opacity-60",
            canEdit && isActive && "group-hover:opacity-70"
          )}
        />
        {!isActive && (
          <div className="absolute inset-0 flex items-center justify-center rounded-full bg-red-500/70">
            <div className="h-1 w-1 rounded-full bg-white" />
          </div>
        )}
        
        {/* Edit overlay for rounded-small */}
        {canEdit && isActive && (
          <UpdateImageTrigger
            entityId={entityId}
            entityType={entityType}
            currentImageUrl={
              avatarUrl ?? profileMap[entityType ?? "default"]?.avatar
            }
            imageType={"avatar"}
            role="resident"
            blob={blob}
          >
            <div className="absolute inset-0 flex items-center justify-center rounded-full bg-black/40 opacity-0 transition-opacity group-hover:opacity-100 cursor-pointer">
              <TbPhotoEdit className="text-white text-sm" />
              <span className="sr-only">Edit Avatar</span>
            </div>
          </UpdateImageTrigger>
        )}
      </div>
    );
  }

  // normal version
  if (type === "normal") {
    return (
      <div className="group relative size-20 flex-shrink-0">
        <Image
          src={avatarUrl ?? profileMap[entityType ?? "default"]?.avatar}
          alt="avatar"
          fill
          className={cn(
            "rounded border border-neutral-200 bg-white object-cover shadow-sm transition-opacity",
            !isActive && "grayscale opacity-60",
            canEdit && isActive && "group-hover:opacity-70"
          )}
        />
        {!isActive && (
          <div className="absolute inset-0 flex items-center justify-center rounded bg-red-500/70">
            <div className="h-1 w-1 rounded bg-white" />
          </div>
        )}
        
        {/* Edit overlay for rounded */}
        {canEdit && isActive && (
          <UpdateImageTrigger
            entityId={entityId}
            entityType={entityType}
            currentImageUrl={
              avatarUrl ?? profileMap[entityType ?? "default"]?.avatar
            }
            imageType={"avatar"}
            role="resident"
            blob={blob}
          >
            <div className="absolute inset-0 flex items-center justify-center rounded bg-black/40 opacity-0 transition-opacity group-hover:opacity-100 cursor-pointer">
              <TbPhotoEdit className="text-white text-xl" />
              <span className="sr-only">Edit Avatar</span>
            </div>
          </UpdateImageTrigger>
        )}
      </div>
    );
  }

  // normal-small version
  if (type === "normal-small") {
    return (
      <div className="group relative h-8 w-8 flex-shrink-0">
        <Image
          src={avatarUrl ?? profileMap[entityType ?? "default"]?.avatar}
          alt="avatar"
          fill
          className={cn(
            "rounded border border-neutral-200 bg-white object-cover shadow-sm transition-opacity",
            !isActive && "grayscale opacity-60",
            canEdit && isActive && "group-hover:opacity-70"
          )}
        />
        {!isActive && (
          <div className="absolute inset-0 flex items-center justify-center rounded bg-red-500/70">
            <div className="h-1 w-1 rounded bg-white" />
          </div>
        )}
        
        {/* Edit overlay for rounded-small */}
        {canEdit && isActive && (
          <UpdateImageTrigger
            entityId={entityId}
            entityType={entityType}
            currentImageUrl={
              avatarUrl ?? profileMap[entityType ?? "default"]?.avatar
            }
            imageType={"avatar"}
            role="resident"
            blob={blob}
          >
            <div className="absolute inset-0 flex items-center justify-center rounded bg-black/40 opacity-0 transition-opacity group-hover:opacity-100 cursor-pointer">
              <TbPhotoEdit className="text-white text-sm" />
              <span className="sr-only">Edit Avatar</span>
            </div>
          </UpdateImageTrigger>
        )}
      </div>
    );
  }

  return (
    <div className="group relative h-28 w-28 flex-shrink-0 transform rounded-lg shadow-xl shadow-neutral-400 md:h-32 md:w-32">
      {/* Blob */}
      <svg
        viewBox="0 0 200 200"
        xmlns="http://www.w3.org/2000/svg"
        className={cn(
          "absolute inset-0 h-[200%] w-[200%] -translate-x-1/4 -translate-y-1/4 stroke-2   opacity-50 blur drop-shadow",
          profileMap[entityType ?? "default"]?.blobColor,
        )}
      >
        <path
          d="M24,-46C30.7,-37.7,35.5,-30.5,40.9,-23C46.3,-15.5,52.2,-7.8,56.6,2.5C61,12.8,63.7,25.6,60.6,36.9C57.4,48.2,48.3,58,37.2,65.7C26.1,73.3,13,78.9,1.3,76.7C-10.5,74.5,-21,64.5,-34.1,58C-47.2,51.4,-62.9,48.4,-69.5,39.3C-76.2,30.2,-73.9,15.1,-66.1,4.5C-58.3,-6.1,-45,-12.2,-38,-20.7C-31,-29.3,-30.3,-40.2,-25,-49.3C-19.7,-58.5,-9.8,-65.8,-0.6,-64.7C8.6,-63.7,17.3,-54.3,24,-46Z"
          transform="translate(100 100)"
        />
      </svg>
      <svg
        viewBox="0 0 200 200"
        xmlns="http://www.w3.org/2000/svg"
        className={cn(
          "stroke absolute inset-0 h-[200%] w-[200%] -translate-x-1/4 -translate-y-1/4  drop-shadow",
          profileMap[entityType ?? "default"]?.blobColor,
        )}
      >
        <path
          d="M24,-46C30.7,-37.7,35.5,-30.5,40.9,-23C46.3,-15.5,52.2,-7.8,56.6,2.5C61,12.8,63.7,25.6,60.6,36.9C57.4,48.2,48.3,58,37.2,65.7C26.1,73.3,13,78.9,1.3,76.7C-10.5,74.5,-21,64.5,-34.1,58C-47.2,51.4,-62.9,48.4,-69.5,39.3C-76.2,30.2,-73.9,15.1,-66.1,4.5C-58.3,-6.1,-45,-12.2,-38,-20.7C-31,-29.3,-30.3,-40.2,-25,-49.3C-19.7,-58.5,-9.8,-65.8,-0.6,-64.7C8.6,-63.7,17.3,-54.3,24,-46Z"
          transform="translate(100 100)"
        />
      </svg>

      {/* Gradient */}
      <div
        className={cn(
          "absolute inset-0 rounded bg-gradient-to-r opacity-50 blur transition-all group-hover:-inset-1 group-hover:opacity-65",
          profileMap[entityType ?? "default"]?.gradient,
        )}
      />

      {/* Edit Image */}
      {canEdit && isActive && (
        <UpdateImageTrigger
          entityId={entityId}
          entityType={entityType}
          currentImageUrl={
            avatarUrl ?? profileMap[entityType ?? "default"]?.avatar
          }
          imageType={"avatar"}
          role="resident"
          blob={blob}
        >
          <button className="absolute bottom-0 right-0 z-20 translate-x-1/3 translate-y-1/3 rounded-lg border-2 border-neutral-200 bg-white p-1 text-2xl text-neutral-950 shadow transition-all hover:bg-blue-700 hover:text-blue-50">
            <TbPhotoEdit />
            <span className="sr-only">Edit Avatar</span>
          </button>
        </UpdateImageTrigger>
      )}

      <Image
        src={avatarUrl ?? profileMap[entityType ?? "default"]?.avatar}
        alt="avatar"
        width={profile && avatarUrl ? 120 : undefined}
        height={profile && avatarUrl ? 120 : undefined}
        fill={!profile || !avatarUrl}
        objectFit="cover"
        className={cn(
          "transform rounded-lg border-2 bg-white shadow",
          !isActive && "grayscale",
        )}
      />
      {!isActive && (
        <div className="absolute left-1/2 top-1/2 w-[90%] -translate-x-1/2 -translate-y-1/2 transform rounded-full bg-red-500/80 px-2 text-center text-white">
          Inactive
        </div>
      )}
    </div>
  );
}

const profileMap: {
  [key: string]: {
    avatar: string;
    blobColor: string;
    gradient: string;
  };
} = {
  dog: {
    avatar: "/images/icons/dog.png",
    blobColor: "fill-orange-300 stroke-orange-400",
    gradient: "bg-gradient-to-r from-orange-500 via-yellow-500 to-orange-400",
  },
  individual: {
    avatar: "/images/icons/user.png",
    blobColor: "fill-blue-300 stroke-blue-400",
    gradient: "bg-gradient-to-r from-blue-500 via-blue-500 to-blue-400",
  },
  default: {
    avatar: "/images/icons/user.png",
    blobColor: "fill-blue-300 stroke-blue-400",
    gradient:
      "bg-gradient-to-r from-neutral-500 via-neutral-500 to-neutral-400",
  },
};