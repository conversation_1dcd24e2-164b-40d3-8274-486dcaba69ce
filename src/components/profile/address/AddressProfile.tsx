import { formatDate, isSenior } from "@/components/license/licenseHelper";
import { IconCount, Title } from "@/components/profile/ProfileComponents";
import PageContainer from "@/components/ui/Page/PageContainer";
import { useParams } from "next/navigation";
import { FaDog } from "react-icons/fa";
import { TbLicense } from "react-icons/tb";
import { Section } from "../helpers/Dialogs";
import { Badge } from "@/components/ui/badge";

const RedAlert = ({label}:{label:string}) => {
  return (
    <Badge className="animate-pulse" variant="destructive">
      {label}
    </Badge>
  );
}

export default function AddressProfile({ data }: { data: any }) {

  console.log(data)

  const { realm, entitytype, entityId } = useParams();
  const { dog, individual, license, address } = data;

  // Name Information
  const first = individual?.firstName || "";
  const middle = individual?.middleName || "";
  const last = individual?.lastName || "";
  const fullname = `${first} ${middle} ${last}`;
  const dateOfBirth = individual.dateOfBirth
    ? `${
        individual?.dateOfBirth ? formatDate(individual.dateOfBirth) : "No Date"
      } ${isSenior(individual.dateOfBirth) ? "(senior)" : ""}`
    : "No Date";

  const status = dog?.status || "";

  const deceased = status === "Deceased";
  const alertOn = deceased

  return (
    <div className="container mx-auto">
      <div className="flex flex-col xl:flex-row gap-10">
        <div className="flex flex-col gap-7 w-full">
          {
            alertOn && (
              <PageContainer className="flex gap-6">
                {deceased && <RedAlert label="Dangerous Dog" />}
              </PageContainer>
            )
          }
          <PageContainer>
            <Title>
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  <span>Profile</span>
                </div>
                <div className="flex gap-4">
                  {Array.isArray(dog) && (
                    <IconCount
                      Icon={FaDog}
                      count={dog?.length}
                      label={"Number of Dogs Registered"}
                      link={`/entity/${entitytype}/${entityId}/associations`}
                    />
                  )}
                  {Array.isArray(license) && (
                    <IconCount
                      Icon={TbLicense}
                      count={license?.length}
                      label={"Number of Licenses"}
                      link={`/entity/${entitytype}/${entityId}/licenses`}
                    />
                  )}
                </div>
              </div>
            </Title>

            <div className="flex flex-col gap-4">
              {/* Name */}
              <Section label="Name:">
                {fullname} 
              </Section>

              {/* Date of Birth */}
              <Section label="Date of Birth:">
                {dateOfBirth}
              </Section>
            </div>
          </PageContainer>
        </div>
      </div>
    </div>
  );
}
