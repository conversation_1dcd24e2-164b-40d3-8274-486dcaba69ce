import Image from "next/image";
import React, { useCallback, useEffect, useState } from "react";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  useUpdateEntityDogProfile,
  useUpdateEntityProfile2,
  useUpdateResidentDogProfile,
  useUpdateResidentProfile,
} from "@/hooks/api/useProfiles";
import { Button } from "../ui/button";
import { toastAtom } from "../ui/toast/toast";
import { useAtom } from "jotai";
import { Accept, useDropzone } from "react-dropzone";
import { ImSpinner } from "react-icons/im";
import { useQueryClient } from "@tanstack/react-query";
import { useMyProfile } from "@/hooks/providers/useMyProfile";

const UpdateImageTrigger = ({
  entityId,
  entityType,
  currentImageUrl,
  children,
  role,
  imageType,
  blob,
}: {
  entityId: string;
  entityType: string;
  role: "resident" | "admin";
  currentImageUrl: string;
  imageType: "avatar" | "photo";
  blob?: any;
  children: React.ReactNode;
}) => {
  const [isOpen, setIsOpen] = useState<boolean>(false);


  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger
        onClick={() => {
          setIsOpen(true);
        }}
      >
        {children}
      </DialogTrigger>
      <UpdateImageModal
        imageType={imageType}
        entityId={entityId}
        entityType={entityType}
        imageUrl={currentImageUrl}
        blob={blob}
        role={role}
        closeModal={() => {
          setIsOpen(false);
        }}
      />
    </Dialog>
  );
};

// Map for api call to upload an image
const updateImageMap: {
  [key: string]: (entityType: string, entityId: string, newImage: any) => void;
} = {
  avatar: (entityType: string, entityId: string, newImage: string) => {},
  photo: (entityType: string, entityId: string, newImage: string) => {},
};

// Map for which mutation to use for the api call based on role
const roleMutationMap: { [key: string]: any } = {
  admin: {
    dog: useUpdateEntityDogProfile,
    individual: useUpdateEntityProfile2,
  },
  resident: {
    dog: useUpdateResidentDogProfile,
    individual: useUpdateResidentProfile,
  },
};

const UpdateImageModal = ({
  entityId,
  entityType,
  role,
  imageUrl,
  closeModal,
  imageType,
  blob,
}: {
  entityId: string;
  entityType: string;
  imageType: "avatar" | "photo";
  role: "resident" | "admin";
  imageUrl: any;
  blob?: any;
  closeModal: () => void;
}) => {
  const { hasPermissions } = useMyProfile();
  const [newImage, setNewImage] = useState(blob);
  const [rawFile, setRawFile] = useState<any>(null);
  const [error, setError] = useState<string>("");
  const admin = hasPermissions(["super-admin"]) ? "admin" : "resident";

  const imageMutate = roleMutationMap[admin][entityType]();
  const [_, setToast] = useAtom(toastAtom);

  const onDrop = useCallback((acceptedFiles: any) => {
    const file = acceptedFiles[0];
    setRawFile(file);
    const reader: any = new FileReader();

    reader.onload = () => {
      setNewImageUrl(reader.result);
    };
    reader.readAsDataURL(file);
  }, []);

  const acceptFiles: Accept = {
    "image/*": [".jpeg", ".png", ".jpg"],
  };

  const [newImageUrl, setNewImageUrl] = useState<string | null>(null);

  useEffect(() => {
    // If there's a blob, create a URL for it
    if (blob) {
      const url = URL.createObjectURL(blob);
      setNewImageUrl(url);

      // Cleanup the URL when it's no longer needed
      return () => {
        URL.revokeObjectURL(url);
      };
    } else {
      setNewImageUrl(imageUrl);
    }
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [blob]);

  // Create body
  const createBody = () => {
    const fd = new FormData();

    fd.append("avatar", rawFile);
    return fd;
  };

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: acceptFiles,
    multiple: false,
  });

  const queryClient = useQueryClient();

  return (
    <DialogContent>
      <DialogHeader>
        <DialogTitle>Update Profile Picture</DialogTitle>
        <DialogDescription>
          <div className="flex flex-col items-center justify-center">
            <div className="flex max-w-sm flex-wrap items-center justify-center gap-6 py-10">
              <div
                {...getRootProps()}
                className="dropzone flex max-w-[260px] shrink-0 flex-col items-center justify-center gap-4 rounded-md border-4 border-dashed border-gray-200 p-4 text-center"
              >
                <input {...getInputProps()} />
                {isDragActive ? (
                  <p>Drop the image here ...</p>
                ) : (
                  <p>
                    Drag &apos;n&apos; drop a new avatar here, or click to
                    select a file
                  </p>
                )}
                {newImageUrl && (
                  <div className="relative size-[200px] shrink-0 overflow-hidden rounded shadow-lg">
                    <Image
                      src={
                        newImageUrl ??
                        profileMap[entityType ?? "default"]?.avatar
                      }
                      alt="Avatar preview"
                      fill
                      objectFit="cover"
                      objectPosition="center center"
                    />
                  </div>
                )}
              </div>
            </div>
            {error && (
              <p className="mb-4 line-clamp-3 max-w-[260px] text-wrap text-red-500">
                {error}
              </p>
            )}
            <div className="flex items-center justify-center gap-6">
              <Button
                variant="ghost"
                onClick={() => {
                  closeModal();
                  setNewImage(imageUrl);
                }}
              >
                Cancel
              </Button>
              <Button
                variant="primary"
                disabled={newImage === imageUrl}
                onClick={() => {
                  imageMutate.mutate(
                    {
                      entityId,
                      body: createBody(),
                    },
                    {
                      onSuccess: () => {
                        queryClient.invalidateQueries(["profile"]);
                        queryClient.invalidateQueries(["profile2"]);

                        setToast({
                          message: "Avatar Updated",
                          status: "success",
                        });
                        closeModal();
                      },
                      onError: (error: any) => {
                        setToast({
                          message: "Failed to update avatar",
                          status: "error",
                        });
                        setError(error.response.data.message);
                      },
                    },
                  );
                }}
              >
                {imageMutate.isLoading ? (
                  <span className="flex items-center gap-1 ">
                    <ImSpinner className="animate-spin" /> Updating...
                  </span>
                ) : (
                  "Save Image"
                )}
              </Button>
            </div>
          </div>
        </DialogDescription>
      </DialogHeader>
    </DialogContent>
  );
};

export default UpdateImageTrigger;

const profileMap: {
  [key: string]: {
    avatar: string;
    blobColor: string;
    gradient: string;
  };
} = {
  dog: {
    avatar: "/images/icons/dog.png",
    blobColor: "fill-orange-300 stroke-orange-400",
    gradient: "bg-gradient-to-r from-orange-500 via-yellow-500 to-orange-400",
  },
  individual: {
    avatar: "/images/icons/user.png",
    blobColor: "fill-blue-300 stroke-blue-400",
    gradient: "bg-gradient-to-r from-blue-500 via-blue-500 to-blue-400",
  },
  default: {
    avatar: "/images/icons/user.png",
    blobColor: "fill-blue-300 stroke-blue-400",
    gradient:
      "bg-gradient-to-r from-neutral-500 via-neutral-500 to-neutral-400",
  },
};
