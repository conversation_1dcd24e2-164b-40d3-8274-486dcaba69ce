import Image from "next/image";
import { FaUser } from "react-icons/fa";
import { HiOutlineMail, HiOutlinePhone } from "react-icons/hi";
import Link from "next/link";
import Accordion from "../ui/Accordion2";
import { useParams } from "next/navigation";

const IndividualResults = ({ users }: { users: any[] }) => {
  
  return (
    <Accordion title="Owners">
      {users &&
        users.map((user: any) => {
          
          // Photo
          const photo = user.avatarUrl ?? null;
         
          // Primary email and phone
          const primaryEmail = user.contacts?.find((contact:any) => contact.isPrimary && contact.type === 'email')?.value ?? 'No Email';
          const primaryPhone = user.contacts?.find((contact:any) => contact.isPrimary && contact.type === 'phone')?.value ?? 'No Phone';

          // Address Information
          const address = user?.associations?.affiliations?.addresses?.find(
            (address:any) => address.addressType === "Primary"
          ) ?? null;

          const street = address?.address ?? "";
          const unit = address?.unit ?? "";
          const city = address?.city ?? "";
          const state = address?.state ?? "";
          const zip = address?.zip ?? "";

          const primaryAddress = `${street} ${unit}, ${city} ${state} ${zip}`;

          return (
            <div className="flex items-center" key={user.entityId}>
              <div
                className={`
                ${ photo ?? "bg-blue-300" }
                flex items-center justify-center shadow rounded relative w-20 h-20 shrink-0`}
              >
                <Image
                  src={photo ?? "/images/icons/user.png"}
                  alt="home"
                  fill
                  className={!user.avatarUrl ? "p-2" : "rounded"}
                />
              </div>
              <div className="flex flex-col ml-4 w-full h-20">
                <p className="font-semibold text-neutral-800 text-xl">
                  {user?.name?.first ?? 'No First Name'} {user?.name?.last ?? 'No Last Name'}
                </p>
                <p className="text-sm text-neutral-400">{primaryAddress}</p>
                <p className="flex gap-4 text-sm mt-auto">
                  <span className="flex items-center">
                    <HiOutlineMail className="inline-block mr-1" />
                    {primaryEmail}
                  </span>
                  <span className="flex items-center">
                    <HiOutlinePhone className="inline-block mr-1" />
                    {primaryPhone}
                  </span>
                </p>
              </div>

              {/* Button */}
              <Link
                href={`/entity/individual/${user.entityId}/license`}
                className="whitespace-nowrap shrink-0 bg-sky-200 rounded py-2 px-2 flex items-center gap-2"
              >
                <FaUser /> View User
              </Link>
            </div>
          );
        })}
    </Accordion>
  );
};

export default IndividualResults;
