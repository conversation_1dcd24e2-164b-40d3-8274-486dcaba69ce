"use client";
import NewNote from "./notes/new-note";
import Notes<PERSON>ist from "./notes/notes-list";
import EditNote from "./notes/edit-note";
import DeleteNote from "./notes/delete-note";
import FilterNotes from "./notes/filter-notes";

export default function NotesComponent() {
  return (
    <div className="md:container relative mx-auto h-full md:px-6 md:py-3 px-3 py-3">
      <h1 className="text-primary mb-6 text-3xl font-bold">Account Notes</h1>

      <div className="flex h-full flex-col gap-10 xl:flex-row">
        <div className="order-last h-full w-full xl:order-first">
          <FilterNotes />
          <NotesList />
          <EditNote />
          <DeleteNote />
        </div>

        <div className="order-first w-full xl:order-last xl:max-w-[400px] xl:sticky xl:top-0 h-fit">
          <NewNote />
        </div>
      </div>
    </div>
  );
}
