import {
  ProfileGrid,
  ProfileGridItem,
  ProfileInput,
  ProfileSection,
  createFormData,
  ProfileCustomSelectInput,
  ProfileGroup,
  ProfileCheckboxInput,
  stateOptions,
} from "@/components/profile/helpers/Setup";
import { Individual } from "@/types/IndividualType";
import { useUpdateEntityProfile2 } from "@/hooks/api/useProfiles";
import { Separator } from "@/components/ui/separator";
import { EditProvider, useEditContext } from "../context/EditContext";
import { useState } from "react";

interface AddressInformationProps {
  homeStreetAddress: string;
  homeStreetAddress2: string;
  homeCity: string;
  homeState: string;
  homeZip: string;
  mailingStreetAddress: string;
  mailingStreetAddress2: string;
  mailingCity: string;
  mailingState: string;
  mailingZip: string;
  isMailingSameAsPrimary: boolean;
}

const AddressInformation = ({
  individual,
  entityRefetch,
  entityIsFetching,
}: {
  individual: Individual;
  entityRefetch: () => void;
  entityIsFetching: boolean;
}) => {
  const updateEntity = useUpdateEntityProfile2();
  const [isMailingSameAsPrimary, setisMailingSameAsPrimary] = useState(
    individual?.addresses?.some(
      (address) =>
        address.participantAddressType === "Mailing" &&
        address.streetAddress ===
          individual?.addresses?.find(
            (address) => address.participantAddressType === "Home",
          )?.streetAddress,
    ) || false,
  );

  const homeAddress =
    individual?.addresses?.find(
      (address) => address.participantAddressType === "Home",
    ) || null;

  const mailingAddress =
    individual?.addresses?.find(
      (address) => address.participantAddressType === "Mailing",
    ) || null;

  const defaultValues: AddressInformationProps = {
    homeStreetAddress: homeAddress?.streetAddress || "",
    homeStreetAddress2: homeAddress?.streetAddress2 || "",
    homeCity: homeAddress?.city || "",
    homeState: homeAddress?.state || "",
    homeZip: homeAddress?.zip || "",
    mailingStreetAddress: isMailingSameAsPrimary
      ? homeAddress?.streetAddress || ""
      : mailingAddress?.streetAddress || "",
    mailingStreetAddress2: isMailingSameAsPrimary
      ? homeAddress?.streetAddress2 || ""
      : mailingAddress?.streetAddress2 || "",
    mailingCity: isMailingSameAsPrimary
      ? homeAddress?.city || ""
      : mailingAddress?.city || "",
    mailingState: isMailingSameAsPrimary
      ? homeAddress?.state || ""
      : mailingAddress?.state || "",
    mailingZip: isMailingSameAsPrimary
      ? homeAddress?.zip || ""
      : mailingAddress?.zip || "",
    isMailingSameAsPrimary: isMailingSameAsPrimary,
  };

  return (
    <EditProvider
      entity={individual}
      entityId={individual.entityId}
      entityType={individual.entityType}
      entityRefetch={entityRefetch}
      entityIsFetching={entityIsFetching}
      defaultValues={defaultValues}
      updateEntity={updateEntity}
    >
      <Content individual={individual} updateEntity={updateEntity} />
    </EditProvider>
  );
};

export default AddressInformation;

const Content = ({
  individual,
  updateEntity,
}: {
  individual: Individual;
  updateEntity: any;
}) => {
  const { canEdit, watchField } = useEditContext();

  // Home Address Default
  const homeAddress =
    individual?.addresses?.find(
      (address) => address.participantAddressType === "Home",
    ) || null;

  // Mailing Address Default
  const mailingAddress =
    individual?.addresses?.find(
      (address) => address.participantAddressType === "Mailing",
    ) || null;

  const rejectedFields = individual?.rejectedFields || [];
  const sameAddress = watchField("isMailingSameAsPrimary");

  const onSubmit = (data: any) => {
    const newData: any = {};

    const homeAddressAddressId = homeAddress?.participantAddressId ?? "newHome";
    const mailingAddressAddressId =
      mailingAddress?.participantAddressId ?? "newMailing";

    newData["address_" + homeAddressAddressId + "_address"] =
      data.homeStreetAddress;
    newData["address_" + homeAddressAddressId + "_address2"] =
      data.homeStreetAddress2;
    newData["address_" + homeAddressAddressId + "_city"] = data.homeCity;
    newData["address_" + homeAddressAddressId + "_state"] = data.homeState;
    newData["address_" + homeAddressAddressId + "_zip"] = data.homeZip;

    if (data.isMailingSameAsPrimary) {
      newData["address_" + mailingAddressAddressId + "_address"] =
        data.homeStreetAddress;
      newData["address_" + mailingAddressAddressId + "_address2"] =
        data.homeStreetAddress2;
      newData["address_" + mailingAddressAddressId + "_city"] = data.homeCity;
      newData["address_" + mailingAddressAddressId + "_state"] = data.homeState;
      newData["address_" + mailingAddressAddressId + "_zip"] = data.homeZip;
    } else {
      newData["address_" + mailingAddressAddressId + "_address"] =
        data.mailingStreetAddress;
      newData["address_" + mailingAddressAddressId + "_address2"] =
        data.mailingStreetAddress2;
      newData["address_" + mailingAddressAddressId + "_city"] =
        data.mailingCity;
      newData["address_" + mailingAddressAddressId + "_state"] =
        data.mailingState;
      newData["address_" + mailingAddressAddressId + "_zip"] = data.mailingZip;
    }

    console.log(newData);

    const formData = createFormData(newData);

    updateEntity.mutate({
      entityId: individual.entityId,
      body: formData,
    });
  };

  return (
    <ProfileSection
      label="Address Information"
      onSubmit={onSubmit}
      saving={updateEntity.isLoading}
      hideEdit={!individual.active}
    >
      {/* Home Address */}
      <ProfileGroup>Home Address</ProfileGroup>
      <div className="flex flex-col gap-6">
        <ProfileGrid cols={3}>
          <ProfileGridItem
            label="Street Address"
            flagged={rejectedFields.includes("homeStreetAddress")}
            field={"homeStreetAddress"}
            span={2}
            required
          >
            <ProfileInput name="homeStreetAddress" required />
          </ProfileGridItem>

          <ProfileGridItem
            label="Apt, Suite, etc."
            flagged={rejectedFields.includes("homeStreetAddress2")}
            field={"homeStreetAddress2"}
          >
            <ProfileInput
              name="homeStreetAddress2"
              placeholder="Apartment, Suite, etc."
            />
          </ProfileGridItem>
        </ProfileGrid>

        <ProfileGrid cols={3}>
          <ProfileGridItem
            label="City"
            flagged={rejectedFields.includes("homeCity")}
            field={"homeCity"}
            required
          >
            <ProfileInput name="homeCity" required />
          </ProfileGridItem>
          <ProfileGridItem
            label="State"
            flagged={rejectedFields.includes("homeState")}
            field={"homeState"}
            required
          >
            <ProfileCustomSelectInput
              name="homeState"
              options={stateOptions}
              required
            />
          </ProfileGridItem>
          <ProfileGridItem
            label="Zip"
            flagged={rejectedFields.includes("homeZip")}
            field={"homeZip"}
            required
          >
            <ProfileInput name="homeZip" required />
          </ProfileGridItem>
        </ProfileGrid>
      </div>

      {/* Is Mailing Address Same As Home Checkbox */}
      <ProfileGrid cols={1}>
        {canEdit && (
          <ProfileGridItem label="" field="isMailingSameAsPrimary" noFlag>
            <ProfileCheckboxInput
              name="isMailingSameAsPrimary"
              label="Same as Home Address"
            />
          </ProfileGridItem>
        )}
        {!canEdit && watchField("isMailingSameAsPrimary") && (
          <div className="px-2 text-neutral-700">
            *Mailing Address is the same.
          </div>
        )}
      </ProfileGrid>

      <Separator className="" />

      {!sameAddress && (
        <>
          <ProfileGroup>Mailing Address</ProfileGroup>
          <div className="flex flex-col gap-6">
            <ProfileGrid cols={3}>
              <ProfileGridItem
                label="Street Address"
                flagged={rejectedFields.includes("mailingStreetAddress")}
                field={"mailingStreetAddress"}
                span={2}
                required
              >
                <ProfileInput name="mailingStreetAddress" required />
              </ProfileGridItem>

              <ProfileGridItem
                label="Apt, Suite, etc."
                flagged={rejectedFields.includes("mailingStreetAddress2")}
                field={"mailingStreetAddress2"}
              >
                <ProfileInput
                  name="mailingStreetAddress2"
                  placeholder="Apartment, Suite, etc."
                />
              </ProfileGridItem>
            </ProfileGrid>

            <ProfileGrid cols={3}>
              <ProfileGridItem
                label="City"
                flagged={rejectedFields.includes("mailingCity")}
                field={"mailingCity"}
                required
              >
                <ProfileInput name="mailingCity" required />
              </ProfileGridItem>
              <ProfileGridItem
                label="State"
                flagged={rejectedFields.includes("mailingState")}
                field={"mailingState"}
                required
              >
                <ProfileCustomSelectInput
                  name="mailingState"
                  options={stateOptions}
                  required
                />
              </ProfileGridItem>
              <ProfileGridItem
                label="Zip"
                flagged={rejectedFields.includes("mailingZip")}
                field={"mailingZip"}
                required
              >
                <ProfileInput name="mailingZip" required />
              </ProfileGridItem>
            </ProfileGrid>
          </div>
        </>
      )}
    </ProfileSection>
  );
};
