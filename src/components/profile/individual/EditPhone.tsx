import { Contact, Individual } from "@/types/IndividualType";
import {
  useUpdateIndividualContact,
  useUpdateResidentContact,
} from "@/hooks/api/useProfiles";
import { useForm } from "react-hook-form";
import { FormValues } from "@/types/DogType";
import { useState } from "react";
import { useAtom } from "jotai";
import { toastAtom } from "@/components/ui/toast/toast";
import { useQueryClient } from "@tanstack/react-query";
import {
  EditDialog,
  EditDialogFooter,
  createFormData,
} from "@/app/(app)/(realm)/(protected)/(resident)/(registered)/resident/[entityType]/[entityId]/components/modals/EditDialog";
import { Comment, Phones } from "@/components/dialog/EditFields";

const formatPhoneNumber = (value: string) => {
  if (!value) return value;

  // Remove all non-numeric characters
  const phoneNumber = value.replace(/[^\d]/g, "");

  // Format the number
  const phoneNumberLength = phoneNumber.length;
  if (phoneNumberLength < 4) return phoneNumber;
  if (phoneNumberLength < 7) {
    return `(${phoneNumber.slice(0, 3)}) ${phoneNumber.slice(3)}`;
  }
  return `(${phoneNumber.slice(0, 3)}) ${phoneNumber.slice(
    3,
    6,
  )}-${phoneNumber.slice(6, 10)}`;
};

export const EditPhone = ({
  individual,
  admin = true,
}: {
  individual: Individual;
  admin?: boolean;
}) => {
  const phones: Contact[] =
    individual?.contacts?.filter((c: any) => c.type === "Phone") || [];

  const order = ["Home", "Cell", "Work", "Fax", "Other"];

  phones.sort((a: Contact, b: Contact) => {
    const orderA = order.indexOf(a.group) !== -1 ? order.indexOf(a.group) : 999;
    const orderB = order.indexOf(b.group) !== -1 ? order.indexOf(b.group) : 999;

    return orderA - orderB;
  });

  const transformedObject: Record<number, string> = phones.reduce(
    (acc: any, current: any) => {
      acc[current?.id] = formatPhoneNumber(current.value);
      return acc;
    },
    {},
  );

  const initialValues = {
    ...transformedObject,
    comment: "",
  };

  const updateContact = useUpdateIndividualContact();

  const {
    reset,
    register,
    handleSubmit,
    formState: { errors, isDirty, isValid },
  } = useForm<FormValues>({
    defaultValues: initialValues,
    mode: "onChange",
  });

  const [isOpen, setIsOpen] = useState(false);
  const [, setToast] = useAtom(toastAtom);
  const queryClient = useQueryClient();

  const handleCancel = () => {
    reset(initialValues);
    setIsOpen(false);
  };

  const handleSave = (data: any) => {
    updateContact.mutate(
      {
        entityId: individual.entityId,
        body: data,
      },
      {
        onSuccess: () => {
          queryClient.invalidateQueries();
          reset(data);
          setToast({
            status: "success",
            label: "Individual Updated",
            message: "Successfully Updated Individual Information",
          });
          setIsOpen(false);
        },
        onError: (error: any) => {
          console.log(error);
          setToast({
            status: "error",
            label: "Error Updating Profile",
            message: error?.response?.data?.message,
          });
        },
      },
    );
  };

  return (
    <EditDialog title="Edit Phone" isOpen={isOpen} setIsOpen={setIsOpen}>
      <form className="flex flex-col gap-4" onSubmit={handleSubmit(handleSave)}>
        <Phones phones={phones} register={register} errors={errors} />
        <Comment register={register} />
        <EditDialogFooter
          handleCancel={handleCancel}
          disabled={!isDirty}
          loading={updateContact.isLoading}
        />
      </form>
    </EditDialog>
  );
};

export default EditPhone;
