import { Individual } from "@/types/IndividualType";
import { useUpdateEntityProfile2 } from "@/hooks/api/useProfiles";
import { useForm } from "react-hook-form";
import { FormValues } from "@/types/DogType";
import { useState } from "react";
import { useAtom } from "jotai";
import { toastAtom } from "@/components/ui/toast/toast";
import { useQueryClient } from "@tanstack/react-query";
import {
  EditDialog,
  EditDialogFooter,
  createFormData,
} from "@/app/(app)/(realm)/(protected)/(resident)/(registered)/resident/[entityType]/[entityId]/components/modals/EditDialog";
import { Comment, IndividualDateOfBirth } from "@/components/dialog/EditFields";

export const EditBirth = ({ individual }: { individual: Individual }) => {
  const initialValues = {
    dateOfBirth: individual?.dateOfBirth || "",
    comment: "",
  };

  const updateEntity = useUpdateEntityProfile2();

  const {
    reset,
    register,
    handleSubmit,
    formState: { errors, isDirty },
  } = useForm<FormValues>({
    defaultValues: initialValues,
    mode: "onChange",
  });

  const [isOpen, setIsOpen] = useState(false);
  const [, setToast] = useAtom(toastAtom);
  const queryClient = useQueryClient();

  const handleCancel = () => {
    reset(initialValues);
    setIsOpen(false);
  };

  const handleSave = (data: any) => {
    const formData = createFormData(data);
    updateEntity.mutate(
      {
        entityId: individual.entityId,
        body: formData,
      },
      {
        onSuccess: () => {
          queryClient.invalidateQueries();
          reset(data);
          setToast({
            status: "success",
            label: "Individual Updated",
            message: "Successfully Updated Individual Information",
          });
          setIsOpen(false);
        },
        onError: (error: any) => {
          console.log(error);
          setToast({
            status: "error",
            label: "Error Updating Profile",
            message: error?.response?.data?.message,
          });
        },
      },
    );
  };

  return (
    <EditDialog title="Edit Birth Date" isOpen={isOpen} setIsOpen={setIsOpen}>
      <form className="flex flex-col gap-4" onSubmit={handleSubmit(handleSave)}>
        <IndividualDateOfBirth register={register} errors={errors} />
        <Comment register={register} />
        <EditDialogFooter
          handleCancel={handleCancel}
          disabled={!isDirty}
          loading={updateEntity.isLoading}
        />
      </form>
    </EditDialog>
  );
};

export default EditBirth;
