import { Individual } from "@/types/IndividualType";
import AddressInformation from "./AddressInformation";
import PersonalInformation from "./PersonalInformation";
import { useEntity } from "@/hooks/providers/useEntity";

export default function IndividualProfile2({
  individual,
  title = "Profile",
}: {
  individual: Individual;
  title?: string;
}) {
  console.log(individual);
  const { entityIsFetching, entityRefetch } = useEntity();

  return (
    <div className="flex flex-col gap-6">
      <div className="text-xl font-semibold text-neutral-700">{title}</div>
      <div className="flex flex-col gap-16">
        <PersonalInformation 
          individual={individual}
          entityIsFetching={entityIsFetching}
          entityRefetch={entityRefetch}
        />
        <AddressInformation 
          individual={individual}
          entityIsFetching={entityIsFetching}
          entityRefetch={entityRefetch}
        />
      </div>
    </div>
  );
}
