import { formatDate, isSenior } from "@/components/license/licenseHelper";
import { Title } from "@/components/profile/ProfileComponents";
import { Section } from "../helpers/Dialogs";
import { EditName } from "./EditName";
import { EditBirth } from "./EditBirth";
import EditEmail from "./EditEmail";
import EditPhone from "./EditPhone";
import { Address } from "@/types/AddressType";
import EditAddresses from "./EditAddress";

export default function IndividualProfile({
  individual,
  refetch,
  layout = "row",
  canEdit = true
}: {
  individual: any;
  refetch: any;
  layout?: string;
  canEdit?: boolean;
}) {

  const address = individual?.addresses || [];

  // Name Information
  const first = individual?.firstName || "";
  const middle = individual?.middleName || "";
  const last = individual?.lastName || "";
  const fullname = `${first} ${middle} ${last}`;
  const dateOfBirth = individual?.dateOfBirth
    ? `${
        individual?.dateOfBirth
          ? formatDate(individual?.dateOfBirth)
          : "No Date"
      } ${isSenior(individual?.dateOfBirth) ? "(senior)" : ""}`
    : "No Date";



  const emails =
    individual?.contacts?.filter((c: any) => c.type === "Email") || null;
  const phones =
    individual?.contacts?.filter((c: any) => c.type === "Phone") || null;

  function getAddressByType(addresses: Address[], type: string) {
    return (
      addresses?.find(
        (address: Address) => address.participantAddressType === type,
      ) || {
        participantAddressTypeId: null,
        participantAddressType: type,
        streetAddress: "",
        streetAddress2: "",
        city: "",
        state: "",
        zip: "",
      }
    );
  }

  const homeAddress = getAddressByType(address, "Home");
  const mailingAddress = getAddressByType(address, "Mailing");
  const workAddress = getAddressByType(address, "Work");

  const hasValidEmails = emails?.some(
    (item: { value: string }) => item?.value !== "" && item?.value !== null,
  );
  const hasValidPhones = phones?.some(
    (item: { value: string }) => item?.value !== "" && item?.value !== null,
  );

  const SectionWrapper = ({ children, label }: { children: any, label:string }) => {
    return <Section label={label} layout={layout}>{children}</Section>;
  }

  return (
    <div className="h-full overflow-x-hidden">
      <div className="flex flex-col gap-10 xl:flex-row">
        <div className="flex w-full flex-col gap-7">
  
          <div className="flex w-full flex-col gap-20">
            <div>
              <Title>
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-3">
                    <span>Profile</span>
                  </div>
                </div>
              </Title>

              <div className="flex flex-col gap-4">
                {/* Name */}
                <SectionWrapper label="Name:">
                  {fullname} {canEdit && <EditName individual={individual} />}
                </SectionWrapper>

                {/* Date of Birth */}
                <SectionWrapper label="Date of Birth:">
                  {dateOfBirth} {canEdit && <EditBirth individual={individual} />}
                </SectionWrapper>
              </div>
            </div>

            <div>
              <Title>
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-3">
                    <span>Contact</span>
                  </div>
                </div>
              </Title>

              <div className="flex flex-col gap-4">
                {/* Phone */}
                <SectionWrapper label="Phone:">
                  <div className="flex flex-col">
                    {phones && hasValidPhones ? (
                      phones.map((c: any) => {
                        if (c?.value.length > 0) {
                          if (c.value.length === 10) {
                            return (
                              <div key={c.id}>
                                {`(${c.value.substring(
                                  0,
                                  3,
                                )}) ${c.value.substring(
                                  3,
                                  6,
                                )}-${c.value.substring(6, 10)}`}{" "}
                                <span className="text-sm text-neutral-700">{`(${c.group})`}</span>
                              </div>
                            );
                          } else if (c.value.length === 11) {
                            return (
                              <div key={c.id}>
                                {`1 (${c.value.substring(
                                  0,
                                  3,
                                )}) ${c.value.substring(
                                  3,
                                  6,
                                )}-${c.value.substring(6, 10)}`}{" "}
                                <span className="text-sm text-neutral-700">{`(${c.group})`}</span>{" "}
                              </div>
                            );
                          }
                          return (
                            <div key={c.id}>{`${c.value} (${c.group})`}</div>
                          );
                        }
                      })
                    ) : (
                      <div>No Phone</div>
                    )}
                  </div>
                  {canEdit && <EditPhone individual={individual} />}
                </SectionWrapper>

                {/* Email */}
                <SectionWrapper label="Email:">
                  <div className="flex flex-col">
                    {emails && hasValidEmails ? (
                      emails.map((c: any) => {
                        if (c?.value.length > 0)
                          return (
                            <div key={c.id} className="w-full">
                              <span className=" w-fit text-wrap break-words">{`${c.value}`}</span>{" "}
                              <span className="text-sm w-full text-neutral-700">{`(${c.group})`}</span>
                            </div>
                          );
                      })
                    ) : (
                      <div className="text-neutral-600">No Email</div>
                    )}
                  </div>
                  {canEdit &&  <EditEmail individual={individual} /> }
                </SectionWrapper>
              </div>
            </div>

            <div>
              <Title>
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-3">
                    <span>Addresses</span>
                    {canEdit && <EditAddresses
                      address={address?.sort(
                        (a: any, b: any) =>
                          a.participantAddressType - b.participantAddressType,
                      )}
                      entityId={individual?.entityId as string}
                      refetch={refetch}
                    />}
                  </div>
                </div>
              </Title>

              <div className="flex flex-col gap-4">
                {[homeAddress, mailingAddress, workAddress]?.map((a: any) => {
                  console.log(a);
                  return (
                    <SectionWrapper label={a.participantAddressType} key={a.entityId}>
                      {a.participantAddressTypeId ? (
                        <div className="flex flex-col">
                          <div>{`${a.streetAddress}`}</div>
                          {a.streetAddress2 ? (
                            <div>{`${a.streetAddress2}`}</div>
                          ) : null}
                          <div>{`${a.city}, ${a.state} ${a.zip}`}</div>
                        </div>
                      ) : (
                        <div className="text-neutral-600">No Address</div>
                      )}
                    </SectionWrapper>
                  );
                })}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
