import { useEntity } from "@/hooks/providers/useEntity";
import { isSenior } from "@/components/license/licenseHelper";
import {
  ProfileGrid,
  ProfileGridItem,
  ProfileInput,
  ProfileSection,
  ProfileDateInput,
  ProfilePhoneInput,
  createFormData,
} from "@/components/profile/helpers/Setup";
import { Individual } from "@/types/IndividualType";
import { useUpdateEntityProfile2 } from "@/hooks/api/useProfiles";
import { FlagIcon } from "lucide-react";
import { EditProvider } from "../context/EditContext";

interface PersonalInformationProps {
  firstName: string;
  middleName: string;
  lastName: string;
  dateOfBirth: string;
  homePhone: string;
  primaryEmail: string;
}

const PersonalInformation = ({
  individual,
  entityRefetch,
  entityIsFetching,
}: {
  individual: Individual;
  entityRefetch: () => void;
  entityIsFetching: boolean;
}) => {
  console.log(individual.active);
  const updateEntity = useUpdateEntityProfile2();

  const homePhone =
    individual?.contacts?.find(
      (contact) =>
        contact.type === "Phone" &&
        contact.group === "Home" &&
        contact.value.length > 0,
    ) || null;

  const primaryEmail =
    individual?.contacts?.find(
      (contact) => contact.type === "Email" && contact.group === "Primary",
    ) || null;

  const defaultValues: PersonalInformationProps = {
    firstName: individual?.firstName || "",
    middleName: individual?.middleName || "",
    lastName: individual?.lastName || "",
    dateOfBirth: individual?.dateOfBirth || "",
    homePhone: homePhone?.value || "",
    primaryEmail: primaryEmail?.value || "",
  };

  const onSubmit = (data: any) => {
    const newData: any = {
      firstName: data?.firstName ?? "",
      middleName: data?.middleName ?? "",
      lastName: data?.lastName ?? "",
      dateOfBirth: data?.dateOfBirth ?? "",
    };

    // Add homephoneid as key
    if (homePhone) {
      const phoneNumberOnly = data.homePhone.replace(/\D/g, "");
      newData["contact" + homePhone.id] = phoneNumberOnly;
    }

    if (primaryEmail) {
      newData["contact" + primaryEmail.id] = data.primaryEmail;
    }

    const formData = createFormData(newData);

    updateEntity.mutate({
      entityId: individual.entityId,
      body: formData,
    });
  };

  const rejectedFields = individual?.rejectedFields || [];

  return (
    <EditProvider
      entity={individual}
      entityId={individual.entityId}
      entityType={individual.entityType}
      entityRefetch={entityRefetch}
      entityIsFetching={entityIsFetching}
      defaultValues={defaultValues}
      updateEntity={updateEntity}
    >
      <ProfileSection
        label="Personal Information"
        onSubmit={onSubmit}
        saving={updateEntity.isLoading}
        hideEdit={!individual.active}
      >
        <ProfileGrid cols={3}>
          <ProfileGridItem
            label="First Name"
            flagged={rejectedFields.includes("firstName")}
            field={"firstName"}
            required
          >
            <ProfileInput name="firstName" required />
          </ProfileGridItem>
          <ProfileGridItem
            label="Middle Name"
            flagged={rejectedFields.includes("middleName")}
            field={"middleName"}
          >
            <ProfileInput name="middleName" placeholder="N/A" />
          </ProfileGridItem>
          <ProfileGridItem
            label="Last Name"
            flagged={rejectedFields.includes("lastName")}
            field={"lastName"}
            required
          >
            <ProfileInput name="lastName" required />
          </ProfileGridItem>
        </ProfileGrid>

        <ProfileGrid cols={3}>
          <ProfileGridItem
            label="Date of Birth"
            flagged={rejectedFields.includes("dateOfBirth")}
            field={"dateOfBirth"}
            required
          >
            <ProfileDateInput
              name="dateOfBirth"
              formatPattern="MMMM dd, yyyy"
              required
              validate={{
                isPastDate: (value) => {
                  const selectedDate = new Date(value);
                  const today = new Date();
                  return (
                    selectedDate < today || "Date of Birth must be in the past"
                  );
                },
              }}
            />
          </ProfileGridItem>
          <ProfileGridItem label="Senior">
            <div className="px-2">
              {isSenior(individual?.dateOfBirth || "") ? "Yes" : "No"}
            </div>
          </ProfileGridItem>
        </ProfileGrid>

        <ProfileGrid cols={1}>
          <ProfileGridItem
            label="Phone"
            flagged={rejectedFields.includes("homePhone")}
            field={"homePhone"}
            required
          >
            <ProfilePhoneInput name="homePhone" required />
          </ProfileGridItem>
        </ProfileGrid>

        <ProfileGrid cols={1}>
          <ProfileGridItem
            label="Email"
            flagged={rejectedFields.includes("primaryEmail")}
            field={"primaryEmail"}
            required
          >
            <ProfileInput
              name="primaryEmail"
              type="email" // Specify type for better keyboard layout
              required
              validate={{
                pattern: /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/, // Regex pattern
              }}
              errorMessage={{
                pattern: "Please enter a valid email address", // Error message
                required: "This field is required", // Required error message
              }}
            />
          </ProfileGridItem>
        </ProfileGrid>
      </ProfileSection>
    </EditProvider>
  );
};

export default PersonalInformation;
