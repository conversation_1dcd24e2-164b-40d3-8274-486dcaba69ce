import { Individual } from "@/types/IndividualType";
import { useUpdateEntityProfile2 } from "@/hooks/api/useProfiles";
import { useForm } from "react-hook-form";
import { FormValues } from "@/types/DogType";
import { useState } from "react";
import { useAtom } from "jotai";
import { toastAtom } from "@/components/ui/toast/toast";
import { useQueryClient } from "@tanstack/react-query";
import {
  EditDialog,
  EditDialogFooter,
  createFormData,
} from "@/app/(app)/(realm)/(protected)/(resident)/(registered)/resident/[entityType]/[entityId]/components/modals/EditDialog";
import {
  Comment,
  IndividualFirstName,
  IndividualLastName,
  IndividualMiddleName,
} from "@/components/dialog/EditFields";
import { useParams } from "next/navigation";

// Form Type
type EditNameForm = {
  firstName: string;
  middleName: string;
  lastName: string;
  comment?: string;
};

export const EditName = ({ individual }: { individual: Individual }) => {
  const initialValues: EditNameForm = {
    firstName: individual?.firstName || "",
    middleName: individual?.middleName || "",
    lastName: individual?.lastName || "",
    comment: "",
  };

  const updateEntity = useUpdateEntityProfile2();

  const {
    reset,
    register,
    handleSubmit,
    formState: { errors, isDirty },
  } = useForm<FormValues>({
    defaultValues: initialValues,
    mode: "onChange",
  });

  const [isOpen, setIsOpen] = useState(false);
  const [, setToast] = useAtom(toastAtom);
  const queryClient = useQueryClient();

  const handleCancel = () => {
    reset(initialValues);
    setIsOpen(false);
  };

  const handleSave = (data: any) => {
    const formData = createFormData(data);
    updateEntity.mutate(
      {
        entityId: individual.entityId,
        body: formData,
      },
      {
        onSuccess: () => {
          queryClient.invalidateQueries();
          reset(data);
          setToast({
            status: "success",
            label: "Individual Updated",
            message: "Successfully Updated Individual Information",
          });
          setIsOpen(false);
        },
        onError: (error: any) => {
          console.log(error);
          setToast({
            status: "error",
            label: "Error Updating Profile",
            message: error?.response?.data?.message,
          });
        },
      },
    );
  };

  return (
    <EditDialog title="Edit Name" isOpen={isOpen} setIsOpen={setIsOpen}>
      <form className="flex flex-col gap-4" onSubmit={handleSubmit(handleSave)}>
        <IndividualFirstName register={register} errors={errors} />
        <IndividualMiddleName register={register} errors={errors} />
        <IndividualLastName register={register} errors={errors} />
        <Comment register={register} />
        <EditDialogFooter handleCancel={handleCancel} disabled={!isDirty} />
      </form>
    </EditDialog>
  );
};

export default EditName;
