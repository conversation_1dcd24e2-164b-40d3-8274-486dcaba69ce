import EventDialog from "../EventDialog";

const DogDeceased = ({
  event,
  entityType,
  entityId,
}: {
  event: any;
  entityType: string;
  entityId: string;
}) => {
  return (
    <EventDialog
      event={event}
      title="You are about to mark this dog as deceased."
      description="Are you sure you want to do this?"
      entityId={entityId}
      entityType={entityType}
    />
  );
};

export default DogDeceased;
