import React, { useState } from "react";
import {
  AlertDialog,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";
import { Button } from "@/components/ui/button";
import { useHandleEvent } from "@/hooks/api/useProfiles";
import { useParams } from "next/navigation";
import { useAtom } from "jotai";
import { toastAtom } from "@/components/ui/toast/toast";
import { useQueryClient } from "@tanstack/react-query";

function EventDialog({
  title,
  description,
  event,
  entityId,
  entityType,
}: {
  title: string;
  description: string;
  event: any;
  entityId: string;
  entityType: string;
}) {
  const queryClient = useQueryClient();
  // const { entityId, entityType } = useParams();
  const handleEvent = useHandleEvent();

  const [open, setOpen] = useState(false);
  const [, setToast] = useAtom(toastAtom);

  return (
    <AlertDialog open={open}>
      <AlertDialogTrigger
        className="w-full text-left"
        onClick={(e) => {
          e.stopPropagation();
          setOpen(true);
        }}
      >
        {event.name ?? "Unknown"}
      </AlertDialogTrigger>
      <AlertDialogContent
        onClick={(e) => {
          e.stopPropagation();
        }}
      >
        <AlertDialogHeader>
          <AlertDialogTitle>{title ?? "No Title"}</AlertDialogTitle>
          <AlertDialogDescription>
            {description ?? "No Description"}
          </AlertDialogDescription>
        </AlertDialogHeader>
        <AlertDialogFooter>
          <Button
            variant="ghost"
            onClick={() => {
              setOpen(false);
            }}
          >
            Cancel
          </Button>
          <Button
            onClick={() => {
              handleEvent.mutate(
                {
                  entityId: entityId as string,
                  entityType: entityType as string,
                  eventType: event.code,
                  body: new FormData(),
                },
                {
                  onSuccess: () => {
                    queryClient.invalidateQueries();
                    setOpen(false);
                    setToast({
                      status: "success",
                      label: "Success",
                      message: "Profile Updated Successfully",
                    });
                  },
                  onError: (error: any) => {
                    console.log(error);
                    setToast({
                      status: "error",
                      label: "Error",
                      message: error?.response?.data?.message,
                    });
                  },
                },
              );
              setOpen(false);
            }}
          >
            Submit
          </Button>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  );
}

export default React.memo(EventDialog);
