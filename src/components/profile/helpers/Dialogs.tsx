import React, { useState } from "react";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { cn } from "@/lib/utils";

export const NewDialog = ({
  title,
  children,
  onSubmit,
  isDirty,
  onCancel,
  trigger,
}: {
  title: string;
  children: React.ReactNode;
  onSubmit: () => void;
  isDirty: boolean;
  onCancel: () => void;
  trigger?: React.ReactNode;
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const closeDialog = () => setIsOpen(false);

  const handleSave = () => {
    console.log(onSubmit);
    onSubmit();
    closeDialog();
  };

  const handleCancel = () => {
    closeDialog();
    onCancel();
  };

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger className="flex items-center">
        {trigger ? trigger : <Badge>Edit</Badge>}
      </DialogTrigger>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>{title}</DialogTitle>
        </DialogHeader>
        <DialogDescription>
          <div className="py-6 text-neutral-900">{children}</div>
        </DialogDescription>
        <DialogFooter>
          <Button
            variant="ghost"
            size="sm"
            type="button"
            onClick={handleCancel}
          >
            Cancel
          </Button>
          <Button
            variant="primary"
            size="sm"
            disabled={!isDirty}
            type="submit"
            onClick={handleSave}
          >
            Save
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export const Section = ({
  label,
  children,
  layout = "row"
}: {
  label: string;
  children: React.ReactNode;
  layout?: string;
}) => {
  return (
    <div className={cn("flex flex-col rounded",
      layout === "column" 
        ? "flex-col " 
        : "flex-col sm:flex-row sm:gap-4"
    )}>
      <div className="w-[180px] font-medium rounded px-0.5 shrink-0">{label}</div>
      <div className=" flex flex-wrap w-full items-center gap-1 sm:gap-4 break-all">
        {children}
      </div>
    </div>
  );
};
