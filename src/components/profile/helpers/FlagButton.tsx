import { use<PERSON>tom } from "jotai";
import { toast<PERSON>tom } from "@/components/ui/toast/toast";
import { useQueryClient } from "@tanstack/react-query";

import { FiFlag, FiLoader } from "react-icons/fi";
import { Button } from "@/components/ui/button";
import { cn } from "@/lib/utils";
import {
  useUpdateEntityRejectedFields,
  useDeleteEntityRejectedFields,
} from "@/hooks/api/useApprovals";

const FlagButton = ({
  entityId,
  entityType,
  flagged,
  field,
  setFlag,
}: {
  entityId: string;
  entityType: string;
  flagged: boolean;
  field: string;
  setFlag: (flagged: boolean) => void;
}) => {

  const updateRejectedFields = useUpdateEntityRejectedFields();
  const deleteRejectedFields = useDeleteEntityRejectedFields();

  const queryClient = useQueryClient();
  const [_, setToast] = useAtom(toastAtom);

  const addFlag = () => {
    updateRejectedFields.mutate(
      {
        entityType: entityType,
        entityId: entityId,
        body: {
          fields: [field],
        },
      },
      {
        onSuccess: () => {
          setFlag(!flagged);
          queryClient.invalidateQueries();
          setToast({
            status: "success",
            label: "Flag Updated",
            message: `Successfully Flagged`,
          });
        },
        onError: (error: any) => {
          console.log(error);
          setToast({
            status: "error",
            label: `Error Flagging`,
            message: error?.response?.data?.message,
          });
        },
      },
    );
  };

  const deleteFlag = () => {
    deleteRejectedFields.mutate(
      {
        entityType,
        entityId,
        field
      },
      {
        onSuccess: () => {
          queryClient.invalidateQueries();
          setFlag(!flagged);
          setToast({
            status: "success",
            label: "Flag Updated",
            message: `Successfully Removed Flag`,
          });
        },
        onError: (error: any) => {
          console.log(error);
          setToast({
            status: "error",
            label: `Error Removing Flagging`,
            message: error?.response?.data?.message,
          });
        },
      },
    );
  };

  return (
    <Button
      type="button"
      size={"sm"}
      variant={flagged ? "destructive" : "outline"}
      onClick={flagged ? deleteFlag : addFlag}
      className={cn(
        "m-0.5 flex h-5 items-center justify-center gap-1 text-xs",
        !flagged && "opacity-70",
      )}
    >
      {updateRejectedFields.isLoading || deleteRejectedFields.isLoading ? (
        <>
          <FiLoader className="animate-spin" />
          <span>Loading</span>
        </>
      ) : (
        <>
          <FiFlag /> <span>{flagged ? "Issue" : "Flag"}</span>
        </>
      )}
    </Button>
  );
};

export default FlagButton;
