import ContactResults from "./ContactResults";

type UserProfileProps = {
  userInformation: {
    contacts: {
      fieldName: string;
      iconImage: string;
      isPrimary: boolean;
      label: string;
      type: string;
      value: string;
    }[]
  }
}

const UserProfile = ({ userInformation }:UserProfileProps) => {
  
  if (!userInformation) return <div>No User Found</div>;

  console.log(userInformation)
  // const residency = userInformation?.residency ?? null;
  const contacts = userInformation?.contacts ?? [];

  return (
    <>
      {/* <ResidencyResults residency={residency} /> */}
      <ContactResults contacts={contacts} />
    </>
  )
}

export default UserProfile