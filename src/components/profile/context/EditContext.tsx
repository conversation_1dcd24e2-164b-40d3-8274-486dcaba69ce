import { toast<PERSON><PERSON> } from "@/components/ui/toast/toast";
import { use<PERSON>tom } from "jotai";
import React, {
  createContext,
  useContext,
  useState,
  ReactNode,
  useEffect,
} from "react";
import { FormProvider, useForm } from "react-hook-form";
import { UseMutationResult } from "@tanstack/react-query";

type UpdateEntityPayload = any;

type UpdateEntitySuccessResponse = any;
type UpdateEntityErrorResponse = any;

export type UpdateEntityType = UseMutationResult<
  UpdateEntitySuccessResponse,
  UpdateEntityErrorResponse,
  UpdateEntityPayload
>;

interface EditProviderProps {
  children: ReactNode;
  defaultValues: {
    [key: string]: any;
  };
  entity: any;
  entityId: string;
  entityType: string;
  entityRefetch: () => void;
  entityIsFetching: boolean;
  updateEntity: UpdateEntityType;
  updateEntitySuccessLabel?: {
    label?: string;
    message?: string;
  };
  updateEntityErrorLabel?: {
    label?: string;
    message?: string;
  };
}

const EditContext = createContext<EditContextType | undefined>(undefined);

export const useEditContext = () => {
  const context = useContext(EditContext);
  if (!context) {
    throw new Error("useEditContext must be used within an EditProvider");
  }
  return context;
};

export const EditProvider = ({
  children,
  defaultValues,
  entity,
  entityId,
  entityType,
  entityRefetch,
  entityIsFetching,
  updateEntity,
  updateEntitySuccessLabel = {
    label: "Profile Updated",
    message: "Successfully Updated Profile Information",
  },
  updateEntityErrorLabel = {
    label: "Error Updating Profile",
    message: "Error Updating Profile Information.  Please try again.",
  },
}: EditProviderProps) => {
  const [canEdit, setCanEdit] = useState(false);
  const [_, setToast] = useAtom(toastAtom);

  const methods = useForm({
    defaultValues,
  });

  const { reset } = methods;

  // useEffect(() => {
  //   if (!entityIsFetching && entity) {
  //     reset(defaultValues);
  //   }
  //   // eslint-disable-next-line react-hooks/exhaustive-deps
  // }, [entityIsFetching, entity]);

  useEffect(() => {
    if (updateEntity.isSuccess) {
      setToast({
        status: "success",
        label: updateEntitySuccessLabel.label,
        message: updateEntitySuccessLabel.message,
      });
      setCanEdit(false);
      entityRefetch(); 
    }
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [updateEntity.isSuccess, entityRefetch, setToast]);

  useEffect(() => {
    if (updateEntity.isError) {
      setToast({
        status: "error",
        label: updateEntityErrorLabel.label,
        message: updateEntityErrorLabel.message,
      });
    }
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [updateEntity.isError, setToast]);

  const handleCancel = () => {
    reset(defaultValues);
    setCanEdit(false);
  };

  const watchField = (field: string) => {
    return methods.watch(field);
  };

  return (
    <EditContext.Provider
      value={{
        canEdit,
        setCanEdit,
        defaultValues,
        entity,
        entityId,
        entityType,
        entityRefetch,
        entityIsFetching,
        handleCancel,
        watchField,
      }}
    >
      <FormProvider {...methods}>{children}</FormProvider>
    </EditContext.Provider>
  );
};

type EditContextType = {
  canEdit: boolean;
  setCanEdit: (canEdit: boolean) => void;
  defaultValues: {
    [key: string]: any;
  };
  entity: any;
  entityId: string;
  entityType: string;
  entityRefetch: () => void;
  entityIsFetching: boolean;
  handleCancel: () => void;
  watchField: (field: string) => any;
};
