import { useMyProfile } from "@/hooks/providers/useMyProfile";

export const InactiveAccountBar = () => {
  const { profile } = useMyProfile();
  const activeAccount = profile["individual"]?.active ?? false;
  if (activeAccount) return null;

  return (
    <div className="bg-red-600 px-1 py-0.5 text-center text-white ">
      Account is not active. If this is a mistake please contact your local city
      clerk&apos;s office.
    </div>
  );
};
