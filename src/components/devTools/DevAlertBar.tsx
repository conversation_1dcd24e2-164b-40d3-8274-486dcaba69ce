export const DevAlertBar = () => {
  const production = process.env.NEXT_PUBLIC_APP_ENV === "production";
  const staging = process.env.NEXT_PUBLIC_APP_ENV === "staging";
  const localhost = process.env.NEXT_PUBLIC_APP_ENV === "localhost";

  if (production) return null;
  if (staging)
    return (
      <div className="w-full bg-gradient-to-r from-blue-600 to-blue-700 px-6 py-0.5 text-center text-blue-50">
        Staging Environment
      </div>
    );

  if(localhost) return (
    <div className="w-full bg-gradient-to-r from-green-600 to-green-700 px-6 py-0.5 text-center text-blue-50">
      Localhost Environment
    </div>
  )

  return (
    <div className="w-full bg-gradient-to-r from-red-600 to-red-700 px-6 py-0.5 text-center text-blue-50">
      Developer Environment
    </div>
  );
};
