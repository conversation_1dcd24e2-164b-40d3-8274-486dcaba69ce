interface Requirement {
  minLength?: number;
  pattern?: string;
}

interface Field {
  id: string;
  label: string;
  type: string;
  required: boolean;
  requirements?: Requirement;
}

type CurrentContexts = Record<string, any>;

export const currencyFormatter = (value: number) => {
  return new Intl.NumberFormat("en-US", {
    style: "currency",
    currency: "USD",
  }).format(value);
};

export const allRequirementsMet = (
  requiredFields: Field[],
  currentContexts: CurrentContexts,
): boolean => {
  return requiredFields.every((field: Field) => {
    const fieldValue = currentContexts[field.id];
    if (fieldValue === null || fieldValue === undefined || fieldValue === "") {
      return false;
    }

    if (field.requirements && Object.keys(field.requirements).length > 0) {
      return Object.entries(field.requirements).every(
        ([requirement, requirementValue]) => {
          switch (requirement) {
            case "minLength":
              return fieldValue.length >= requirementValue;
            case "pattern":
              const pattern = new RegExp(requirementValue as string);
              return pattern.test(fieldValue);
            default:
              console.log(`Unknown requirement: ${requirement}`);
              return true;
          }
        },
      );
    }

    return true;
  });
};

export const getConditional = (path: string, object: any) => {
  return path.split(".").reduce((acc, part) => acc && acc[part], object);
};

export function evaluateUnless(unless: any, context: any){
  if(typeof unless === "object" && unless?.conditions){
    return evaluateConditions(unless, context);
  }
  if(typeof unless === "object" && unless?.permissions){
    return context?.hasPermissions ? !context.hasPermissions(unless?.permissions) : false;
  }
  return false;
}


export function extractPageStatesForNavigation(
  sidebar: {
    order: number;
    label: string;
    states: string[];
  }[],
  currentPageId: any,
) {
  const orderedSidebar = sidebar?.sort((a, b) => a.order - b.order) || [];

  const nav = orderedSidebar.map((page) => {
    const isActive = page.states.includes(currentPageId);
    return {
      ...page,
      isActive,
    };
  });

  return nav;
}

export function extractWantedHiddenPageStatesForNavigation(
  machineConfig: any,
  key: string,
) {
  let pages: { name: string; label: string }[] = [];

  function traverseStates(states: any) {
    Object.entries(states).forEach(
      ([stateName, stateDetails]: [string, any]) => {
        const currentForm = stateDetails.key;
        if (
          stateDetails.tags &&
          stateDetails.tags.includes("addToSideBar") &&
          currentForm === key
        ) {
          const previousPage = stateDetails.config?.on?.BACK?.target ?? "";
          const nextPage = stateDetails.config?.on?.NEXT?.target ?? "";

          const page = {
            name: stateName,
            label: stateDetails.meta?.sidebar?.label || stateName,
            description: stateDetails.meta?.sidebar?.description || "",
            hiddenPage: true,
            previousPage,
            nextPage,
          };
          pages.push(page);
        }

        if (stateDetails.states) {
          traverseStates(stateDetails.states);
        }
      },
    );
  }

  traverseStates(machineConfig.states);

  return pages;
}

interface Permission{
  permissions: string[];
}

interface Condition {
  field: string;
  value: any;
  comparison?:
    | "greaterOrEqual"
    | "lessOrEqual"
    | "lessThan"
    | "greaterThan"
    | "equals"
    | "coercion";
  conditions?: ConditionGroup;
  unless?: ConditionGroup | Permission;
}

interface ConditionGroup {
  conditionType?: "AND" | "OR";
  conditions: Condition[];
}

function evaluateCondition(condition: Condition, context: any): boolean {
  if(condition?.unless){
    return evaluateUnless(condition?.unless, context);
  }
  const fieldValue = getConditional(condition?.field, context);
  switch (condition?.comparison) {
    case "greaterOrEqual":
      return fieldValue >= condition?.value;
    case "lessOrEqual":
      return fieldValue <= condition?.value;
    case "lessThan":
      return fieldValue < condition?.value;
    case "greaterThan":
      return fieldValue > condition?.value;
    case "equals":
      return fieldValue === condition?.value;
    case "coercion":
      return !!fieldValue === !!condition?.value;
    default:
      return fieldValue === condition?.value;
  }
}

export function evaluateConditions(
  conditionsGroup: ConditionGroup,
  context: any,
): boolean {
  if (
    !conditionsGroup?.conditions ||
    conditionsGroup?.conditions?.length === 0
  ) {
    return true;
  }

  const evaluate = (condition: Condition) =>
    condition.conditions
      ? evaluateConditions(condition?.conditions, context)
      : evaluateCondition(condition, context);

  return conditionsGroup?.conditionType === "AND"
    ? conditionsGroup?.conditions.every(evaluate)
    : conditionsGroup?.conditions.some(evaluate);
}


export function evaluateRequiredOrConditions(required: any, context: any) {
  if (typeof required === "boolean") {
    return required;
  }

  if (typeof required === "object" && required.conditions) {
    return evaluateConditions(required, context);
  }

  return false;
}

// export function handlePath(obj: any, path: string) {
//   if (!path) return obj;
//   return path
//     .split(".")
//     .reduce((current: any, part: string) => current && current[part], obj);
// }

// export function handlePath2(obj: any, path: string) {
//   if (!path) return obj;
//   return path.split(".").reduce((current: any, part: string) => {
//     if (current === null || current === undefined) return undefined;
//     const match = part.match(/^(\w+)\[(\d+)\]$/);
//     if (match) {
//       const [, arrayPart, index] = match; 
//       return current[arrayPart] && current[arrayPart][index];
//     }
//     return current[part];
//   }, obj);
// }

// export function replacePathPlaceholders(template: any, context: any) {
//   return template.replace(/\{([^}]+)\}/g, (_: any, key: any) => {
//     const value = handlePath(context, key);
//     return value !== undefined ? value : _;
//   });
// }

type fields = {
  [key: string]: any;
}[];

// Function to grab all group children
export function getGroupChildren(fields: fields) {
  // Find all fields with a groupId
  const groupFields = fields.filter((field) => field.groupId);

  // Find all unique groupIds
  const groupIds = Array.from(
    new Set(groupFields.map((field) => field.groupId)),
  );

  // Create an object with the groupIds as keys and the fields as values
  const groupChildren = groupIds.reduce((acc, groupId) => {
    acc[groupId] = groupFields.filter((field) => field.groupId === groupId);
    return acc;
  }, {});

  // If group is empty return null
  if (Object.keys(groupChildren).length === 0) {
    return null;
  }

  return groupChildren;
}

// Function to grab group fields
export function getGroupFields(fields: fields, groupId: string) {
  return fields.filter(
    (field) =>
      field.id === groupId && field.type.toLowerCase().includes("group"),
  );
}

// Function to grab all group children (can be arrays or objects)
// Used with getContextValue to read the fields from the context
// Example: {{context:group1[0].field1}}
// function getNestedValueByPath(context: any, path: string) {
//   const pathParts = path
//     .split(/\.|\[|\]\./)
//     .map((part) => part.replace(/\]$/, ""));
//   let current = context;

//   for (const part of pathParts) {
//     if (current === null || current === undefined) {
//       console.error(
//         `Path error: reached a null or undefined value at '${part}'`,
//       );
//       return undefined;
//     }
//     if (part.match(/^\d+$/)) {
//       current = current[parseInt(part, 10)];
//     } else {
//       current = current[part];
//     }
//   }
//   return current;
// }