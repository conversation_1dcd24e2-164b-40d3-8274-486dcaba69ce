export type AnyValue =
  | string
  | number
  | undefined
  | boolean
  | null
  | AnyValue[]
  | { [key: string]: AnyValue };

interface DynamicObject {
  [key: string]: AnyValue;
}

export type FormProp = {
  id: string;
  formName: string;
  initial: string;
  context: ContextProp;
  states: StatesProp;
  actions: ActionsProp;
  actors: ActorsProp;
};

export type ContextProp = {
  form: {
    [key: string]: AnyValue;
  };
  error: AnyValue;
  entityId?: string;
  entityType?: string;
};

export type StatesProp = {
  [key: string]: {
    id: string;
    tags: TagsProp[];
    invoke?: InvokeProp;
    meta?: MetaProp;
    on?: OnProp;
  };
};

type TagsProp = "loading" | "page" | "error" | "submit" | "hiddenPage";

export type InvokeProp = {
  src: string;
  input: any;
  onDone: {
    target: string;
    actions: string[];
  };
  onError: {
    target: string;
    actions: string[];
  };
};

export type MetaProp = {
  form: string;
  label: string;
  description: string;
  title?: string;
  paragraph?: string;
  content?: string;
  feilds?: FieldsProp;
  navigation: NavigationProp[];
};

export type FieldsProp = {
  id: string;
  label: string;
  type: string;
  required: ConditionalProp | boolean;
  displayConditions: ConditionalProp;
  options?: AnyValue[];
};

export type ConditionalProp = {
  conditionType?: "OR" | "AND";
  conditions?: {
    field: string;
    value: string;
  }[];
};

export type NavigationProp = {
  type: "button" | "text" | "link";
  label: string;
  action: string;
  variant: "primary" | "secondary" | "destructive" | "ghost";
};

export type OnProp = {
  [key: string]: {
    target?: string;
    actions?: any;
    guard?: any;
  };
};

// ACTIONS

export type ActionsProp = {
  [key: string]: ActionProp ;
};

export interface ActionProp {
  type: "assign" | "navigate" | "assignEvent";
}

export interface ActionAssignProp extends ActionProp {
  type: "assign";
  from: ActionFromProp;
  saveTo: ActionSaveToProp;
}

export interface ActionAssignEventProp extends ActionProp {
  type: "assignEvent";
  context: { [key: string]: any };
}

export interface ActionNavigateProp extends ActionProp {
  type: "navigate";
  goTo: string;
  handle: string;
}

export type ActionFromProp = {
  type: "event" | "context";
  value: string;
  criteria?: ConditionalProp;
};

export type ActionSaveToProp = {
  type: "context" | "event";
  value: string;
};

// Actors

export type ActorsProp = {
  [key: string]: ActorProp;
};

export interface ActorProp {
  // 
  description?: string;

  type?: "rest" | "function";
  method?: ActorMethods;
  format?: ActorFormat;
  url?: string;
  body?: ActorBodyProp[];
  inputs?: DynamicObject;
  // Functions
  function?: string;
  values?: DynamicObject;
}

export type ActorMethods = "GET" | "POST" | "PUT" | "DELETE" | "PATCH";
export type ActorFormat = "json" | "formData";

export type ActorBodyProp = {
  [key: string]: string | ActorBodyProp;
};
