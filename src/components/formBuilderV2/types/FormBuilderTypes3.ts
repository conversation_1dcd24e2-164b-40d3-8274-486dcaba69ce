export interface MachineConfig {
  id: string;
  initialPage: string;
  formName: string;
  functions: {
    [key: string]: FunctionConfig;
  };
  pages: {
    [key: string]: PagesConfig;
  }[];
  context: {
    [key: string]: any;
  };
  sidebar: {
    label: string;
    order: number;
    pages: string[];
  }[];
}

export interface FunctionConfig {
  type: "rest" | "function";
  function?: string;
  url?: string;
  body?: {
    [key: string]: string;
  };
  parameters?:{
    [key: string]: string;
  }
  format?: "formData" | "json";
  method?: "GET" | "POST" | "PUT" | "DELETE" | "PATCH";
  description?: string;
}

export interface PagesConfig {
  settings: ("error" | "hideSidebar" | "loading" | "hiddenPage")[];
}

export interface ContentPageConfig extends PagesConfig {
  id: string;
  title: string;
  content?: string;
  paragraph?: string;
  components?: any[];
  disclaimer?: string;
  description?: string;
  navigation: NavigationConfig[];
  fields?: Fields[] 
}

export interface Fields {
  id: string;
  type:
    | "text"
    | "select"
    | "checkbox"
    | "radio"
    | "date"
    | "file"
    | "textarea"
    | "table"
    | "customSelect ";
  label: string;
  title: string;
  value?: string;
  groupId?: string;
  disabled?: boolean;
  required?: Conditions[] | boolean;
  displayConditions?: Conditions[] | boolean;
  className?: string;
  information: string;
  placeholder?: string;
  validate?: ValidateOption[];
  size?: number;
}

export type ValidateOption = {
  type:
    | "charMaxLength"
    | "charMinLength"
    | "charExactLength"
    | "charMinMaxLength"
    | "numberMin"
    | "numberMax"
    | "numberMinLength"
    | "numberMaxLength"
    | "pattern"
    | "exact"
    | "datePastMin"
    | "datePastMax"
    | "futureMin"
    | "futureMax"
    | "emailExists"
    | "registrationCodeExists"
    | "oneOf"
  value: number | string | [number, number] | RegExp;
  values?: string[]; // For oneOf validation
  unit?: string;
  message: string;
};

export interface SelectField extends Fields {
  options:
    | {
        label: string;
        value: string;
      }[]
    | string;
}

export interface FileField extends Fields {
  acceptedFileType: ("jpeg" | "jpg" | "png" | "pdf")[];
}

type ConditionalField = {
  field: string;
  value: string | boolean;
  conditions?: Conditions;
};

type Conditions = {
  conditionsType?: "and" | "or";
  conditions: ConditionalField[];
};

export interface NavigationConfig {
  type: "button" | "link";
  label: string;
  variant?: "primary" | "secondary" | "ghost" | "destructive";
  operation?: "back";
  action?: ButtonAction;
  navigate?: NavigationLink;
}

export interface NavigationLink {
  url: string;
  handle?: string;
}

export interface GuardAction {
  field: string;
  value: string | boolean | number | (string | number)[];
  matchType:
    | "exact"
    | "contains"
    | "startsWith"
    | "endsWith"
    | "notNull"
    | "isNull"
    | "boolean";
  logic?: "and" | "or";
}

export interface ButtonAction {
  goToPage?: string;
  goToLink?: string;
  assignEvent?: { [key: string]: string };
  guard?: GuardAction;
  callApi?: string;
  onSuccess?: ButtonAction;
  onError?: ButtonAction;
  onLoading?: ButtonLoading;
}

interface ButtonLoading {
  label?: string;
  title?: string;
  message?: string;
  modal?: boolean;
}


export type PlaceholderValue =
| string
| number
| boolean
| null
| File
| PlaceholderValue[]
| { [key: string]: PlaceholderValue };
