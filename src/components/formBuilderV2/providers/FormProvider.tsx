import React, { createContext, useContext, useState, useEffect } from "react";
import { useMachineContext } from "./MachineProvider";
import {
  addDays,
  addMonths,
  addYears,
  isAfter,
  isBefore,
  isValid,
  parseISO,
} from "date-fns";
import { ValidateOption } from "../types/FormBuilderTypes3";
import { requests } from "@/utils/agent";

type ErrorType = Record<string, any>;

type FormContextType = {
  errors: ErrorType;
  setErrors: (errors: ErrorType) => void;
  validity: ErrorType;
  setFieldError: (name: string, error: any) => void;
  validateFields: () => Promise<boolean>;
  isValid: boolean;
  clearFieldError: (name: string) => void;
  isLoading: boolean;
  resetValidation: () => void;
};

const FormContext = createContext<FormContextType | undefined>(undefined);

export const useFormContext = () => {
  const context = useContext(FormContext);
  if (!context) {
    throw new Error("useFormContext must be used within a FormProvider");
  }
  return context;
};

export const FormProvider = ({
  children,
  fields = [],
}: {
  children: React.ReactNode;
  fields?: any[] | undefined | null;
}) => {
  // Machine Context
  const { context, getContextValue, currentPage, replaceContextPlaceholders } =
    useMachineContext();

  // States
  const [errors, setErrors] = useState<ErrorType>({});
  const [validity, setValidity] = useState<ErrorType>({});
  const [isValid, setIsValid] = useState<boolean>(true);
  const [isLoading, setIsLoading] = useState<boolean>(false);

  // Reset Validation
  const resetValidation = () => {
    setErrors({});
    setValidity({});
    setIsValid(true);
  };

  // Set field error
  const setFieldError = (name: string, error: any) => {
    setErrors((prevErrors) => ({
      ...prevErrors,
      [name]: error,
    }));
  };

  // Remove field error
  const clearFieldError = (name: string) => {
    setErrors((prevErrors) => {
      const { [name]: _, ...newErrors } = prevErrors;
      return newErrors;
    });
  };

  // Check if conditions are met
  const areConditionsMet = (
    conditions: any[],
    matchType?: "all" | "any",
  ): boolean => {
    if (matchType === "all") {
      return conditions.every((condition) => {
        const fieldValue = getContextValue(condition.field);
        return fieldValue === condition.value;
      });
    }

    return conditions.some((condition) => {
      const fieldValue = getContextValue(condition.field);
      return fieldValue === condition.value;
    });
  };

  const validateFields = async (): Promise<boolean> => {
    setIsLoading(true); // Start loading
    const newErrors: ErrorType = {};
    if (!fields) {
      setIsLoading(false);
      return false;
    }

    for (const field of fields) {
      const value = getContextValue(field.id);
      if (
        field?.displayConditions &&
        !areConditionsMet(field.displayConditions.conditions)
      ) {
        continue;
      }

      let isRequired = false;
      if (typeof field.required === "boolean") {
        isRequired = field.required;
      } else if (field.required?.conditions) {
        isRequired = areConditionsMet(field.required.conditions);
      }

      // If the field is required and the value is empty
      if (isRequired && !value) {
        newErrors[field.id] = `${field.label} is required.`;
      } else if (value) {
        // Perform validation if the field has a value
        if (field.validate) {
          console.log(field.validate);

          // Handle async validation
          const validationError = await validateOptions(
            field.validate,
            replaceContextPlaceholders,
          )(value);

          console.log(validationError);
          if (
            typeof validationError === "string" ||
            typeof validationError === "object"
          ) {
            newErrors[field.id] = validationError;
          }
        }
      }
    }

    setValidity(newErrors);
    const valid = Object.keys(newErrors).length === 0;
    setIsValid(valid);
    setIsLoading(false); // End loading
    return valid;
  };

  useEffect(() => {
    validateFields();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [context, currentPage]);

  useEffect(() => {
    let newErrors = { ...errors };
    Object.keys(errors).forEach((key) => {
      if (!validity[key]) {
        delete newErrors[key];
      }
    });
    setErrors(newErrors);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [validity]);

  return (
    <FormContext.Provider
      value={{
        errors,
        setErrors,
        setFieldError,
        validateFields,
        clearFieldError,
        isValid,
        validity,
        isLoading,
        resetValidation,
      }}
    >
      {children}
    </FormContext.Provider>
  );
};

const validateOptions = (
  validations: ValidateOption[],
  replaceContextPlaceholders: any,
) => {
  return async (value: number | string) => {
    if (!value || value === "") {
      return "This field is required.";
    }

    console.log(validations);

    for (let rule of validations) {
      let result;

      rule = {
        ...rule,
        value:
          typeof rule.value === "string"
            ? replaceContextPlaceholders(rule.value)
            : rule.value,
      };

      console.log(rule.type);

      switch (rule.type) {
        case "charMaxLength":
          result = validateCharMaxLength(value as string, rule);
          break;
        case "charMinLength":
          result = validateCharMinLength(value as string, rule);
          break;
        case "charExactLength":
          result = validateCharExactLength(value as string, rule);
          break;
        case "charMinMaxLength":
          result = validateCharMinMaxLength(value as string, rule);
          break;
        case "numberMin":
        case "numberMax":
        case "numberMinLength":
        case "numberMaxLength":
          result = validateNumber(value as number, rule);
          break;
        case "pattern":
          result = validatePattern(value as string, rule);
          break;
        case "exact":
          result = validateExact(value as string, rule);
          break;
        case "datePastMin":
        case "datePastMax":
        case "futureMin":
        case "futureMax":
          result = validateDate(value as string, rule);
          break;
        case "emailExists":
          result = await validateEmail(value as string);
          break;
        case "registrationCodeExists":
          result = await validateRegistrationCode(value as string);
          break;
        case "oneOf":
          result = validateOneOf(value as string, rule);
          break;
        default:
          break;
      }

      if (result) return result;
    }

    return true;
  };
};

const validateCharMaxLength = (value: string, rule: ValidateOption) => {
  if (value.length > (rule.value as number)) {
    return rule.message ?? `Value must be less than ${rule.value} characters.`;
  }
};

const validateCharMinLength = (value: string, rule: ValidateOption) => {
  if (value.length < (rule.value as number)) {
    return rule.message ?? `Value must be at least ${rule.value} characters.`;
  }
};

const validateCharExactLength = (value: string, rule: ValidateOption) => {
  if (value.length !== (rule.value as number)) {
    return rule.message ?? `Value must be exactly ${rule.value} characters.`;
  }
};

const validateCharMinMaxLength = (value: string, rule: ValidateOption) => {
  if (Array.isArray(rule.value) && rule.value.length === 2) {
    const [min, max] = rule.value as [number, number];
    if (value.length < min || value.length > max) {
      return (
        rule.message ?? `Value must be between ${min} and ${max} characters.`
      );
    }
  } else {
    console.error(
      "Validation error: value must be a tuple [number, number] for charMinMaxLength.",
    );
    return "Invalid validation configuration.";
  }
};

const validateNumber = (value: number, rule: ValidateOption) => {
  switch (rule.type) {
    case "numberMin":
      if (value < (rule.value as number)) {
        return rule.message ?? `Value must be greater than ${rule.value}.`;
      }
      break;
    case "numberMax":
      if (value > (rule.value as number)) {
        return rule.message ?? `Value must be less than ${rule.value}.`;
      }
      break;
    case "numberMinLength":
      if (value.toString().length < (rule.value as number)) {
        return rule.message ?? `Value must have at least ${rule.value} digits.`;
      }
      break;
    case "numberMaxLength":
      if (value.toString().length > (rule.value as number)) {
        return (
          rule.message ?? `Value must have less than ${rule.value} digits.`
        );
      }
      break;
    default:
      break;
  }
};

const validatePattern = (value: string, rule: ValidateOption) => {
  const regex = new RegExp(rule.value as string);
  if (!regex.test(value)) {
    return rule.message ?? `Value does not match the required pattern.`;
  }
};

const validateExact = (value: string, rule: ValidateOption) => {
  if (value !== (rule.value as string)) {
    return rule.message ?? `Value must exactly match "${rule.value}".`;
  }
};

const validateDate = (value: string, rule: ValidateOption) => {
  const unit = rule.unit ?? "years";
  const amount = Number(rule.value) || 0;

  // compute threshold
  let targetDate: Date;
  if (rule.type === "futureMin" || rule.type === "futureMax") {
    // positive offset for future checks
    switch (unit) {
      case "months": targetDate = addMonths(new Date(),  amount); break;
      case "days":   targetDate = addDays(  new Date(),  amount); break;
      default:       targetDate = addYears( new Date(),  amount); break;
    }
  } else {
    // past checks remain negative offset
    switch (unit) {
      case "months": targetDate = addMonths(new Date(), -amount); break;
      case "days":   targetDate = addDays(  new Date(), -amount); break;
      default:       targetDate = addYears( new Date(), -amount); break;
    }
  }

  const parsedDate = parseISO(value);
  if (!isValid(parsedDate)) return "Invalid date format.";

  // apply correct comparisons
  if (
    (rule.type === "datePastMin"  && isAfter(parsedDate,  targetDate)) ||
    (rule.type === "datePastMax"  && isBefore(parsedDate, targetDate)) ||
    (rule.type === "futureMin"    && isBefore(parsedDate, targetDate)) ||
    (rule.type === "futureMax"    && isAfter(parsedDate,  targetDate))
  ) {
    return rule.message ?? `Date must satisfy ${rule.type} ${amount} ${unit}.`;
  }

  return null;
};

const validateOneOf = (value: string, rule: ValidateOption) => {
  const allowedValues = rule.values as string[];
  if (!allowedValues || !Array.isArray(allowedValues)) {
    console.error("Validation error: 'values' must be an array for oneOf validation.");
    return "Invalid validation configuration.";
  }
  
  if (!allowedValues.includes(value)) {
    return rule.message ?? `Value must be one of: ${allowedValues.join(', ')}.`;
  }
  
  return null;
};


export type EmailExistsProp = {
  firstName: string;
  lastName: string;
  entityId: string;
} | null;

export interface ValidateEmailResult {
  error: string | null;
  existingUser: EmailExistsProp;
}

export const validateEmail = async (
  email: string,
): Promise<ValidateEmailResult | string | null> => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;

  // If the email doesn't match the regex, return an error string
  if (!emailRegex.test(email)) {
    return "Invalid email format";
  }

  try {
    const response = await requests.get<EmailExistsProp>(
      `/license/search/emailExists/${email}`,
    );

    if (response) {
      console.log(response);
      return {
        error: "Email already exists.",
        existingUser: response,
      };
    } else {
      return null;
    }
  } catch (error) {
    console.error("Failed to check email", error);
    return { error: "Error checking email.", existingUser: null };
  }
};

export const validateRegistrationCode = async (
  registrationCode: string,
): Promise<{ existingUser: any } | string | null> => {
  try {
    const response = await requests.get(
      `/license/me/merge-by-code/${registrationCode}/exists`,
    );
    console.log(response);
    if (response) {
      return null
    } else {
      return "Invalid registration code. Please ensure the code is entered correctly, including all uppercase and lowercase letters, and try again.";
    }
  } catch (error) {
    console.error("Failed to check registration code", error);
    return "Error checking registration code.";
  }
};
