"use client";
import React, { ReactNode, useState } from "react";
import { useSearchParams } from "next/navigation";
import {
  ContentPageConfig,
  FunctionConfig,
  GuardAction,
  MachineConfig,
  PlaceholderValue,
} from "../types/FormBuilderTypes3";

import { toast<PERSON>tom } from "@/components/ui/toast/toast";
import { useAtom } from "jotai";
import { useMyProfile } from "@/hooks/providers/useMyProfile";
import { requests } from "@/utils/agent";
import { useKeycloak } from "@/hooks/useKeycloak";
import { useMyCart } from "@/hooks/useMyCart";
import { ActiveCartItem } from "@/types/CartType";

interface MachineProviderProps {
  config: any;
  additionalContext?: any;
  children: ReactNode;
}

export default function MachineProvider({
  config,
  additionalContext = {},
  children,
}: MachineProviderProps) {
  const searchParams = useSearchParams();
  const { profile } = useMyProfile();
  const [_, setToast] = useAtom(toastAtom);
  const { logout } = useKeycloak();
  const { refetchProfile, hasPermissions } = useMyProfile();
  const { cartSummary, cartRefetch, removeFromCart } = useMyCart();

  // Current Page
  const [currentPage, setCurrentPage] = useState({
    id: config.initialPage,
    ...config.pages[config.initialPage],
  });

  // Context
  const [context, setContext] = useState(() => {
    const mergedContext = deepMerge({ ...config.context }, additionalContext);

    const getSearchParams = () => {
      const searchParamsObject = Object.fromEntries(searchParams.entries());
      const updatedContext = mergedContext;

      for (const [key, value] of Object.entries(searchParamsObject)) {
        const keys = key.split(".");
        let current = mergedContext;

        for (let i = 0; i < keys.length - 1; i++) {
          if (!current[keys[i]]) {
            current[keys[i]] = {};
          }
          current = current[keys[i]];
        }

        current[keys[keys.length - 1]] = value;
      }

      return updatedContext;
    };

    return {
      myId: profile?.individual?.entityId ?? "No EntityID",
      myFirstName: profile?.individual?.firstName ?? "No First Name",
      myLastName: profile?.individual?.lastName ?? "No Last Name",
      myEmail: profile?.individual?.email ?? "No Email",
      errorMessage: null,
      errorStatus: null,
      errorResults: null,
      ...getSearchParams(),
    };
  });

  // Debug mode
  const [debugMode, setDebugMode] = useState<boolean>(false);

  // Modal state for API loading
  const [modalConfig, setModalConfig] = useState<{
    title?: string;
    message?: string;
  } | null>(null);

  // Allows you to change pages
  function changePage(pageId: string) {
    setCurrentPage({
      id: pageId,
      ...config.pages[pageId],
    });
  }

  // Function to update and get context values
  function updateContextValue(key: string, value: any) {
    setContext((prevContext: Record<string, any>) => {
      const keys = key.split(".");
      const lastKey = keys.pop();

      let nestedContext = { ...prevContext };

      let currentLevel = nestedContext;
      for (const k of keys) {
        if (!currentLevel[k]) {
          currentLevel[k] = {};
        }
        currentLevel = currentLevel[k];
      }

      if (lastKey) {
        currentLevel[lastKey] = value;
      }

      return nestedContext;
    });
  }

  // Function to check guard conditions
  function checkGuardConditions({
    field,
    value,
    matchType,
    logic = "or",
  }: GuardAction): boolean {
    const fieldValue = getContextValue(field);

    const normalize = (val: any) =>
      val === null || val === undefined
        ? null
        : typeof val === "boolean"
          ? val
          : typeof val === "string"
            ? val.toLowerCase()
            : Array.isArray(val)
              ? val.map((item) => typeof item === "string" ? item.toLowerCase() : item)
              : val;

    const normalizedFieldValue = normalize(fieldValue);
    const normalizedValue = Array.isArray(value)
      ? value.map(normalize)
      : normalize(value);

    const checkCondition = (val: any): boolean => {
      switch (matchType) {
        case "exact":
          return normalizedFieldValue === val;
        case "contains":
          return normalizedFieldValue?.includes(val);
        case "startsWith":
          return normalizedFieldValue?.startsWith(val);
        case "endsWith":
          return normalizedFieldValue?.endsWith(val);
        case "boolean":
          return normalizedFieldValue === val;
        case "notNull":
          return (
            normalizedFieldValue !== null && normalizedFieldValue !== undefined
          );
        case "isNull":
          return normalizedFieldValue === null;
        default:
          return false;
      }
    };

    if (Array.isArray(normalizedValue)) {
      if (logic === "and") {
        return normalizedValue.every(checkCondition);
      } else {
        return normalizedValue.some(checkCondition);
      }
    } else {
      return checkCondition(normalizedValue);
    }
  }

  // Function to replace context placeholders
  function replaceContextPlaceholders(
    value: PlaceholderValue,
  ): PlaceholderValue {
    if (typeof value === "string") {
      // Check if the entire string exactly matches a context placeholder
      const fullPlaceholderRegex = /^{{context:([^}]+)}}$/;
      const fullMatch = value.match(fullPlaceholderRegex);
      if (fullMatch) {
        const path = fullMatch[1];
        // Assume getContextValue returns a PlaceholderValue
        const contextValue = getContextValue(path);
        return contextValue as PlaceholderValue;
      }

      // Otherwise, perform the usual replacement for embedded placeholders
      return value.replace(/{{context:([^}]+)}}/g, (_, path) => {
        const contextValue = getContextValue(path);
        // Returning a File here would force conversion to a string,
        // so we expect that file placeholders are only full matches.
        return contextValue instanceof File
          ? contextValue
          : contextValue?.toString() || "";
      });
    } else if (Array.isArray(value)) {
      return value.map((item) => replaceContextPlaceholders(item));
    } else if (typeof value === "object" && value !== null) {
      return Object.fromEntries(
        Object.entries(value).map(([key, val]) => [
          key,
          val === null
            ? null
            : val instanceof File
              ? val
              : replaceContextPlaceholders(val),
        ]),
      );
    } else {
      // For numbers, booleans, or other primitives, return as-is.
      return value;
    }
  }

  // Function to replace setting placeholders
  async function replaceSettingPlaceholders(
    value: PlaceholderValue,
  ): Promise<PlaceholderValue> {
    if (typeof value === "string") {
      const fullPlaceholderRegex = /^{{settings:\s*(\w+)\.(\w+)\.(\w+)\s*}}$/;
      const fullMatch = value.match(fullPlaceholderRegex);
      if (fullMatch) {
        const category = fullMatch[1];
        const type = fullMatch[2];
        const option = fullMatch[3];
        const url = hasPermissions(["super-admin"])
          ? `/config/json-storage?category=${category}&type=${type}&option=${option}`
          : `/config/me/json-storage?category=${category}&type=${type}&option=${option}`;
        try {
          const response = await requests.get(url);
          return (response as any).data ?? response;
        } catch (error: any) {
          console.error("Error fetching settings for", category, type, option, error);
          return value;
        }
      } else if (value.includes("{{settings:")) {
        const dynamicSettingsRegex = /{{settings:\s*(\w+)\.(\w+)\.(\w+)\s*}}/g;
        let newValue = value;
        for (const match of Array.from(value.matchAll(dynamicSettingsRegex))) {
          const [placeholder, category, type, option] = match;
          const url = hasPermissions(["super-admin"])
            ? `/config/json-storage?category=${category}&type=${type}&option=${option}`
            : `/config/me/json-storage?category=${category}&type=${type}&option=${option}`;
          try {
            const response = await requests.get(url);
            const replacement =
              typeof response === "string"
                ? response
                : ((response as any).data ?? JSON.stringify(response));
            newValue = newValue.replace(placeholder, replacement);
          } catch (error: any) {
            console.error("Error fetching settings for", category, type, option, error);
            newValue = newValue.replace(placeholder, "");
          }
        }
        return newValue;
      }
      return value;
    } else if (Array.isArray(value)) {
      const promises = value.map((item) => replaceSettingPlaceholders(item));
      return Promise.all(promises);
    } else if (typeof value === "object" && value !== null) {
      const entries = await Promise.all(
        Object.entries(value).map(async ([key, val]) => [
          key,
          await replaceSettingPlaceholders(val),
        ]),
      );
      return Object.fromEntries(entries);
    } else {
      return value;
    }
  }
  

  // Get context value by key with array bracket notation support
  function getContextValue(path: string) {
    if (!path) return context;
    
    const keys = path.split(".");
    let currentValue = context;

    for (let key of keys) {
      if (currentValue === null || currentValue === undefined) {
        console.log(`Context path "${path}" failed at key "${key}" - currentValue is null/undefined`);
        return null;
      }
      
      // Check if the key has array bracket notation like "dogs[0]"
      const arrayMatch = key.match(/^(\w+)\[(\d+)\]$/);
      if (arrayMatch) {
        const [, arrayKey, index] = arrayMatch;
        if (currentValue[arrayKey] && Array.isArray(currentValue[arrayKey])) {
          currentValue = currentValue[arrayKey][parseInt(index, 10)];
        } else {
          console.log(`Context path "${path}" failed at array access "${arrayKey}[${index}]"`);
          return null;
        }
      } else {
        // Regular property access
        if (currentValue[key] !== undefined) {
          currentValue = currentValue[key];
        } else {
          console.log(`Context path "${path}" failed at key "${key}" - key does not exist`);
          return null;
        }
      }
    }

    return currentValue;
  }

  // Handle API calls
  const handleAPICall = async (apiCall: FunctionConfig, onLoading?: { modal?: boolean; title?: string; message?: string }) => {
    
    switch (apiCall.type) {
      // Internal API calls
      case "rest":
        const url = replaceContextPlaceholders(apiCall.url as string);
        let resolvedBody = await replaceSettingPlaceholders(apiCall.body ?? {});
        resolvedBody = replaceContextPlaceholders(resolvedBody);
        
        console.log("Making REST call to:", url);
        console.log("Method:", apiCall.method ?? "GET");
        console.log("Body:", resolvedBody);
        console.log("Format:", apiCall.format ?? "json");
        
        try {
          const result = await handleRestActor(
            apiCall.method ?? "GET",
            url as string,
            resolvedBody,
            apiCall.format ?? "json",
          );
          console.log("API call successful, result:", result);
          return result;
        } catch (error) {
          console.log("API call failed with error:", error);
          throw error;
        }

      // Defined functions, must exist to work.
      case "function":
        const resolvedParameters: any = {};
        Object.entries(apiCall?.parameters ?? {}).forEach(([key, value]) => {
          resolvedParameters[key] = replaceContextPlaceholders(value);
        });


        switch (apiCall.function) {
          case "fullRefresh": {
            window.location.reload();
            break;
          }

          case "logout": {
            logout();
            break;
          }

          case "addToCart":
            const admin = hasPermissions(["super-admin"]);
            const isItemInCart = (
              entityId: string,
              entityType: string,
            ): boolean => {
              if (!cartSummary) return false;
              if (entityType === "tag") return false;
              return cartSummary.items.some(
                (item: ActiveCartItem) =>
                  item.itemId === entityId && item.itemType === entityType,
              );
            };

            if (!cartSummary) {
              setToast({
                label: "Error Adding To Cart",
                message: "No active cart",
                status: "error",
              });
              break;
            }

            const url = admin
              ? `/coordinator/cart/${cartSummary.cartId}/add`
              : `/coordinator/me/cart/${cartSummary.cartId}/add`;

            const { entityId, entityType } = resolvedParameters;

            try {
              if (isItemInCart(entityId, entityType)) {
                const removedItem = cartSummary.items.find(
                  (item: ActiveCartItem) =>
                    item.itemId === entityId && item.itemType === entityType,
                )?.cartItemId;

                if (removedItem) {
                  removeFromCart(removedItem);
                }
              }

              const response = await requests.post<any>(url, {
                itemType: entityType,
                itemId: entityId,
              });
              if (response) {
                cartRefetch();
                setToast({
                  label: "Item Added To Cart",
                  message: "Item has been added to cart",
                  status: "success",
                });
              }
              break;
            } catch (error: any) {
              const errorMsg = error.response?.data?.message || error.message;
              setToast({
                label: "Error Adding To Cart",
                message: errorMsg,
                status: "error",
              });
              break;
            }

          case "refetchProfile":
            refetchProfile();
            break;

          case "clearActiveTenant":
            try {
              await requests.post(`/config/me/tenant/user/clear-active`, {});
            } catch (error: any) {
              const errorMsg = error.response?.data?.message || error.message;
              setToast({
                label: "Error Clearing Active Tenant",
                message: errorMsg,
                status: "error",
              });
              break;
            }

          default:
            console.log("Unknown function type:", apiCall.function);
        }
        break;
      default:
        console.log('Missing actor type. "rest" or "function"  Current Value:', apiCall.type);
        break;
    }
  };

  // Handle REST calls
  const handleRestActor = async (
    method: string,
    url: string,
    body: any,
    format: string,
  ) => {
    console.log("=== handleRestActor Debug ===");
    console.log("Method:", method);
    console.log("URL:", url);
    console.log("Body:", body);
    console.log("Format:", format);
    
    try {
      let requestBody: any;
      if (format === "formData") {
        requestBody = new FormData();
        Object.entries(body ?? {}).forEach(([key, value]) => {
          console.log(`Adding to FormData: ${key} =`, value);
          requestBody.append(
            key,
            value instanceof File
              ? value
              : typeof value === "object"
                ? JSON.stringify(value)
                : String(value),
          );
        });
        console.log("Final FormData entries:");
        for (let [key, value] of requestBody.entries()) {
          console.log(`  ${key}:`, value);
        }
      } else {
        requestBody = body;
      }

      console.log("About to make request with body:", requestBody);
      let result;
      
      switch (method) {
        case "GET":
          result = await requests.get(url);
          break;
        case "POST":
          result = await requests.post(url, requestBody);
          break;
        case "PUT":
          result = await requests.put(url, requestBody);
          break;
        case "DELETE":
          result = await requests.del(url, { data: requestBody });
          break;
        case "PATCH":
          result = await requests.patch(url, requestBody);
          break;
      }
      
      console.log("Request completed successfully, result:", result);
      return result;
    } catch (error: any) {
      console.log("Request failed with error:", error);
      throw error;
    }
  };

  // Handle events
  const handleResponse = async (results: any, assignEvent: any) => {
    const cloneContext = { ...context };

    const resolvedAssignEvent = Object.entries(assignEvent).reduce(
      (acc, [contextKey, eventPath]) => {

        let resolvedValue: any = eventPath;

        // If the eventPath is a string and contains a placeholder
        if (typeof eventPath === "string" && eventPath.includes("{{event:")) {
          const matches = eventPath.match(/{{event:([^}]+)}}/);
          if (matches) {
            const path = matches[1];
            const keys = path.split(".");
            let value = results;
            
            // Handle case where results is undefined/null
            if (results === undefined || results === null) {
              console.log(`Results is ${results}, setting ${contextKey} to null`);
              resolvedValue = null;
            } else {
              for (const key of keys) {
                const arrayMatch = key.match(/(.+?)\[(.+?)='(.+?)'\]/);
                if (arrayMatch) {
                  const [, arrayKey, filterKey, filterValue] = arrayMatch;
                  if (value && value[arrayKey]) {
                    value = value[arrayKey].find(
                      (item: any) => item[filterKey] === filterValue,
                    );
                  } else {
                    value = null;
                  }
                } else {
                  if (value && typeof value === 'object' && key in value) {
                    value = value[key];
                  } else {
                    console.log(`Key "${key}" in path "${path}" is undefined, setting to null`);
                    value = null;
                    break;
                  }
                }
                if (value === undefined) {
                  console.log(`Key "${key}" in path "${path}" is undefined, setting to null`);
                  value = null;
                  break;
                }
              }
              resolvedValue = value;
            }
          }
        } else {
          resolvedValue = eventPath;
        }

        console.log(`Resolved ${contextKey} = ${resolvedValue}`);
        acc[contextKey] = resolvedValue;
        return acc;
      },
      {} as any,
    );

    console.log("Final resolved assign event:", resolvedAssignEvent);

    Object.entries(resolvedAssignEvent).forEach(([contextKey, value]) => {
      // Convert undefined values to null to ensure context keys are always created
      const finalValue = value === undefined ? null : value;
      console.log(`Setting context ${contextKey} = ${finalValue}`);
    
      const contextPath = contextKey.split(".");
      let currentContext = cloneContext;
    
      // Always create the context path structure, even for null/empty values
      for (let i = 0; i < contextPath.length - 1; i++) {
        const key = contextPath[i];
        if (!currentContext[key]) currentContext[key] = {};
        currentContext = currentContext[key];
      }
    
      // Always assign the value (null, empty string, or actual value)
      currentContext[contextPath[contextPath.length - 1]] = finalValue;
    });
    
    console.log("Updated context:", cloneContext);
    await setContext(cloneContext);
  };

  return (
    <MachineContext.Provider
      value={{
        // Config Values
        originalConfig: config,
        formName: config.formName,
        formId: config.id,
        sidebar: config.sidebar,
        functions: config.functions,

        // Functions
        updateContextValue,
        getContextValue,
        checkGuardConditions,
        replaceContextPlaceholders,
        handleAPICall,
        handleRestActor,
        handleResponse,

        // States
        context,
        setContext,
        currentPage,
        changePage,
        debugMode,
        setDebugMode,
        modalConfig,
        setModalConfig,
      }}
    >
      {children}
    </MachineContext.Provider>
  );
}

const MachineContext = React.createContext<
  | {
      originalConfig: MachineConfig;
      formName: string;
      formId: string;
      sidebar: {
        label: string;
        order: number;
        pages: string[];
      }[];
      functions: {
        [key: string]: {
          url: string;
          body?: {
            [key: string]: string;
          };
          type: "rest" | "function";
          format: "json" | "formData";
          method: "GET" | "POST" | "PUT" | "DELETE" | "PATCH";
        };
      };
      
      // Functions
      updateContextValue: (key: string, value: any) => void;
      getContextValue: (key: string) => any;
      checkGuardConditions: (guard: GuardAction) => boolean;
      replaceContextPlaceholders: (value: any) => any;
      handleAPICall: (apiCall: FunctionConfig, onLoading?: { modal?: boolean; title?: string; message?: string }) => Promise<any>;
      handleRestActor: (
        method: string,
        url: string,
        body: any,
        format: string,
      ) => Promise<any>;
      handleResponse: (results: any, assignEvent: any) => Promise<void>;

      // States
      context: any;
      setContext: any;
      currentPage: ContentPageConfig;
      changePage: (pageId: string) => void;
      debugMode: boolean;
      setDebugMode: any;
      modalConfig: { title?: string; message?: string } | null;
      setModalConfig: (config: { title?: string; message?: string } | null) => void;
    }
  | undefined
>(undefined);

// Custom hook to use the context
export const useMachineContext = () => {
  const context = React.useContext(MachineContext);
  if (!context) {
    throw new Error("useMachineContext must be used within a MachineProvider");
  }
  return context;
};

const deepMerge = (target: any, source: any) => {
  for (const key in source) {
    if (
      source[key] &&
      typeof source[key] === "object" &&
      !Array.isArray(source[key]) &&
      !(source[key] instanceof File) // Don't merge File objects
    ) {
      target[key] = target[key] || {};
      deepMerge(target[key], source[key]);
    } else {
      target[key] = source[key];
    }
  }
  return target;
};
