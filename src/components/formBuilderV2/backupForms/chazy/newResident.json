{"id": "residentRegistrationForm", "pages": {"error": {"title": "Error Loading <PERSON>", "settings": ["error", "hideSidebar"], "paragraph": "An error occurred while submitting your registration. Please try again later.", "navigation": [{"type": "button", "label": "Try Again", "action": {"goToPage": "instructions"}, "variant": "primary"}, {"type": "button", "label": "Cancel", "action": {"goToLink": "/home"}, "variant": "ghost"}]}, "success": {"title": "Success", "settings": ["hideSidebar"], "paragraph": "Congrats! You have successfully registered your account! Please navigate to the home page to continue.", "navigation": [{"type": "button", "label": "Go Home", "action": {"callApi": "refetchProfile", "onError": {"goToPage": "error"}, "onSuccess": {}}, "variant": "primary"}]}, "editAddress": {"title": "Address Information", "fields": [{"id": "main.address", "type": "text", "label": "Address", "required": true, "validate": [{"type": "char<PERSON><PERSON><PERSON><PERSON><PERSON>", "value": 50, "message": "Address must be less than 100 characters."}]}, {"id": "main.address2", "type": "text", "label": "Apt, Suite, etc.", "required": false, "validate": [{"type": "char<PERSON><PERSON><PERSON><PERSON><PERSON>", "value": 50, "message": "Address 2 must be less than 50 characters."}]}, {"id": "main.city", "type": "text", "label": "City", "required": true, "validate": [{"type": "char<PERSON><PERSON><PERSON><PERSON><PERSON>", "value": 50, "message": "City must be less than 50 characters."}]}, {"id": "main.state", "type": "select", "label": "State", "options": "{{settings: entity.parcel.states}}", "required": true, "defaultValue": "NY", "requirements": {"message": "Please enter a valid state", "pattern": "^[A-Z]{2}$"}}, {"id": "main.zip", "type": "zipcode", "label": "Zip Code", "required": true, "customValue": true}, {"id": "main.mailingSameAsPrimary", "type": "checkbox", "label": "Mailing address is the same as home address", "checked": true, "className": "mb-10 mt-10"}, {"id": "main.mailaddress", "type": "text", "label": "Mailing Address", "required": true, "validate": [{"type": "char<PERSON><PERSON><PERSON><PERSON><PERSON>", "value": 100, "message": "Address must be less than 100 characters."}], "displayConditions": {"conditions": [{"field": "main.mailingSameAsPrimary", "value": false}]}}, {"id": "main.mailaddress2", "type": "text", "label": "Apt, Suite, etc.", "required": false, "validate": [{"type": "char<PERSON><PERSON><PERSON><PERSON><PERSON>", "value": 50, "message": "Address 2 must be less than 50 characters."}], "displayConditions": {"conditions": [{"field": "main.mailingSameAsPrimary", "value": false}]}}, {"id": "main.mailcity", "type": "text", "label": "City", "required": true, "displayConditions": {"conditions": [{"field": "main.mailingSameAsPrimary", "value": false}]}}, {"id": "main.mailstate", "type": "select", "label": "State", "options": "{{settings: entity.parcel.states}}", "required": true, "defaultValue": "NY", "displayConditions": {"conditions": [{"field": "main.mailingSameAsPrimary", "value": false}]}}, {"id": "main.mailzip", "type": "zipcode", "label": "Zip Code", "required": true, "customValue": true, "displayConditions": {"conditions": [{"field": "main.mailingSameAsPrimary", "value": false}]}}], "settings": [], "paragraph": "Please enter your address information:", "navigation": [{"type": "button", "label": "Next", "action": {"goToPage": "confirmation"}, "variant": "primary"}, {"type": "button", "label": "Back", "action": {"goToPage": "editProfile"}, "variant": "ghost", "operation": "back"}], "description": "Address Information"}, "editProfile": {"title": "Let's get started.", "fields": [{"id": "main.firstName", "type": "text", "label": "First Name", "required": true, "validate": [{"type": "char<PERSON><PERSON><PERSON><PERSON><PERSON>", "value": 50, "message": "First name must be less than 50 characters."}]}, {"id": "main.middleName", "type": "text", "label": "Middle Name", "required": false, "validate": [{"type": "char<PERSON><PERSON><PERSON><PERSON><PERSON>", "value": 50, "message": "Middle name must be less than 50 characters."}]}, {"id": "main.lastName", "type": "text", "label": "Last Name", "required": true, "validate": [{"type": "char<PERSON><PERSON><PERSON><PERSON><PERSON>", "value": 50, "message": "Last name must be less than 50 characters."}]}, {"id": "main.suffix", "type": "text", "label": "Suffix", "required": false, "validate": [{"type": "char<PERSON><PERSON><PERSON><PERSON><PERSON>", "value": 10, "message": "Suffix must be less than 10 characters."}], "placeholder": "Jr., Sr., etc."}, {"id": "main.dateOfBirth", "type": "date", "label": "Date of Birth", "required": true, "validate": [{"type": "datePastMin", "unit": "years", "value": 18, "message": "You must be at least 18 years old to register."}, {"type": "datePastMax", "unit": "years", "value": 120, "message": "Please enter a valid date of birth."}], "className": ""}, {"id": "main.phone", "type": "phone", "label": "Phone Number", "required": true}], "paragraph": "Please enter your information below:", "navigation": [{"type": "button", "label": "Next", "action": {"goToPage": "<PERSON><PERSON><PERSON><PERSON>"}, "variant": "primary"}, {"type": "button", "label": "Back", "action": {"guard": {"field": "hasRegistrationCode", "value": "yes", "matchType": "exact"}, "onError": {"goToPage": "registration"}, "onSuccess": {"goToPage": "<PERSON><PERSON><PERSON><PERSON>"}}, "variant": "ghost", "operation": "back"}], "description": "Personal Information"}, "confirmation": {"title": "Confirm Selections", "fields": [{"id": "personalInfo", "type": "confirmationGroup", "label": "Personal Information", "value": ""}, {"id": "main.firstName", "type": "text", "label": "First Name", "groupId": "personalInfo", "disabled": true, "required": true}, {"id": "main.lastName", "type": "text", "label": "Last Name", "groupId": "personalInfo", "disabled": true, "required": true}, {"id": "main.dateOfBirth", "type": "date", "label": "Date of Birth", "groupId": "personalInfo", "disabled": true, "required": true, "className": ""}, {"id": "main.phone", "type": "phone", "label": "Phone Number", "groupId": "personalInfo", "disabled": true, "required": true}, {"id": "addressInfo", "type": "confirmationGroup", "label": "Address"}, {"id": "main.address", "type": "text", "label": "Address", "groupId": "addressInfo", "disabled": true, "required": true}, {"id": "main.city", "type": "text", "label": "City", "groupId": "addressInfo", "disabled": true, "required": true}, {"id": "main.state", "type": "select", "label": "State", "groupId": "addressInfo", "options": "{{settings: entity.parcel.states}}", "disabled": true, "required": true, "defaultValue": "NY", "requirements": {"message": "Please enter a valid state", "pattern": "^[A-Z]{2}$"}}, {"id": "main.zip", "type": "zipcode", "label": "Zip Code", "groupId": "addressInfo", "disabled": true, "required": true, "customValue": true}, {"id": "main.mailAddressInfo", "type": "confirmationGroup", "label": "Address"}, {"id": "main.mailaddress", "type": "text", "label": "Mailing Address", "groupId": "mailAddressInfo", "disabled": true, "required": true, "displayConditions": {"conditions": [{"field": "main.mailingSameAsPrimary", "value": false}]}}, {"id": "main.mailaddress2", "type": "text", "label": "Apt, Suite, etc.", "groupId": "mailAddressInfo", "disabled": true, "required": false, "displayConditions": {"conditions": [{"field": "main.mailingSameAsPrimary", "value": false}]}}, {"id": "main.mailcity", "type": "text", "label": "City", "groupId": "mailAddressInfo", "disabled": true, "required": true, "displayConditions": {"conditions": [{"field": "main.mailingSameAsPrimary", "value": false}]}}, {"id": "main.mailstate", "type": "select", "label": "State", "groupId": "mailAddressInfo", "options": "{{settings: entity.parcel.states}}", "disabled": true, "required": true, "defaultValue": "NY", "displayConditions": {"conditions": [{"field": "main.mailingSameAsPrimary", "value": false}]}}, {"id": "main.mailzip", "type": "zipcode", "label": "Zip Code", "groupId": "mailAddressInfo", "disabled": true, "required": true, "customValue": true, "displayConditions": {"conditions": [{"field": "main.mailingSameAsPrimary", "value": false}]}}, {"id": "acceptance", "type": "confirmationGroup", "label": "Acceptance"}, {"id": "main.optInTermsAndConditions", "type": "checkbox", "label": "I agree to the terms and conditions", "groupId": "acceptance", "required": true}, {"id": "main.optInPaperless", "type": "checkbox", "label": "Opt in for paperless", "groupId": "acceptance", "required": false, "information": "When you choose paperless options, we will reduce the number of documents sent by mail. Instead, licenses and other documents will be delivered to your email or accessible online."}], "settings": [], "paragraph": "Please confirm the following information is correct:", "disclaimer": "<p>By clicking submit, you agree to the <a style='color: blue;' href='/termsofservice' target='_blank'>terms of service.</a></p>", "navigation": [{"type": "button", "label": "Submit", "action": {"callApi": "submitProfile", "onError": {"goToPage": "error"}, "onSuccess": {"goToPage": "success"}}, "variant": "primary"}, {"type": "button", "label": "Back", "action": {"goToPage": "<PERSON><PERSON><PERSON><PERSON>"}, "variant": "ghost", "operation": "back"}], "description": "Confirm Selections"}, "instructions": {"title": "New Online Resident Registration", "content": "<p>If you have any questions, please email <NAME_EMAIL> or call us at (************* EXT. 2.</p>", "settings": [], "paragraph": "Please fill out the form to register as a new resident.", "navigation": [{"type": "button", "label": "Continue", "action": {"callApi": "getResident", "onError": {"goToPage": "error"}, "onSuccess": {"goToPage": "registration", "assignEvent": {"main.zip": "{{event:individual.zip}}", "main.city": "{{event:individual.city}}", "main.phone": "{{event:individual.phone}}", "main.state": "{{event:individual.state}}", "main.suffix": "{{event:individual.suffix}}", "main.address": "{{event:individual.address}}", "main.mailzip": "{{event:individual.addresses[participantAddressType='Mailing'].zip}}", "main.address2": "{{event:individual.address2}}", "main.lastName": "{{event:individual.lastName}}", "main.mailcity": "{{event:individual.addresses[participantAddressType='Mailing'].city}}", "main.firstName": "{{event:individual.firstName}}", "main.mailstate": "{{event:individual.addresses[participantAddressType='Mailing'].state}}", "main.middleName": "{{event:individual.middleName}}", "main.dateOfBirth": "{{event:individual.dateOfBirth}}", "main.mailaddress": "{{event:individual.addresses[participantAddressType='Mailing'].streetAddress}}", "main.mailaddress2": "{{event:individual.addresses[participantAddressType='Mailing'].streetAddress2}}"}}}}, {"type": "button", "label": "Cancel", "action": {"callApi": "clearActiveTenant", "onError": {"goToPage": "instructions"}, "onSuccess": {"callApi": "fullRefresh", "onError": {"goToPage": "instructions"}, "onSuccess": {}}}, "variant": "ghost"}], "description": "How to fill out this "}, "registration": {"title": "Registration Code Verification", "fields": [{"id": "hasRegistrationCode", "type": "select", "label": "Do you have a registration code?", "options": [{"label": "Yes", "value": "yes"}, {"label": "No", "value": "no"}], "required": true, "className": "mb-10 mt-10", "placeholder": "Select an option"}, {"id": "registrationCode", "type": "text", "label": "Registration Code", "required": {"conditions": [{"field": "hasRegistrationCode", "value": "yes"}]}, "validate": [{"type": "<PERSON>ar<PERSON><PERSON><PERSON><PERSON><PERSON>", "value": 12, "message": "Registration code must be at least 12 characters."}, {"type": "char<PERSON><PERSON><PERSON><PERSON><PERSON>", "value": 12, "message": "Registration code must be at most 12 characters."}, {"type": "registrationCodeExists", "value": "{{context:registrationCode}}", "message": "Registration Code is invalid."}], "className": "mb-10", "placeholder": "Enter your registration code", "displayConditions": {"conditions": [{"field": "hasRegistrationCode", "value": "yes"}]}}], "content": "<div><p>Before continuing, please note that some residents may have already received a registration code by mail.</p><br/><p style=\"margin: 0px 0px 20px 0px;\">If you are already registered for any of the services listed below but did not receive a registration code, you can request to add these items to your account once you have finished registering.<ul style=\"list-style-type: disc; padding-left: 20px; font-weight: bold;\"><li style=\"list-style-type: disc;\">Dog License</li></ul></p></div>", "settings": [], "navigation": [{"type": "button", "label": "Continue", "action": {"guard": {"field": "hasRegistrationCode", "value": "no", "matchType": "exact"}, "onError": {"guard": {"field": "registrationCode", "value": true, "matchType": "notNull"}, "onError": {"goToPage": "error"}, "onSuccess": {"callApi": "getRegistrationCode", "onError": {"goToPage": "registration"}, "onSuccess": {"goToPage": "editProfile", "assignEvent": {"main.zip": "{{event:zip}}", "main.city": "{{event:city}}", "main.phone": "{{event:phone}}", "main.state": "{{event:state}}", "main.suffix": "{{event:suffix}}", "main.address": "{{event:address}}", "main.mailzip": "{{event:addresses[participantAddressType='Mailing'].zip}}", "main.address2": "{{event:address2}}", "main.lastName": "{{event:lastName}}", "main.mailcity": "{{event:addresses[participantAddressType='Mailing'].city}}", "main.firstName": "{{event:firstName}}", "main.mailstate": "{{event:addresses[participantAddressType='Mailing'].state}}", "main.middleName": "{{event:middleName}}", "main.dateOfBirth": "{{event:dateOfBirth}}", "main.mailaddress": "{{event:addresses[participantAddressType='Mailing'].streetAddress}}", "main.mailaddress2": "{{event:addresses[participantAddressType='Mailing'].streetAddress2}}"}}}}, "onSuccess": {"goToPage": "editProfile"}}, "variant": "primary"}, {"type": "button", "label": "Back", "action": {"goToPage": "instructions"}, "variant": "ghost", "operation": "back"}], "description": "<PERSON><PERSON> Existing Account."}}, "context": {"main": {"zip": null, "city": "Schenectady", "phone": null, "state": "NY", "suffix": null, "address": null, "mailzip": null, "address2": null, "lastName": null, "mailcity": null, "passport": null, "firstName": null, "mailstate": null, "idCardBack": null, "middleName": null, "dateOfBirth": null, "idCardFront": null, "mailaddress": null, "mailaddress2": null, "optInPaperless": true, "identificationType": null, "mailingSameAsPrimary": true, "optInTermsAndConditions": false}, "registrationCode": null, "hasRegistrationCode": null, "registrationCodeValid": false}, "sidebar": [{"label": "Instructions", "order": 1, "pages": ["instructions"]}, {"label": "Personal Information", "order": 1, "pages": ["editProfile"]}, {"label": "Address", "order": 3, "pages": ["<PERSON><PERSON><PERSON><PERSON>"]}, {"label": "Confirmation", "order": 4, "pages": ["confirmation"]}], "formName": "Resident Registration Form", "functions": {"idCardScan": {"url": "/coordinator/me/textextractor", "body": {"file": "{{context:main.idCardFront}}", "fileIdentifier": "idCardFront"}, "type": "rest", "format": "formData", "method": "POST", "description": "Scan ID Card"}, "fullRefresh": {"type": "function", "function": "fullRefresh", "description": "Refresh the entire window."}, "getResident": {"url": "/license/me/profile/{{context:entityType}}", "type": "rest", "format": "json", "method": "GET"}, "passportScan": {"url": "/coordinator/me/textextractor", "body": {"file": "{{context:main.passport}}", "fileIdentifier": "passport"}, "type": "rest", "format": "formData", "method": "POST"}, "submitProfile": {"url": "/license/me/participant", "body": {"zip": "{{context:main.zip}}", "city": "{{context:main.city}}", "email": "{{context:main.email}}", "phone": "{{context:main.phone}}", "state": "{{context:main.state}}", "suffix": "{{context:main.suffix}}", "address": "{{context:main.address}}", "mailzip": "{{context:main.mailzip}}", "address2": "{{context:main.address2}}", "lastName": "{{context:main.lastName}}", "mailcity": "{{context:main.mailcity}}", "passport": "{{context:main.passport}}", "firstName": "{{context:main.firstName}}", "mailstate": "{{context:main.mailstate}}", "idCardBack": "{{context:main.idCardBack}}", "middleName": "{{context:main.middleName}}", "dateOfBirth": "{{context:main.dateOfBirth}}", "idCardFront": "{{context:main.idCardFront}}", "mailaddress": "{{context:main.mailaddress}}", "mailaddress2": "{{context:main.mailaddress2}}", "optInPaperless": "{{context:main.optInPaperless}}", "registrationCode": "{{context:registrationCode}}", "identificationType": "{{context:main.identificationType}}", "mailingSameAsPrimary": "{{context:main.mailingSameAsPrimary}}", "optInTermsAndConditions": "{{context:main.optInTermsAndConditions}}"}, "type": "rest", "format": "formData", "method": "POST"}, "refetchProfile": {"type": "function", "function": "refetchProfile", "description": "Refresh the profile data of the logged in user."}, "clearActiveTenant": {"type": "function", "function": "clearActiveTenant", "description": "Clear the active tenant from the session."}, "getRegistrationCode": {"url": "/license/me/merge-by-code/{{context:registrationCode}}/exists", "type": "rest", "format": "json", "method": "GET"}}, "initialPage": "instructions", "permissions": ["resident"]}