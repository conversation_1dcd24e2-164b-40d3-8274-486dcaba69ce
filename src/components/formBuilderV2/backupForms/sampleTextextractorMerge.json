{"schema": {"dogSex": "string", "dogName": "string", "dogBreed": [{"label": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "value": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"label": "Afghan Hound", "value": "Afghan Hound"}, {"label": "Airedale Terrier", "value": "Airedale Terrier"}, {"label": "<PERSON><PERSON><PERSON>", "value": "<PERSON><PERSON><PERSON>"}, {"label": "Alaskan Malamute", "value": "Alaskan Malamute"}, {"label": "American English Coonhound", "value": "American English Coonhound"}, {"label": "American Eskimo Dog", "value": "American Eskimo Dog"}, {"label": "American Foxhound", "value": "American Foxhound"}, {"label": "American Hairless Terrier", "value": "American Hairless Terrier"}, {"label": "American Leopard Hound", "value": "American Leopard Hound"}, {"label": "American Staffordshire Terrier", "value": "American Staffordshire Terrier"}, {"label": "American Water Spaniel", "value": "American Water Spaniel"}, {"label": "Anatolian Shepherd Dog", "value": "Anatolian Shepherd Dog"}, {"label": "Appenzeller Sennenhunde", "value": "Appenzeller Sennenhunde"}, {"label": "Australian Cattle Dog", "value": "Australian Cattle Dog"}, {"label": "Australian Shepherd", "value": "Australian Shepherd"}, {"label": "Australian Terrier", "value": "Australian Terrier"}, {"label": "<PERSON><PERSON><PERSON>", "value": "<PERSON><PERSON><PERSON>"}, {"label": "Barbet", "value": "Barbet"}, {"label": "Basenji", "value": "Basenji"}, {"label": "Basset Hound", "value": "Basset Hound"}, {"label": "<PERSON><PERSON><PERSON>", "value": "<PERSON><PERSON><PERSON>"}, {"label": "<PERSON><PERSON>", "value": "<PERSON><PERSON>"}, {"label": "<PERSON><PERSON><PERSON>", "value": "<PERSON><PERSON><PERSON>"}, {"label": "Bedlington Terrier", "value": "Bedlington Terrier"}, {"label": "Belgian <PERSON><PERSON><PERSON><PERSON>", "value": "Belgian <PERSON><PERSON><PERSON><PERSON>"}, {"label": "Belgian <PERSON><PERSON><PERSON>", "value": "Belgian <PERSON><PERSON><PERSON>"}, {"label": "Belgian Sheepdog", "value": "Belgian Sheepdog"}, {"label": "Belgian Tervuren", "value": "Belgian Tervuren"}, {"label": "Bergamasco Sheepdog", "value": "Bergamasco Sheepdog"}, {"label": "<PERSON>", "value": "<PERSON>"}, {"label": "Bernese Mountain Dog", "value": "Bernese Mountain Dog"}, {"label": "<PERSON><PERSON><PERSON>", "value": "<PERSON><PERSON><PERSON>"}, {"label": "Biewer Terrier", "value": "Biewer Terrier"}, {"label": "Black and Tan Coonhound", "value": "Black and Tan Coonhound"}, {"label": "Black Russian Terrier", "value": "Black Russian Terrier"}, {"label": "Bloodhound", "value": "Bloodhound"}, {"label": "Bluetick Coonhound", "value": "Bluetick Coonhound"}, {"label": "Boerboel", "value": "Boerboel"}, {"label": "Border Collie", "value": "Border Collie"}, {"label": "Border Terrier", "value": "Border Terrier"}, {"label": "<PERSON><PERSON><PERSON>", "value": "<PERSON><PERSON><PERSON>"}, {"label": "Boston Terrier", "value": "Boston Terrier"}, {"label": "Bouvier des Flandres", "value": "Bouvier des Flandres"}, {"label": "Boxer", "value": "Boxer"}, {"label": "<PERSON><PERSON>l", "value": "<PERSON><PERSON>l"}, {"label": "Bracco <PERSON>o", "value": "Bracco <PERSON>o"}, {"label": "<PERSON>ria<PERSON>", "value": "<PERSON>ria<PERSON>"}, {"label": "Brittany", "value": "Brittany"}, {"label": "Brussels Griffon", "value": "Brussels Griffon"}, {"label": "Bulldog", "value": "Bulldog"}, {"label": "<PERSON><PERSON><PERSON><PERSON>", "value": "<PERSON><PERSON><PERSON><PERSON>"}, {"label": "Cairn Terrier", "value": "Cairn Terrier"}, {"label": "Canaan Dog", "value": "Canaan Dog"}, {"label": "Cane Corso", "value": "Cane Corso"}, {"label": "Cardigan Welsh Corgi", "value": "Cardigan Welsh Corgi"}, {"label": "Cavalier King <PERSON>", "value": "Cavalier King <PERSON>"}, {"label": "Central Asian Shepherd Dog", "value": "Central Asian Shepherd Dog"}, {"label": "Cesky Terrier", "value": "Cesky Terrier"}, {"label": "Chesapeake Bay Retriever", "value": "Chesapeake Bay Retriever"}, {"label": "Chihuahua", "value": "Chihuahua"}, {"label": "Chinese Crested", "value": "Chinese Crested"}, {"label": "Chinese Shar-Pei", "value": "Chinese Shar-Pei"}, {"label": "Chinook", "value": "Chinook"}, {"label": "<PERSON>", "value": "<PERSON>"}, {"label": "Cirneco dell'Etna", "value": "Cirneco dell'Etna"}, {"label": "<PERSON><PERSON> Spaniel", "value": "<PERSON><PERSON> Spaniel"}, {"label": "Cocker Spaniel", "value": "Cocker Spaniel"}, {"label": "<PERSON><PERSON>", "value": "<PERSON><PERSON>"}, {"label": "Coonhound", "value": "Coonhound"}, {"label": "<PERSON><PERSON>", "value": "<PERSON><PERSON>"}, {"label": "Curly-Coated Retriever", "value": "Curly-Coated Retriever"}, {"label": "Dachshund", "value": "Dachshund"}, {"label": "<PERSON><PERSON><PERSON>", "value": "<PERSON><PERSON><PERSON>"}, {"label": "Dan<PERSON> Terrier", "value": "Dan<PERSON> Terrier"}, {"label": "<PERSON><PERSON>", "value": "<PERSON><PERSON>"}, {"label": "Deutscher Wachtelhund", "value": "Deutscher Wachtelhund"}, {"label": "<PERSON><PERSON> Pi<PERSON>", "value": "<PERSON><PERSON> Pi<PERSON>"}, {"label": "Dogo Argentino", "value": "Dogo Argentino"}, {"label": "Dogue de Bordeaux", "value": "Dogue de Bordeaux"}, {"label": "Drentsche Patrijshond", "value": "Drentsche Patrijshond"}, {"label": "Finnish Lapphund", "value": "Finnish Lapphund"}, {"label": "Finnish Spitz", "value": "Finnish Spitz"}, {"label": "Flat-Coated Retriever", "value": "Flat-Coated Retriever"}, {"label": "French Bulldog", "value": "French Bulldog"}, {"label": "German Pinscher", "value": "German Pinscher"}, {"label": "German Shepherd Dog", "value": "German Shepherd Dog"}, {"label": "German Short<PERSON><PERSON>", "value": "German Short<PERSON><PERSON>"}, {"label": "German <PERSON><PERSON><PERSON>", "value": "German <PERSON><PERSON><PERSON>"}, {"label": "<PERSON>", "value": "<PERSON>"}, {"label": "Glen of Imaal Terrier", "value": "Glen of Imaal Terrier"}, {"label": "Golden Retriever", "value": "Golden Retriever"}, {"label": "<PERSON>", "value": "<PERSON>"}, {"label": "Great Dane", "value": "Great Dane"}, {"label": "Great Pyrenees", "value": "Great Pyrenees"}, {"label": "Greater Swiss Mountain Dog", "value": "Greater Swiss Mountain Dog"}, {"label": "Greyhound", "value": "Greyhound"}, {"label": "Harrier", "value": "Harrier"}, {"label": "<PERSON><PERSON><PERSON>", "value": "<PERSON><PERSON><PERSON>"}, {"label": "Ibizan <PERSON>", "value": "Ibizan <PERSON>"}], "vaccineName": "string", "dogBirthDate": "string", "vaccineBrand": [{"label": "DEFENSOR 1", "value": "DEFENSOR 1"}, {"label": "DEFENSOR 3", "value": "DEFENSOR 3"}, {"label": "IMRAB 1", "value": "IMRAB 1"}, {"label": "IMRAB 1 (TF)", "value": "IMRAB 1 (TF)"}, {"label": "IMRAB 3", "value": "IMRAB 3"}, {"label": "IMRAB 3 (TF)", "value": "IMRAB 3 (TF)"}, {"label": "Killed", "value": "Killed"}, {"label": "Killed virus", "value": "Killed virus"}, {"label": "NOBIVAC 1", "value": "NOBIVAC 1"}, {"label": "NOBIVAC 3", "value": "NOBIVAC 3"}, {"label": "NOBIVAC 3 CA", "value": "NOBIVAC 3 CA"}, {"label": "Other", "value": "Other"}, {"label": "PUREVAX 1", "value": "PUREVAX 1"}, {"label": "PUREVAX 3", "value": "PUREVAX 3"}, {"label": "RABVAC 1", "value": "RABVAC 1"}, {"label": "RABVAC 3", "value": "RABVAC 3"}, {"label": "VANGUARD 1", "value": "VANGUARD 1"}, {"label": "VANGUARD 3", "value": "VANGUARD 3"}], "vaccineDueDate": "string", "veterinaryName": "string", "dogPrimaryColor": [{"label": "Apricot", "value": "Apricot"}, {"label": "Bicolor", "value": "Bicolor"}, {"label": "Black", "value": "Black"}, {"label": "Blue", "value": "Blue"}, {"label": "Brindle", "value": "Brindle"}, {"label": "<PERSON>", "value": "<PERSON>"}, {"label": "Charc<PERSON>l", "value": "Charc<PERSON>l"}, {"label": "Chocolate", "value": "Chocolate"}, {"label": "Cream", "value": "Cream"}, {"label": "<PERSON>awn", "value": "<PERSON>awn"}, {"label": "Gold", "value": "Gold"}, {"label": "<PERSON>", "value": "<PERSON>"}, {"label": "Ha<PERSON><PERSON>", "value": "Ha<PERSON><PERSON>"}, {"label": "Isabella", "value": "Isabella"}, {"label": "Lemon", "value": "Lemon"}, {"label": "Lilac", "value": "Lilac"}, {"label": "Liver", "value": "Liver"}, {"label": "Mahogany", "value": "Mahogany"}, {"label": "Mantle", "value": "Mantle"}, {"label": "<PERSON><PERSON>", "value": "<PERSON><PERSON>"}, {"label": "Parti", "value": "Parti"}, {"label": "Pied", "value": "Pied"}, {"label": "Red", "value": "Red"}, {"label": "<PERSON><PERSON>", "value": "<PERSON><PERSON>"}, {"label": "Rust", "value": "Rust"}, {"label": "Sable", "value": "Sable"}, {"label": "Seal", "value": "Seal"}, {"label": "Silver", "value": "Silver"}, {"label": "Speckled", "value": "Speckled"}, {"label": "Spotted", "value": "Spotted"}, {"label": "<PERSON>", "value": "<PERSON>"}, {"label": "Tick", "value": "Tick"}, {"label": "Tri-color", "value": "Tri-color"}, {"label": "Trindle", "value": "Trindle"}, {"label": "White", "value": "White"}], "microchipNumber": "string", "rabiesTagNumber": "string", "vaccineProducer": "string", "vaccineLotNumber": "string", "dogSecondaryColor": [{"label": "Apricot", "value": "Apricot"}, {"label": "Bicolor", "value": "Bicolor"}, {"label": "Black", "value": "Black"}, {"label": "Blue", "value": "Blue"}, {"label": "Brindle", "value": "Brindle"}, {"label": "<PERSON>", "value": "<PERSON>"}, {"label": "Charc<PERSON>l", "value": "Charc<PERSON>l"}, {"label": "Chocolate", "value": "Chocolate"}, {"label": "Cream", "value": "Cream"}, {"label": "<PERSON>awn", "value": "<PERSON>awn"}, {"label": "Gold", "value": "Gold"}, {"label": "<PERSON>", "value": "<PERSON>"}, {"label": "Ha<PERSON><PERSON>", "value": "Ha<PERSON><PERSON>"}, {"label": "Isabella", "value": "Isabella"}, {"label": "Lemon", "value": "Lemon"}, {"label": "Lilac", "value": "Lilac"}, {"label": "Liver", "value": "Liver"}, {"label": "Mahogany", "value": "Mahogany"}, {"label": "Mantle", "value": "Mantle"}, {"label": "<PERSON><PERSON>", "value": "<PERSON><PERSON>"}, {"label": "Parti", "value": "Parti"}, {"label": "Pied", "value": "Pied"}, {"label": "Red", "value": "Red"}, {"label": "<PERSON><PERSON>", "value": "<PERSON><PERSON>"}, {"label": "Rust", "value": "Rust"}, {"label": "Sable", "value": "Sable"}, {"label": "Seal", "value": "Seal"}, {"label": "Silver", "value": "Silver"}, {"label": "Speckled", "value": "Speckled"}, {"label": "Spotted", "value": "Spotted"}, {"label": "<PERSON>", "value": "<PERSON>"}, {"label": "Tick", "value": "Tick"}, {"label": "Tri-color", "value": "Tri-color"}, {"label": "Trindle", "value": "Trindle"}, {"label": "White", "value": "White"}], "vaccineAdministeredDate": "string", "vaccineLotExpirationDate": "string"}, "dogSpayedOrNeuteredDocument": null, "dogRabiesVaccinationDocument": {"path": "rabiesSample.jpg", "name": "rabiesSample.jpg", "lastModified": 1741974266283, "lastModifiedDate": "2025-03-14T17:44:26.000Z", "webkitRelativePath": "", "size": 52950, "type": "image/jpeg"}, "dogServiceAnimalExemptionDocument": null, "dogSpayedOrNeuteredExemptionDocument": null, "dogRabiesVaccinationExemptionDocument": null}