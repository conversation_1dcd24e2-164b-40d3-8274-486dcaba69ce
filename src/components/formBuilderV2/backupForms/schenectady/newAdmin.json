{"id": "adminRegistrationForm", "functions": {"getIndividual": {"url": "/license/me/profile/individual", "type": "rest", "format": "json", "method": "GET", "description": "Get Admin Profile"}, "submitProfile": {"url": "/license/participant", "body": {"suffix": "{{context:individual.suffix}}", "lastName": "{{context:individual.lastName}}", "firstName": "{{context:individual.firstName}}", "middleName": "{{context:individual.middleName}}"}, "type": "rest", "format": "formData", "method": "POST", "description": "Submit the main form"}, "activateAccount": {"url": "/license/participant/{{context:entityId}}/mark-online", "body": {}, "type": "rest", "format": "json", "method": "POST", "description": "Activate Account"}}, "pages": {"error": {"title": "Error Loading <PERSON>", "settings": ["hideSidebar"], "paragraph": "An error occurred while submitting your registration. Please try again later.", "navigation": [{"type": "button", "label": "Try Again", "action": {"goToPage": "instructions"}, "variant": "primary"}, {"type": "link", "label": "Cancel", "variant": "ghost", "navigate": {"url": "/dashboard"}}]}, "success": {"title": "Success", "settings": ["hideSidebar"], "paragraph": "Congrats! You have successfully registered your account! Please navigate to the dashboard page to continue.", "navigation": [{"type": "link", "label": "Go to Dashboard", "variant": "primary", "navigate": {"url": "/dashboard"}}]}, "instructions": {"title": "New Admin Registration", "content": "<p style=\"text-align: center;\">Please fill out the form to register as a new admin.</p>", "navigation": [{"type": "button", "label": "Continue", "action": {"callApi": "getIndividual", "onSuccess": {"goToPage": "editProfile", "assignEvent": {"individual.suffix": "{{event:individual.suffix}}", "individual.lastName": "{{event:individual.lastName}}", "individual.firstName": "{{event:individual.firstName}}"}}, "onError": {"goToPage": "instructions"}}, "variant": "primary"}, {"type": "link", "label": "Cancel", "variant": "ghost", "navigate": {"url": "/login", "handle": "logout"}}], "description": "How to fill out this form.", "settings": []}, "editProfile": {"title": "Let's get started.", "fields": [{"id": "individual.firstName", "type": "text", "label": "First Name", "required": true, "validate": [{"type": "char<PERSON><PERSON><PERSON><PERSON><PERSON>", "value": 50, "message": "First name must be less than 50 characters."}]}, {"id": "individual.middleName", "type": "text", "label": "Middle Name", "required": false, "validate": [{"type": "char<PERSON><PERSON><PERSON><PERSON><PERSON>", "value": 50, "message": "Middle name must be less than 50 characters."}]}, {"id": "individual.lastName", "type": "text", "label": "Last Name", "required": true, "validate": [{"type": "char<PERSON><PERSON><PERSON><PERSON><PERSON>", "value": 50, "message": "Last name must be less than 50 characters."}]}, {"id": "individual.suffix", "type": "text", "label": "Suffix", "required": false, "validate": [{"type": "char<PERSON><PERSON><PERSON><PERSON><PERSON>", "value": 10, "message": "Suffix must be less than 10 characters."}], "placeholder": "Jr., Sr., etc."}], "paragraph": "Please enter your information below:", "navigation": [{"type": "button", "label": "Next", "action": {"goToPage": "confirmation"}, "variant": "primary"}, {"type": "button", "label": "Back", "action": {"goToPage": "instructions"}, "variant": "ghost", "operation": "back"}], "description": "Personal Information", "settings": []}, "confirmation": {"title": "Confirm Selections", "fields": [{"id": "personalInfo", "type": "confirmationGroup", "label": "Personal Information", "value": ""}, {"id": "individual.firstName", "type": "text", "label": "First Name", "groupId": "personalInfo", "disabled": true, "required": true}, {"id": "individual.middleName", "type": "text", "label": "Middle Name", "groupId": "personalInfo", "disabled": true, "required": false}, {"id": "individual.lastName", "type": "text", "label": "Last Name", "groupId": "personalInfo", "disabled": true, "required": true}], "paragraph": "Please confirm the following information is correct:", "disclaimer": "<p>By clicking submit, you agree to the <a style='color: blue;' href='/termsofservice' target='_blank'>terms of service.</a></p>", "navigation": [{"type": "button", "label": "Submit", "action": {"callApi": "submitProfile", "onSuccess": {"callApi": "activateAccount", "onSuccess": {"goToPage": "success"}, "onError": {"goToPage": "confirmation"}}, "onError": {"goToPage": "confirmation"}}, "variant": "primary"}, {"type": "button", "label": "Back", "action": "BACK", "variant": "ghost", "operation": "back"}], "description": "Confirm Selections", "settings": ["hideSideBar"]}}, "context": {"individual": {"suffix": null, "lastName": null, "firstName": null, "middleName": null}, "entityId": null, "entityType": "individual"}, "initialPage": "instructions", "sidebar": [{"label": "Instructions", "order": 1, "pages": ["instructions"]}, {"label": "Profile", "order": 2, "pages": ["editProfile"]}, {"label": "Confirmation", "order": 3, "pages": ["confirmation"]}], "formName": "Admin Registration Form"}