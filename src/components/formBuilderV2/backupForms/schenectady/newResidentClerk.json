{"id": "new-resident-registration-clerk", "pages": {"error": {"title": "Error Loading <PERSON>", "buttons": [{"label": "Retry", "action": {"goToPay": "editProfile"}, "variant": "primary"}, {"type": "link", "label": "Cancel", "variant": "ghost", "navigate": {"url": "/dashboard"}}], "settings": [], "description": "An error has occurred. Please try again."}, "success": {"title": "Success", "settings": ["hideSidebar"], "paragraph": "You have successfully registered a new resident.  Please navigate to the profile page.", "navigation": [{"type": "link", "label": "Go to Profile Page", "variant": "primary", "navigate": {"url": "/profile/individual/{{context:entityId}}?tab=profile"}}]}, "editAddress": {"label": "Address", "title": "Address Information", "fields": [{"id": "individual.address", "type": "text", "label": "Address", "required": true, "maxLength": {"value": 100, "message": "Address must be less than 100 characters"}}, {"id": "individual.address2", "type": "text", "label": "Apt, Suite, etc.", "required": false, "maxLength": {"value": 50, "message": "Address 2 must be less than 50 characters"}}, {"id": "individual.city", "type": "text", "label": "City", "required": true, "maxLength": {"value": 50, "message": "City must be less than 50 characters"}}, {"id": "individual.state", "type": "select", "label": "State", "options": "{{settings: entity.parcel.states}}", "required": true, "defaultValue": "NY", "requirements": {"message": "Please enter a valid state", "pattern": "^[A-Z]{2}$"}}, {"id": "individual.zip", "type": "zipcode", "label": "Zip Code", "required": true, "customValue": true}, {"id": "individual.mailingSameAsPrimary", "type": "checkbox", "label": "Mailing address is the same as home address", "checked": true, "className": "mb-10 mt-10"}, {"id": "individual.mailaddress", "type": "text", "label": "Mailing Address", "required": true, "maxLength": {"value": 100, "message": "Address must be less than 100 characters"}, "displayConditions": {"conditions": [{"field": "individual.mailingSameAsPrimary", "value": false}]}}, {"id": "individual.mailaddress2", "type": "text", "label": "Apt, Suite, etc.", "required": false, "maxLength": {"value": 50, "message": "Address 2 must be less than 50 characters"}, "displayConditions": {"conditions": [{"field": "individual.mailingSameAsPrimary", "value": false}]}}, {"id": "individual.mailcity", "type": "text", "label": "City", "required": true, "maxLength": {"value": 50, "message": "City must be less than 50 characters"}, "displayConditions": {"conditions": [{"field": "individual.mailingSameAsPrimary", "value": false}]}}, {"id": "individual.mailstate", "type": "select", "label": "State", "options": "{{settings: entity.parcel.states}}", "required": true, "defaultValue": "NY", "displayConditions": {"conditions": [{"field": "individual.mailingSameAsPrimary", "value": false}]}}, {"id": "individual.mailzip", "type": "zipcode", "label": "Zip Code", "required": true, "customValue": true, "displayConditions": {"conditions": [{"field": "individual.mailingSameAsPrimary", "value": false}]}}], "settings": [], "paragraph": "Please enter your address information:", "navigation": [{"type": "button", "label": "Next", "action": {"goToPage": "confirmation"}, "variant": "primary"}, {"type": "button", "label": "Back", "action": {"goToPage": "editProfile"}, "variant": "ghost", "operation": "back"}], "description": "Address Information"}, "editProfile": {"label": "Profile", "title": "Resident Profile.", "fields": [{"id": "individual.email", "type": "email", "label": "Email (Optional)", "required": false, "validate": [{"type": "emailExists", "value": "{{context:individual.email}}", "message": "Email already in use"}]}, {"id": "individual.firstName", "type": "text", "label": "First Name", "required": true, "validate": [{"type": "char<PERSON><PERSON><PERSON><PERSON><PERSON>", "value": 50, "message": "First name must be less than 100 characters"}]}, {"id": "individual.middleName", "type": "text", "label": "Middle Name", "required": false, "validate": [{"type": "char<PERSON><PERSON><PERSON><PERSON><PERSON>", "value": 50, "message": "Middle name must be less than 50 characters"}]}, {"id": "individual.lastName", "type": "text", "label": "Last Name", "required": true, "validate": [{"type": "char<PERSON><PERSON><PERSON><PERSON><PERSON>", "value": 50, "message": "Last name must be less than 50 characters"}]}, {"id": "individual.suffix", "type": "text", "label": "Suffix", "required": false, "validate": [{"type": "char<PERSON><PERSON><PERSON><PERSON><PERSON>", "value": 10, "message": "Suffix must be less than 10 characters"}], "placeholder": "Jr., Sr., etc."}, {"id": "individual.dateOfBirth", "type": "date", "label": "Date of Birth", "required": true, "validate": [{"type": "datePastMin", "unit": "years", "value": 18, "message": "You must be at least 18 years old to register."}, {"type": "datePastMax", "unit": "years", "value": 120, "message": "Please enter a valid date of birth."}], "className": ""}, {"id": "individual.phone", "type": "phone", "label": "Phone Number", "required": true, "validate": [{"type": "pattern", "value": "^\\(\\d{3}\\) \\d{3}-\\d{4}$", "message": "Invalid Phone Number."}]}], "settings": [], "paragraph": "Please enter residents information below:", "navigation": [{"type": "button", "label": "Next", "action": {"goToPage": "<PERSON><PERSON><PERSON><PERSON>"}, "variant": "primary"}, {"type": "link", "label": "Cancel", "variant": "ghost", "navigate": {"url": "/dashboard"}}], "description": "Personal Information"}, "confirmation": {"label": "Confirmation", "title": "Confirm Selections", "fields": [{"id": "personalInfo", "type": "confirmationGroup", "label": "Personal Information"}, {"id": "individual.firstName", "type": "text", "label": "First Name", "groupId": "personalInfo", "disabled": true, "required": true}, {"id": "individual.lastName", "type": "text", "label": "Last Name", "groupId": "personalInfo", "disabled": true, "required": true}, {"id": "individual.dateOfBirth", "type": "date", "label": "Date of Birth", "groupId": "personalInfo", "disabled": true, "required": true, "className": ""}, {"id": "individual.phone", "type": "phone", "label": "Phone Number", "groupId": "personalInfo", "disabled": true, "required": true}, {"id": "individual.addressInfo", "type": "confirmationGroup", "label": "Address"}, {"id": "individual.address", "type": "text", "label": "Address", "groupId": "addressInfo", "disabled": true, "required": true}, {"id": "individual.city", "type": "text", "label": "City", "groupId": "addressInfo", "disabled": true, "required": true}, {"id": "individual.state", "type": "select", "label": "State", "groupId": "addressInfo", "options": "{{settings: entity.parcel.states}}", "disabled": true, "required": true, "defaultValue": "NY", "requirements": {"message": "Please enter a valid state", "pattern": "^[A-Z]{2}$"}}, {"id": "individual.zip", "type": "zipcode", "label": "Zip Code", "groupId": "addressInfo", "disabled": true, "required": true, "customValue": true}, {"id": "mailAddressInfo", "type": "confirmationGroup", "label": "Mailing Address", "displayConditions": {"conditions": [{"field": "individual.mailingSameAsPrimary", "value": false}]}}, {"id": "individual.mailaddress", "type": "text", "label": "Mailing Address", "groupId": "mailAddressInfo", "disabled": true, "required": true, "displayConditions": {"conditions": [{"field": "individual.mailingSameAsPrimary", "value": false}]}}, {"id": "individual.mailaddress2", "type": "text", "label": "Apt, Suite, etc.", "groupId": "mailAddressInfo", "disabled": true, "required": false, "displayConditions": {"conditions": [{"field": "individual.mailingSameAsPrimary", "value": false}]}}, {"id": "individual.mailcity", "type": "text", "label": "City", "value": "{individual.mailcity}", "groupId": "mailAddressInfo", "disabled": true, "required": true, "displayConditions": {"conditions": [{"field": "individual.mailingSameAsPrimary", "value": false}]}}, {"id": "individual.mailstate", "type": "select", "label": "State", "value": "{individual.mailstate}", "groupId": "mailAddressInfo", "options": "{{settings: entity.parcel.states}}", "disabled": true, "required": true, "defaultValue": "NY", "displayConditions": {"conditions": [{"field": "individual.mailingSameAsPrimary", "value": false}]}}, {"id": "mailzip", "type": "zipcode", "label": "Zip Code", "value": "{individual.mailzip}", "groupId": "mailAddressInfo", "disabled": true, "required": true, "customValue": true, "displayConditions": {"conditions": [{"field": "individual.mailingSameAsPrimary", "value": false}]}}], "settings": ["hideSidebar"], "paragraph": "Please confirm the following information is correct:", "navigation": [{"type": "button", "label": "Submit", "action": {"callApi": "submitProfile", "onError": {"goToPage": "error"}, "onSuccess": {"goToPage": "success", "assignEvent": {"entityId": "{{event:entityId}}"}}}, "variant": "primary"}, {"type": "button", "label": "Back", "action": {"goToPage": "<PERSON><PERSON><PERSON><PERSON>"}, "variant": "ghost", "operation": "back"}], "description": "Confirm Selections"}}, "context": {"error": null, "entityId": null, "entityType": "individual", "individual": {"zip": null, "city": "Schenectady", "email": null, "phone": null, "state": "NY", "suffix": null, "address": null, "mailzip": null, "address2": null, "lastName": null, "mailcity": null, "firstName": null, "mailstate": null, "middleName": null, "dateOfBirth": null, "idCardFront": null, "mailaddress": null, "mailaddress2": null, "mailingSameAsPrimary": true}}, "sidebar": [{"label": "Profile", "order": 1, "pages": ["editProfile"]}, {"label": "Address", "order": 2, "pages": ["<PERSON><PERSON><PERSON><PERSON>"]}, {"label": "Confirmation", "order": 3, "pages": ["confirmation"]}], "formName": "Resident Registration Form", "functions": {"submitProfile": {"url": "/license/participant", "body": {"zip": "{{context:individual.zip}}", "city": "{{context:individual.city}}", "email": "{{context:individual.email}}", "phone": "{{context:individual.phone}}", "state": "{{context:individual.state}}", "suffix": "{{context:individual.suffix}}", "address": "{{context:individual.address}}", "mailzip": "{{context:individual.mailzip}}", "address2": "{{context:individual.address2}}", "lastName": "{{context:individual.lastName}}", "mailcity": "{{context:individual.mailcity}}", "firstName": "{{context:individual.firstName}}", "mailstate": "{{context:individual.mailstate}}", "middleName": "{{context:individual.middleName}}", "dateOfBirth": "{{context:individual.dateOfBirth}}", "mailaddress": "{{context:individual.mailaddress}}", "mailaddress2": "{{context:individual.mailaddress2}}", "identificationType": "{{context:individual.identificationType}}", "mailingSameAsPrimary": "{{context:individual.mailingSameAsPrimary}}"}, "type": "rest", "format": "formData", "method": "POST", "description": "Submit the main form"}}, "initialPage": "editProfile", "permissions": ["super-admin"]}