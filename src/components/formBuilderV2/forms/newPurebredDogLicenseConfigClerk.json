{"id": "newDogLicense", "actors": {"addToCart": {"type": "function", "function": "addToCart", "description": "Add the license to the cart"}, "createDog": {"url": "/license/license/{licenseId}/add-dog", "body": {"avatar": "{avatar}", "dogBio": "{dogBio}", "dogSex": "{dogSex}", "dogName": "{dogName}", "dogBreed": "{dogBreed}", "tagNumber": "{tagNumber}", "catFriendly": "{catFriendly}", "dogFriendly": "{dogFriendly}", "dogMarkings": "{dogMarkings}", "vaccineName": "{vaccineName}", "dogBirthDate": "{dogBirthDate}", "vaccineBrand": "{vaccine<PERSON><PERSON>}", "childFriendly": "{childFriendly}", "licenseExempt": "{licenseExempt}", "vaccineDueDate": "{vaccineDueDate}", "veterinaryName": "{veterinaryName}", "dogPrimaryColor": "{dogPrimaryColor}", "microchipNumber": "{microchipNumber}", "rabiesTagNumber": "{rabiesTagNumber}", "vaccineProducer": "{vaccineProducer}", "vaccineLotNumber": "{vaccineLotNumber}", "dogSecondaryColor": "{dogSecondaryColor}", "vaccineDatesExempt": "{vaccineDatesExempt}", "dogSpayedOrNeutered": "{dogSpayedOrNeutered}", "spayNeuterExemption": "{spayNeuterExemption}", "serviceAnimalExemption": "{serviceAnimalExemption}", "vaccineAdministeredDate": "{vaccineAdministeredDate}", "vaccineLotExpirationDate": "{vaccineLotExpirationDate}"}, "type": "rest", "format": "formData", "method": "POST", "description": "Create a new dog"}, "sendDuration": {"url": "/license/license/final/{licenseId}?duration={licenseDuration}", "type": "rest", "format": "json", "method": "POST", "description": "Send the duration of the license"}, "createDraftPurebredLicense": {"url": "/license/license/pending", "body": {"licenseType": "{licenseType}", "participantId": "{individualId}"}, "type": "rest", "format": "json", "method": "POST", "description": "Create a new draft license"}}, "states": {"error": {"on": {"RETRY": {"target": "createDraft"}, "CANCEL": {"actions": ["navigateToProfile"]}}, "meta": {"form": "main", "title": "Error Loading <PERSON>", "buttons": [{"label": "Retry", "action": "RETRY", "variant": "primary"}, {"label": "Cancel", "action": "CANCEL", "variant": "ghost"}], "description": "An error has occurred. Please try again."}, "tags": ["error"]}, "editDog": {"on": {"BACK": {"target": "exemptions"}, "NEXT": {"target": "dogProfile"}}, "meta": {"form": "dog", "label": "Dog Profile", "title": "Let's get started.", "fields": [{"id": "<PERSON><PERSON><PERSON>", "type": "text", "label": "Dog Name", "required": true}, {"id": "dogBreed", "type": "customSelect", "label": "Dog Breed", "options": "{{settings: entity.dog.breeds}}", "required": true}, {"id": "dogSex", "type": "select", "label": "Dog Sex", "options": "{{settings: entity.dog.sex}}", "required": true}, {"id": "dogSpayedOrNeutered", "type": "select", "label": "Spayed or Neutered?", "options": [{"label": "Yes", "value": "yes"}, {"label": "No", "value": "no"}], "required": {"conditions": [{"field": "form.dog.spayNeuterExemption", "value": false}]}, "className": "", "placeholder": "Select options", "displayConditions": {"conditions": [{"field": "form.dog.spayNeuterExemption", "value": false}]}}, {"id": "dogBirthDate", "type": "date", "label": "Dog Birth Date", "required": true}, {"id": "dogPrimaryColor", "type": "select", "label": "Primary Color", "options": "{{settings: entity.dog.colors}}", "required": true, "placeholder": "Select Primary Color"}, {"id": "dogSecondaryColor", "type": "select", "label": "Secondary Color", "options": "{{settings: entity.dog.colors}}", "required": false, "placeholder": "Select Secondary Color"}], "paragraph": "Please enter your information below:", "sections:": [], "navigation": [{"type": "button", "label": "Next", "action": "NEXT", "variant": "primary"}, {"type": "button", "label": "Back", "action": "BACK", "variant": "ghost"}], "description": "Dog Information"}, "tags": ["page"]}, "postDog": {"tags": ["loading"], "invoke": {"src": "createDog", "input": {"avatar": "{form.dog.avatar}", "dogBio": "{form.dog.dogBio}", "dogSex": "{form.dog.dogSex}", "dogName": "{form.dog.dogName}", "dogBreed": "{form.dog.dogBreed}", "licenseId": "{licenseId}", "tagNumber": "{form.dog.tagNumber}", "catFriendly": "{form.dog.catFriendly}", "dogFriendly": "{form.dog.dogFriendly}", "dogMarkings": "{form.dog.dogMarkings}", "vaccineName": "{form.dog.vaccineName}", "dogBirthDate": "{form.dog.dogBirthDate}", "vaccineBrand": "{form.dog.<PERSON><PERSON><PERSON>}", "childFriendly": "{form.dog.childFriendly}", "licenseExempt": "{form.dog.licenseExempt}", "vaccineDueDate": "{form.dog.vaccineDueDate}", "veterinaryName": "{form.dog.veterinaryName}", "dogPrimaryColor": "{form.dog.dogPrimaryColor}", "microchipNumber": "{form.dog.microchipNumber}", "rabiesTagNumber": "{form.dog.rabiesTagNumber}", "vaccineProducer": "{form.dog.vaccineProducer}", "vaccineLotNumber": "{form.dog.vaccineLotNumber}", "dogSecondaryColor": "{form.dog.dogSecondaryColor}", "vaccineDatesExempt": "{form.dog.vaccineDatesExempt}", "dogSpayedOrNeutered": "{form.dog.dogSpayedOrNeutered}", "spayNeuterExemption": "{form.dog.spayNeuterExemption}", "serviceAnimalExemption": "{form.dog.serviceAnimalExemption}", "vaccineAdministeredDate": "{form.dog.vaccineAdministeredDate}", "vaccineLotExpirationDate": "{form.dog.vaccineLotExpirationDate}"}, "onDone": {"target": "addAdditionalDog", "actions": ["resetDogForm"]}, "onError": {"target": "confirmationDog"}}}, "success": {"meta": {"form": "main", "title": "Success", "paragraph": "You have successfully completed a new License. Please navigate to the cart page to continue.", "navigation": [{"type": "link", "label": "Go to Cart", "variant": "primary", "navigate": {"url": "/cart"}}]}, "tags": ["hiddenPage", "hideSideBar"]}, "dogProfile": {"on": {"BACK": {"target": "editDog"}, "NEXT": [{"guard": {"field": "form.dog.vaccineDatesExempt", "value": true}, "target": "confirmationDog"}, {"guard": {"field": "form.dog.vaccineDatesExempt", "value": false}, "target": "vaccineInformation"}]}, "meta": {"form": "dog", "label": "Dog Personal Information", "title": "(Optional) Dog Profile Information", "fields": [{"id": "microchipNumber", "type": "text", "label": "Microchip Number", "required": false}, {"id": "dogBio", "type": "textarea", "label": "Dog B<PERSON>", "required": false}, {"id": "avatar", "type": "file", "label": "Dog Profile Picture", "options": "dogPhoto", "required": false}], "paragraph": "Please enter information about your dog below:", "sections:": [], "navigation": [{"type": "button", "label": "Next", "action": "NEXT", "variant": "primary"}, {"type": "button", "label": "Back", "action": "BACK", "variant": "ghost"}], "description": "Dog Information"}, "tags": ["page"]}, "exemptions": {"on": {"NEXT": {"target": "editDog"}, "CANCEL": {"actions": ["navigateToProfile"]}}, "meta": {"form": "dog", "label": "Exemptions", "title": "Dog License Exemptions", "fields": [{"id": "spayNeuterExemption", "type": "checkbox", "label": "Dog is exempt from spay/neuter requirement", "className": ""}, {"id": "vaccineDatesExempt", "type": "checkbox", "label": "Dog is exempt from rabies vaccinations", "className": ""}], "paragraph": "Please select all applicable exemptions.", "sections:": [], "navigation": [{"type": "button", "label": "Next", "action": "NEXT", "variant": "primary"}, {"type": "button", "label": "Cancel", "action": "CANCEL", "variant": "ghost"}], "description": "Dog License Exemptions"}, "tags": ["page"]}, "submitting": {"tags": ["loading"], "invoke": {"id": "submitting", "src": "addToCart", "input": {"cartId": "{cartId}", "licenseId": "{licenseId}", "cartItemType": "{cartItemType}"}, "onDone": {"target": "success"}, "onError": {"target": "error"}}}, "createDraft": {"id": "createDraft", "tags": ["loading"], "invoke": {"src": "createDraftPurebredLicense", "input": {"licenseType": "{licenseType}", "participantId": "{individualId}"}, "onDone": {"target": "exemptions", "actions": ["setLicenseId"]}, "onError": {"target": "confirmationDog"}}}, "confirmationDog": {"on": {"BACK": [{"guard": {"field": "form.dog.vaccineDatesExempt", "value": true}, "target": "dogProfile"}, {"guard": {"field": "form.dog.vaccineDatesExempt", "value": false}, "target": "vaccineInformation"}], "NEXT": {"target": "postDog"}}, "meta": {"form": "dog", "label": "Confirmation", "title": "Confirm Dog Information", "fields": [{"id": "basicInfo", "type": "confirmationGroup", "label": "Basic Dog Info", "value": ""}, {"id": "<PERSON><PERSON><PERSON>", "type": "text", "label": "Dog Name", "groupId": "basicInfo", "disabled": true, "required": true}, {"id": "dogBreed", "type": "customSelect", "label": "Dog Breed", "groupId": "basicInfo", "options": "{{settings: entity.dog.breeds}}", "disabled": true, "required": true}, {"id": "dogSex", "type": "select", "label": "Dog Sex", "groupId": "basicInfo", "options": "{{settings: entity.dog.sex}}", "disabled": true, "required": true}, {"id": "dogBirthDate", "type": "date", "label": "Dog Birth Date", "groupId": "basicInfo", "disabled": true, "required": true}, {"id": "dogPrimaryColor", "type": "select", "label": "Primary Color", "groupId": "basicInfo", "options": "{{settings: entity.dog.colors}}", "disabled": true, "required": true}, {"id": "vaccineInfo", "type": "confirmationGroup", "label": "Dog Vaccine Info", "value": ""}, {"id": "vaccineName", "type": "select", "label": "Vaccine Name", "groupId": "vaccineInfo", "options": [{"label": "Rabies", "value": "Rabies"}], "disabled": true, "required": true, "displayConditions": {"conditions": [{"field": "form.dog.vaccineDatesExempt", "value": false}]}}, {"id": "vaccineProducer", "type": "select", "label": "Vaccine Producer", "groupId": "vaccineInfo", "options": "{{settings: entity.dog.rabies_producer}}", "disabled": true, "required": true, "displayConditions": {"conditions": [{"field": "form.dog.vaccineDatesExempt", "value": false}]}}, {"id": "vaccineBrand", "type": "select", "label": "Vaccine Brand", "groupId": "vaccineInfo", "options": "{{settings: entity.dog.rabies_brand}}", "disabled": true, "required": true, "displayConditions": {"conditions": [{"field": "form.dog.vaccineDatesExempt", "value": false}]}}, {"id": "vaccineAdministeredDate", "type": "date", "label": "Vaccine Administered Date", "groupId": "vaccineInfo", "disabled": true, "required": true, "displayConditions": {"conditions": [{"field": "form.dog.vaccineDatesExempt", "value": false}]}}, {"id": "vaccineDueDate", "type": "date", "label": "Vaccine Due Date", "groupId": "vaccineInfo", "disabled": true, "required": true, "displayConditions": {"conditions": [{"field": "form.dog.vaccineDatesExempt", "value": false}]}}, {"id": "vaccineDatesExempt", "type": "checkbox", "label": "Rabies Exemption", "groupId": "vaccineInfo", "disabled": true, "required": false, "displayConditions": {"conditions": [{"field": "form.dog.vaccineDatesExempt", "value": true}]}}], "paragraph": "Please confirm the following information is correct:", "navigation": [{"type": "button", "label": "Confirm", "action": "NEXT", "variant": "primary"}, {"type": "button", "label": "Back", "action": "BACK", "variant": "ghost"}], "description": "Confirm Dog Information"}, "tags": ["hiddenPage", "hideSideBar"]}, "addAdditionalDog": {"on": {"NO": [{"target": "exemptions"}], "YES": {"target": "licenseDurationInformation"}, "CANCEL": {"actions": ["navigateToHome"]}}, "meta": {"form": "main", "label": "Additional Dog", "title": "Additional Dog", "paragraph": "Would you like to add another certified purebred dog?", "navigation": [{"type": "button", "label": "Continue", "action": "YES", "variant": "primary"}, {"type": "button", "label": "No", "action": "NO", "variant": "primary"}, {"type": "button", "label": "Cancel", "action": "CANCEL", "variant": "ghost"}], "description": "Add Additional Dog"}, "tags": ["page", "hideSideBar"]}, "vaccineInformation": {"on": {"BACK": {"target": "dogProfile"}, "NEXT": {"target": "confirmationDog"}}, "meta": {"form": "dog", "label": "Vaccine", "title": "Vaccine Information", "fields": [{"id": "veterinaryName", "type": "text", "label": "Veterinary Name", "required": true}, {"id": "vaccineName", "type": "select", "label": "Vaccine Name", "options": [{"label": "Rabies", "value": "Rabies"}], "required": true}, {"id": "vaccineProducer", "type": "select", "label": "Vaccine Producer", "options": "{{settings: entity.dog.rabies_producer}}", "required": true}, {"id": "vaccineBrand", "type": "select", "label": "Vaccine Brand", "options": "{{settings: entity.dog.rabies_brand}}", "required": true}, {"id": "vaccineAdministeredDate", "type": "date", "label": "Vaccine Administered Date", "required": true, "validate": [{"type": "futureMax", "value": {"unit": "months", "amount": 0}, "message": "Vaccine administered date must be in the past."}]}, {"id": "vaccineDueDate", "type": "date", "label": "Vaccine Due Date", "required": true, "validate": [{"type": "future<PERSON>in", "value": {"unit": "months", "amount": 1}, "message": "Vaccine due date must have at least 1 month of validity."}]}, {"id": "vaccineLotNumber", "type": "text", "label": "Vaccine Lot Number", "required": false}, {"id": "vaccineLotExpirationDate", "type": "date", "label": "Vaccine Lot Expiration Date", "required": false}], "paragraph": "Please enter the vaccine information for your dog:", "navigation": [{"type": "button", "label": "Next", "action": "NEXT", "variant": "primary"}, {"type": "button", "label": "Back", "action": "BACK", "variant": "ghost"}], "description": "Vaccine Information"}, "tags": ["hiddenPage", "addToSideBar"]}, "postLicenseDuration": {"tags": ["loading"], "invoke": {"src": "sendDuration", "input": {"licenseId": "{licenseId}", "licenseDuration": "{form.main.licenseDuration}"}, "onDone": {"target": "submitting"}, "onError": {"target": "error"}}}, "licenseDurationInformation": {"on": {"BACK": {"target": "confirmationDog"}, "NEXT": {"target": "confirmationLicenseDuration"}}, "meta": {"form": "main", "label": "License Information", "title": "How many years would you like to register for?", "fields": [{"id": "licenseDuration", "type": "select", "label": "License Duration", "options": [{"label": "1 Year", "value": "1"}, {"label": "2 Years", "value": "2"}, {"label": "3 Years", "value": "3"}], "required": true}], "paragraph": "Please enter the duration of the license:", "navigation": [{"type": "button", "label": "Next", "action": "NEXT", "variant": "primary"}, {"type": "button", "label": "Back", "action": "BACK", "variant": "ghost"}], "description": "License Duration"}, "tags": ["hiddenPage", "addToSideBar"]}, "confirmationLicenseDuration": {"on": {"BACK": {"target": "licenseDurationInformation"}, "NEXT": {"target": "postLicenseDuration"}}, "meta": {"form": "main", "label": "Confirmation", "title": "Confirm Selections", "fields": [{"id": "licenseDuration", "type": "confirmationGroup", "label": "License Duration", "value": ""}, {"id": "licenseDuration", "type": "select", "label": "License Duration", "value": "{form.main.licenseDuration}", "groupId": "licenseDuration", "options": [{"label": "1 Year", "value": "1"}, {"label": "2 Years", "value": "2"}, {"label": "3 Years", "value": "3"}], "disabled": true, "required": true}], "paragraph": "Please confirm the following information is correct:", "navigation": [{"type": "button", "label": "Confirm", "action": "NEXT", "variant": "primary"}, {"type": "button", "label": "Back", "action": "BACK", "variant": "ghost"}], "description": "Confirm Selections"}, "tags": ["page", "hideSideBar"]}}, "actions": {"resetDogForm": {"type": "assignEvent", "context": {"form.dog.avatar": null, "form.dog.dogBio": null, "form.dog.dogSex": null, "form.dog.dogName": null, "form.dog.dogBreed": null, "form.dog.tagNumber": null, "form.dog.catFriendly": null, "form.dog.dogFriendly": null, "form.dog.dogMarkings": null, "form.dog.vaccineName": null, "form.dog.dogBirthDate": null, "form.dog.vaccineBrand": null, "form.dog.childFriendly": null, "form.dog.licenseExempt": false, "form.dog.serviceDogType": null, "form.dog.vaccineDueDate": null, "form.dog.veterinaryName": null, "form.dog.dogPrimaryColor": null, "form.dog.microchipNumber": null, "form.dog.rabiesTagNumber": null, "form.dog.vaccineProducer": null, "form.dog.vaccineLotNumber": null, "form.dog.dogSecondaryColor": null, "form.dog.vaccineDatesExempt": false, "form.dog.dogSpayedOrNeutered": null, "form.dog.spayNeuterExemption": false, "form.dog.serviceAnimalExemption": false, "form.dog.vaccineAdministeredDate": null, "form.dog.vaccineLotExpirationDate": null}}, "setLicenseId": {"type": "assignEvent", "context": {"licenseId": "{{event:entityId}}", "licenseType": "{{event:entityType}}"}}, "navigateToProfile": {"goTo": "/profile/individual/{individualId}?tab=licenses", "type": "navigate"}}, "context": {"form": {"dog": {"avatar": null, "dogBio": null, "dogSex": null, "dogName": null, "dogBreed": null, "tagNumber": null, "catFriendly": null, "dogFriendly": null, "dogMarkings": null, "vaccineName": "Rabies", "dogBirthDate": null, "vaccineBrand": null, "childFriendly": null, "licenseExempt": false, "serviceDogType": null, "vaccineDueDate": null, "veterinaryName": null, "dogPrimaryColor": null, "microchipNumber": null, "rabiesTagNumber": null, "vaccineProducer": null, "vaccineLotNumber": null, "dogSecondaryColor": null, "vaccineDatesExempt": false, "dogSpayedOrNeutered": null, "spayNeuterExemption": false, "serviceAnimalExemption": false, "vaccineAdministeredDate": null, "vaccineLotExpirationDate": null}, "main": {"licenseDuration": null}}, "error": null, "cartId": null, "licenseId": null, "dogEntityId": null, "licenseType": "purebredDogLicense", "cartItemType": "license", "individualId": null, "dogEntityType": null}, "initial": "exemptions", "sidebar": [{"label": "Exemptions", "order": 1, "states": ["exemptions"]}, {"label": "Dog Information", "order": 2, "states": ["editDog", "dogProfile", "vaccineInformation"]}, {"label": "License Duration", "order": 5, "states": ["licenseDurationInformation"]}, {"label": "Confirmation", "order": 6, "states": ["confirmationDog", "confirmationLicenseDuration"]}], "formName": "New Purebred Dog License Form", "requirements": ["individualId"]}