"use client";
import React from "react";
import MachineProvider, {
} from "../providers/MachineProvider";
import { AnimatePresence } from "framer-motion";
import DynamicFormPage from "./DynamicFormPage";
import ErrorPage from "@/components/error/ErrorPage";

export default function Machine({
  config,
  additionalContext = {},
}: {
  config: any;
  additionalContext?: any;
}) {

  if(!config) {
    return <ErrorPage message="No config provided" />
  }
  return (
    <MachineProvider
      config={config}
      additionalContext={additionalContext}
    >
      <div className="h-full w-full flex flex-col items-center overflow-hidden bg-neutral-100">
        <div className="flex h-full w-full flex-col items-center justify-center overflow-auto">
          <AnimatePresence mode="wait">
            <DynamicFormPage />
          </AnimatePresence>
        </div>
      </div>
    </MachineProvider>
  );
}
