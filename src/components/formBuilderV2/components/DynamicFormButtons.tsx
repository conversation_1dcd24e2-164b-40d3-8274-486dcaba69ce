import React, { useState } from "react";
import { SectionButtons } from "./FormStyles";
import { Button } from "@/components/ui/button";
import { useRouter } from "next/navigation";
import { useKeycloak } from "@/hooks/useKeycloak";
import { useMyProfile } from "@/hooks/providers/useMyProfile";
import { useMachineContext } from "../providers/MachineProvider";
import { useFormContext } from "../providers/FormProvider";
import { ButtonAction } from "../types/FormBuilderTypes3";
import { useAtom } from "jotai";
import { toastAtom } from "@/components/ui/toast/toast";
import { CgSpinner } from "react-icons/cg";
import { useMyCart } from "@/hooks/useMyCart";
import DynamicLoadingModal from "./sidebar/DynamicLoadingModal";

export default function DynamicFormButtons() {
  const [isLoading, setIsLoading] = useState(false);

  const router = useRouter();
  const { logout } = useKeycloak();

  const {
    currentPage,
    changePage,
    checkGuardConditions,
    replaceContextPlaceholders,
    functions,
    handleAPICall,
    handleResponse,
    modalConfig,
    setModalConfig,
  } = useMachineContext();
  const {
    setErrors,
    isValid,
    validity,
    isLoading: formLoading,
    resetValidation,
    validateFields
  } = useFormContext();
  const [_, setToast] = useAtom(toastAtom);

  const handleButtonClick = async ({
    operation,
    action,
  }: {
    operation: string;
    action: ButtonAction;
  }) => {
    setIsLoading(true);

    try {
      const processAction = async (action: ButtonAction, results?: any) => {
        console.log(results);
        try {
          console.log(action);

          // Handle Assign Events
          if (action.assignEvent) {
            const assignEvent = action.assignEvent;
            console.log(assignEvent);
            await handleResponse(results, assignEvent);
          }

          // Handle Guard Conditions
          if (action.guard && checkGuardConditions(action.guard)) {
            if (action.onSuccess) await processAction(action.onSuccess);
          } else if (action.guard) {
            if (action.onError) await processAction(action.onError);
          }

          // Handle API Calls
          if (action.callApi) {
            // Show modal if onLoading is configured
            if (action.onLoading && action.onLoading.modal) {
              setModalConfig({
                title: action.onLoading.title,
                message: action.onLoading.message,
              });
            }
            
            try {
              const results = await handleAPICall(functions[action.callApi], action.onLoading);
              // Hide modal on success
              setModalConfig(null);
              if (action.onSuccess) {
                await processAction(action.onSuccess, results);
              }
            } catch (error) {
              // Hide modal on error
              setModalConfig(null);
              if (action.onError) {
                await processAction(action.onError);
              } else {
                throw error;
              }
            }
          }

          // Handle Page Change
          if (action.goToPage) {
            changePage(action.goToPage);
            return;
          }

          // Handle Link Navigation
          if (action.goToLink) {
            const url = replaceContextPlaceholders(action.goToLink) as string;
            router.push(url);
          }
        } catch (error: any) {
          console.log(error);
          const errorMsg = error.response?.data?.message || error.message;
          setToast({
            label: "Error Processing Action",
            message: errorMsg,
            status: "error",
          });
          if (action.onError) await processAction(action.onError);
        }
      };

      type ErrorValue = string | { error: string; [key: string]: any };
      type ValidityType = Record<string, ErrorValue>;
      type ErrorMessagesType = Record<string, string>;

      const extractErrorMessages = (
        validity: ValidityType,
      ): ErrorMessagesType => {
        return Object.entries(validity).reduce((acc, [key, value]) => {
          if (typeof value === "object" && "error" in value) {
            acc[key] = value.error;
          } else {
            acc[key] = value as string;
          }
          return acc;
        }, {} as ErrorMessagesType);
      };

      switch (operation) {
        case "back":
          resetValidation();
          processAction(action);
          break;
        default:
          console.log(isValid);
          const valid = await validateFields();

          if (isValid) {
            console.log(valid)
            await processAction(action);
          } else {
            setErrors(extractErrorMessages(validity));
          }
          break;
      }
    } finally {
      setIsLoading(false);
      // Don't clear modalConfig here since it's handled per API call
    }
  };

  return (
    <>
      <SectionButtons>
        {currentPage?.navigation?.slice().map((button: any) => {
          switch (button.type) {
            case "link":
              const url = button?.navigate?.url;
              const actualLink = replaceContextPlaceholders(url as string);
              return (
                <Button
                  key={button.label}
                  type="button"
                  disabled={!url || !actualLink || isLoading}
                  onClick={() => {
                    switch (button?.navigate?.handle) {
                      case "logout":
                        try {
                          logout();
                        } catch (e) {
                          console.log(e);
                        }
                        break;
                      default:
                        router.push(actualLink as string);
                    }
                  }}
                  className="min-w-[200px]"
                  variant={button.variant ?? "primary"}
                >
                  {button.label}
                </Button>
              );

            case "button":
              return (
                <Button
                  key={button.label}
                  type={button.operation === "submit" ? "submit" : "button"}
                  onClick={() => handleButtonClick(button)}
                  className="min-w-[200px]"
                  variant={button.variant ?? "primary"}
                  aria-label={button.label}
                  disabled={isLoading || formLoading}
                >
                  {(isLoading || formLoading) && button.operation !== "back" ? (
                    <div className="flex items-center justify-center gap-1">
                      <CgSpinner className="size-5 animate-spin" />
                      Loading...
                    </div>
                  ) : (
                    button.label
                  )}
                </Button>
              );
          }
        })}
      </SectionButtons>

      {modalConfig && (
        <DynamicLoadingModal
          title={modalConfig.title}
          message={modalConfig.message}
        />
      )}
    </>
  );
}
