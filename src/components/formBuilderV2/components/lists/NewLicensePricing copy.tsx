import { Dog } from "@/types/DogType";
import { License } from "@/types/LicenseType";
import { isSenior } from "@/components/license/licenseHelper";
import { useGetProfile } from "@/hooks/api/useProfiles";
import { TbFidgetSpinner } from "react-icons/tb";
import { addMonths, addYears, differenceInYears, endOfMonth } from "date-fns";
import { useEffect } from "react";
import { useMachineContext } from "../../providers/MachineProvider";

const convertToDollars = (amount: number) => {
  return amount.toLocaleString("en-US", {
    style: "currency",
    currency: "USD",
  });
};

function calculateRenewalYears(
  vaccineExpirationDate: string | null | undefined,
  isVaccineExempt: boolean,
  licenseExpirationDate?: string,
) {
  const maxRenewalYears = 3;
  const currentLicenseExpirationDate = licenseExpirationDate
    ? new Date(licenseExpirationDate)
    : new Date();
  const currentDate = new Date();
  const futureCurrentDate = addYears(endOfMonth(currentDate), maxRenewalYears);

  // If the individual is vaccine exempt, calculate based on the license expiration.
  if (isVaccineExempt)
    return calculateYearsUntilExpiration(
      futureCurrentDate,
      currentLicenseExpirationDate,
      maxRenewalYears,
    );

  const currentVaccineExpirationDate = vaccineExpirationDate
    ? new Date(vaccineExpirationDate)
    : null;

  // Not Vaccine Exempt
  if (currentVaccineExpirationDate) {
    const maxVaccineExpirationDate = endOfMonth(
      addMonths(currentVaccineExpirationDate, 11),
    );
    const whicheverIsLess =
      maxVaccineExpirationDate < futureCurrentDate
        ? maxVaccineExpirationDate
        : futureCurrentDate;
    return calculateYearsUntilExpiration(
      whicheverIsLess,
      currentLicenseExpirationDate,
      maxRenewalYears,
    );
  }

  return 0;
}

function calculateYearsUntilExpiration(
  date1: Date,
  date2: Date,
  maxYears: number = 3,
) {
  const yearsUntilExp = differenceInYears(date1, date2);

  if (yearsUntilExp > maxYears) return maxYears;
  if (yearsUntilExp < 0) return 0;
  return yearsUntilExp;
}

// const FeesTable = ({ data, headers }: FeesTableProps) => (
//   <table className="mt-4 w-full divide-y divide-gray-200">
//     <thead className="bg-gray-50">
//       <tr>
//         {headers.map((header, index) => (
//           <th
//             key={index}
//             className="px-2 py-1 text-left text-xs font-medium uppercase tracking-wider text-gray-500"
//           >
//             {header}
//           </th>
//         ))}
//       </tr>
//     </thead>
//     <tbody className="divide-y divide-gray-200 bg-white">
//       {data.map((row, index) => (
//         <tr key={index}>
//           {Object.values(row).map((value, cellIndex) => (
//             <td
//               key={cellIndex}
//               className="whitespace-nowrap px-2 py-1 text-sm font-medium text-gray-900"
//             >
//               {typeof value === "number" ? `$${value.toFixed(2)}` : value}
//             </td>
//           ))}
//         </tr>
//       ))}
//     </tbody>
//   </table>
// );

// const DogLicenseFees = () => (
//   <div className="mb-10 w-full">
//     <h2 className="text-lg font-medium leading-6 text-gray-900">
//       Normal Dog License{" "}
//       <span className="text-sm text-neutral-700">per year</span>
//     </h2>
//     <FeesTable
//       data={feesData.normal}
//       headers={["Dog Status", "Standard Fee", "Senior Citizen", "Exempt"]}
//     />

//     <h2 className="mt-8 text-lg font-medium leading-6 text-gray-900">
//       Purebred Dog License{" "}
//       <span className="text-sm text-neutral-700">per year</span>
//     </h2>
//     <FeesTable data={feesData.purebred} headers={["Fee Type", "Amount"]} />

//     <p className="mt-6 text-sm text-gray-600">
//       All fees are required annually.
//     </p>
//   </div>
// );

export default function NewLicensePricing({
  licenseId,
}: {
  licenseId: string;
}) {
  const { updateContextValue } = useMachineContext();
  console.log(licenseId);

  const {
    data: licenseData,
    isLoading,
    isError,
  } = useGetProfile("license", licenseId, true);

  useEffect(() => {
    console.log(licenseData);
    if (licenseData) {
      const dog = licenseData?.license?.dogs[0];
      console.log(dog);
      const vaccineExpirationDate = dog?.vaccineDueDate;
      const isVaccineExempt = dog?.vaccineDatesExempt;
      const renewalYears = calculateRenewalYears(
        vaccineExpirationDate,
        isVaccineExempt,
      );
      updateContextValue("license.maxLicenseDuration", renewalYears);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [licenseData]);

  if (!licenseId) return <div>License ID is required</div>;

  if (isLoading) {
    return (
      <div className="mb-4 flex items-center gap-1 text-2xl">
        <TbFidgetSpinner className="animate-spin" />
        <p>Loading Prices</p>
      </div>
    );
  }

  if (isError) {
    return <div>Error loading prices</div>;
  }

  console.log(licenseData);
  const license: License = licenseData?.license;
  const owner = licenseData?.individual[0];
  const dogs: Dog[] = licenseData?.license?.dogs || [];

  console.log(license)

  // License Info
  const isPurebredLicense = license?.licenseType.code === "purebredDogLicense";
  const senior = isSenior(owner?.dateOfBirth);

  // Dog Info
  const dog: Dog = dogs[0];
  const isSpayedorNeutered = dog?.dogSpayedOrNeutered === "yes" ? true : false;
  const isLicenseExempt = dog?.licenseExempt ? true : false;
  const isAlteredExempt = dog?.isAlteredExempt ? true : false;

  const calculateFee = (years: number) => {
    if (isPurebredLicense) {
      let license = 40;
      let total = 0;
      dogs.forEach((dog: Dog) => {
        const stateFee = dog.isAlteredExempt
          ? 1
          : dog.dogSpayedOrNeutered === "yes"
            ? 1
            : 3;
        total += stateFee;
      });
      return convertToDollars((total + license) * years);
    } else {
      if (isLicenseExempt) return convertToDollars(0);

      let license = isSpayedorNeutered ? 10.0 : 15;
      let discount = senior ? 10 : 0;
      let stateFees = isAlteredExempt ? 1 : isSpayedorNeutered ? 1 : 3;
      let numerationFees = 2.5;

      const total = license + stateFees + numerationFees - discount;
      return convertToDollars(total * years);
    }
  };

  const pricing: {
    label: string;
    value: number;
  }[] = [
    {
      label: "1 Year",
      value: 1,
    },
    {
      label: "2 Years",
      value: 2,
    },
    {
      label: "3 Years",
      value: 3,
    },
  ];

  return (
    <table className="mb-10 mt-4 w-full divide-y divide-gray-200">
      <thead className="bg-gray-50">
        <tr>
          <th className="px-2 py-1 text-left text-xs font-medium uppercase tracking-wider text-gray-500">
            Term
          </th>
          <th className="px-2 py-1 text-left text-xs font-medium uppercase tracking-wider text-gray-500">
            Price
          </th>
        </tr>
      </thead>
      <tbody className="divide-y divide-gray-200 bg-white">
        {pricing.map((item) => (
          <tr key={item.value}>
            <td className="whitespace-nowrap px-2 py-1 text-sm font-medium text-gray-900">
              {item.label}
            </td>
            <td className="whitespace-nowrap px-2 py-1 text-sm font-medium text-gray-900">
              {calculateFee(item.value)}
            </td>
          </tr>
        ))}
      </tbody>
    </table>
  );
}
