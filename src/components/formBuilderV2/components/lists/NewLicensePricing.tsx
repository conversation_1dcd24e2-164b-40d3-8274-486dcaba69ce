import { Dog } from "@/types/DogType";
import { License } from "@/types/LicenseType";
import { isSenior } from "@/components/license/licenseHelper";
import { useGetProfile } from "@/hooks/api/useProfiles";
import { TbFidgetSpinner } from "react-icons/tb";
import { addMonths, addYears, differenceInYears, endOfMonth } from "date-fns";
import { useEffect } from "react";
import { useMachineContext } from "../../providers/MachineProvider";

const convertToDollars = (amount: number) => {
  return amount.toLocaleString("en-US", {
    style: "currency",
    currency: "USD",
  });
};

function calculateRenewalYears(
  vaccineExpirationDate: string | null | undefined,
  isVaccineExempt: boolean,
  licenseExpirationDate?: string,
) {
  const maxRenewalYears = 3;
  const currentLicenseExpirationDate = licenseExpirationDate
    ? new Date(licenseExpirationDate)
    : new Date();
  const currentDate = new Date();
  const futureCurrentDate = addYears(endOfMonth(currentDate), maxRenewalYears);

  // If the individual is vaccine exempt, calculate based on the license expiration.
  if (isVaccineExempt)
    return calculateYearsUntilExpiration(
      futureCurrentDate,
      currentLicenseExpirationDate,
      maxRenewalYears,
    );

  const currentVaccineExpirationDate = vaccineExpirationDate
    ? new Date(vaccineExpirationDate)
    : null;

  // Not Vaccine Exempt
  if (currentVaccineExpirationDate) {
    const maxVaccineExpirationDate = endOfMonth(
      addMonths(currentVaccineExpirationDate, 11),
    );
    const whicheverIsLess =
      maxVaccineExpirationDate < futureCurrentDate
        ? maxVaccineExpirationDate
        : futureCurrentDate;
    return calculateYearsUntilExpiration(
      whicheverIsLess,
      currentLicenseExpirationDate,
      maxRenewalYears,
    );
  }

  return 0;
}

function calculateYearsUntilExpiration(
  date1: Date,
  date2: Date,
  maxYears: number = 3,
) {
  const yearsUntilExp = differenceInYears(date1, date2);

  if (yearsUntilExp > maxYears) return maxYears;
  if (yearsUntilExp < 0) return 0;
  return yearsUntilExp;
}

// const FeesTable = ({ data, headers }: FeesTableProps) => (
//   <table className="mt-4 w-full divide-y divide-gray-200">
//     <thead className="bg-gray-50">
//       <tr>
//         {headers.map((header, index) => (
//           <th
//             key={index}
//             className="px-2 py-1 text-left text-xs font-medium uppercase tracking-wider text-gray-500"
//           >
//             {header}
//           </th>
//         ))}
//       </tr>
//     </thead>
//     <tbody className="divide-y divide-gray-200 bg-white">
//       {data.map((row, index) => (
//         <tr key={index}>
//           {Object.values(row).map((value, cellIndex) => (
//             <td
//               key={cellIndex}
//               className="whitespace-nowrap px-2 py-1 text-sm font-medium text-gray-900"
//             >
//               {typeof value === "number" ? `$${value.toFixed(2)}` : value}
//             </td>
//           ))}
//         </tr>
//       ))}
//     </tbody>
//   </table>
// );

// const DogLicenseFees = () => (
//   <div className="mb-10 w-full">
//     <h2 className="text-lg font-medium leading-6 text-gray-900">
//       Normal Dog License{" "}
//       <span className="text-sm text-neutral-700">per year</span>
//     </h2>
//     <FeesTable
//       data={feesData.normal}
//       headers={["Dog Status", "Standard Fee", "Senior Citizen", "Exempt"]}
//     />

//     <h2 className="mt-8 text-lg font-medium leading-6 text-gray-900">
//       Purebred Dog License{" "}
//       <span className="text-sm text-neutral-700">per year</span>
//     </h2>
//     <FeesTable data={feesData.purebred} headers={["Fee Type", "Amount"]} />

//     <p className="mt-6 text-sm text-gray-600">
//       All fees are required annually.
//     </p>
//   </div>
// );

export default function NewLicensePricing() {
  const { updateContextValue, context } = useMachineContext();
  console.log(context)

  return (
    <table className="mb-10 mt-4 w-full divide-y divide-gray-200">
      <thead className="bg-gray-50">
        <tr>
          <th className="px-2 py-1 text-left text-xs font-medium uppercase tracking-wider text-gray-500">
            Term
          </th>
          <th className="px-2 py-1 text-left text-xs font-medium uppercase tracking-wider text-gray-500">
            Price
          </th>
        </tr>
      </thead>
      <tbody className="divide-y divide-gray-200 bg-white">
        {/* {pricing.map((item) => (
          <tr key={item.value}>
            <td className="whitespace-nowrap px-2 py-1 text-sm font-medium text-gray-900">
              {item.label}
            </td>
            <td className="whitespace-nowrap px-2 py-1 text-sm font-medium text-gray-900">
              {calculateFee(item.value)}
            </td>
          </tr>
        ))} */}
      </tbody>
    </table>
  );
}
