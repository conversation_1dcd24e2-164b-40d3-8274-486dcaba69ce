import {
  formatDate,
  isSpayedOrNeutered,
  isVaccineValid,
} from "@/components/license/licenseHelper";
import Image from "next/image";
import React from "react";
import { BiInjection, BiSolidDog, BiX } from "react-icons/bi";
import {
  <PERSON><PERSON><PERSON>,
  Toolt<PERSON>Content,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/Tooltip";
import { Dog } from "@/types/DogType";

const DogInfo = ({ dog, send }: { dog: Dog; send: any }) => {
  console.log(dog);

  const vaccineDueDate =
    dog.vaccineDueDate === null ? isVaccineValid(dog.vaccineDueDate) : "N/A";

  return (
    <div className="flex w-full items-center justify-between gap-4 rounded bg-white p-4 shadow">
      <div className="flex shrink-0 gap-3 ">
        {/* Image */}
        <div className="relative h-12 w-12 shrink-0 overflow-auto rounded">
          <Image
            src="/images/icons/dog.png"
            alt="Picture of the Dog"
            className="shrink-0 rounded"
            fill
            style={{
              objectFit: "cover",
            }}
          />
        </div>
        <div className="">
          <h3>
            <span className="font-medium">{dog.dogName}</span> ({dog.dogBreed})
          </h3>
          <div className="flex items-center gap-2 text-neutral-700">
            <div>
              {dog.dogSex} -{" "}
              <span className="font-semibold">
                {isSpayedOrNeutered(
                  dog?.dogSpayedOrNeutered ?? null,
                  dog?.dogSex ?? null,
                )}
              </span>
            </div>
          </div>
        </div>
      </div>

      {/* Dog Information */}
      <div className="w-full border-x border-neutral-300 px-8">
        {/* Dog Name */}

        <div className="flex items-center gap-10">
          <div className="flex w-full  flex-col justify-center">
            <div className="text-sm font-medium italic text-neutral-700 ">
              Vaccines:
            </div>

            {/* Dog Vaccine */}
            <div className="flex items-center gap-2">
              <div className="flex items-center gap-1">
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger>
                      Rabies:{" "}
                      <span
                        className={`rounded px-1 py-0.5 text-sm ${
                          dog.vaccineDueDate === null
                            ? (isVaccineValid(dog.vaccineDueDate)
                              ? "bg-emerald-600 text-emerald-50"
                              : "bg-red-600 text-red-50")
                            : "bg-gray-400 text-gray-50"
                        }`}
                      >
                        {vaccineDueDate !==  "N/A" ? (
                          vaccineDueDate ? (
                            <span>Valid</span>
                          ) : (
                            <span>Expired</span>
                          )
                        ) : (
                          "N/A"
                        )}
                      </span>
                    </TooltipTrigger>
                    <TooltipContent>
                      <p>
                        {dog.vaccineAdministeredDate ? formatDate(dog.vaccineAdministeredDate) : "N/A"} -{" "}
                        {vaccineDueDate}
                      </p>
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Update Buttons */}
      <div className="flex shrink-0 gap-3">
        <button
          onClick={() => {
            send({ type: "DOG_VACCINE", dogId: dog.entityId });
          }}
          className="flex items-center justify-center gap-2 rounded bg-blue-500 p-2 text-sm text-blue-50 hover:bg-blue-700"
        >
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger>
                <BiInjection className="text-xl" />
              </TooltipTrigger>
              <TooltipContent>
                <p>Update Vaccine</p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
        </button>
        <button
          onClick={() => {
            send({ type: "DOG_PROFILE", dogId: dog.entityId });
          }}
          className="flex items-center justify-center gap-2 rounded bg-neutral-600 p-2 text-sm text-neutral-50 hover:bg-neutral-700"
        >
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger>
                <BiSolidDog className="text-xl" />
              </TooltipTrigger>
              <TooltipContent>
                <p>Update Profile</p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
        </button>
      </div>
    </div>
  );
};

export default DogInfo;
