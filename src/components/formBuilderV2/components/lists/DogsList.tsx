import React from "react";
import DogInfo from "./DogInfo";
import { Dog } from "@/types/DogType";

interface DogsListProps {
  dogs: Dog[];
  send: any;
}

const DogsList = ({ dogs, send }: DogsListProps) => {
  console.log("dogs", dogs);
  return (
    <div className="w-full">
      {dogs &&
        dogs?.map((dog) => (
          <DogInfo key={dog.entityId} dog={dog} send={send} />
        ))}
    </div>
  );
};

export default DogsList;
