import React from "react";
import { evaluateConditions } from "../../scripts/utils";
import { cn } from "@/lib/utils";
import { PagesConfig } from "../../types/FormBuilderTypes3";
type ItemProp = {
  id: string;
  label: string;
};

export default function Confirmation({ form, state, config }: any) {
  const meta = state?.getMeta();
  const formName = meta[Object.keys(meta)[0]].form;
  
  return (
    <div className="container flex max-w-xl flex-col gap-2">
      <ConfirmationGroup
        list={extractFieldInfo(config, formName)}
        values={form}
        context={state?.context}
      />
    </div>
  );
}

function ConfirmationGroup({
  values,
  list,
  context,
}: {
  values: any;
  list: any;
  context: any;
}) {
  console.log(list);
  return (
    <div>
      {Object.entries(list).map(([formKey, formDetails]: [string, any]) => (
        <div key={formKey} className="mb-10 flex w-full flex-col items-center">
          <div className="mb-4 w-full text-lg font-bold">
            {formDetails.label}
          </div>
          {formDetails.fields
            .filter((item: any) => {
              const hidden = !evaluateConditions(
                item.displayConditions,
                context,
              );
              return !hidden;
            })
            .map((item: any) => {
              const fieldValue = values[item.id] ?? null;
              return (
                <ConfirmationList
                  key={item.id}
                  item={item}
                  fieldValue={fieldValue}
                />
              );
            })}
        </div>
      ))}
    </div>
  );
}

function ConfirmationList({
  item,
  fieldValue,
}: {
  item: ItemProp;
  fieldValue: any;
}) {
  const hasValue =
    fieldValue &&
    (!(fieldValue instanceof Object) ||
      (typeof fieldValue === "string" && fieldValue.trim().length > 0));

  let displayValue;
  if (fieldValue && typeof fieldValue === "object" && fieldValue.path) {
    displayValue = fieldValue.path.split("/").pop();
  } else {
    displayValue = hasValue ? fieldValue : "No Value";
  }

  return (
    <div className="my-2 flex w-full justify-between text-sm" key={item.id}>
      <div className="shrink-0 font-semibold">{item.label}:</div>
      <div className="w-full border-b border-dotted" />
      <div
        className={cn("max-w-[200px] shrink-0", {
          "text-red-500": displayValue === "No Value",
        })}
      >
        {displayValue}
      </div>
    </div>
  );
}

export function extractFieldInfo(config: any, formName: string) {
  let fieldsByLabel: { [label: string]: { label: string; fields: any[] } } = {};

  function traversePages(pages: PagesConfig, currentLabel?: string) {
    Object.entries(pages).forEach(([pageKey, page]) => {
      const thisFormName = config.formName;
      const pageTitle = page.title || currentLabel;
      const pageFields = page.fields;

      if (pageTitle && pageFields && formName === thisFormName) {
        if (!fieldsByLabel[pageTitle]) {
          fieldsByLabel[pageTitle] = { label: pageTitle, fields: [] };
        }

        page.fields.forEach((field: any) => {
          const fieldInfo = {
            id: field.id,
            label: field.label,
            type: field.type,
            options: field.options ? field.options : undefined,
            required: field.required,
            validate: field.validate ? field.validate : undefined,
            placeholder: field.placeholder ? field.placeholder : undefined,
          };
          fieldsByLabel[pageTitle].fields.push(fieldInfo);
        });
      }

      // Recursively traverse any nested states or subpages if they exist
      if (page.pages) {
        traversePages(page.pages, pageTitle);
      }
    });
  }

  traversePages(config.pages);

  return fieldsByLabel;
}