import React from "react";
import { useMachineContext } from "../../providers/MachineProvider";
import { useMyProfile } from "@/hooks/providers/useMyProfile";
import { cn } from "@/lib/utils";
import { useGetJSONStorage } from "@/hooks/api/useAdmin";
import { useGetTenantInformation } from "@/hooks/api/useSupport";

export default function SidebarLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <div className="flex h-fit w-full shrink-0 flex-col overflow-y-auto border-r bg-white px-6 py-3 md:h-full md:w-[300px] md:gap-10 md:py-10 md:shadow">
      {children}
    </div>
  );
}

export function FormTitle() {
  const { formName } = useMachineContext();

  return <p className=" px-1 text-lg text-neutral-700">{formName ?? "Form"}</p>;
}

export function FormSidebarFooter() {
  const { hasPermissions } = useMyProfile();
  const { data, isError, isLoading } = useGetTenantInformation();

  const permitted = hasPermissions(["super-admin"]);

  if (isError) return null;

  if (isLoading) {
    return (
      <div className={cn("hidden md:block", permitted && "hidden")}>
        <p>Support</p>
        <p className="text-sm text-neutral-800">
          For assistance, please contact the Clerk&apos;s Office.
        </p>
      </div>
    );
  }

  if (data)
    return (
      <div className={cn("hidden md:block", permitted && "hidden")}>
        <p>Support</p>
        <p className="text-sm text-neutral-800">
          For assistance, please contact the {data?.cityClerkOfficeName ?? "Clerk's"} Office.
        </p>
      </div>
    );

  return null;
}
