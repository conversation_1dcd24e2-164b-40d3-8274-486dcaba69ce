import React from "react";
import { CgSpinner } from "react-icons/cg";

function DynamicLoadingModal({ title, message }: { title?: string; message?: string }) {
  return (
    <div
      role="dialog"
      aria-modal="true"
      aria-labelledby="loading-modal-title"
      aria-describedby="loading-modal-message"
      className="fixed inset-0 z-50 flex items-center justify-center bg-black/50 backdrop-blur-sm"
      style={{
        position: "fixed",
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        display: "flex",
        alignItems: "center",
        justifyContent: "center",
        zIndex: 1000,
      }}
    >
      <div
        className="animate-fadeIn"
        style={{
          animation: "fadeIn 0.3s ease-out",
        }}
      >
        <div
          className="relative overflow-hidden rounded-lg bg-white p-6 shadow-lg dark:bg-gray-800 sm:p-8"
          style={{
            background: "white",
            padding: "24px",
            borderRadius: "12px",
            minWidth: "300px",
            maxWidth: "90vw",
            textAlign: "center",
            boxShadow: "0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)",
            border: "1px solid rgba(255, 255, 255, 0.1)",
          }}
        >
          {/* Decorative elements for a friendly look */}
          <div 
            className="absolute inset-0 overflow-hidden"
            style={{
              pointerEvents: "none",
              opacity: 0.05,
              zIndex: 0,
            }}
          >
            <div 
              style={{
                position: "absolute",
                top: "-100px",
                left: "-100px",
                width: "200px",
                height: "200px",
                borderRadius: "50%",
                background: "radial-gradient(circle, #4f46e5 0%, transparent 70%)",
              }}
            />
            <div 
              style={{
                position: "absolute",
                bottom: "-80px",
                right: "-80px",
                width: "180px",
                height: "180px",
                borderRadius: "50%",
                background: "radial-gradient(circle, #7c3aed 0%, transparent 70%)",
              }}
            />
          </div>

          {/* Content */}
          <div style={{ position: "relative", zIndex: 1 }}>
            <div className="mb-4 flex justify-center">
              <CgSpinner
                className="animate-spin text-primary"
                style={{
                  color: "#4f46e5",
                  fontSize: "48px",
                }}
                aria-hidden="true"
              />
            </div>

            {title && (
              <h3
                id="loading-modal-title"
                className="mb-2 text-xl font-semibold text-gray-900 dark:text-white"
                style={{
                  fontSize: "1.25rem",
                  fontWeight: "600",
                  marginBottom: "8px",
                  color: "#111827",
                }}
              >
                {title}
              </h3>
            )}
            
            {message && (
              <p
                id="loading-modal-message"
                className="text-gray-600 dark:text-gray-300"
                style={{
                  color: "#4B5563",
                  fontSize: "1rem",
                  lineHeight: "1.5",
                }}
              >
                {message}
              </p>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}

export default DynamicLoadingModal;