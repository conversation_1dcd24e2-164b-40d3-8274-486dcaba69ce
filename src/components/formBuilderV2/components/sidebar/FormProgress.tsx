import { cn } from "@/lib/utils";
import { motion } from "framer-motion";
import { FiCheck } from "react-icons/fi";
import { useMachineContext } from "../../providers/MachineProvider";

export default function FormProgress() {
  const { sidebar, currentPage } = useMachineContext();

  if (!sidebar) return <div>No sidebar in config</div>;
  if (!currentPage) return <div>No current page in config</div>;

  const currentIndex = sidebar?.findIndex((item) => item.pages?.includes(currentPage.id));
    
  return (
    <div className="flex w-full flex-wrap items-start justify-start px-2 text-left md:h-full md:flex-col md:flex-nowrap md:overflow-auto">
      {sidebar?.map((item: any, index: number) => {
        const isLastItem = index === sidebar.length - 1;
        const complete = index < currentIndex;
        const isActive = index === currentIndex;

        const iconBorderColor = complete
          ? "border-green-700 bg-green-700 text-green-50"
          : isActive
            ? "border-blue-700 font-medium"
            : "border-gray-400";
        const labelColor = complete
          ? "text-green-700"
          : isActive
            ? "text-blue-700"
            : "text-gray-500";
        const barColor = complete ? "bg-green-700" : "bg-gray-300";

        return (
          <div
            key={item.label + "-" + index}
            className={cn("flex gap-2 md:w-full md:gap-4", labelColor)}
          >
            <div className="flex shrink-0 flex-col items-center justify-center py-1 text-xs md:h-full md:text-base">
              <motion.div
                className={cn(
                  "mb-1 flex size-5 shrink-0 items-center justify-center rounded-full border-[2px] md:size-9",
                  iconBorderColor,
                )}
                initial={false}
                animate={{ rotate: complete ? 360 : 0 }}
                transition={{ duration: 0.5 }}
              >
                <div
                >
                  {complete ? (
                    <>
                      <FiCheck
                        className="md:text-xs"
                        aria-label={`Step ${index + 1}: Completed`}
                      />
                      <span className="sr-only">
                        Step {index + 1}: Completed
                      </span>
                    </>
                  ) : (
                    <span aria-label={`Step ${index + 1}`}>{index + 1}</span>
                  )}
                </div>
              </motion.div>

              {!isLastItem ? (
                <motion.div
                  className={cn(
                    `hidden w-0.5 rounded-full md:block md:h-full`,
                    barColor,
                  )}
                  layout
                />
              ) : (
                <div className="hidden w-0.5 opacity-0 md:block md:h-full"></div>
              )}
            </div>

            <div className="md:w-full">
              <div
                className={cn(
                  "hidden text-left text-xs font-medium md:block md:w-full md:text-base",
                  labelColor,
                )}
                aria-label={item?.label}
              >
                {item?.label}
              </div>
              <div
                className={cn(
                  "mb-10 hidden text-left text-xs md:block md:w-full",
                )}
              >
                {isActive
                  ? "In Progress"
                  : complete
                    ? "Completed"
                    : "Not Started"}
              </div>
            </div>
          </div>
        );
      })}
    </div>
  );
}
