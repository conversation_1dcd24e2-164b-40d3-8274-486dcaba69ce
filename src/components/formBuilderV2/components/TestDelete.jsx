"use client";

import { usePathname } from "next/navigation";
import Link from "next/link";
import { Menu } from "lucide-react";
import { useState } from "react";

export default function Navbar() {
  const [isOpen, setIsOpen] = useState(false);

  return (
    <div className="sticky flex flex-row left-0 top-0 z-[9999] h-20 w-full bg-black p-2">
      <button
        className="lg:hidden"
        onClick={() => setIsOpen(!isOpen)}
      >
        <Menu className="size-10" />
      </button>
      <LinksRender />
    </div>
  );
}

const LinksRender = ({
  isOpen,
  setIsOpen
}) => {
  const pathname = usePathname();
  const navbarLinks = [
    {
      name: "Home",
      href: "/",
    },
    {
      name: "About Me",
      href: "/aboutMe",
    },
    {
      name: "Something",
      href: "/something",
    },
  ];
  return (
    <div className={`
      ${isOpen ? "flex" : "hidden"}
    fixed top-0 left-0 lg:block flex flex-col items-center justify-center gap-4 lg:flex-row`}>
      {navbarLinks.map((link) => {
        return (
          <Link
            href={link.href}
            key={link.href}
            className={
              pathname === link.href ? "text-blue-500" : "text-neutral-300"
            }
          >
            {link.name}
          </Link>
        );
      })}
    </div>
  );
};
