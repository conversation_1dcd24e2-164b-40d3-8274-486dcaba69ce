import React from "react";
import { motion } from "framer-motion";

const PageContainer = ({ children }: { children: React.ReactNode }) => {
  return (
    <motion.div
      initial={{ opacity: 0, y: -50 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: 50 }}
      transition={{ duration: 0.3 }}
      className="flex h-full w-full flex-col items-center gap-10 overflow-y-auto overflow-x-hidden bg-white px-1 py-2 md:bg-neutral-50 md:px-6 md:py-28"
    >
      <div className="w-full max-w-xl rounded-xl md:border bg-white md:shadow">
        {children}
      </div>
    </motion.div>
  );
};

const Title = ({ children }: { children: React.ReactNode }) => {
  return (
    <h1 className="md:border-b px-6 py-3 text-xl font-semibold">{children}</h1>
  );
};

const Description = ({ children }: { children: React.ReactNode }) => {
  return <p className="mb-10 text-neutral-700">{children}</p>;
};

const SectionButtons = ({ children }: { children: React.ReactNode }) => {
  const numChildren = React.Children.count(children);
  return (
    <div
      className={`
      flex w-full flex-col items-center gap-3 p-6
      ${numChildren > 1 ? "justify-between" : "justify-end"}`}
    >
      {children}
    </div>
  );
};

export { PageContainer, Title, Description, SectionButtons };
