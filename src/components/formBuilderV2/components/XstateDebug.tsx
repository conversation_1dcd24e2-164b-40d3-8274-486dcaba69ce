import React, { useState } from "react";
import {
  Accordion,
} from "@/components/ui/accordion";
import { cn } from "@/lib/utils";
import { useMachineContext } from "../providers/MachineProvider";
import Context from "./debug/Context";
import Errors from "./debug/Errors";
import Validity from "./debug/Validity";
import Guards from "./debug/Guards";

const XstateDebug = () => {
  const { debugMode, currentPage } = useMachineContext();
  const [openItems, setOpenItems] = useState<string[]>(["context"]);

  return (
    <div
      className={cn(
        "flex h-full w-[400px] shrink-0 flex-col overflow-auto bg-white p-4 text-sm shadow",
        !debugMode && "hidden",
      )}
    >
      <h2 className="text-lg font-semibold">Form Builder Debugger Tool</h2>

      {/* Display Current Page */}
      <div className="mb-4">
        <div className="font-semibold text-blue-600">
          Current Page:
          <br />
          <div className="font-sembold rounded border bg-blue-100 p-2 font-normal text-neutral-600">
            {currentPage.id}
          </div>
        </div>
      </div>

      <Guards />

      <Accordion type="multiple" value={openItems} onValueChange={setOpenItems}>
        <Errors />
        <Validity />
        <Context />
      </Accordion>
    </div>
  );
};

export default XstateDebug;