import React from "react";
import { cn } from "@/lib/utils"; // Assuming cn utility is available for className handling
import { useMachineContext } from "../../providers/MachineProvider";
import { useFormContext } from "../../providers/FormProvider";

export default function Zipcode({
  input,
  required,
}: {
  input: any;
  required: boolean;
}) {
  const { errors, setFieldError } = useFormContext();
  const { updateContextValue, getContextValue } = useMachineContext();

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    let value = e.target.value.replace(/\D/g, "");

    if (value.length > 9) {
      value = value.substring(0, 9);
    }

    if (value.length >= 5) {
      value = `${value.substring(0, 5)}-${value.substring(5)}`;
    }

    if (value.length === 6 && value.charAt(5) === "-") {
      value = value.substring(0, 5);
    }

    updateContextValue(input.id, value);
  };

  const handleBlur = () => {
    const value = getContextValue(input.id);
    if (required && !value) {
      setFieldError(input.id, "This field is required");
    } else if (input.pattern && !new RegExp(input.pattern).test(value)) {
      setFieldError(input.id, "Invalid zipcode format");
    } else {
      setFieldError(input.id, null); // Clear the error if valid
    }
  };

  const currentValue = getContextValue(input.id);

  return (
    <div className={cn("", input?.className)}>
      <div className={cn("flex flex-col gap-1")} key={input?.label}>
        <label
          className="font-base text-left text-neutral-600"
          htmlFor={input.id}
        >
          {input.label}
          {required && <span className="text-red-600">*</span>}
        </label>
        <input
          type="text"
          value={currentValue || ""}
          onChange={handleChange}
          onBlur={handleBlur}
          className={cn(
            `w-full rounded-md border border-neutral-300 p-2 ${
              errors[input.id] ? "border-red-500" : ""
            }`
          )}
          name={input?.id}
          placeholder={input.placeholder}
          disabled={input?.disabled}
        />
      </div>
      {input?.id && errors[input.id] && (
        <p className="mb-2 text-left text-sm text-red-500">
          {errors[input.id]?.toString()}
        </p>
      )}
    </div>
  );
}
