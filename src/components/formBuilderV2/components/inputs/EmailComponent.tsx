import { cn } from "@/lib/utils";
// import React, { useEffect, useState } from "react";
import Link from "next/link";
// import { requests } from "@/utils/agent";
import { useMachineContext } from "../../providers/MachineProvider";
import { useFormContext } from "../../providers/FormProvider";

type InputProp = {
  id: string;
  label: string;
  type: string;
  validateEmail?: boolean;
  className?: string;
  placeholder?: string;
  disabled?: boolean;
};

export type EmailExistsProp = {
  firstName: string;
  lastName: string;
  entityId: string;
} | null;

export default function EmailComponent({
  input,
  required,
}: {
  input: InputProp;
  required: boolean;
}) {
  // const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  // const [emailExists, setEmailExists] = useState<EmailExistsProp>(null);

  const { updateContextValue, getContextValue } = useMachineContext();
  const { errors, isLoading, validity } = useFormContext();

  // const checkEmailExists = async (email: string) => {
  //   const isValidEmail = emailRegex.test(email);
  //   if (!isValidEmail) {
  //     updateContextValue(input.id, "");
  //   }

  //   setLoading(true);
  //   try {
  //     const response = (await requests.get(
  //       `/license/search/emailExists/${email}`,
  //     )) as EmailExistsProp;
  //     if (response) {
  //       setFieldError(input.id, "Email already exists");
  //     } else {
  //       clearFieldError(input.id);
  //     }
  //     updateContextValue(input.id, response ? "" : isValidEmail ? email : "");
  //     console.log(response);
  //   } catch (error) {
  //     console.error("Failed to check email", error);
  //   } finally {
  //     setLoading(false);
  //   }
  // };

  const watchEmail = getContextValue(input.id);
  console.log(watchEmail);
  console.log(validity[input.id]?.error);
  console.log(errors[input.id]);

  const existingUser = validity[input.id]?.existingUser || false;

  return (
    <div className={cn("", input?.className)}>
      <div className={cn("flex flex-col gap-1")} key={input?.label}>
        <label
          className="font-base flex items-end gap-2 text-left text-neutral-600"
          htmlFor={input.id}
        >
          {input?.label}
          {required && <span className="text-red-600">*</span>}
          {watchEmail && isLoading && (
            <div className="animate-pulse text-sm text-neutral-500">
              Checking Email...
            </div>
          )}
        </label>
        <input
          onChange={(e) => {
            const email = e.target.value;
            updateContextValue(input.id, email);
          }}
          type={input?.type}
          className={cn(
            `w-full rounded-md border border-neutral-300 p-2 ${
              errors[input.id] ? "border-red-500" : ""
            }`,
          )}
          name={input?.id}
          placeholder={input?.placeholder}
          id={input?.id}
          disabled={input?.disabled}
        />
      </div>
      {(errors[input.id] || validity[input.id]?.error) && (
        <div className="mb-2 flex gap-2 text-sm text-red-500">
          <p className="">
            {errors[input.id] ||
              validity[input.id]?.error ||
              "Email Error"}
          </p>{" "}
          {validity[input.id]?.existingUser && (
            <Link
              href={`/profile/individual/${existingUser.entityId}?tab=profile`}
              className="text-blue-500"
              target="_blank"
            >
              View{" "}
              {existingUser.firstName
                ? `${existingUser.firstName} ${existingUser.lastName}`
                : "Profile"}{" "}Profile
            </Link>
          )}
        </div>
      )}
    </div>
  );
}
