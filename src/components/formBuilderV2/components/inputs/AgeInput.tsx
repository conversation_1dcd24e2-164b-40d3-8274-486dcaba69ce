import React, { useState, useEffect } from "react";
import { cn } from "@/lib/utils";
import { format, subMonths, subYears } from "date-fns";
import InputLabel from "./InputLabel";
import { useMachineContext } from "../../providers/MachineProvider";
import { useFormContext } from "../../providers/FormProvider";
import { Fields } from "../../types/FormBuilderTypes3";

interface AgeData {
  years: number;
  months: number;
}

function calculateBirthDate(ageData: AgeData): string {
  const now = new Date();
  
  let birthDate = new Date(now.getFullYear(), now.getMonth(), 1);
  
  birthDate = subYears(birthDate, ageData.years);
  birthDate = subMonths(birthDate, ageData.months);
  
  return format(birthDate, "yyyy-MM-dd");
}

function calculateAgeFromBirthDate(birthDateString: string, maxAge: number = 120): AgeData {
  if (!birthDateString) return { years: 0, months: 0 };
  
  const birthDate = new Date(birthDateString + 'T00:00:00');
  const now = new Date();
  
  if (birthDate > now) {
    return { years: 0, months: 0 };
  }
  
  let totalMonths = (now.getFullYear() - birthDate.getFullYear()) * 12 + (now.getMonth() - birthDate.getMonth());
  
  if (now.getDate() < birthDate.getDate()) {
    totalMonths--;
  }
  
  const years = Math.floor(Math.max(0, totalMonths) / 12);
  const months = Math.max(0, totalMonths) % 12;
  
  return { 
    years: Math.min(years, maxAge),
    months: months 
  };
}

export default function AgeInput({
  input,
  required,
}: {
  input: Fields;
  required: boolean;
}) {
  const { updateContextValue, getContextValue } = useMachineContext();
  const { errors } = useFormContext();
  
  const storedBirthDate = getContextValue(input.id) || "";
  const [years, setYears] = useState<number>(0);
  const [months, setMonths] = useState<number>(0);

  const getMaxAge = (): number => {
    if (input.validate) {
      const datePastMaxRule = input.validate.find(rule => rule.type === "datePastMax" && rule.unit === "years");
      if (datePastMaxRule && typeof datePastMaxRule.value === "number") {
        return datePastMaxRule.value;
      }
    }
    return 120; 
  };

  const maxAge = getMaxAge();

  useEffect(() => {
    if (storedBirthDate) {
      const ageData = calculateAgeFromBirthDate(storedBirthDate, maxAge);
      setYears(ageData.years);
      setMonths(ageData.months);
    }
  }, [storedBirthDate, maxAge]);

  const handleYearsChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    const newYears = parseInt(e.target.value) || 0;
    setYears(newYears);
    
    if (newYears > 0 || months > 0) {
      const birthDate = calculateBirthDate({ years: newYears, months });
      updateContextValue(input.id, birthDate);
    } else {
      updateContextValue(input.id, "");
    }
  };

  const handleMonthsChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    const newMonths = parseInt(e.target.value) || 0;
    setMonths(newMonths);
    
    if (years > 0 || newMonths > 0) {
      const birthDate = calculateBirthDate({ years, months: newMonths });
      updateContextValue(input.id, birthDate);
    } else {
      updateContextValue(input.id, "");
    }
  };

  const yearOptions = Array.from({ length: maxAge + 1 }, (_, i) => i);
  
  const monthOptions = Array.from({ length: 12 }, (_, i) => i);

  return (
    <div className={cn("", input?.className)}>
      <div className="flex flex-col gap-1">
        <InputLabel id={input.id} label={input.label} required={required} />
        <div className="flex gap-2">
          <div className="flex-1">
            <select
              value={years}
              onChange={handleYearsChange}
              className={cn(
                `w-full rounded-md border border-slate-200 bg-white p-2 disabled:opacity-50`,
                errors[input.id] && "border-red-500",
                input?.disabled && "opacity-50"
              )}
              disabled={input?.disabled}
            >
              {yearOptions.map((year) => (
                <option key={year} value={year}>
                  {year} {year === 1 ? 'year' : 'years'}
                </option>
              ))}
            </select>
          </div>
          <div className="flex-1">
            <select
              value={months}
              onChange={handleMonthsChange}
              className={cn(
                `w-full rounded-md border border-slate-200 bg-white p-2 disabled:opacity-50`,
                errors[input.id] && "border-red-500",
                input?.disabled && "opacity-50"
              )}
              disabled={input?.disabled}
            >
              {monthOptions.map((month) => (
                <option key={month} value={month}>
                  {month} {month === 1 ? 'month' : 'months'}
                </option>
              ))}
            </select>
          </div>
        </div>
      </div>
      {errors[input.id] && (
        <p className="text-sm text-red-500">{errors[input.id]}</p>
      )}
    </div>
  );
} 