import { cn } from "@/lib/utils";
import Image from "next/image";
import React, { useMemo, useState, useEffect } from "react";
import { Accept, FileRejection, useDropzone } from "react-dropzone";
import { BiImageAdd } from "react-icons/bi";
import { useMachineContext } from "../../providers/MachineProvider";
import { useFormContext } from "../../providers/FormProvider";

interface FileUploadProps {
  input: any;
  required: boolean;
}

const FileUpload: React.FC<FileUploadProps> = ({ input, required }) => {
  const { clearFieldError, setFieldError, errors } = useFormContext();
  const { updateContextValue, getContextValue } = useMachineContext();
  const [isLoading, setIsLoading] = useState(false);
  
  const file = getContextValue(input.id);

  // Reset loading state when file changes or is removed
  useEffect(() => {
    if (!file) {
      setIsLoading(false);
    } else if (file) {
      // Clear loading state quickly once file is accepted into form state
      const timer = setTimeout(() => {
        setIsLoading(false);
      }, 100); // Very short delay just to show the loading state briefly
      return () => clearTimeout(timer);
    }
  }, [file]);

  const defaultAcceptedTypes = ["jpeg", "jpg", "png", "pdf"];
  const acceptedTypes = input.acceptedFileType || defaultAcceptedTypes;
  
  // Default to 5MB, allow override from input.maxFileSize
  const maxFileSize = input.maxFileSize || (5 * 1024 * 1024); // 5MB default
  const maxFileSizeMB = Math.round(maxFileSize / (1024 * 1024));
  const fileExtensions: { [key: string]: string } = {
    jpeg: ".jpeg",
    jpg: ".jpg",
    png: ".png",
    pdf: ".pdf",
  };

  const acceptFiles: Accept = useMemo(() => {
    const accept: Accept = {};
    acceptedTypes.forEach((type: string) => {
      if (type === "pdf") {
        accept["application/pdf"] = [".pdf"];
      } else if (type in fileExtensions) {
        accept["image/*"] = [
          ...(accept["image/*"] || []),
          fileExtensions[type],
        ];
      }
    });
    return accept;
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [acceptedTypes]);

  const validTypes = useMemo(() => {
    return acceptedTypes.map((type: string) => {
      if (type === "pdf") {
        return "application/pdf";
      }
      return `image/${type}`;
    });
  }, [acceptedTypes]);

  const customValidator = (file: File) => {
    if (!validTypes.includes(file.type)) {
      return {
        code: "file-invalid-type",
        message: `Unsupported file type. Allowed types: ${acceptedTypes.join(", ")}.`,
      };
    }
    return null;
  };

  const onDrop = (acceptedFiles: File[], fileRejections: FileRejection[]) => {
    clearFieldError(input.id);

    if (fileRejections.length > 0) {
      setIsLoading(false);
      fileRejections.forEach(({ errors }) => {
        errors.forEach((error) => {
          switch (error.code) {
            case "file-too-large":
              setFieldError(input.id, `File size should not exceed ${maxFileSizeMB}MB`);
              break;
            case "file-invalid-type":
              setFieldError(input.id, `Unsupported file type. Allowed types: ${acceptedTypes.join(", ")}`);
              break;
            case "too-many-files":
              setFieldError(input.id, "You can only drop one file at a time");
              break;
            default:
              setFieldError(input.id, "There was an issue with your file");
          }
        });
      });
    } else if (acceptedFiles.length > 0) {
      setIsLoading(true);
      updateContextValue(input.id, acceptedFiles[0]);
    }
  };

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    accept: acceptFiles,
    maxSize: maxFileSize,
    onDrop,
    multiple: false,
    validator: customValidator,
  });

  const removeFile = () => {
    updateContextValue(input.id, null);
  };

  const renderPreview = () => {
    if (!file) return null;

    const fileUrl = URL.createObjectURL(file);
    if (file.type.includes("image")) {
      return (
        <div className="flex items-center justify-center">
          <Image 
            width={300} 
            height={300} 
            src={fileUrl} 
            alt="Preview" 
          />
        </div>
      );
    } else if (file.type.includes("pdf")) {
      return (
        <div className="flex items-center justify-center">
          <object
            data={fileUrl}
            type="application/pdf"
            style={{ maxWidth: "auto", height: "300px" }}
          >
            <p>
              Your browser does not support PDFs.{" "}
              <a href={fileUrl}>Download the PDF</a>.
            </p>
          </object>
        </div>
      );
    }
  };

  return (
    <div className={cn("", input?.className)}>
      <p className="font-base text-left text-neutral-600">
        {input?.label}
        {required && <span className="text-red-600">*</span>}
      </p>
      <div
        {...getRootProps()}
        className={`dropzone cursor-pointer rounded-md border-2 border-dashed p-4 text-center ${
          isDragActive
            ? "border-blue-500 bg-blue-100"
            : "border-gray-300 bg-gray-50"
        }`}
      >
        <input {...getInputProps()} />
        {file ? (
          isLoading ? (
            <div className="flex flex-col items-center justify-center py-8">
              <div className="h-8 w-8 animate-spin rounded-full border-2 border-blue-500 border-t-transparent"></div>
              <p className="mt-2 text-sm text-gray-600">Processing file...</p>
            </div>
          ) : (
            <div className="preview">{renderPreview()}</div>
          )
        ) : isDragActive ? (
          <p className="text-gray-700">Drop the file here ...</p>
        ) : (
          <div className="flex flex-col items-center text-gray-500">
            <BiImageAdd className="text-2xl" />
            Drag &apos;n&apos; drop a file here, or click to select a file
            <br />
            <span className="text-xs text-gray-400">
              Only {acceptedTypes.join(", ")} files are allowed
            </span>
          </div>
        )}
      </div>
      {file && (
        <div className="mt-2 flex items-center justify-between rounded border border-neutral-900 p-1">
          <span className="line-clamp-1 text-gray-800">{file.name}</span>
          <button
            onClick={removeFile}
            className="text-sm text-red-500 hover:text-red-700"
          >
            Remove
          </button>
        </div>
      )}
      {errors[input?.id] && (
        <p className="text-red-500">{errors[input?.id]}</p>
      )}
    </div>
  );
};

export default FileUpload;
