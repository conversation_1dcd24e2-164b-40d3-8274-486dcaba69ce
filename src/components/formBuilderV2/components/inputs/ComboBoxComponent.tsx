"use client";
import { useEffect, useRef, useState } from "react";
import { Check, ChevronDown } from "lucide-react";
import { cn } from "@/lib/utils";
import { Button } from "@/components/ui/button";
import {
  Command,
  CommandButton,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from "@/components/ui/command";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";

interface IOption {
  value: string;
  label: string;
}

interface CustomSelectProps {
  name: string;
  placeholder: string;
  options: IOption[];
  defaultValue: string;
  customValue?: boolean;
  onValueChange: (name: string, value: string) => void;
  disabled?: boolean;
}

export default function ComboBoxComponent({
  name,
  defaultValue,
  onValueChange,
  options,
  customValue,
  placeholder,
  disabled,
}: CustomSelectProps) {
  const [open, setOpen] = useState(false);
  const [buttonWidth, setButtonWidth] = useState<number>(0);
  const [searchText, setSearchText] = useState("");
  const buttonRef = useRef<HTMLButtonElement>(null);

  useEffect(() => {
    if (buttonRef.current) {
      setButtonWidth(buttonRef.current.offsetWidth);
    }
  }, []);

  // const handleSelect = (value: string) => {
  //   onValueChange(name, value);
  //   setOpen(false);
  // };

  return (
    <Popover open={open} onOpenChange={setOpen}>
      {disabled ? (
        <div className="w-full">
          <Button
            ref={buttonRef}
            variant="outline"
            role="combobox"
            className="relative w-full justify-between px-2 py-1 text-base font-normal"
          >
            {defaultValue ?? placeholder}
            <ChevronDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
          </Button>
        </div>
      ) : (
        <PopoverTrigger asChild className="w-full">
          <Button
            ref={buttonRef}
            variant="outline"
            role="combobox"
            aria-expanded={open}
            className="relative w-full justify-between px-2 py-1 text-base font-normal line-clamp-1"
          >
            {defaultValue ?? placeholder}
            <ChevronDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
          </Button>
        </PopoverTrigger>
      )}
      <PopoverContent
        className="w-full p-0"
        style={{ width: buttonWidth ?? "" }}
      >
        <Command className="">
          <CommandInput
            disabled={disabled}
            placeholder="Search option..."
            onChangeCapture={(e: React.ChangeEvent<HTMLInputElement>) =>
              setSearchText(e.target.value)
            }
          />
          <CommandEmpty className="p-1">
            {customValue ? (
              <CommandButton
                onClick={() => {
                  onValueChange(name, searchText);
                  setOpen(false);
                }}
              >
                Add &quot;<strong>{searchText}&quot;</strong>&nbsp; as an option
              </CommandButton>
            ) : (
              <div>
                No options found for <strong>{searchText}</strong>
                button
              </div>
            )}
          </CommandEmpty>
          <CommandList>
            {options.map((option) => (
              <CommandItem
                key={option.value}
                value={option.value}
                onSelect={() => {
                  console.log("option.value", option.value);
                  onValueChange(name, option.value);
                  setOpen(false);
                }}
              >
                <Check
                  className={cn(
                    "mr-2 h-4 w-4",
                    defaultValue === option.value ? "opacity-100" : "opacity-0",
                  )}
                />
                {option.label}
              </CommandItem>
            ))}
          </CommandList>
        </Command>
      </PopoverContent>
    </Popover>
  );
}
