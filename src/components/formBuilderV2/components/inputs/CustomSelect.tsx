import React, { useEffect, useState } from "react";
import { Controller, useFormContext } from "react-hook-form";
import { cn } from "@/lib/utils";
import ComboBoxComponent from "./ComboBoxComponent";
import { useGetSettingsByOption } from "@/hooks/api/useAdmin";
import { useMachineContext } from "../../providers/MachineProvider";

export default function CustomSelect({
  input,
  required,
}: {
  input: any;
  required: boolean;
}) {
  const {
    control,
    formState: { errors },
  } = useFormContext();
  const { updateContextValue } = useMachineContext();


  const [options, setOptions] = useState([]);
  const [fetchError, setFetchError] = useState<string | null>(null);

  const dynamicOptionsRegex = /{{settings:\s*(\w+)\.(\w+)\.(\w+)\s*}}/;
  const matches = input.options.match(dynamicOptionsRegex);
  const isDynamicOptions = matches !== null;

  const category = matches?.[1];
  const type = matches?.[2];
  const option = matches?.[3];

  const { data: fetchedOptions, error: fetchErrorObject } = useGetSettingsByOption(category, type, option);

  useEffect(() => {
    if (isDynamicOptions) {
      if (fetchErrorObject) {
        setFetchError("Failed to fetch options. Please try again later.");
      } else if (fetchedOptions) {
        setOptions(fetchedOptions.map((item: any) => ({
          label: item.label,
          value: item.value,
        })));
      }
    } else {
      setOptions(input.options);
    }
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [input.options, fetchedOptions, fetchErrorObject]);

  return (
    <div className={cn("", input?.className)}>
      <Controller
        control={control}
        name={input.id}
        rules={{ required: required ? "This field is required" : false }}
        render={({ field }) => (
          <>
            <div className={cn("flex flex-col gap-1", input.className)} key={input.label}>
              <label className="font-base text-left text-neutral-600">
                {input.label}
                {required && <span className="text-red-600">*</span>}
              </label>
              {fetchError ? (
                <p className="mb-2 text-sm text-red-500">{fetchError}</p>
              ) : (
                <ComboBoxComponent
                  disabled={input?.disabled}
                  name={field.name}
                  placeholder={`Select or Type ${input.label}`}
                  options={options}
                  defaultValue={field.value}
                  customValue={input.customValue ?? false}
                  onValueChange={(name: any, value: any) => {
                    field.onChange(value);
                    updateContextValue(input.id, value);
                  }}
                />
              )}
            </div>
            <div>
              {input?.id && errors[input.id] && (
                <p className="mb-2 text-sm text-red-500">
                  {errors[input.id]?.message?.toString()}
                </p>
              )}
            </div>
          </>
        )}
      />
    </div>
  );
}
