import FileUpload from "./FileUpload";
import Zipcode from "./Zipcode";
import PhoneNumber from "./PhoneNumber";
import {
  evaluateConditions,
  evaluateRequiredOrConditions,
} from "../../scripts/utils";
import FormSelect from "./FormSelect";
import CustomSelect from "./CustomSelect";
import Checkbox from "./Checkbox";
import InputComponent from "./InputComponent";
import TextAreaComponent from "./TextAreaComponent";
import EmailComponent from "./EmailComponent";
import RegistrationCodeComponent from "./RegistrationCode";
import { useMachineContext } from "../../providers/MachineProvider";
import CodeComponent from "./CodeComponent";
import AvatarUpload from "./AvatarUpload";
import TableComponent from "./TableComponent";
import FormSearchSelect from "./FormSearchSelect";
import AgeInput from "./AgeInput";

export default function FormInputs({
  input,
}: {
  input: any;
}) {
  const { context } = useMachineContext();
  const hidden = !evaluateConditions(input.displayConditions, context);
  const required = evaluateRequiredOrConditions(input.required, context);

  if (hidden) return null;

  switch (input?.type) {
    case "number":
    case "date":
    case "text":
      return (
        <InputComponent
          input={input}
          required={required}
        />
      );
    case "age":
      return (
        <AgeInput
          input={input}
          required={required}
        />
      );
    case "code":
      return (
        <CodeComponent
          input={input}
          required={required}
        />
      );

    case "email":
      return (
        <EmailComponent
          input={input}
          required={required}
        />
      );

    case "registrationCode":
      return (
        <RegistrationCodeComponent
          input={input}
          required={required}
        />
      );
    case "tel":
    case "phone":
      return <PhoneNumber input={input} />;

    case "zipcode":
      return (
        <Zipcode
          input={input}
          required={required}
        />
      );

    case "select":
      return (
        <FormSelect
          input={input}
          required={required}
        />
      );
    
    case "searchSelect":
      return (
        <FormSearchSelect
          input={input}
          required={required}
        />
      );

    case "table":
      return (
        <TableComponent
          input={input}
        />
      );

    case "customSelect":
      return (
        <CustomSelect
          input={input}
          required={required}
        />
      );

    case "checkbox":
      return (
        <Checkbox input={input} required={required} />
      );

    case "file":
      return (
        <FileUpload input={input} required={required} />
      );

    case "avatar":
      return (
        <AvatarUpload input={input} required={required} />
      );

    case "textarea":
      return (
        <TextAreaComponent
          input={input}
          required={required}
        />
      );
    default:
      return null;
  }
}
