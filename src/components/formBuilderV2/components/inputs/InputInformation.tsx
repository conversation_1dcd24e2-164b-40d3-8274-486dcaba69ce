import React from "react";
import {
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>ontent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/Tooltip";
import { FiInfo } from "react-icons/fi";

import {
  Drawer,
  DrawerClose,
  DrawerContent,
  DrawerDes<PERSON>,
  DrawerFooter,
  Drawer<PERSON>eader,
  Drawer<PERSON><PERSON><PERSON>,
  DrawerTrigger,
} from "@/components/ui/drawer";
import { Button } from "@/components/ui/button";

export default function InputInformation({
  information,
}: {
  information: string | null | undefined;
}) {
  if (!information) return null;

  return (
    <>
      <div className="ml-2 hidden h-full items-center justify-center md:flex">
        <TooltipProvider
        delayDuration={0}
        >
          <Tooltip>
            <TooltipTrigger>
              <FiInfo className="size-4" />
            </TooltipTrigger>
            <TooltipContent className="max-w-[300px] rounded-lg bg-white p-4 shadow-lg">
              <p>{information}</p>
            </TooltipContent>
          </Tooltip>
        </TooltipProvider>
      </div>
      <div className="ml-1 flex h-full items-center justify-center md:hidden ">
        <Drawer>
          <DrawerTrigger>
            <FiInfo className="size-4" />
          </DrawerTrigger>
          <DrawerContent>
            <DrawerHeader>
              <DrawerTitle>Information</DrawerTitle>
              <DrawerDescription>
                <p>{information}</p>
              </DrawerDescription>
            </DrawerHeader>
            <DrawerFooter>
              <DrawerClose>
                <Button type="button" variant="outline">
                  Close
                </Button>
              </DrawerClose>
            </DrawerFooter>
          </DrawerContent>
        </Drawer>
      </div>
    </>
  );
}
