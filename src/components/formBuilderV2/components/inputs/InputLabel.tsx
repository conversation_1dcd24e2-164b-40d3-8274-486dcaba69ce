import React from "react";
import InputInformation from "./InputInformation";

export default function InputLabel({
  id,
  label,
  required = false,
  information
}: {
  id: string;
  label: string;
  required?: boolean;
  information?: string | null | undefined;
}) {
  return (
    <label className="flex items-center font-base text-left text-neutral-600" htmlFor={id}>
      {label}
      {required && <span className="text-red-600">*</span>}
      <InputInformation information={information} />
    </label>
  );
}
