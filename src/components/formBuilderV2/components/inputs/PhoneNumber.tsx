import React, { useEffect } from "react";
import { cn } from "@/lib/utils";
import InputLabel from "./InputLabel";
import InputError from "./InputError";
import { useMachineContext } from "../../providers/MachineProvider";
import { useFormContext } from "../../providers/FormProvider";

export default function PhoneNumber({
  input,
}: {
  input: any;
}) {
  const { errors } = useFormContext();
  const { updateContextValue, getContextValue } = useMachineContext();
  const phoneNumber = getContextValue(input.id);
  
  console.log(errors)

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const rawValue = e.target.value.replace(/\D/g, "");

    let formattedValue = formatPhoneNumber(rawValue);
    updateContextValue(input.id, formattedValue);
  };

  const formatPhoneNumber = (value: string) => {
    if (!value) return value;

    const phoneNumber = value.replace(/[^\d]/g, "");
    const phoneNumberLength = phoneNumber.length;

    if (phoneNumberLength < 4) return phoneNumber;

    if (phoneNumberLength < 7) {
      return `(${phoneNumber.slice(0, 3)}) ${phoneNumber.slice(3)}`;
    }

    return `(${phoneNumber.slice(0, 3)}) ${phoneNumber.slice(
      3,
      6,
    )}-${phoneNumber.slice(6, 10)}`;
  };

  return (
    <div className={cn("", input.className)}>
      <div className="flex flex-col gap-1">
        <InputLabel id={input.id} label={input.label} required={input.required} />
        <input
          type="tel"
          name={input.id}
          value={phoneNumber || ""}
          onChange={handleChange}
          className={cn(
            "w-full rounded-md border p-2 disabled:opacity-50",
            errors[input.id] ? "border-red-500" : "border-neutral-300",
          )}
          placeholder={input.placeholder}
          disabled={input?.disabled}
        />
      </div>
      <InputError id={input.id} errors={errors} />
    </div>
  );
}
