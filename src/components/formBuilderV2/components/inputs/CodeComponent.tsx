import React, { useState, useEffect } from "react";
import { cn } from "@/lib/utils";
import InputLabel from "./InputLabel";
import { useMachineContext } from "../../providers/MachineProvider";
import { useFormContext } from "../../providers/FormProvider";
import { Fields } from "../../types/FormBuilderTypes3";
import {
  InputOTP,
  InputOTPGroup,
  InputOTPSlot,
} from "@/components/ui/input-otp";
import { REGEXP_ONLY_DIGITS_AND_CHARS } from "input-otp";

export default function CodeComponent({
  input,
  required,
}: {
  input: Fields;
  required: boolean;
}) {
  const { updateContextValue, getContextValue } = useMachineContext();
  const value = getContextValue(input.id) || "";
  const [debouncedValue, setDebouncedValue] = useState(value);
  const { errors } = useFormContext();

  useEffect(() => {
    const handler = setTimeout(() => {
      if (getContextValue(input.id) !== debouncedValue) {
        updateContextValue(input.id, debouncedValue);
      }
    }, 300);

    return () => {
      clearTimeout(handler);
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [debouncedValue]);

  console.log(input);

  return (
    <div className={cn("", input?.className)}>
      <div className="flex flex-col gap-1">
        <InputLabel id={input.id} label={input.label} required={required} />
        <InputOTP
          maxLength={6}
          pattern={REGEXP_ONLY_DIGITS_AND_CHARS}
          className="border-neutral-500"
          value={debouncedValue}
          onChange={(value) => {
            setDebouncedValue(value.toUpperCase());
          }}
          disabled={input?.disabled}
        >
          <InputOTPGroup
            className={cn(
              "border-neutral-500",
              errors[input.id] && "!border-red-500",
            )}
          >
            {[...Array(input.size)].map((_, i) => (
              <InputOTPSlot
                key={i}
                index={i}
                className="h-14 w-14 border-neutral-500 text-3xl"
              />
            ))}
          </InputOTPGroup>
        </InputOTP>
      </div>
      {errors[input.id] && (
        <p className="text-sm text-red-500">{errors[input.id]}</p>
      )}
    </div>
  );
}
