import { cn } from "@/lib/utils";
import Image from "next/image";
import React, { useMemo } from "react";
import { Accept, FileRejection, useDropzone } from "react-dropzone";
import { useMachineContext } from "../../providers/MachineProvider";
import { useFormContext } from "../../providers/FormProvider";
import { TbPhotoEdit } from "react-icons/tb";
import { BiImageAdd } from "react-icons/bi";

interface FileUploadProps {
  input: any;
  required: boolean;
}

const AvatarUpload: React.FC<FileUploadProps> = ({ input, required }) => {
  const { clearFieldError, setFieldError, errors } = useFormContext();
  const { updateContextValue, getContextValue } = useMachineContext();

  const file = getContextValue(input.id);

  const defaultAcceptedTypes = ["jpeg", "jpg", "png"];
  const acceptedTypes = input.acceptedFileType || defaultAcceptedTypes;
  const fileExtensions: { [key: string]: string } = {
    jpeg: ".jpeg",
    jpg: ".jpg",
    png: ".png",
    pdf: ".pdf",
  };

  const acceptFiles: Accept = useMemo(() => {
    const accept: Accept = {};
    acceptedTypes.forEach((type: string) => {
      if (type === "pdf") {
        accept["application/pdf"] = [".pdf"];
      } else if (type in fileExtensions) {
        accept["image/*"] = [
          ...(accept["image/*"] || []),
          fileExtensions[type],
        ];
      }
    });
    return accept;
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [acceptedTypes]);

  const validTypes = useMemo(() => {
    return acceptedTypes.map((type: string) => {
      if (type === "pdf") {
        return "application/pdf";
      }
      return `image/${type}`;
    });
  }, [acceptedTypes]);

  const customValidator = (file: File) => {
    if (!validTypes.includes(file.type)) {
      return {
        code: "file-invalid-type",
        message: `Unsupported file type. Allowed types: ${acceptedTypes.join(", ")}.`,
      };
    }
    return null;
  };

  const onDrop = (acceptedFiles: File[], fileRejections: FileRejection[]) => {
    clearFieldError(input.id);

    if (fileRejections.length > 0) {
      fileRejections.forEach(({ errors }) => {
        errors.forEach((error) => {
          switch (error.code) {
            case "file-too-large":
              setFieldError(input.id, "File size should not exceed 100MB");
              break;
            case "file-invalid-type":
              setFieldError(
                input.id,
                `Unsupported file type. Allowed types: ${acceptedTypes.join(", ")}`,
              );
              break;
            case "too-many-files":
              setFieldError(input.id, "You can only drop one file at a time");
              break;
            default:
              setFieldError(input.id, "There was an issue with your file");
          }
        });
      });
    } else if (acceptedFiles.length > 0) {
      updateContextValue(input.id, acceptedFiles[0]);
    }
  };

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    accept: acceptFiles,
    maxSize: 100 * 1024 * 1024,
    onDrop,
    multiple: false,
    validator: customValidator,
  });

  const removeFile = () => {
    updateContextValue(input.id, null);
  };

  const renderPreview = () => {
    if (!file) return null;

    const fileUrl = URL.createObjectURL(file);
    if (file.type.includes("image")) {
      return (
        <div className="flex items-center justify-center">
          <Image width={300} height={300} src={fileUrl} alt="Preview" />
        </div>
      );
    }
  };

  return (
    <div className={cn("", input?.className)}>
      <p className="font-base text-left text-neutral-600">
        {input?.label}
        {required && <span className="text-red-600">*</span>}
      </p>{" "}
      <div className="flex flex-col items-center justify-center py-10">
        <div
          {...getRootProps()}
          className={`group relative h-28 w-28 flex-shrink-0 transform rounded-lg shadow-xl shadow-neutral-400 md:h-32 md:w-32 ${
            isDragActive
              ? "border-blue-500 bg-blue-100"
              : "border-gray-300 bg-gray-50"
          }`}
        >
          {/* Blob  */}
          <svg
            viewBox="0 0 200 200"
            xmlns="http://www.w3.org/2000/svg"
            className={cn(
              "absolute inset-0 h-[200%] w-[200%] -translate-x-1/4 -translate-y-1/4 stroke-2 opacity-50 blur drop-shadow",
              profileMap[input.avatarType ?? "default"]?.blobColor,
            )}
          >
            <path
              d="M24,-46C30.7,-37.7,35.5,-30.5,40.9,-23C46.3,-15.5,52.2,-7.8,56.6,2.5C61,12.8,63.7,25.6,60.6,36.9C57.4,48.2,48.3,58,37.2,65.7C26.1,73.3,13,78.9,1.3,76.7C-10.5,74.5,-21,64.5,-34.1,58C-47.2,51.4,-62.9,48.4,-69.5,39.3C-76.2,30.2,-73.9,15.1,-66.1,4.5C-58.3,-6.1,-45,-12.2,-38,-20.7C-31,-29.3,-30.3,-40.2,-25,-49.3C-19.7,-58.5,-9.8,-65.8,-0.6,-64.7C8.6,-63.7,17.3,-54.3,24,-46Z"
              transform="translate(100 100)"
            />
          </svg>

          <svg
            viewBox="0 0 200 200"
            xmlns="http://www.w3.org/2000/svg"
            className={cn(
              "stroke absolute inset-0 h-[200%] w-[200%] -translate-x-1/4 -translate-y-1/4  drop-shadow",
              profileMap[input.avatarType ?? "default"]?.blobColor,
            )}
          >
            <path
              d="M24,-46C30.7,-37.7,35.5,-30.5,40.9,-23C46.3,-15.5,52.2,-7.8,56.6,2.5C61,12.8,63.7,25.6,60.6,36.9C57.4,48.2,48.3,58,37.2,65.7C26.1,73.3,13,78.9,1.3,76.7C-10.5,74.5,-21,64.5,-34.1,58C-47.2,51.4,-62.9,48.4,-69.5,39.3C-76.2,30.2,-73.9,15.1,-66.1,4.5C-58.3,-6.1,-45,-12.2,-38,-20.7C-31,-29.3,-30.3,-40.2,-25,-49.3C-19.7,-58.5,-9.8,-65.8,-0.6,-64.7C8.6,-63.7,17.3,-54.3,24,-46Z"
              transform="translate(100 100)"
            />
          </svg>

          {/* Gradient */}
          <div
            className={cn(
              "absolute inset-0 rounded bg-gradient-to-r opacity-50 blur transition-all group-hover:-inset-1 group-hover:opacity-65",
              profileMap[input.avatarType ?? "default"]?.gradient,
            )}
          />

          <div className="flex flex-col items-center justify-center">
            <input {...getInputProps()} />
            {/* {isDragActive ? (
              <p className="text-gray-700">Drop the file here ...</p>
            ) : file ? (
              <div className="preview">{renderPreview()}</div>
            ) : ( */}
            <>
              <Image
                src={
                  file
                    ? URL.createObjectURL(file)
                    : profileMap[input.avatarType ?? "default"]?.avatar
                }
                alt="Avatar"
                fill
                objectFit="cover"
                className={cn("transform rounded-lg border-2 bg-white shadow")}
              />
              <button className="absolute bottom-0 right-0 z-20 translate-x-1/3 translate-y-1/3 rounded-lg border-2 border-neutral-200 bg-white p-1 text-2xl text-neutral-950 shadow transition-all hover:bg-blue-500 hover:text-blue-50">
                <TbPhotoEdit />
                <span className="sr-only">Edit Avatar</span>
              </button>
            </>
            {/* )} */}
          </div>
        </div>
      </div>
      <div className="flex flex-col items-center text-gray-500">
        <p className="mt-2">
          Drag &apos;n&apos; drop a file, or click to select
        </p>
        <span className="text-xs text-gray-400">
          Only {acceptedTypes.join(", ")} files are allowed
        </span>
      </div>
      {/* Remove file button */}
      {file && (
        <div className="mt-2 flex items-center justify-between rounded border p-2">
          <span className="line-clamp-1 text-gray-800">{file.name}</span>
          <button
            onClick={removeFile}
            className="text-sm text-red-500 hover:text-red-700"
          >
            Remove
          </button>
        </div>
      )}
      {/* Error message */}
      {errors[input?.id] && <p className="text-red-500">{errors[input?.id]}</p>}
    </div>
  );
};

export default AvatarUpload;

const profileMap: {
  [key: string]: {
    avatar: string;
    blobColor: string;
    gradient: string;
  };
} = {
  dog: {
    avatar: "/images/icons/dog.png",
    blobColor: "fill-orange-300 stroke-orange-400",
    gradient: "bg-gradient-to-r from-orange-500 via-yellow-500 to-orange-400",
  },
  individual: {
    avatar: "/images/icons/user.png",
    blobColor: "fill-blue-300 stroke-blue-400",
    gradient: "bg-gradient-to-r from-blue-500 via-blue-500 to-blue-400",
  },
  default: {
    avatar: "/images/icons/user.png",
    blobColor: "fill-blue-300 stroke-blue-400",
    gradient:
      "bg-gradient-to-r from-neutral-500 via-neutral-500 to-neutral-400",
  },
};
