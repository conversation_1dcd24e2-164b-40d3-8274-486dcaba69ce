import React, { useEffect, useState } from "react";
import { useQuery } from "@tanstack/react-query";
import { requests } from "@/utils/agent";
import { useMachineContext } from "../../providers/MachineProvider";
import { useFormContext } from "../../providers/FormProvider";

type InputProp = {
  id: string;
  label: string;
  type: string;
  className?: string;
  placeholder?: string;
  disabled?: boolean;
  minLength: {
    value: number;
    message: string;
  };
};

export default function RegistrationCodeComponent({
  input,
  required,
}: {
  input: InputProp;
  required: boolean;
}) {
  const { context, getContextValue, updateContextValue } = useMachineContext();
  const { errors, isLoading, validity } = useFormContext();

  const watchRegistrationCode = getContextValue(input.id) || "";
  console.log(watchRegistrationCode);

  console.log(validity)
  console.log(validity[input.id]);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value.trim();
    updateContextValue(input.id, value);
  };

  return (
    <div className="flex flex-col gap-1" key={input.label}>
      <label
        className="font-base flex items-end gap-2 text-left text-neutral-600"
        htmlFor={input.id}
      >
        {input.label}
        {required && <span className="text-red-600">*</span>}
      </label>
      <input
        onChange={handleInputChange}
        type={input.type}
        className={`w-full rounded-md border border-neutral-300 p-2 ${context.errors[input.id] ? "border-red-500" : ""}`}
        name={input.id}
        placeholder={input.placeholder}
        id={input.id}
        disabled={input.disabled}
      />
      {isLoading && (
        <div className="animate-pulse text-sm text-neutral-500">
          Checking Registration Code...
        </div>
      )}
      {(errors[input.id] || validity[input.id]?.error) && (
        <div className="mb-2 flex gap-2 text-sm text-red-500">
          <p className="">
            {errors[input.id] ||
              validity[input.id]?.error ||
              "Invalid registration code."}
          </p>{" "}
        </div>
      )}
      {validity[input.id] && (
        <div className="mb-2 flex gap-2 text-sm text-red-500">
          <p>{validity[input.id]}</p>
        </div>
      )}
      {/* {isFetching && (
        <div className="animate-pulse text-sm text-neutral-500">
          Checking Registration Code...
        </div>
      )}
      {context.errors[input.id] && !isFetching && (
        <div className="mb-2 flex gap-2 text-sm text-red-500">
          <p>{context.errors[input.id]?.message as any}</p>
        </div>
      )}
      {valid && !isFetching && (
        <div className="text-sm text-green-500">Registration Code is valid</div>
      )} */}
    </div>
  );
}
