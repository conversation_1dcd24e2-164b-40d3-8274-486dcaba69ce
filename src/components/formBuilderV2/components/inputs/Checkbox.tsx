import { cn } from "@/lib/utils";
import InputInformation from "./InputInformation";
import { useMachineContext } from "../../providers/MachineProvider";
import { useFormContext } from "../../providers/FormProvider";

export default function Checkbox({
  input,
  required,
}: {
  input: any;
  required: any;
}) {
  const { errors } = useFormContext();
  const { getContextValue, updateContextValue } = useMachineContext();

  const currentValue = getContextValue(input.id);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const isChecked = e.target.checked;
    updateContextValue(input.id, isChecked);
  };

  return (
    <div className={cn(``, input?.className)}>
      <div className={cn("flex w-full items-center gap-2")} key={input?.label}>
        <input
          id={input?.id}
          type="checkbox"
          checked={currentValue || false}
          onChange={handleChange}
          disabled={input?.disabled}
          className={`rounded-md border border-neutral-300 p-2`}
          name={input?.id}
          placeholder={input?.placeholder}
        />
        <label
          className="font-base text-left text-neutral-600"
          htmlFor={input?.id}
        >
          {input?.label}
          {required && <span className="text-red-600">*</span>}
        </label>
        <InputInformation information={input.information} />
      </div>
      <p className="mb-2 text-sm text-red-500">
        {errors[input?.id]?.toString()}
      </p>
    </div>
  );
}
