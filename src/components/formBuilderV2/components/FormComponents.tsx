import React from "react";
import DogsList from "./lists/DogsList";
import Confirmation from "./lists/Confirmation";
import NewLicensePricing from "./lists/NewLicensePricing";
import { useMachineContext } from "../providers/MachineProvider";

interface ComponentConfig {
  type: string;
  params?: { 
    [key: string]: string 
  };
}

interface FormComponentsProps {
  components: ComponentConfig[];
}

const componentMappings: { [key: string]: React.ComponentType<any> } = {
  DogsList: DogsList,
  Confirmation: Confirmation
};

export default function FormComponents({ components }: FormComponentsProps) {
  const { replaceContextPlaceholders } = useMachineContext();

  const renderComponent = (
    componentConfig: ComponentConfig,
    key: React.Key,
  ) => {
    const Component = componentMappings[componentConfig.type];

    if (!Component) {
      console.error("Undefined component for type:", componentConfig.type);
      return null;
    }

    const resolvedProps = Object.fromEntries(
      Object.entries(componentConfig.params || {}).map(([key, value]) => [
        key,
        replaceContextPlaceholders(value),
      ])
    );

    return <Component {...resolvedProps} key={key} />;
  };

  return (
    <div className="flex w-full flex-col items-center justify-center gap-4">
      {components?.map((componentConfig, index) =>
        renderComponent(componentConfig, index),
      )}
    </div>
  );
}