import {
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";
import { useFormContext } from "../../providers/FormProvider";

const Errors = () => {
  const { errors } = useFormContext();

  return (
    <AccordionItem value="errors">
      <AccordionTrigger>
        <h3
          className={`text-left ${Object.keys(errors).length > 0 ? "text-red-500" : ""}`}
        >
          Errors: {Object.keys(errors).length}
          {Object.keys(errors).length ? " Has Errors" : " No Errors"}
        </h3>
      </AccordionTrigger>
      <AccordionContent>
        <div>
          {Object.keys(errors).map((key: string) => (
            <div key={key}>
              <div>{`${key}: ${
                typeof errors[key] === "string"
                  ? errors[key]
                  : JSON.stringify(errors[key])
              }`}</div>
            </div>
          ))}
        </div>
      </AccordionContent>
    </AccordionItem>
  );
};

export default Errors;