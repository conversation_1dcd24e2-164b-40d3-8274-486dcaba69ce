import { AccordionContent, AccordionItem, AccordionTrigger } from "@/components/ui/accordion";
import { useMachineContext } from "../../providers/MachineProvider";
import Editor from "@monaco-editor/react";

const Context = () => {
  const { context } = useMachineContext();
  
  // Convert context to formatted JSON string
  const contextJson = JSON.stringify(context, null, 2);
  
  return (
    <AccordionItem value="context">
      <AccordionTrigger>
        <h3>Context</h3>
      </AccordionTrigger>
      <AccordionContent>
        <div className="border rounded-md overflow-hidden">
          <Editor
            height="400px"
            language="json"
            value={contextJson}
            theme="vs-dark"
            options={{
              readOnly: true,
              minimap: { enabled: false },
              scrollBeyondLastLine: false,
              wordWrap: "on",
              lineNumbers: "off",
              folding: true,
              foldingStrategy: "indentation",
              automaticLayout: true,
              fontSize: 12,
              padding: { top: 10, bottom: 10 },
              renderLineHighlight: "none",
              overviewRulerBorder: false,
              hideCursorInOverviewRuler: true,
              overviewRulerLanes: 0,
              scrollbar: {
                vertical: "auto",
                horizontal: "auto",
                verticalScrollbarSize: 8,
                horizontalScrollbarSize: 8,
              },
            }}
          />
        </div>
      </AccordionContent>
    </AccordionItem>
  );
};

export default Context;