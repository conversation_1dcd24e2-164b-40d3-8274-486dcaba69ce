import { PageContainer, Title } from "./FormStyles";
import FormProgress from "./sidebar/FormProgress";
import SidebarLayout, {
  FormSidebarFooter,
  FormTitle,
} from "./sidebar/SidebarLayout";
import DOMPurify from "dompurify";
import XstateDebug from "./XstateDebug";
import DynamicFormButtons from "./DynamicFormButtons";
import { Switch } from "@/components/ui/switch";
import { useMachineContext } from "../providers/MachineProvider";
import { FormProvider } from "../providers/FormProvider";
import FormComponents from "./FormComponents";
import FormInputs from "./inputs/FormInputs";
import { useEffect, useRef } from "react";
 
const DynamicFormPage = () => {
  const ref = useRef<HTMLFormElement>(null);

  useEffect(() => {
    if (ref.current) {
      ref.current.scrollIntoView({
        behavior: "smooth",
        block: "start",
      });
    }
  }, []);

  const isProductionOrStaging = ["production", "staging"].includes(
    process.env.NEXT_PUBLIC_APP_ENV as string,
  );

  const { currentPage, context, replaceContextPlaceholders } = useMachineContext();

  // Process all text fields through context placeholder replacement
  const title = currentPage?.title ? replaceContextPlaceholders(currentPage.title) : null;
  const paragraph = currentPage?.paragraph ? replaceContextPlaceholders(currentPage.paragraph) : null;
  const content = currentPage?.content ? replaceContextPlaceholders(currentPage.content) : null;
  const disclaimer = currentPage?.disclaimer ? replaceContextPlaceholders(currentPage.disclaimer) : null;
  
  const components = currentPage?.components ?? null;
  const fields = currentPage?.fields ?? null;

  return (
    <FormProvider fields={fields}>
      <form
        ref={ref}
        onSubmit={(e) => {
          e.preventDefault();
        }}
        className="flex h-full w-full flex-col overflow-hidden bg-neutral-50"
      >
        <div className="flex h-full flex-col overflow-auto md:flex-row md:overflow-hidden">
          {/* Sidebar */}
          {!currentPage.settings?.includes("hideSidebar") && (
            <SidebarLayout>
              <FormTitle />
              <FormProgress />
              <FormSidebarFooter />
            </SidebarLayout>
          )}

          {/* Content Section */}
          <PageContainer>
            {title && <Title>{title}</Title>}
            <div className="p-6">
              {/* Paragraphs */}
              {paragraph && (
                <SanitizedHTML
                  html={paragraph as string}
                  className="mb-10 text-neutral-700"
                />
              )}

              {/* Content */}
              {content && (
                <div
                  className="max-w-2xl"
                  dangerouslySetInnerHTML={{
                    __html: DOMPurify.sanitize(content as string),
                  }}
                />
              )}

              {/* components  */}
              {components && <FormComponents components={components} />}

              {/* Fields */}
              {fields && (
                <div className="flex flex-col gap-6">
                {fields.map((input: any) => {
                  return (
                    <FormInputs
                      key={input.id}
                      input={input}
                    />
                  );
                })}
              </div>
              )}

              {/* Disclaimer */}
              {disclaimer && (
                <div
                  className="max-w-2xl text-center"
                  dangerouslySetInnerHTML={{
                    __html: DOMPurify.sanitize(disclaimer as string, {
                      ADD_ATTR: ["target"],
                    }),
                  }}
                />
              )}
            </div>

            {/* Buttons */}
            <DynamicFormButtons />
          </PageContainer>

          {/* Debug Window */}
          <XstateDebug />
        </div>
        {!isProductionOrStaging && <DeveloperTools />}
      </form>
    </FormProvider>
  );
};

export default DynamicFormPage;

export function DeveloperTools() {
  const { debugMode, setDebugMode } = useMachineContext();
  return (
    <div className="flex w-full items-center justify-end gap-3 border-t bg-white px-6 py-2">
      <label htmlFor="debugSwitch" className="flex items-center gap-2">
        <Switch
          id="debugSwitch"
          checked={debugMode}
          onCheckedChange={setDebugMode}
        />
        Toggle Developer Mode
      </label>
    </div>
  );
}

const SanitizedHTML = ({
  html,
  className,
  add = {},
}: {
  html: string;
  className: string;
  add?: {
    ADD_ATTR?: string[];
  };
}) => {
  return (
    <div
      className={className}
      dangerouslySetInnerHTML={{
        __html: DOMPurify.sanitize(html, add),
      }}
    />
  );
};
