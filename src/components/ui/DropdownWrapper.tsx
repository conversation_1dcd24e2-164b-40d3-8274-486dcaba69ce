import React, { useRef, useEffect, useContext, createContext } from "react";
import { IconType } from "react-icons/lib";
import { motion, AnimatePresence } from "framer-motion";
import Button from "./buttons/Button";
import Link from "next/link";
import { FiUserPlus } from "react-icons/fi";
import { FaDog } from "react-icons/fa";

type Entity = {
  icon: string;
  title: string;
  description: string;
  link: string;
};

type DropDownWrapperProps = {
  title: string;
  titleSize?: string;
  position?: "left" | "right";
  notificationCount?: number;
  notificationColor?: "primary" | "success" | "warning" | "danger";
  size: "sm" | "md" | "lg" | "default" | "full";
  options?: Entity[];
};

export const DropdownContext = createContext({ closeDropdown: () => {} });

export const useDropdownContext = () => useContext(DropdownContext);

const DropDownWrapper = ({
  title,
  titleSize = "",
  position = "left",
  notificationCount = 0,
  notificationColor = "primary",
  size = "default",
  options = [],
}: DropDownWrapperProps) => {
  const [isHidden, setIsHidden] = React.useState<boolean>(true);
  const ref = useRef<HTMLDivElement>(null);

  const settings = {
    position: {
      left: "left-2",
      right: "right-2",
    },
    notificationColor: {
      primary: "bg-blue-500",
      success: "bg-green-500",
      warning: "bg-yellow-500",
      danger: "bg-red-500",
    },
    size: {
      sm: "w-[200px]",
      md: "w-[300px]",
      lg: "w-[400px]",
      default: "w-auto",
      full: "w-full",
    },
  };

  const handleClickOutside = (event: { target: any }) => {
    if (ref.current && !ref.current.contains(event.target)) {
      setIsHidden(true);
    }
  };

  const closeDropdown = () => setIsHidden(true);

  useEffect(() => {
    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);

  return (
    <DropdownContext.Provider value={{ closeDropdown }}>
      <div
        className="flex items-center justify-center transition-all"
        ref={ref}
      >
        {/* Button */}
        <Button
          variant="primary"
          size="sm"
          onClick={() => setIsHidden(!isHidden)}
          className="relative text-neutral-500"
        >
          <p className={`${titleSize}`}>{title}</p>
          {notificationCount > 0 && (
            <div
              className={`
              absolute -top-2 -right-2 z-10 rounded-full px-1 border border-white
              text-white text-xs flex items-center justify-center
              ${settings.notificationColor[notificationColor]}
            `}
            >
              {notificationCount}
            </div>
          )}
        </Button>

        {/* Dropdown */}
        <AnimatePresence>
          <motion.div
            className="
              rounded-lg shadow-xl shadow-neutral-500 border border-neutral-300
              bg-white overflow-auto
            "
            id="cart-dropdown"
            ref={ref}
            initial={{ opacity: 0, y: -10 }}
            animate={{ opacity: 1, y: 0 }}
          >
            {!isHidden && (
              <div
                className={`
                  absolute top-[70px] z-10 min-w-[200px] shadow-lg border
                  bg-white rounded
                  ${settings.position[position]}
                  ${settings.size[size]}
                `}
              >
                {options &&
                  options.map((option: Entity, idx: number) => {
                    const { icon, title, description, link } = option;
                    const IconComponent = icon === "user" ? FiUserPlus : null;

                    return (
                      <Link key={`${icon}-${idx}`} href={link} className="">
                        <div className="flex flex-row items-center justify-center gap-4 p-2 rounded-lg bg-gray-200 hover:bg-gray-100 m-2 border-2 cursor-pointer">
                          {/* Icon */}
                          <div>
                            {IconComponent && <IconComponent size={30} />}
                          </div>

                          {/* Title & Description */}
                          <div>
                            {/* Title */}
                            <h3>{title}</h3>
                          </div>
                        </div>
                      </Link>
                    );
                  })}
              </div>
            )}
          </motion.div>
        </AnimatePresence>
      </div>
    </DropdownContext.Provider>
  );
};

export default DropDownWrapper;
