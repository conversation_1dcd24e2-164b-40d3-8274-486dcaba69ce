import React from "react";
import Link from "next/link";
import { FiHome } from "react-icons/fi";

type breadcrumbType = {
  name: string;
  href: string;
};

const Breadcrumbs = ({ links }: { links: breadcrumbType[] }) => {
  return (
    <div className="flex gap-2">
      {links.map((link, index) => {
        const isLastLink = index === links.length - 1;

        return (
          <>
            <Link
              href={link.href}
              className={
                isLastLink
                  ? "text-neutral-900 font-semibold"
                  : "text-neutral-500"
              }
            >
              {decodeURIComponent(link.name)}
            </Link>
            {index !== links.length - 1 && (
              <span className="text-neutral-500">/</span>
            )}
          </>
        );
      })}
    </div>
  );
};

export default Breadcrumbs;
