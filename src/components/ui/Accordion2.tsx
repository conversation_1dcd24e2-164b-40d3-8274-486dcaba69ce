"use client"

import { useState } from 'react'
import { TbChevronDown, TbChevronRight } from 'react-icons/tb'

type AccordionProps = {
  title: string;
  children: React.ReactNode;
  open?: boolean;
}

const Accordion = ({title, children, open = true}:AccordionProps) => {
  const [isOpen, setIsOpen] = useState(open)

  return (
    <div className='flex flex-col gap-4 h-ful'>
      <div 
        className='flex w-full cursor-pointer bg-neutral-200/80 justify-between items-center px-2 rounded mt-5 font-semibold'
        onClick={() => setIsOpen(!isOpen)}
      >
        {title}
        {isOpen ? <TbChevronDown /> : <TbChevronRight />}
      </div>
      {isOpen && (
        <div className='px-2'>
          {children}
        </div>
      )}
    </div>
  )
}

export default Accordion