"use client";

import { cn } from "@/lib/utils";
import { motion } from "framer-motion";
import { ImSpinner } from "react-icons/im";

const LoadingSpinner = ({ className }: { className?: string }) => {
  return (
    <motion.div
      animate={{ rotate: 360 }}
      transition={{ repeat: Infinity, duration: 1, ease: "linear" }}
      className={cn(
        "my-5 flex size-10 items-center justify-center rounded-full text-4xl",
        className,
      )}
    >
      <ImSpinner />
    </motion.div>
  );
};

export default LoadingSpinner;
