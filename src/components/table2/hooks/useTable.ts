import { useEffect, useState } from 'react';
import {
  createColumnHelper,
} from '@tanstack/react-table';
import { TableHeader, TableData } from '../types/tableTypes';

export const useTableHeaders = (tableData: TableData[]): TableHeader[] => {
  const [headers, setHeaders] = useState<TableHeader[]>([]);

  useEffect(() => {
    if (tableData.length > 0) {
      const tableHeaders = Object.keys(tableData[0]).map((key) => ({
        header: key.charAt(0).toUpperCase() + key.slice(1),
        assessorKey: key,
      }));
      setHeaders(tableHeaders);
    }
  }, [tableData]);

  return headers;
};

export const useDynamicColumns = (headers:TableHeader[], dataKeys:string[]) => {
  const [columns, setColumns] = useState<any[]>([]);

  if (!dataKeys.includes('actions')) {
    dataKeys.push('actions');
  }

  useEffect(() => {
    const columnHelper = createColumnHelper();

    const dynamicColumns = headers
    .filter((header) => dataKeys.includes(header.assessorKey as any))
    .map((header) =>
      columnHelper.accessor(header.assessorKey as any, {
        header: header.header,
        cell: header.cell || ((info) => {
          return info.getValue();
        }),
      })
    );

    setColumns(dynamicColumns);
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [headers]);

  return columns;
};