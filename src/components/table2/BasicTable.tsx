"use client";
import { useState } from "react";
import {
  SortingState,
  flexRender,
  getCoreRowModel,
  getPaginationRowModel,
  getSortedRowModel,
  getFilteredRowModel,
  useReactTable,
} from "@tanstack/react-table";
import PaginationContainer from "./PaginationContainer";
import TableSize from "../dashboard/TableSize";
import { FiChevronDown, FiChevronUp } from "react-icons/fi";
import ColumnFilterDropdown from "./ColumnFilterDropdown";
import { useDynamicColumns } from "./hooks/useTable";
import { TableData, TableHeader } from "./types/tableTypes";
import Button from "../ui/buttons/Button";

const BasicTable = ({
  data,
  defaultColumns,
  actionButtons,
}: {
  data: TableData[];
  defaultColumns: TableHeader[];
  actionButtons: {
    label: string;
    onClick: () => void;
  }[];
}) => {
  const dataKeys = Object.keys(data[0] ?? []);

  const columns = useDynamicColumns(defaultColumns, dataKeys);

  const [sorting, setSorting] = useState<SortingState>([]);
  const [globalFilter, setGlobalFilter] = useState<string>("");
  const [columnVisibility, setColumnVisibility] = useState<any>({});

  const table = useReactTable({
    columns,
    data,
    initialState: {
      pagination: {
        pageIndex: 0,
        pageSize: 50,
      },
      columnFilters: [],
    },
    state: {
      sorting,
      globalFilter,
      columnVisibility,
    },
    getCoreRowModel: getCoreRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    getSortedRowModel: getSortedRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    onSortingChange: setSorting,
    onGlobalFilterChange: setGlobalFilter,
    onColumnVisibilityChange: setColumnVisibility,
  });

  return (
    <div className="w-full  flex flex-col gap-4">
      <div className="flex justify-between gap-4 rounded">
        <div className="w-full">
          <input
            id="globalSearch"
            type="text"
            className="border border-neutral-500 rounded px-2 py-1 w-full h-full ring-gray-600  focus:outline-indigo-700"
            placeholder="Search"
            value={globalFilter}
            onChange={(e) => setGlobalFilter(e.target.value)}
          />
        </div>
        <ColumnFilterDropdown table={table} />
        {actionButtons &&
          actionButtons.map((button, idx) => (
            <Button
              key={`${idx}-${button.label}`}
              variant="primary"
              size="sm"
              onClick={button.onClick}
            >
              {button.label}
            </Button>
          ))}
      </div>

      <div className="bg-white rounded">
        <table className="w-full rounded overflow-hidden">
          <thead className="border-b-2 text-sm">
            {table.getHeaderGroups().map((headerGroup) => (
              <tr key={headerGroup.id} className="hover:bg-blue-300/30">
                {headerGroup.headers.map((header) => (
                  <th
                    className="w-auto text-left font-semibold px-2 py-1 bg-blue-100 text-neutral-900"
                    key={header.id}
                  >
                    {header.isPlaceholder ? null : (
                      <div
                        {...{
                          className: header.column.getCanSort()
                            ? "cursor-pointer select-none"
                            : "",
                          onClick: header.column.getToggleSortingHandler(),
                        }}
                      >
                        {flexRender(
                          header.column.columnDef.header,
                          header.getContext()
                        )}
                        {{
                          asc: (
                            <FiChevronUp
                              className="inline self-center ml-1"
                              size={20}
                            />
                          ),
                          desc: (
                            <FiChevronDown
                              className="inline self-center ml-1"
                              size={20}
                            />
                          ),
                        }[header.column.getIsSorted() as string] ?? null}
                      </div>
                    )}
                  </th>
                ))}
              </tr>
            ))}
          </thead>
          <tbody className="text-black text-sm font-light">
            {table.getRowModel().rows.map((row) => (
              <tr key={row.id} className="hover:bg-blue-100/30">
                {row.getVisibleCells().map((cell) => {
                  return (
                    <td className="text-left px-2 py-1" key={cell.id}>
                      {flexRender(
                        cell.column.columnDef.cell,
                        cell.getContext()
                      )}
                    </td>
                  );
                })}
              </tr>
            ))}
          </tbody>
        </table>

        <div className={`flex items-center text-center w-full py-2`}>
          <PaginationContainer table={table} />
          &nbsp; &#124; &nbsp;
          <TableSize table={table} totalRows={data.length} />
        </div>
      </div>
    </div>
  );
};

export default BasicTable;
