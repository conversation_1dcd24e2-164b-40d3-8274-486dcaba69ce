import { motion } from "framer-motion";
import { AlertCircle, CheckCircle, Info, Megaphone } from "lucide-react";

interface AlertProps {
  status?: "error" | "warning" | "success" | "default";
  link?: {
    href: string;
    text: string;
  };
  children: React.ReactNode;
}

const statusStyles = {
  error: {
    border: "border-red-500",
    bg: "bg-red-50",
    text: "text-red-800",
    icon: <AlertCircle className="text-2xl text-red-500" />,
  },
  warning: {
    border: "border-amber-500",
    bg: "bg-amber-50",
    text: "text-amber-800",
    icon: <Megaphone className="text-2xl text-amber-500" />,
  },
  success: {
    border: "border-green-500",
    bg: "bg-green-50",
    text: "text-green-800",
    icon: <CheckCircle className="text-2xl text-green-500" />,
  },
  default: {
    border: "border-blue-500",
    bg: "bg-blue-50",
    text: "text-blue-800",
    icon: <Info className="text-2xl text-blue-500" />,
  },
};

const AlertBanner = ({ status = "default", children, link }: AlertProps) => {
  const { border, bg, text, icon } = statusStyles[status];
  return (
    <motion.div
      initial={{ opacity: 0, y: -10 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
      className={`flex w-full gap-4 rounded-lg border-l-4 ${border} ${bg} px-4 py-3 ${text} shadow-sm`}
    >
      {icon}
      <div>{children}</div>
    </motion.div>
  );
};

export default AlertBanner;
