"use client";
import React from "react";
import { TbArrowBarToRight, TbArrowBarToLeft } from "react-icons/tb";
import { FiActivity, FiFlag, FiSettings, FiUser } from "react-icons/fi";
import { useAtom } from "jotai";
import { atomWithStorage } from "jotai/utils";
import { IconType } from "react-icons/lib";
import { motion } from "framer-motion";
import { useSearchParams } from "next/navigation";
import { useRouter } from "next/navigation";

export const collapsedAtom = atomWithStorage("collapsed", true);

export type TabType = {
  label: string;
  key: IconKeys;
};

type IconKeys = "contacts" | "settings" | "activity" | "associations" | "notes";
type IconMap = Record<IconKeys, IconType>;

const icons: IconMap = {
  contacts: FiUser,
  activity: FiActivity,
  settings: FiSettings,
  associations: FiUser,
  notes: FiUser
};

const MiniSidebar = ({ 
  tabs, 
  children 
}:{ 
  tabs: TabType[], 
  children: React.ReactNode
}) => {
  const searchParams = useSearchParams()
  const router = useRouter();
  const [isCollapsed, setIsCollapsed] = useAtom(collapsedAtom);

  if(!tabs) return null

  return (
    <motion.div className="w-fit h-full flex rounded bg-gradient-to-b from-gray-50 to-neutral-100 z-20 shadow shadow-neutral-400">
      {!isCollapsed && (
        <div className="flex flex-col overflow-hidden w-72 px-2 py-1">
          {children}
        </div>
      )}
      <div className="rounded flex flex-col items-center w-12 shrink-0 overflow-auto px-1 py-2 border-l-2 border-neutral-300 text-2xl text-neutral-500">
        <div className="flex flex-col gap-6">
          <button
            className="rounded hover:bg-blue-200 p-1 hover:text-neutral-900"
            onClick={() => setIsCollapsed(!isCollapsed)}
          >
            {!isCollapsed ? <TbArrowBarToRight /> : <TbArrowBarToLeft />}
          </button>
          {tabs &&
            tabs.map((tab) => {
              return(
                <button
                  key={tab.key}
                  className="rounded hover:bg-indigo-200 p-1 hover:text-neutral-900"
                  onClick={() => {
                    
                    setIsCollapsed(false);
                  }}
                >
                  {React.createElement(icons[tab.key]) ?? <FiUser />}
                </button>
              )
            })}
        </div>
        <div className="flex flex-col mt-auto gap-6">
          <button className="rounded text-red-500 hover:bg-red-200 p-1">
            <FiFlag />
          </button>
        </div>
      </div>
    </motion.div>
  );
};

export default MiniSidebar;
