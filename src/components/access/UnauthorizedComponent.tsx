import React from "react";
import PageContainer from "../ui/Page/PageContainer";
import { But<PERSON> } from "../ui/button";
import { useParams, useRouter } from "next/navigation";

export default function UnauthorizedComponent() {
  
  const { push } = useRouter();

  const message = "You do not have permission to access this page"
  return (
    <section className="w-full h-full flex flex-col overflow-hidden bg-gradient-to-br from-clerk-background to-clerk-background/90 relative p-24">
      <PageContainer>
        <div className="flex flex-col items-center">
          <h1 className="text-2xl px-6 py-10 text-center">{message}</h1>
          <Button onClick={() => push(`/home`)}>Go Home</Button>
        </div>
      </PageContainer>
    </section>
  );
}
