import React from "react";
import { But<PERSON> } from "../ui/button";
import { useRouter } from "next/navigation";
import { useMyProfile } from "@/hooks/providers/useMyProfile";
import Image from "next/image";

export default function UnauthorizedPage() {
  const { push } = useRouter();
  const { hasPermissions } = useMyProfile();

  const message = "You do not have permission to access this page";
  return (
    <section className="relative flex h-full w-full flex-col overflow-hidden bg-white p-24">
      <div className="flex flex-col items-center gap-10">
        <Image
          alt="Access Denied Image"
          src="/images/resident/saddog.png"
          width={300}
          height={300}
        />
        <h1 className="text-center text-2xl">{message}</h1>
        {hasPermissions(["super-admin"]) && (
          <Button onClick={() => push("/dashboard")}>Go to Dashboard</Button>
        )}
        {hasPermissions(["resident"]) && (
          <Button onClick={() => push("/home")}>Go Home</Button>
        )}
      </div>
    </section>
  );
}
