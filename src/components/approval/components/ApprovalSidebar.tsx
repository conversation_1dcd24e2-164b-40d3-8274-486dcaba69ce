// Sidebar

import { cn } from "@/lib/utils";
import { format } from "date-fns";
import { usePathname, useRouter, useSearchParams } from "next/navigation";
import { useLicenseListContext, useCurrentLicenseContext } from "../hooks/useDogLicenseApproval";
import { useMergeApprovalContext } from "../hooks/useMergeApproval";
import { RequestedItem } from "../type/approvalTypes";
import { Badge } from "@/components/ui/badge";
import { TbFidgetSpinner } from "react-icons/tb";

export const ApprovalSidebarContainer = ({
  children,
}: {
  children: React.ReactNode;
}) => {
  return (
    <div
      className={cn(
        "flex h-full w-full max-w-[180px] shrink-0 flex-col overflow-hidden rounded bg-white shadow lg:max-w-[260px] ",
      )}
    >
      {children}
    </div>
  );
};

export const MergeAccountsApproval = () => {
  const { push } = useRouter();
  const pathname = usePathname();
  const searchParams = useSearchParams();

  const {
    mergeApprovalList,
    refetchMergeList,
    loadingMergeList,
    errorMergeList,
  } = useMergeApprovalContext();

  if (loadingMergeList) return <div>Loading...</div>;
  if (errorMergeList) return <div>Error</div>;

  if (mergeApprovalList) {
    return mergeApprovalList.map((item: RequestedItem) => {
      console.log(item);
      const active =
        item.requestedIndividualId ===
        searchParams.get("requestedIndividualId");

      return (
        <SidebarCard
          key={item.requestedIndividualId}
          onClick={() => {
            push(
              `${pathname}?requestedIndividualId=${item.requestedIndividualId}`,
            );
            refetchMergeList();
          }}
          className={active ? "bg-blue-50" : "hover:bg-blue-100"}
        >
          <SidebarHeader>
            <div className="font-base text-neutral-700">
              {item.requestedIndividualId}
            </div>
            <div
              className={cn(
                "text-neutral-600",
                active ? "text-neutral-800" : "text-neutral-500",
              )}
            >
              {item.requestedIndividualId}
            </div>
          </SidebarHeader>
          <div className="text-xl font-medium">
            {item.requestedIndividualId}
          </div>
          <div className={cn("text-sm text-neutral-600")}>
            {item.requestedIndividualId} year | {item.requestedIndividualId} |{" "}
            {item.requestedIndividualId}
          </div>
        </SidebarCard>
      );
    });
  }

  return <div>No Users Found</div>;
};

export const DogLicenseApproval = () => {
  const { push } = useRouter();
  const pathname = usePathname();
  const searchParams = useSearchParams();

  const {
    loadingLicenseList,
    errorLicenseList,
    licenseList,
  } = useLicenseListContext();
  
  const { refetchCurrentLicense } = useCurrentLicenseContext();

  if (loadingLicenseList) return <div>Loading...</div>;
  if (errorLicenseList) return <div>Error</div>;

  if (licenseList) {
    return (
      <>
        {licenseList.map((item: any) => {
          const { license, individual } = item;
          const fullname = `${individual[0].firstName} ${individual[0].lastName}`;
          const active = license.entityId === searchParams.get("licenseId");
          const modifiedDate = license.lastModifiedDate
            ? format(new Date(license.lastModifiedDate), "MM/dd/yyyy")
            : "N/A";

          return (
            <SidebarCard
              key={license.entityId}
              onClick={() => {
                push(`${pathname}?licenseId=${license.entityId}`);
                refetchCurrentLicense();
              }}
              className={active ? "bg-blue-50" : "hover:bg-blue-100"}
            >
              <SidebarHeader>
                <div className="font-base text-neutral-700">
                  {license.licenseNumber}
                </div>
                <div
                  className={cn(
                    "text-neutral-600",
                    active ? "text-neutral-800" : "text-neutral-500",
                  )}
                >
                  {modifiedDate}
                </div>
              </SidebarHeader>
              <div className="text-xl font-medium">{fullname}</div>
              <div className={cn("text-sm text-neutral-600")}>
                {license.licenseDuration} year | {license.activityType} |{" "}
                {license.licenseType.name}
              </div>
            </SidebarCard>
          );
        })}
      </>
    );
  }
  // Fallback or additional UI render
  return null;
};

export const SidebarTitle = ({
  title,
  children,
}: {
  title: string;
  children?: React.ReactNode;
}) => {
  const { fetchingMergeList } = useMergeApprovalContext();

  return (
    <div className="flex flex-col border-b border-neutral-200 p-2">
      <h2 className="flex w-full justify-between gap-1 text-lg font-bold">
        {title}
        {fetchingMergeList && (
          <Badge
            variant={"success"}
            className="flex items-center justify-center gap-1"
          >
            <TbFidgetSpinner
              className="animate-spin text-sm"
              aria-label="Refetching"
            />
          </Badge>
        )}
      </h2>
      {children}
    </div>
  );
};

export const SidebarContent = ({ children }: { children: React.ReactNode }) => {
  return <div className="flex flex-col gap-2">{children}</div>;
};

export const SidebarHeader = ({ children }: { children: React.ReactNode }) => {
  return (
    <div className="flex w-full flex-wrap items-center justify-between text-xs lg:text-sm">
      {children}
    </div>
  );
};

interface SidebarCardProps
  extends React.ButtonHTMLAttributes<HTMLButtonElement> {}

export const SidebarCard = ({
  children,
  className,
  ...props
}: SidebarCardProps) => {
  return (
    <button
      className={cn(
        "flex h-fit cursor-pointer flex-col gap-1  p-2 text-left transition-all",
        className,
      )}
      {...props}
    >
      {children}
    </button>
  );
};
