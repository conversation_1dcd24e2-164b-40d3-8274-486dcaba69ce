"use client";
import { cn } from "@/lib/utils";
import { usePathname, useRouter } from "next/navigation";

import { Button } from "@/components/ui/button";
import LicenseDeniedModal from "../LicenseDeniedModal";

import { useLicenseListContext, useLicenseNavigation } from "../hooks/useDogLicenseApproval";
import LicenseApprovalModal from "../LicenseApprovalModal";

interface ApprovalContainerProps extends React.HTMLAttributes<HTMLDivElement> {
  className?: string;
}

export const ApprovalContainer = ({
  children,
  className,
  ...props
}: ApprovalContainerProps) => {
  return (
    <div
      className={cn("flex h-full w-full gap-2 overflow-hidden p-2", className)}
      {...props}
    >
      {children}
    </div>
  );
};

export const ContentContainer = ({
  children,
}: {
  children: React.ReactNode;
}) => {
  return <div className="flex h-full w-full flex-col gap-2">{children}</div>;
};

export const ApprovalBar = ({ type }: { type: string }) => {
  const pathname = usePathname();
  const { licenseList } = useLicenseListContext();
  const { prevLicenseId, nextLicenseId } = useLicenseNavigation();




  const pathMap: {
    [key: string]: {
      next: { enabled: boolean; pathname: string; label: string };
      back: { enabled: boolean; pathname: string; label: string };
    };
  } = {
    license: {
      next: {
        enabled: !!nextLicenseId,
        pathname: `${pathname}?licenseId=${nextLicenseId}`,
        label: "Next License",
      },
      back: {
        enabled: !!prevLicenseId,
        pathname: `${pathname}?licenseId=${prevLicenseId}`,
        label: "Previous License",
      },
    },
    mergeAccounts: {
      next: {
        enabled: false,
        pathname: "",
        label: "Next Approval",
      },
      back: {
        enabled: false,
        pathname: "",
        label: "Previous Approval",
      },
    },
  };

  const enabled = pathMap[type].back.enabled;
  const path = pathMap[type].back.pathname;
  const { push } = useRouter();

  if(licenseList.length === 0) {
    return null
  }

  return (
    <div
      className={cn(
        "mt-2 flex w-full shrink-0 justify-between gap-2 rounded bg-white p-2 shadow",
      )}
    >
      <Button variant={"outline"} onClick={() => enabled && push(path)} disabled={!enabled}>
        Previous License
      </Button>
      <ApprovalButtons>
        <LicenseDeniedModal />
        <LicenseApprovalModal />
      </ApprovalButtons>
      <Button
        onClick={() =>
          nextLicenseId && push(`${pathname}?licenseId=${nextLicenseId}`)
        }
        variant={"outline"}
        disabled={!nextLicenseId}
      >
        Next License
      </Button>
    </div>
  );
};

export const Line = ({
  children,
  className,
}: {
  children: React.ReactNode;
  className?: string;
}) => {
  return (
    <div className={cn("flex items-center gap-2", className)}>{children}</div>
  );
};

export const LineGroup = ({
  children,
  className,
}: {
  children: React.ReactNode;
  className?: string;
}) => {
  return <div className={cn("flex flex-col gap-2", className)}>{children}</div>;
};

export const LineItem = ({
  label,
  children,
}: {
  label: string;
  children: React.ReactNode;
}) => {
  return (
    <div className="flex items-center gap-2">
      <div className="w-[120px] font-medium">{label}:</div>
      <div>{children}</div>
    </div>
  );
};

const ApprovalButtons = ({ children }: { children: React.ReactNode }) => {
  return <div className="flex items-center gap-6">{children}</div>;
};