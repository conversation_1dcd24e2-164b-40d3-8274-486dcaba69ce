import React, { useState } from "react";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { useApproveLicense } from "@/hooks/api/useLicense";
import { useForm } from "react-hook-form";
import { Button } from "../ui/button";
import { useAtom } from "jotai";
import { toastAtom } from "../ui/toast/toast";
import { useRouter } from "next/navigation";
import { License } from "@/types/LicenseType";
import { useQueryClient } from "@tanstack/react-query";
import {
  useLicenseListContext,
  useCurrentLicenseContext,
  useLicenseNavigation,
} from "./hooks/useDogLicenseApproval";
import { LicenseListContentProps } from "./type/approvalTypes";
import { downloadLicense, formatDate } from "../license/licenseHelper";
import { useGetDocument2 } from "@/hooks/api/useProfiles";
import { FiDownload, FiSlash } from "react-icons/fi";
import LoadingSpinner from "../ui/LoadingSpinner";

export default function LicenseApprovalModal() {
  const { handleSubmit, reset } = useForm();

  const { refetchLicenseList, setLicenseList } = useLicenseListContext();
  const { currentLicense, tabApprovals, canApprove, refetchCurrentLicense } =
    useCurrentLicenseContext();
  const { nextLicenseId, getNextLicenseAfterRemoval } = useLicenseNavigation();

  // Preserve the license data that was active when modal opened
  const [modalLicense, setModalLicense] = useState<License | null>(null);
  const license = modalLicense ?? (currentLicense?.license as License) ?? null;

  const [_, setToast] = useAtom(toastAtom);

  const approveMutate = useApproveLicense();

  const { replace } = useRouter();

  const queryClient = useQueryClient();

  // States
  const [isOpen, setIsOpen] = useState<boolean>(false);
  const [isApproved, setIsApproved] = useState<boolean>(false);
  const [userHasChosenAction, setUserHasChosenAction] =
    useState<boolean>(false);
  const [isInCompletionMode, setIsInCompletionMode] = useState<boolean>(false);

  const onSubmit = () => {
    setIsApproved(true);

    // Don't do optimistic update here - wait until modal is closed
    // This prevents navigation from switching while modal is open

    approveMutate.mutate(
      {
        entityId: license?.entityId,
      },
      {
        onError: (error: any) => {
          // If approval fails, just refetch
          refetchLicenseList();
          setIsApproved(false); // Reset approval state on error
          setToast({
            status: "error",
            label: "Error approve license",
            message: error.message,
          });
        },
        onSuccess: () => {
          setToast({
            status: "success",
            label: "License Approved",
            message: `${license.licenseNumber} has been approved successfully`,
          });

          setIsApproved(true);
          setIsInCompletionMode(true);
          // Don't update license list or navigate until user chooses an action
        },
      },
    );
  };

  const handleOpenChange = (newOpenState: boolean) => {
    setIsOpen(newOpenState);

    if (!newOpenState) {
      reset();

      // Only navigate if user has explicitly chosen an action (like clicking "Next License")
      // If user just closes modal with X, don't navigate automatically
      if (userHasChosenAction) {
        // User has already chosen their action, don't interfere with navigation
      } else {
        // Modal closed without explicit action - remove approved license and refetch
        if (isApproved) {
          setLicenseList((prev: LicenseListContentProps[]) =>
            prev.filter(
              (item: LicenseListContentProps) =>
                item.license?.entityId !== license?.entityId,
            ),
          );
        }
        refetchLicenseList();
        refetchCurrentLicense();
        queryClient.invalidateQueries();
      }
    }

    setIsApproved(false);
    setUserHasChosenAction(false);
    setIsInCompletionMode(false);
    setModalLicense(null); // Clear preserved license data when modal closes
  };

  const handleCloseNext = () => {
    setUserHasChosenAction(true);
    setIsInCompletionMode(false); // Exit completion mode
    setIsOpen(false);
    setIsApproved(false);
    setModalLicense(null); // Clear preserved license data

    // Calculate the next license before the current one is removed
    const targetLicenseId = getNextLicenseAfterRemoval(license?.entityId || "");

    // Now remove the approved license from the list since user is navigating away
    if (isApproved) {
      setLicenseList((prev: LicenseListContentProps[]) =>
        prev.filter(
          (item: LicenseListContentProps) =>
            item.license?.entityId !== license?.entityId,
        ),
      );
    }

    refetchCurrentLicense();
    queryClient.invalidateQueries();

    if (targetLicenseId) {
      replace(`/approval/license/dog?licenseId=${targetLicenseId}`);
    } else {
      replace(`/approval/license/dog`);
    }
  };

  const handleGoToAccount = () => {
    // Move
    setUserHasChosenAction(true);
    setIsInCompletionMode(false); // Exit completion mode
    setIsOpen(false);
    setIsApproved(false);
    setModalLicense(null); // Clear preserved license data

    // Remove the approved license from the list since user is navigating away
    if (isApproved) {
      setLicenseList((prev: LicenseListContentProps[]) =>
        prev.filter(
          (item: LicenseListContentProps) =>
            item.license?.entityId !== license?.entityId,
        ),
      );
    }

    refetchCurrentLicense();
    queryClient.invalidateQueries();

    replace(`/a/license/${license?.entityId}`);
  };

  const getDocument = useGetDocument2();

  const handleDownload = () => {
    // Don't close modal or refetch - user may want to navigate after download
    const licenseId =
      license?.licenseForm && license?.licenseForm !== ""
        ? license?.licenseForm
        : null;

    if (licenseId) {
      downloadLicense(licenseId, true);
    } else {
      getDocument.mutate(
        {
          licenseId: license.entityId,
        },
        {
          onSuccess: (data) => {
            console.log(data);
            const url = data.url.replace(
              "/document-service/download?documentUUID=",
              "",
            );
            downloadLicense(url, true);
            // Don't refetch here - keep modal open for user to choose navigation
          },
          onError: (error) => {
            console.log(error);
          },
        },
      );
    }
  };

  const handleClose = () => {
    setUserHasChosenAction(true);
    setIsInCompletionMode(false); // Exit completion mode
    setIsOpen(false);
    setIsApproved(false);
    setModalLicense(null); // Clear preserved license data

    // Remove the approved license from the list since user is closing modal
    if (isApproved) {
      setLicenseList((prev: LicenseListContentProps[]) =>
        prev.filter(
          (item: LicenseListContentProps) =>
            item.license?.entityId !== license?.entityId,
        ),
      );
    }

    refetchCurrentLicense();
    queryClient.invalidateQueries();

    // Stay on the approval page without navigating
  };

  console.log(license);

  return (
    <Dialog open={isOpen} onOpenChange={handleOpenChange}>
      <DialogTrigger
        disabled={
          !currentLicense?.license?.entityId ||
          tabApprovals.length > 0 ||
          !canApprove
        }
        onClick={() => {
          // Preserve the current license data when opening the modal
          setModalLicense(currentLicense?.license as License);
          setIsOpen(true);
        }}
      >
        <Button variant="success" disabled={!canApprove}>
          Approve License
        </Button>
      </DialogTrigger>
      <DialogContent className="mx-4 max-w-lg sm:mx-auto">
        <DialogHeader className="space-y-3 text-center">
          <DialogTitle className="text-2xl font-bold text-gray-900">
            {isApproved ? "License Approved! 🎉" : "Approve License"}
          </DialogTitle>
          <DialogDescription className="text-gray-600">
            {isApproved
              ? "The license has been successfully approved and is now active."
              : "Please review the license details before approving."}
          </DialogDescription>
        </DialogHeader>

        {isApproved ? (
          <div className="space-y-8 py-6">
            {/* Success State */}
            <div className="space-y-4 text-center">
              <div className="mx-auto flex h-16 w-16 items-center justify-center rounded-full bg-gradient-to-r from-green-400 to-emerald-500 shadow-lg">
                <svg
                  className="h-8 w-8 text-white"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={3}
                    d="M5 13l4 4L19 7"
                  />
                </svg>
              </div>
              <div>
                {/* <h3 className="text-lg font-semibold text-gray-900">License #{license?.licenseNumber}</h3> */}
                <p className="text-sm text-gray-500">
                  {nextLicenseId
                    ? "Successfully approved and activated"
                    : "Successfully approved and activated • Last license in queue"}
                </p>
              </div>
            </div>

            {/* Action Buttons */}
            <div className="space-y-4">
              {/* Primary Action - Download */}
              <Button
                className="flex h-12 w-full items-center justify-center gap-3 rounded-lg bg-gradient-to-r from-blue-600 to-blue-700 font-semibold text-white shadow-lg transition-all duration-200 hover:from-blue-700 hover:to-blue-800 hover:shadow-xl"
                onClick={handleDownload}
                disabled={getDocument.isLoading || getDocument.isError}
              >
                {getDocument.isError ? (
                  <>
                    <FiSlash className="h-5 w-5" />
                    Error Downloading License
                  </>
                ) : getDocument.isLoading ? (
                  <>
                    <LoadingSpinner className="h-5 w-5" />
                    Generating License...
                  </>
                ) : (
                  <>
                    <FiDownload className="h-5 w-5" />
                    Download License PDF
                  </>
                )}
              </Button>

              {/* Secondary Actions */}
              {nextLicenseId ? (
                // When there are more licenses - show View License and Next License
                <div className="grid grid-cols-2 gap-3">
                  <Button
                    variant="outline"
                    className="h-11 rounded-lg border-gray-300 font-medium transition-all duration-200 hover:border-gray-400 hover:bg-gray-50"
                    onClick={handleGoToAccount}
                  >
                    <span className="text-sm">View License</span>
                  </Button>
                  <Button
                    variant="outline"
                    className="h-11 rounded-lg border-blue-300 font-medium text-blue-700 transition-all duration-200 hover:border-blue-400 hover:bg-blue-50"
                    onClick={handleCloseNext}
                  >
                    <span className="text-sm">Next License</span>
                  </Button>
                </div>
              ) : (
                // When no more licenses - show View License and Close
                <div className="grid grid-cols-2 gap-3">
                  <Button
                    variant="outline"
                    className="h-11 rounded-lg border-gray-300 font-medium transition-all duration-200 hover:border-gray-400 hover:bg-gray-50"
                    onClick={handleGoToAccount}
                  >
                    <span className="text-sm">View License</span>
                  </Button>
                  <Button
                    variant="outline"
                    className="h-11 rounded-lg border-green-300 font-medium text-green-700 transition-all duration-200 hover:border-green-400 hover:bg-green-50"
                    onClick={handleClose}
                  >
                    <span className="text-sm">Close</span>
                  </Button>
                </div>
              )}
            </div>
          </div>
        ) : (
          <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
            {/* License Details Card */}
            <div className="rounded-xl border border-gray-200 bg-gradient-to-r from-gray-50 to-gray-100 p-6">
              <h3 className="mb-4 flex items-center gap-2 text-lg font-semibold text-gray-900">
                <svg
                  className="h-5 w-5 text-blue-600"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
                  />
                </svg>
                License Details
              </h3>
              <div className="space-y-3">
                <div className="flex items-center justify-between border-b border-gray-200 py-2 last:border-b-0">
                  <span className="text-sm font-medium text-gray-600">
                    Dog Name
                  </span>
                  <span className="rounded-full bg-white px-3 py-1 text-sm font-semibold text-gray-900">
                    {license?.dogs?.[0]?.dogName ?? "N/A"}
                  </span>
                </div>
                <div className="flex items-center justify-between border-b border-gray-200 py-2 last:border-b-0">
                  <span className="text-sm font-medium text-gray-600">
                    Dog Tag Number
                  </span>
                  <span className="rounded-full bg-white px-3 py-1 text-sm font-semibold text-gray-900">
                    {license?.dogs?.[0]?.tagNumber ?? "N/A"}
                  </span>
                </div>
              </div>
            </div>

            {/* Warning Notice */}
            <div className="rounded-lg border border-amber-200 bg-amber-50 p-4">
              <div className="flex items-start gap-3">
                <svg
                  className="mt-0.5 h-5 w-5 flex-shrink-0 text-amber-600"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 18.5c-.77.833.192 2.5 1.732 2.5z"
                  />
                </svg>
                <div>
                  <p className="text-sm font-medium text-amber-800">
                    Approval Confirmation
                  </p>
                  <p className="mt-1 text-sm text-amber-700">
                    This action will permanently approve the license and cannot
                    be undone.
                  </p>
                </div>
              </div>
            </div>

            {/* Action Buttons */}
            <div className="flex flex-col-reverse gap-3 pt-2 sm:flex-row">
              <Button
                type="button"
                variant="outline"
                className="h-11 flex-1 rounded-lg border-gray-300 font-medium transition-all duration-200 hover:border-gray-400 hover:bg-gray-50"
                onClick={() => handleOpenChange(false)}
              >
                Cancel
              </Button>
              <Button
                type="submit"
                className="h-11 flex-1 rounded-lg bg-gradient-to-r from-green-600 to-emerald-600 font-semibold text-white shadow-lg transition-all duration-200 hover:from-green-700 hover:to-emerald-700 hover:shadow-xl disabled:cursor-not-allowed disabled:opacity-50"
                disabled={approveMutate.isLoading}
              >
                {approveMutate.isLoading ? (
                  <div className="flex items-center gap-2">
                    <LoadingSpinner className="h-4 w-4" />
                    Approving...
                  </div>
                ) : (
                  <div className="flex items-center gap-2">
                    <svg
                      className="h-4 w-4"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M5 13l4 4L19 7"
                      />
                    </svg>
                    Approve License
                  </div>
                )}
              </Button>
            </div>
          </form>
        )}
      </DialogContent>
    </Dialog>
  );
}
