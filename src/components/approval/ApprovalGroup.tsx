import { cn } from "@/lib/utils";
import React, { forwardRef, useEffect, useRef, useState } from "react";
import { IconType } from "react-icons";
import { FiCheck, FiFlag, FiSave, FiSlash, FiX } from "react-icons/fi";
import { LuClipboardList } from "react-icons/lu";
import { Button } from "../ui/button";
import { useCurrentLicenseContext } from "./hooks/useDogLicenseApproval";
import LoadingSpinner from "../ui/LoadingSpinner";

export const approvalInputConfig = {
  input:
    "border rounded border-gray-300 px-1 focus:border-blue-500 focus:border-2 focus:outline-none mr-auto w-full min-h-[32px]",
  inputContainer: "flex flex-col gap-1 max-w-[200px] w-full",
};

interface ApprovalGroupProps {
  title: string;
  children: React.ReactNode;
  status: string;
}

const approvalGroupConfig: {
  [key: string]: {
    icon: IconType;
    iconStyle: string;
    cardStyle: string;
    text: string;
  };
} = {
  verify: {
    icon: LuClipboardList,
    iconStyle: "bg-orange-500 text-orange-50",
    cardStyle: "bg-orange-50 border-orange-500",
    text: "Verification required",
  },
  required: {
    icon: FiX,
    iconStyle: "bg-red-500 text-red-50",
    cardStyle: "border-red-500",
    text: "Requirement not met",
  },
  approved: {
    icon: FiCheck,
    iconStyle: "",
    cardStyle: "",
    text: "",
  },
};

export const ApprovalGroup = ({
  title,
  children,
  status = "approved",
}: ApprovalGroupProps) => {
  const Icon = approvalGroupConfig[status].icon;

  return (
    <div
      className={cn(
        "relative mt-4 max-w-lg",
        approvalGroupConfig[status].cardStyle,
      )}
    >
      {status !== "approved" && (
        <span className="absolute -top-3 left-4">
          <div
            className={cn(
              "z-10 flex items-center justify-center gap-2 rounded px-2 py-1 text-sm font-medium",
              approvalGroupConfig[status].iconStyle,
            )}
          >
            <Icon /> <span>{approvalGroupConfig[status].text}</span>
          </div>
        </span>
      )}
      <h2 className="mb-2 text-xl font-medium px-2 ">{title}</h2>
      <div className="flex flex-col gap-2">{children}</div>
    </div>
  );
};

interface ApprovalItemProps {
  closeEdit: () => void;
  children: React.ReactNode;
  className?: string;
  [key: string]: any;
}

const ApprovalItem = ({
  closeEdit,
  children,
  className,
  ...props
}: ApprovalItemProps) => {
  const ref = useRef<HTMLDivElement>(null);
  const [isFocused, setIsFocused] = useState<boolean>(false);

  useEffect(() => {
    const checkIfClickedOutside = (e: MouseEvent) => {
      if (isFocused && ref.current && !ref.current.contains(e.target as Node)) {
        setIsFocused(false);
        closeEdit();
      }
    };

    document.addEventListener("mousedown", checkIfClickedOutside);

    return () => {
      document.removeEventListener("mousedown", checkIfClickedOutside);
    };
  }, [isFocused, closeEdit]);

  const handleFocus = () => setIsFocused(true);
  const handleBlur = () => setIsFocused(false);

  return (
    <div
      ref={ref}
      className={cn(
        "relative flex w-full items-center gap-1 rounded p-2 hover:bg-blue-100 ",
        className,
      )}
      onFocus={handleFocus}
      onBlur={handleBlur}
      tabIndex={-1}
      {...props}
    >
      {children}
    </div>
  );
};

export default ApprovalItem;

interface ApprovalInputContainerProps extends React.HTMLProps<HTMLDivElement> {
  children: React.ReactNode;
  className?: string;
}

export const ApprovalInputContainer = ({
  children,
  className,
  ...props
}: ApprovalInputContainerProps) => {
  return (
    <div
      className={cn("flex w-full max-w-[200px] flex-col gap-1", className)}
      {...props}
    >
      {children}
    </div>
  );
};

interface ApprovalLabelProps extends React.HTMLProps<HTMLLabelElement> {
  children: React.ReactNode;
  className?: string;
}

interface ApprovalError extends React.HTMLProps<HTMLDivElement> {
  children: React.ReactNode;
  className?: string;
}

export const ApprovalError = ({
  children,
  className,
  ...props
}: ApprovalError) => {
  return (
    <div className="text-sm text-red-600" {...props}>
      {children}
    </div>
  );
};

export const ApprovalLabel = ({ children, className }: ApprovalLabelProps) => {
  return (
    <label
      className={cn(
        "flex min-h-[32px] w-full items-center gap-2 text-sm",
        className,
      )}
    >
      {children}
    </label>
  );
};

interface ButtonProps {
  onClick: (e: React.MouseEvent<HTMLButtonElement>) => void;
  loading?: boolean;
  disabled?: boolean;
}

export const DenyButton = ({ id }: { id: string }) => {
  const { denyList, setDenyList } = useCurrentLicenseContext();
  const flagged = denyList.includes(id);

  const handleDeny = () => {
    if (denyList.includes(id)) {
      setDenyList(denyList.filter((item) => item !== id));
    } else {
      setDenyList([...denyList, id]);
    }
  };

  return (
    <Button
      type="button"
      variant={flagged ? "destructive" : "outline"}
      size={"icon"}
      onClick={handleDeny}
      className="ml-auto h-8 w-8 shrink-0"
    >
      <FiFlag />
    </Button>
  );
};

interface SaveButtonProps extends ButtonProps {
  disabled?: boolean;
  loading?: boolean;
}

export const SaveButton = ({ onClick, ...prop }: SaveButtonProps) => {
  return prop.loading ? (
    <Button
      type="submit"
      variant={prop.disabled ? "disabled" : "success"}
      size={"icon"}
      className="h-8 w-8 shrink-0"
      disabled
    >
      <LoadingSpinner className="size-4" />
    </Button>
  ) : (
    <Button
      type="submit"
      variant={prop.disabled ? "disabled" : "success"}
      size={"icon"}
      onClick={onClick}
      className="h-8 w-8 shrink-0 text-lg"
      disabled={prop.disabled}
      {...prop}
    >
      <FiSave />
    </Button>
  );
};

export const CancelButton = ({ onClick, ...prop }: ButtonProps) => {
  return (
    <Button
      type="button"
      variant={prop.disabled ? "disabled" : "default"}
      size={"icon"}
      onClick={onClick}
      className="h-8 w-8 shrink-0 text-lg"
      disabled={prop.disabled}
      {...prop}
    >
      <FiSlash />
    </Button>
  );
};
