import React, { createContext, useContext, useEffect, useState, useMemo, useCallback, ReactNode } from "react";
import { ApprovalTabProps } from "../type/approvalTypes";

interface ApprovalContextType {
  denyList: string[];
  setDenyList: (denyList: string[]) => void;
  tabs: ApprovalTabProps[];
  setTabs: (tabs: ApprovalTabProps[]) => void;
  tabApprovals: string[];
  setTabApprovals: (tabApprovals: string[]) => void;
  currentTab: ApprovalTabProps | null;
  setCurrentTab: (tab: ApprovalTabProps | null) => void;
  currentTabName: string;
  setCurrentTabName: (name: string) => void;
  entity: any; // Consider defining a specific type for entity
  setEntity: (entity: any) => void; // Same as above, consider a specific type
  updateFieldValue: (groupName: string, fieldName: string, newValue: string) => void;
}

const initialContext: ApprovalContextType = {
  denyList: [],
  setDenyList: () => {},
  tabs: [],
  setTabs: () => {},
  tabApprovals: [],
  setTabApprovals: () => {},
  currentTab: null,
  setCurrentTab: () => {},
  currentTabName: "",
  setCurrentTabName: () => {},
  entity: null,
  setEntity: () => {},
  updateFieldValue: () => {},
};

const ApprovalContext = createContext<ApprovalContextType>(initialContext);

type ApprovalProviderProps = {
  children: ReactNode;
  realTabs: ApprovalTabProps[];
  realEntity: any;
};

export function ApprovalProvider({ children, realTabs, realEntity }: ApprovalProviderProps) {
  const [tabs, setTabs] = useState<ApprovalTabProps[]>(realTabs);
  const [denyList, setDenyList] = useState<string[]>([]);
  const [currentTab, setCurrentTab] = useState<ApprovalTabProps | null>(realTabs[0] || null);
  const [currentTabName, setCurrentTabName] = useState<string>(realTabs[0]?.name || "");
  const [entity, setEntity] = useState<any>(realEntity);

  const tabApprovals = useMemo(() => checkTabsFields(realTabs), [realTabs]);

  const updateFieldValue = useCallback((groupName: string, fieldName: string, newValue: string) => {
    setTabs(currentTabs => currentTabs.map(tab => {
      if (tab.name !== currentTabName) return tab;
      return {
        ...tab,
        entities: tab.entities.map(entity => ({
          ...entity,
          groups: entity.groups.map(group => {
            if (group.name !== groupName) return group;
            return {
              ...group,
              fields: group.fields.map(field => {
                if (field.name !== fieldName) return field;
                return { ...field, value: newValue };
              }),
            };
          }),
        })),
      };
    }));
  }, [currentTabName]);

  const providerValue = useMemo(() => ({
    denyList,
    setDenyList,
    tabs,
    setTabs,
    tabApprovals,
    setTabApprovals: () => {},
    currentTabName,
    setCurrentTabName,
    entity,
    setEntity,
    updateFieldValue,
    currentTab,
    setCurrentTab,
  }), [denyList, tabs, tabApprovals, currentTabName, entity, currentTab, updateFieldValue]);

  return <ApprovalContext.Provider value={providerValue}>{children}</ApprovalContext.Provider>;
}

export function useApprovalList() {
  return useContext(ApprovalContext);
}


const checkTabsFields = (realTabs: ApprovalTabProps[]): string[] => {
  return realTabs.reduce((approvalList:string[], tab:ApprovalTabProps) => {
    let isTabInvalid = tab.entities.some(entity =>
      entity.groups.some(group =>
        group.fields.some(field =>
          field.required && (!field.value || field.value === "")
        )
      )
    );

    if (isTabInvalid) {
      approvalList.push(tab.name);
    }

    return approvalList;
  }, []);
};