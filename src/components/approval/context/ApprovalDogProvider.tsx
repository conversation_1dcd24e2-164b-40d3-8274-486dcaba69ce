import { createContext, useContext, useState } from "react";
import { Dog } from "@/types/DogType";

export function ApprovalDogProvider({
  children,
  dog,
}: {
  children: React.ReactNode;
  dog: <PERSON>;
}) {
  console.log(dog)
  const [basicInfoApproved, setBasicInfoApproved] = useState<boolean>(false);
  const [behaviorApproved, setBehaviorApproved] = useState<boolean>(false);
  const [insuranceApproved, setInsuranceApproved] = useState<boolean>(false);
  const [vaccineApproved, setVaccineApproved] = useState<boolean>(false);
  const [physicalCharacteristicsApproved, setPhysicalCharacteristicsApproved] = useState<boolean>(false);
  return (
    <ApprovalContext.Provider value={{ 
      basicInfoApproved, setBasicInfoApproved,
      behaviorApproved, setBehaviorApproved,
      insuranceApproved, setInsuranceApproved,
      vaccineApproved, setVaccineApproved,
      physicalCharacteristicsApproved, setPhysicalCharacteristicsApproved,
    }}>
      {children}
    </ApprovalContext.Provider>
  );
}

const ApprovalContext = createContext({
  basicInfoApproved: false,
  setBasicInfoApproved: (basicInfoApproved: boolean) => {},
  behaviorApproved: false,
  setBehaviorApproved: (behaviorApproved: boolean) => {},
  insuranceApproved: false,
  setInsuranceApproved: (insuranceApproved: boolean) => {},
  vaccineApproved: false,
  setVaccineApproved: (vaccineApproved: boolean) => {},
  physicalCharacteristicsApproved: false,
  setPhysicalCharacteristicsApproved: (physicalCharacteristicsApproved: boolean) => {},
});


export function useDogApproval() {
  return useContext(ApprovalContext);
}