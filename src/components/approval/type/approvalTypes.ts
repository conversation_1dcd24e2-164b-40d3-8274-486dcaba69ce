import { Address } from "@/types/AddressType";
import { Document } from "@/types/DocumentType";
import { Dog } from "@/types/DogType";
import { License } from "@/types/LicenseType";
import { User } from "@/types/UserType";

// Config
interface Field {
  name: string;
  label: string;
  type: "text" | "date" | "select";
  required: boolean;
  disabled?: boolean;
  options?: string | { label: string; value: string }[];
}

interface Group {
  name: string;
  label: string;
  fields: Field[];
}

export interface ApprovalConfigProps {
  name: string;
  label: string;
  default?: boolean;
  groups: Group[];
}

// Tabs

export interface ApprovalTabProps {
  name: string;
  label: string;
  default?: boolean;
  entities: ApprovalEntityProps[];
  approved?: boolean;
}

export interface ApprovalEntityProps {
  entityId: string;
  documents: Document[];
  groups: ApprovalGroupProps[];
}

export interface ApprovalGroupProps {
  name: string;
  label: string;
  fields: ApprovalFieldsPropsValue[];
}

export interface ApprovalFieldsPropsValue extends ApprovalFieldsProps {
  value: string;
  options?: { label: string; value: string; default?: boolean }[];
}

interface ApprovalFieldsContactProps {
  id: string;
  type: string;
}

export interface ApprovalFieldsProps {
  name: string;
  label: string;
  type: "select" | "text" | "date" | "checkbox" | "radio";
  options?: string | { label: string; value: string; default?: boolean }[];
  required?: boolean;
  disabled?: boolean;
  errorText?: string;
  key?: string;
  contactData?: ApprovalFieldsContactProps;
}

export interface ApprovalConfig {
  name: string;
  label: string;
  default?: boolean;
  groups?: ApprovalGroupProps[];
}

export interface LicenseListProps {
  content: LicenseListContentProps[];
  pageable: LicenseListPageableProps;
  totalPages: number;
  totalElements: number;
  last: boolean;
  first: boolean;
  size: number;
  number: number;
  sort: LicenseListSortProps[];
  numberOfElements: number;
  empty: boolean;
}

export interface LicenseListContentProps {
  license?: License;
  address?: Address[];
  individual?: User[];
  dog?: Dog[];
}

export interface LicenseListSortProps {
  direction: string;
  property: string;
  ignoreCase: boolean;
  nullHandling: string;
  ascending: boolean;
  descending: boolean;
}

export interface LicenseListPageableProps {
  pageNumber: number;
  pageSize: number;
  sort: LicenseListSortProps[];
  offset: number;
  paged: boolean;
  unpaged: boolean;
}

export interface RequestedIndividual {
  requestedIndividualId: string;
  items: RequestedItem[];
}

export interface RequestedItem {
  entityId: string;
  requestedIndividualId: string;
  existingIndividualIds: ExistingIndividualId[];
  tagNumber: string;
  licenseNumber: string;
  status: string;
  createdDate: string;
}

export interface ExistingIndividualId {
  existingUserId: string;
  probability: number;
  matchType: string;
}
