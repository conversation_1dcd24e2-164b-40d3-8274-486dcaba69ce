[{"name": "license", "label": "License", "default": true, "groups": [{"name": "basicLicenseApproval", "label": "License Information", "fields": [{"name": "licenseNumber", "label": "License Number", "type": "text", "required": true, "disabled": true}, {"name": "validFromDate", "label": "Start Date", "type": "date", "required": true, "disabled": true}, {"name": "validToDate", "label": "End Date", "type": "date", "required": true, "disabled": true}, {"name": "licenseDuration", "label": "License Duration", "type": "text", "required": true, "disabled": true}]}]}, {"name": "individual", "label": "Individual", "groups": [{"name": "basicInfoApproved", "label": "Basic Information", "fields": [{"name": "firstName", "label": "First Name", "type": "text", "required": true}, {"name": "middleName", "label": "Middle Name", "type": "text", "required": false}, {"name": "lastName", "label": "Last Name", "type": "text", "required": true}, {"name": "dateOfBirth", "label": "Date of Birth", "type": "date", "required": true}]}]}, {"name": "dog", "label": "Dogs", "groups": [{"name": "basicInfoApproved", "label": "Basic Information", "fields": [{"name": "<PERSON><PERSON><PERSON>", "label": "Dog Name", "type": "text", "required": true}, {"name": "tagNumber", "label": "Tag Number", "type": "text", "required": true}, {"name": "dogBirthDate", "label": "Date of Birth", "type": "date", "required": true}, {"name": "dogSpayedOrNeutered", "label": "Spayed or Neutered", "type": "select", "options": "dogSpayedOrNeuteredOptions", "required": true}, {"name": "dogSex", "label": "Sex", "type": "select", "options": "dogSexOptions", "required": true}, {"name": "microchipNumber", "label": "Microchip Number", "type": "text", "required": false}]}, {"name": "physicalCharacteristicsApproved", "label": "Physical Characteristics", "fields": [{"name": "dogBreed", "label": "Breed", "type": "select", "options": "dogBreedOptions", "required": true}, {"name": "dogPrimaryColor", "label": "Primary Color", "type": "select", "options": "dogColorOptions", "required": true}, {"name": "dogSecondaryColor", "label": "Secondary Color", "type": "select", "options": "dogColorOptions", "required": false}]}, {"name": "vaccineApproved", "label": "Vaccination Information", "fields": [{"name": "veterinaryName", "label": "Veterinary Name", "type": "text", "required": true}, {"name": "vaccineDatesExempt", "label": "Vaccine Dates Exempt", "type": "select", "options": [{"label": "Yes", "value": "true"}, {"label": "No", "value": "false"}], "required": false}, {"name": "vaccineName", "label": "Vaccine Name", "type": "select", "options": "vaccineNameOptions", "required": true}, {"name": "vaccineProducer", "label": "Vaccine Producer", "type": "text", "required": false}, {"name": "vaccineBrand", "label": "Vaccine Brand", "type": "text", "required": false}, {"name": "vaccineAdministeredDate", "label": "Vaccine Administered Date", "type": "date", "required": false}, {"name": "vaccineDueDate", "label": "Vaccine Expiration Date", "type": "date", "required": false}]}, {"name": "insuranceApproved", "label": "Insurance Information", "fields": [{"name": "insuranceCompany", "label": "Insurance Company", "type": "text", "required": false}, {"name": "insurancePolicyNumber", "label": "Policy Number", "type": "text", "required": false}, {"name": "insuranceStartDate", "label": "Start Date", "type": "date", "required": false}, {"name": "insuranceExpirationDate", "label": "Expiration Date", "type": "date", "required": false}]}]}]