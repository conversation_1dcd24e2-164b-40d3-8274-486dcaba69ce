"use client";
import React, { createContext, useContext, useState, useEffect } from "react";
import { ApprovalTabProps } from "../type/approvalTypes";
import { useSearchParams } from "next/navigation";
import {
  useGetMergeAccount,
  useGetMergeAccountsList,
} from "@/hooks/api/useApprovals";

export function useMergeApproval() {
  const {
    data: mergeList,
    isLoading: loadingMergeList,
    isFetching: fetchingMergeList,
    isError: errorMergeList,
    refetch: refetchMergeList,
  } = useGetMergeAccountsList();

  const requestingResidentId = useSearchParams().get("requestedUserId");
  const existsInMergeList = mergeList?.content.find(
    (item: any) => item?.entityId === requestingResidentId,
  );

  const {
    data: currentResidentData,
    isLoading: loadingCurrentResident,
    isFetching: fetchingCurrentResident,
    isError: errorCurrentResident,
    refetch: refetchCurrentResident,
  } = useGetMergeAccount(existsInMergeList?.entityId ?? "");

  const [tabs, setTabs] = useState<ApprovalTabProps[]>([]);
  const [denyList, setDenyList] = useState<string[]>([]);
  const [currentTab, setCurrentTab] = useState<ApprovalTabProps | null>(null);
  const [currentTabName, setCurrentTabName] = useState<string>("");
  const [entity, setEntity] = useState<any>(null);
  const [nextEntityId, setNextEntityId] = useState<string | null>(null);
  const [prevEntityId, setPrevEntityId] = useState<string | null>(null);
  const [tabApprovals, setTabApprovals] = useState<string[]>([]);
  const [mergeApprovalList, setMergeApprovalList] = useState<any>([]);
  const [currentMergeRequest, setCurrentMergeRequest] = useState<any>(null);

  useEffect(() => {
    if (mergeList) {
      setMergeApprovalList(mergeList.content);
    }
  }, [mergeList]);

  useEffect(() => {
    if (currentResidentData) {
      setCurrentMergeRequest(currentResidentData);
    }
  }, [currentResidentData]);

  useEffect(() => {
    refetchCurrentResident();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [requestingResidentId]);

  useEffect(() => {
    if (!loadingMergeList && !errorMergeList && mergeApprovalList?.content) {
      const currentIndex = mergeApprovalList.requests.findIndex(
        (item: any) => item.license.entityId === requestingResidentId,
      );

      const nextIndex =
        currentIndex + 1 < mergeApprovalList.requests.length
          ? currentIndex + 1
          : null;
      const prevIndex = currentIndex - 1 >= 0 ? currentIndex - 1 : null;

      const nextEntityId =
        nextIndex !== null
          ? mergeApprovalList.requests[nextIndex].license.entityId
          : null;
      const prevEntityId =
        prevIndex !== null
          ? mergeApprovalList.requests[prevIndex].license.entityId
          : null;

      if (nextEntityId !== null) {
        setNextEntityId(nextEntityId);
      } else {
        setNextEntityId(null);
      }

      if (prevEntityId !== null) {
        setPrevEntityId(prevEntityId);
      } else {
        setPrevEntityId(null);
      }
    }
  }, [
    mergeApprovalList,
    errorMergeList,
    loadingMergeList,
    requestingResidentId,
  ]);

  return {
    denyList,
    setDenyList,
    tabs,
    setTabs,
    currentTabName,
    setCurrentTabName,
    entity,
    setEntity,
    currentTab,
    setCurrentTab,
    tabApprovals,
    setTabApprovals,
    refetchMergeList,
    mergeApprovalList,
    setMergeApprovalList,
    errorMergeList,
    loadingMergeList,
    nextEntityId,
    setNextEntityId,
    prevEntityId,
    setPrevEntityId,
    currentMergeRequest,
    loadingCurrentResident,
    errorCurrentResident,
    refetchCurrentResident,
    fetchingCurrentResident,
    fetchingMergeList,
  };
}

interface ApprovalContextType {
  denyList: string[];
  setDenyList: (denyList: string[]) => void;
  tabs: ApprovalTabProps[];
  setTabs: (tabs: ApprovalTabProps[]) => void;
  tabApprovals: string[];
  setTabApprovals: (tabApprovals: string[]) => void;
  currentTab: ApprovalTabProps | null;
  setCurrentTab: (tab: ApprovalTabProps | null) => void;
  currentTabName: string;
  setCurrentTabName: (name: string) => void;
  entity: any;
  setEntity: (entity: any) => void;
  nextEntityId: string | null;
  setNextEntityId: (id: string | null) => void;
  prevEntityId: string | null;
  setPrevEntityId: (id: string | null) => void;
  mergeApprovalList: any;
  setMergeApprovalList: (list: any) => void;
  loadingMergeList: boolean;
  errorMergeList: boolean;
  refetchMergeList: () => void;
  currentMergeRequest: any;
  loadingCurrentResident: boolean;
  errorCurrentResident: boolean;
  refetchCurrentResident: () => void;
  fetchingCurrentResident: boolean;
  fetchingMergeList: boolean;
}

const initialContext: ApprovalContextType = {
  denyList: [],
  setDenyList: () => {},
  tabs: [],
  setTabs: () => {},
  tabApprovals: [],
  setTabApprovals: () => {},
  currentTab: null,
  setCurrentTab: () => {},
  currentTabName: "",
  setCurrentTabName: () => {},  
  entity: null,
  setEntity: () => {},
  nextEntityId: null,
  setNextEntityId: () => {},
  prevEntityId: null,
  setPrevEntityId: () => {},
  refetchMergeList: () => {},
  mergeApprovalList: null,
  setMergeApprovalList: () => {},
  loadingMergeList: true,
  errorMergeList: false,
  currentMergeRequest: null,
  loadingCurrentResident: true,
  errorCurrentResident: false,
  refetchCurrentResident: () => {},
  fetchingCurrentResident: false,
  fetchingMergeList: false,
};

const ApprovalContext = createContext<ApprovalContextType>(initialContext);

export const ApprovalProvider = ({
  children,
}: {
  children: React.ReactNode;
}) => {
  const approval = useMergeApproval();

  return (
    <ApprovalContext.Provider value={approval}>
      {children}
    </ApprovalContext.Provider>
  );
};

// Hook to access the context
export const useMergeApprovalContext = () => useContext(ApprovalContext);
