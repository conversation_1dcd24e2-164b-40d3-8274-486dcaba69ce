import React, { createContext, useContext, useState, useEffect } from "react";
import {
  LicenseListContentProps,
} from "../type/approvalTypes";
import { useSearchParams, useRouter, usePathname } from "next/navigation";
import { useGetLicense3 } from "@/hooks/api/useProfiles";
import { useGetLicenses } from "@/hooks/api/useLicense";
import { Dog } from "@/types/DogType";

// SIDEBAR LICENSE LIST PROVIDER
export function useLicenseList() {
  const router = useRouter();
  const pathname = usePathname();
  const searchParams = useSearchParams();
  
  // Get page and size from URL, with defaults
  const currentPage = parseInt(searchParams.get("page") || "0");
  const pageSize = parseInt(searchParams.get("size") || "8");

  const {
    data: licenseListData,
    isLoading: loadingLicenseList,
    isError: errorLicenseList,
    refetch: refetchLicenseList,
    isFetching: fetchingLicenseList,
  } = useGetLicenses({
    size: pageSize.toString(),
    page: currentPage.toString(),
    status: "Pending Approval",
    approved: "false",
    sort: "applicationDate,asc",
  });

  // List of Licenses needing approval
  const [licenseList, setLicenseList] = useState<any[]>([]);

  // Pagination helper values
  const totalPages = licenseListData?.totalPages || 0;
  const totalElements = licenseListData?.totalElements || 0;
  const hasNextPage = currentPage < totalPages - 1;
  const hasPrevPage = currentPage > 0;

  // Pagination functions that update URL
  const updatePageInUrl = (newPage: number) => {
    const params = new URLSearchParams(searchParams.toString());
    params.set("page", newPage.toString());
    params.set("size", pageSize.toString());
    const currentLicenseId = searchParams.get("licenseId");
    if (currentLicenseId) {
      params.set("licenseId", currentLicenseId);
    }
    router.push(`${pathname}?${params.toString()}`);
  };

  const goToNextPage = () => {
    if (hasNextPage) {
      updatePageInUrl(currentPage + 1);
    }
  };

  const goToPrevPage = () => {
    if (hasPrevPage) {
      updatePageInUrl(currentPage - 1);
    }
  };

  const setCurrentPage = (page: number) => {
    updatePageInUrl(page);
  };

  // Sidebar List
  useEffect(() => {
    if (licenseListData) {
      // API already returns data sorted by createdDate,asc - no need to sort again
      setLicenseList(licenseListData.content);
    }
  }, [licenseListData]);

  return {
    licenseList,
    setLicenseList,
    refetchLicenseList,
    loadingLicenseList,
    fetchingLicenseList,
    errorLicenseList,
    // Pagination
    currentPage,
    pageSize,
    setCurrentPage,
    totalPages,
    totalElements,
    hasNextPage,
    hasPrevPage,
    goToNextPage,
    goToPrevPage,
  };
}

// CURRENT LICENSE PROVIDER
export function useCurrentLicense() {
  const searchParams = useSearchParams();
  const currentLicenseId = searchParams.get("licenseId");
  const tabs = ["dog", "owner"];

  // Current License Data
  const {
    data: currentLicenseData,
    isLoading: loadingCurrentLicense,
    isError: errorCurrentLicense,
    refetch: refetchCurrentLicense,
    isFetching: isRefetchingCurrentLicense,
  } = useGetLicense3(currentLicenseId);

  // Tab Control
  const [currentTab, setCurrentTab] = useState<string>("dog");

  // List of denied reasons
  const [denyList, setDenyList] = useState<string[]>([]);
  const [tabApprovals, setTabApprovals] = useState<string[]>([]);

  // License Entity
  const [currentLicense, setCurrentLicense] = useState<any>(null);

  // Can approve
  const [canApprove, setCanApprove] = useState<boolean>(false);

  // Current License Data
  useEffect(() => {
    if (currentLicenseData) {
      checkDogTags();
      setDenyList([]);
      setCurrentLicense(currentLicenseData);
    }
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [currentLicenseData]);

  useEffect(() => {
    setCurrentTab(tabs[0]);
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [currentLicenseId]);

  const checkDogTags = () => {
    const findAllDogTags = currentLicenseData.dog.filter((dog: Dog) => {
      console.log(dog.tagNumber);
      return !dog.tagNumber || dog.tagNumber.length === 0;
    });
    console.log(findAllDogTags);
    if (findAllDogTags.length > 0) {
      setCanApprove(false);
    } else {
      setCanApprove(true);
    }
  };

  return {
    denyList,
    setDenyList,
    tabs,
    currentLicense,
    setCurrentLicense,
    currentTab,
    setCurrentTab,
    tabApprovals,
    setTabApprovals,
    loadingCurrentLicense,
    errorCurrentLicense,
    refetchCurrentLicense,
    isRefetchingCurrentLicense,
    canApprove,
    currentLicenseId,
  };
}

// NAVIGATION HELPER (combines both contexts)
export function useLicenseNavigation() {
  const searchParams = useSearchParams();
  const currentLicenseId = searchParams.get("licenseId");
  
  // Get license list from context to calculate navigation
  const { licenseList, loadingLicenseList, errorLicenseList } = useLicenseListContext();
  
  // License Navigation
  const [nextLicenseId, setNextLicenseId] = useState<string | null>(null);
  const [prevLicenseId, setPrevLicenseId] = useState<string | null>(null);

  useEffect(() => {
    if (!loadingLicenseList && !errorLicenseList && licenseList?.length) {
      const currentIndex = licenseList.findIndex(
        (item) => item?.license?.entityId === currentLicenseId,
      );

      // Handle edge cases
      if (currentIndex === -1) {
        // Current license not found in list - it may have been approved/denied
        // Navigate to the first available license (or the license that now occupies the same position)
        if (licenseList.length > 0) {
          // If there are still licenses, use the first one as next
          setNextLicenseId(licenseList[0]?.license?.entityId || null);
          setPrevLicenseId(licenseList.length > 1 ? licenseList[licenseList.length - 1]?.license?.entityId || null : null);
        } else {
          setNextLicenseId(null);
          setPrevLicenseId(null);
        }
        return;
      }

      if (licenseList.length <= 1) {
        // Only one license left
        setNextLicenseId(null);
        setPrevLicenseId(null);
        return;
      }

      // Standard navigation: get next/prev sequentially through the list
      const nextIndex = currentIndex + 1 < licenseList.length ? currentIndex + 1 : null;
      const prevIndex = currentIndex - 1 >= 0 ? currentIndex - 1 : null;

      setNextLicenseId(nextIndex !== null ? licenseList[nextIndex]?.license?.entityId || null : null);
      setPrevLicenseId(prevIndex !== null ? licenseList[prevIndex]?.license?.entityId || null : null);
    } else {
      // No licenses available
      setNextLicenseId(null);
      setPrevLicenseId(null);
    }
  }, [
    licenseList,
    loadingLicenseList,
    errorLicenseList,
    currentLicenseId,
  ]);

  // Helper function to calculate next license ID after current license is removed
  const getNextLicenseAfterRemoval = (licenseIdToRemove: string) => {
    const currentIndex = licenseList.findIndex(
      (item) => item?.license?.entityId === licenseIdToRemove
    );
    
    if (currentIndex === -1 || licenseList.length <= 1) {
      return null; // No next license available
    }
    
    // Maintain position in the queue - go to the license that will be at the same index
    // after the current one is removed
    if (currentIndex + 1 < licenseList.length) {
      // There's a license after the current one - it will shift to current position
      return licenseList[currentIndex + 1]?.license?.entityId || null;
    } else {
      // We're removing the last license - go to the new last license (previous one)
      return licenseList[currentIndex - 1]?.license?.entityId || null;
    }
  };

  return {
    nextLicenseId,
    prevLicenseId,
    getNextLicenseAfterRemoval,
  };
}

// LICENSE LIST CONTEXT AND PROVIDER
interface LicenseListContextType {
  licenseList: LicenseListContentProps[];
  setLicenseList: (
    value: React.SetStateAction<LicenseListContentProps[]>,
  ) => void;
  refetchLicenseList: () => void;
  loadingLicenseList: boolean;
  fetchingLicenseList: boolean;
  errorLicenseList: boolean;
  // Pagination
  currentPage: number;
  pageSize: number;
  setCurrentPage: (page: number) => void;
  totalPages: number;
  totalElements: number;
  hasNextPage: boolean;
  hasPrevPage: boolean;
  goToNextPage: () => void;
  goToPrevPage: () => void;
}

const initialLicenseListContext: LicenseListContextType = {
  licenseList: [],
  setLicenseList: () => {},
  refetchLicenseList: () => {},
  loadingLicenseList: true,
  fetchingLicenseList: false,
  errorLicenseList: false,
  // Pagination
  currentPage: 0,
  pageSize: 8,
  setCurrentPage: () => {},
  totalPages: 0,
  totalElements: 0,
  hasNextPage: false,
  hasPrevPage: false,
  goToNextPage: () => {},
  goToPrevPage: () => {},
};

const LicenseListContext = createContext<LicenseListContextType>(initialLicenseListContext);

export const LicenseListProvider = ({
  children,
}: {
  children: React.ReactNode;
}) => {
  const licenseList = useLicenseList();
  return (
    <LicenseListContext.Provider value={licenseList}>
      {children}
    </LicenseListContext.Provider>
  );
};

export const useLicenseListContext = () => useContext(LicenseListContext);

// CURRENT LICENSE CONTEXT AND PROVIDER
interface CurrentLicenseContextType {
  denyList: string[];
  setDenyList: (denyList: string[]) => void;
  tabApprovals: string[];
  setTabApprovals: (tabApprovals: string[]) => void;
  currentTab: string;
  setCurrentTab: (tab: string) => void;
  currentLicense: any;
  setCurrentLicense: (currentLicense: any) => void;
  loadingCurrentLicense: boolean;
  errorCurrentLicense: boolean;
  refetchCurrentLicense: () => void;
  isRefetchingCurrentLicense: boolean;
  canApprove: boolean;
  currentLicenseId: string | null;
  tabs: string[];
}

const initialCurrentLicenseContext: CurrentLicenseContextType = {
  denyList: [],
  setDenyList: () => {},
  tabApprovals: [],
  setTabApprovals: () => {},
  currentTab: "",
  setCurrentTab: () => {},
  currentLicense: null,
  setCurrentLicense: () => {},
  loadingCurrentLicense: true,
  errorCurrentLicense: false,
  refetchCurrentLicense: () => {},
  isRefetchingCurrentLicense: false,
  canApprove: false,
  currentLicenseId: null,
  tabs: [],
};

const CurrentLicenseContext = createContext<CurrentLicenseContextType>(initialCurrentLicenseContext);

export const CurrentLicenseProvider = ({
  children,
}: {
  children: React.ReactNode;
}) => {
  const currentLicense = useCurrentLicense();
  return (
    <CurrentLicenseContext.Provider value={currentLicense}>
      {children}
    </CurrentLicenseContext.Provider>
  );
};

export const useCurrentLicenseContext = () => useContext(CurrentLicenseContext);