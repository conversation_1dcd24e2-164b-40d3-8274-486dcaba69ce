import React, { useState } from "react";

import {
  <PERSON>alog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";

import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Textarea } from "../ui/textarea";
import { Label } from "../ui/label";
import { useRejectLicense } from "@/hooks/api/useLicense";
import { Controller, useForm } from "react-hook-form";
import { Button } from "../ui/button";
import { useAtom } from "jotai";
import { toastAtom } from "../ui/toast/toast";
import { usePara<PERSON>, useRouter, useSearchParams } from "next/navigation";
import { License } from "@/types/LicenseType";
import { FiChevronRight } from "react-icons/fi";
import {
  ApprovalFieldsPropsValue,
  LicenseListContentProps,
} from "./type/approvalTypes";
import { useQueryClient } from "@tanstack/react-query";
import {
  useLicenseListContext,
  useCurrentLicenseContext,
  useLicenseNavigation,
} from "./hooks/useDogLicenseApproval";
import { cn } from "@/lib/utils";
import LoadingSpinner from "../ui/LoadingSpinner";
import { Flag } from "lucide-react";

export default function LicenseDeniedModal() {
  const {
    control,
    handleSubmit,
    reset,
    formState: { errors },
  } = useForm({
    defaultValues: {
      reason: "",
      comment: "",
    },
  });
  const searchParams = useSearchParams();
  const nextItemId = searchParams.get("licenseId");
  const { setLicenseList, refetchLicenseList } = useLicenseListContext();
  const { currentLicense, denyList, refetchCurrentLicense } =
    useCurrentLicenseContext();
  const { nextLicenseId, getNextLicenseAfterRemoval } = useLicenseNavigation();

  // Preserve the license data that was active when modal opened
  const [modalLicense, setModalLicense] = useState<License | null>(null);
  const license = modalLicense ?? (currentLicense?.license as License);

  const [_, setToast] = useAtom(toastAtom);

  const denyMutate = useRejectLicense();
  const [isOpen, setIsOpen] = useState(false);
  const [isDenied, setIsDenied] = useState<boolean>(false);
  const [userHasChosenAction, setUserHasChosenAction] =
    useState<boolean>(false);
  const [isInCompletionMode, setIsInCompletionMode] = useState<boolean>(false);
  const { replace } = useRouter();

  const queryClient = useQueryClient();

  const onDeny = (data: any) => {
    console.log("Form data:", data);
    console.log("License entity ID:", license?.entityId);
    console.log("Deny list:", denyList);

    if (!license?.entityId) {
      setToast({
        status: "error",
        label: "Error denying license",
        message: "No license selected for denial",
      });
      return;
    }

    // Don't do optimistic update here - wait until modal is closed
    // This prevents navigation from switching while modal is open

    denyMutate.mutate(
      {
        entityId: license.entityId,
        body: { ...data, denyList },
      },
      {
        onError: (error: any) => {
          console.error("Denial error:", error);
          // If denial fails, just refetch
          refetchLicenseList();
          setIsDenied(false); // Reset denial state on error
          setToast({
            status: "error",
            label: "Error denying license",
            message: error.message || "Failed to deny license",
          });
        },
        onSuccess: () => {
          console.log("License denied successfully");
          setToast({
            status: "success",
            label: "License Denied",
            message: `License for ${(modalLicense ?? currentLicense)?.dog?.[0]?.dogName ?? "dog"} has been denied`,
          });
          

          setIsDenied(true);
          setIsInCompletionMode(true);
          // Don't update license list or navigate until user chooses an action
        },
      },
    );
  };

  const handleOpenChange = (newOpenState: boolean) => {
    setIsOpen(newOpenState);

    if (!newOpenState) {
      // Reset form fields first
      reset({
        reason: "",
        comment: "",
      });

      // Only navigate if user has explicitly chosen an action (like clicking "Next License")
      // If user just closes modal with X, don't navigate automatically
      if (userHasChosenAction) {
        // User has already chosen their action, don't interfere with navigation
      } else {
        // Modal closed without explicit action - remove denied license and refetch
        if (isDenied) {
          setLicenseList((prev: LicenseListContentProps[]) =>
            prev.filter(
              (item: LicenseListContentProps) =>
                item.license?.entityId !== license?.entityId,
            ),
          );
        }
        refetchLicenseList();
        refetchCurrentLicense();
        queryClient.invalidateQueries();
      }
    }

    setIsDenied(false);
    setUserHasChosenAction(false);
    setIsInCompletionMode(false);
    setModalLicense(null); // Clear preserved license data when modal closes
  };

  const handleNext = () => {
    setUserHasChosenAction(true);
    setIsInCompletionMode(false); // Exit completion mode
    setIsOpen(false);
    setIsDenied(false);
    setModalLicense(null); // Clear preserved license data

    // Calculate the next license before the current one is removed
    const targetLicenseId = getNextLicenseAfterRemoval(license?.entityId || "");

    // Now remove the denied license from the list since user is navigating away
    if (isDenied) {
      setLicenseList((prev: LicenseListContentProps[]) =>
        prev.filter(
          (item: LicenseListContentProps) =>
            item.license?.entityId !== license?.entityId,
        ),
      );
    }

    refetchCurrentLicense();
    queryClient.invalidateQueries();

    if (targetLicenseId) {
      replace(`/approval/license/dog?licenseId=${targetLicenseId}`);
    } else {
      replace(`/approval/license/dog`);
    }
  };

  const handleClose = () => {
    setUserHasChosenAction(true);
    setIsInCompletionMode(false); // Exit completion mode
    setIsOpen(false);
    setIsDenied(false);
    setModalLicense(null); // Clear preserved license data

    // Remove the denied license from the list since user is closing modal
    if (isDenied) {
      setLicenseList((prev: LicenseListContentProps[]) =>
        prev.filter(
          (item: LicenseListContentProps) =>
            item.license?.entityId !== license?.entityId,
        ),
      );
    }

    refetchCurrentLicense();
    queryClient.invalidateQueries();

    // Stay on the approval page without navigating
  };

  return (
    <Dialog open={isOpen} onOpenChange={handleOpenChange}>
      <DialogTrigger
        onClick={() => {
          // Preserve the current license data when opening the modal
          setModalLicense(currentLicense?.license as License);
          // Reset all modal state when opening
          setIsDenied(false);
          setUserHasChosenAction(false);
          setIsInCompletionMode(false);
          // Reset form fields when opening modal
          reset({
            reason: "",
            comment: "",
          });
          setIsOpen(true);
        }}
      >
        <Button variant="destructive">Deny License</Button>
      </DialogTrigger>
      <DialogContent className="mx-4 max-w-lg sm:mx-auto">
        <DialogHeader className="space-y-3 text-center">
          <DialogTitle className="flex items-center justify-center gap-2 text-2xl font-bold text-gray-900">
            {isDenied ? "License Denied" : "Deny License"}
          </DialogTitle>
          <DialogDescription className="text-center text-gray-600">
            {isDenied
              ? "The license has been denied and the applicant will be notified."
              : "Please provide a reason and comments for denying this license application."}
          </DialogDescription>
        </DialogHeader>

        {isDenied ? (
          <div className="space-y-8 py-6">
            {/* Success State */}
            <div className="space-y-4 text-center">
              <div className="mx-auto flex h-16 w-16 items-center justify-center rounded-full bg-gradient-to-r from-red-500 to-red-600 shadow-lg">
                <Flag className="h-8 w-8 text-white" />
              </div>
              <div>
                <p className="text-sm text-gray-500">
                  {nextLicenseId
                    ? "License denied and applicant notified"
                    : "License denied and applicant notified • Last license in queue"}
                </p>
              </div>
            </div>

            {/* Action Buttons */}
            <div className="space-y-4">
              {nextLicenseId ? (
                // When there are more licenses - show Next License and Close
                <div className="grid grid-cols-2 gap-3">
                  <Button
                    variant="outline"
                    className="h-11 rounded-lg border-red-300 font-medium text-red-700 transition-all duration-200 hover:border-red-400 hover:bg-red-50"
                    onClick={handleNext}
                  >
                    <span className="text-sm">Next License</span>
                  </Button>
                  <Button
                    variant="outline"
                    className="h-11 rounded-lg border-gray-300 font-medium transition-all duration-200 hover:border-gray-400 hover:bg-gray-50"
                    onClick={handleClose}
                  >
                    <span className="text-sm">Close</span>
                  </Button>
                </div>
              ) : (
                // When no more licenses - show Close button
                <Button
                  className="h-12 w-full rounded-lg bg-gradient-to-r from-gray-600 to-gray-700 font-semibold text-white shadow-lg transition-all duration-200 hover:from-gray-700 hover:to-gray-800 hover:shadow-xl"
                  onClick={handleClose}
                >
                  Close
                </Button>
              )}
            </div>
          </div>
        ) : (
          <form onSubmit={handleSubmit(onDeny)} className="space-y-6">
            {/* License Information Card */}
            <div className="rounded-xl border border-red-200 bg-gradient-to-r from-red-50 to-red-100 p-6">
              <h3 className="mb-4 flex items-center gap-2 text-lg font-semibold text-gray-900">
                <svg
                  className="h-5 w-5 text-red-600"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
                  />
                </svg>
                License to be Denied
              </h3>
              <div className="space-y-3">
                <div className="flex items-center justify-between border-b border-red-200 py-2 last:border-b-0">
                  <span className="text-sm font-medium text-gray-600">
                    Dog Name
                  </span>
                  <span className="rounded-full bg-white px-3 py-1 text-sm font-semibold text-gray-900">
                    {(modalLicense ?? currentLicense)?.dog?.[0]?.dogName ??
                      "N/A"}
                  </span>
                </div>
                <div className="flex items-center justify-between border-b border-red-200 py-2 last:border-b-0">
                  <span className="text-sm font-medium text-gray-600">
                    Tag Number
                  </span>
                  <span className="rounded-full bg-white px-3 py-1 text-sm font-semibold text-gray-900">
                    {(modalLicense ?? currentLicense)?.dog?.[0]?.tagNumber ??
                      "N/A"}
                  </span>
                </div>
              </div>
            </div>

            {/* Denial Reason */}
            <div className="space-y-2">
              <Label
                htmlFor="reason"
                className="text-sm font-semibold text-gray-900"
              >
                Denial Reason *
              </Label>
              <Controller
                name="reason"
                control={control}
                rules={{ required: "Reason for denial is required." }}
                render={({
                  field: { onChange, value },
                  fieldState: { error },
                }) => (
                  <div className="space-y-1">
                    <Select value={value} onValueChange={onChange}>
                      <SelectTrigger
                        aria-invalid={error ? "true" : "false"}
                        className={cn(
                          "h-11 border-gray-300 focus:border-red-400 focus:ring-red-400",
                          error && "border-red-400 ring-1 ring-red-400",
                        )}
                      >
                        <SelectValue placeholder="Select denial reason" />
                      </SelectTrigger>
                      <SelectContent className="z-[9999]">
                        <SelectItem value="incomplete">
                          Incomplete Application
                        </SelectItem>
                        <SelectItem value="requirements">
                          Failed to meet Requirements
                        </SelectItem>
                        <SelectItem value="documentation">
                          Missing Documentation
                        </SelectItem>
                        <SelectItem value="other">Other Reason</SelectItem>
                      </SelectContent>
                    </Select>
                    {error && (
                      <p className="flex items-center gap-1 text-sm text-red-600">
                        <svg
                          className="h-4 w-4"
                          fill="none"
                          stroke="currentColor"
                          viewBox="0 0 24 24"
                        >
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth={2}
                            d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
                          />
                        </svg>
                        {error.message}
                      </p>
                    )}
                  </div>
                )}
              />
            </div>

            {/* Comments */}
            <div className="space-y-2">
              <Label
                htmlFor="comment"
                className="text-sm font-semibold text-gray-900"
              >
                Comments *
              </Label>
              <Controller
                name="comment"
                control={control}
                rules={{ required: "Denial comment is required." }}
                render={({ field, fieldState: { error } }) => (
                  <div className="space-y-1">
                    <Textarea
                      {...field}
                      placeholder="Provide detailed comments about the denial..."
                      className={cn(
                        "min-h-[100px] resize-none border-gray-300 focus:border-red-400 focus:ring-red-400",
                        error && "border-red-400 ring-1 ring-red-400",
                      )}
                    />
                    {error && (
                      <p className="flex items-center gap-1 text-sm text-red-600">
                        <svg
                          className="h-4 w-4"
                          fill="none"
                          stroke="currentColor"
                          viewBox="0 0 24 24"
                        >
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth={2}
                            d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
                          />
                        </svg>
                        {error.message}
                      </p>
                    )}
                  </div>
                )}
              />
            </div>

            {/* Warning Notice */}
            <div className="rounded-lg border border-red-200 bg-red-50 p-4">
              <div className="flex items-start gap-3">
                <svg
                  className="mt-0.5 h-5 w-5 flex-shrink-0 text-red-600"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 18.5c-.77.833.192 2.5 1.732 2.5z"
                  />
                </svg>
                <div>
                  <p className="text-sm font-medium text-red-800">
                    Denial Confirmation
                  </p>
                  <p className="mt-1 text-sm text-red-700">
                    This action will permanently deny the license application.
                    The applicant will be notified.
                  </p>
                </div>
              </div>
            </div>

            {/* Action Buttons */}
            <div className="flex flex-col-reverse gap-3 pt-2 sm:flex-row">
              <Button
                type="button"
                variant="outline"
                className="h-11 flex-1 rounded-lg border-gray-300 font-medium transition-all duration-200 hover:border-gray-400 hover:bg-gray-50"
                onClick={() => handleOpenChange(false)}
              >
                Cancel
              </Button>
              <Button
                type="submit"
                className="h-11 flex-1 rounded-lg bg-gradient-to-r from-red-600 to-red-700 font-semibold text-white shadow-lg transition-all duration-200 hover:from-red-700 hover:to-red-800 hover:shadow-xl disabled:cursor-not-allowed disabled:opacity-50"
                disabled={denyMutate.isLoading}
              >
                {denyMutate.isLoading ? (
                  <div className="flex items-center gap-2">
                    <LoadingSpinner className="h-4 w-4" />
                    Denying...
                  </div>
                ) : (
                  <div className="flex items-center gap-2">
                    <svg
                      className="h-4 w-4"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M6 18L18 6M6 6l12 12"
                      />
                    </svg>
                    Deny License
                  </div>
                )}
              </Button>
            </div>
          </form>
        )}
      </DialogContent>
    </Dialog>
  );
}

// const Flags = () => {

//   const findFieldLabelWithParentGroupName = (fieldName: string) => {
//     for (const tab of tabs) {
//       for (const entity of tab.entities) {
//         for (const group of entity.groups) {
//           const field = group.fields.find(
//             (f: ApprovalFieldsPropsValue) => f.name === fieldName,
//           );
//           if (field) {
//             return (
//               <div className="mb-1 flex items-center text-neutral-800">
//                 <span>{tab.label}</span>
//                 <FiChevronRight className="translate-y-0.5 text-neutral-500" />
//                 <span>{group.label}</span>
//                 <FiChevronRight className="translate-y-0.5 text-neutral-500" />
//                 <span>{field.label}</span>
//               </div>
//             );
//           }
//         }
//       }
//     }
//     return null;
//   };

//   return (
//     <div>
//       <div className="font-semibold">Flags:</div>
//       <div className="flex flex-col gap-1">
//         {denyList.map((id) => {
//           const fieldLabelWithParentGroup =
//             findFieldLabelWithParentGroupName(id);
//           return (
//             <div key={id} className="flex items-center gap-2">
//               {fieldLabelWithParentGroup
//                 ? fieldLabelWithParentGroup
//                 : `Label not found for ${id}`}
//             </div>
//           );
//         })}
//       </div>
//     </div>
//   );
// };
