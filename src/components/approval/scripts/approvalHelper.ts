import { optionsSelector } from "@/fakeData/options/optionsSelector";
import {
  ApprovalFieldsPropsValue,
  ApprovalTabProps,
} from "../type/approvalTypes";
import { Dog } from "@/types/DogType";
import { Individual } from "@/types/IndividualType";
import { License } from "@/types/LicenseType";

interface Contact {
  type: string;
  group: string;
  value: string;
  id?: any;
}

interface Address {
  participantAddressType?: string;
  streetAddress: string;
  streetAddress2?: string;
  city: string;
  state: string;
  zip: string;
}

export const checkValidArray = (fieldsArray: ApprovalFieldsPropsValue[]) => {
  let valid = true;
  fieldsArray.forEach((field: ApprovalFieldsPropsValue) => {
    if (typeof field.value === "string") {
      if (field.required && !field.value.trim()) {
        valid = false;
      }
    } else if (
      field.required &&
      (field.value === null || field.value === false)
    ) {
      valid = false;
    }
  });
  return valid;
};

export const checkValidField = (field: ApprovalFieldsPropsValue): boolean => {
  if (field.required && !field.value?.trim()) {
    return false;
  }
  return true;
};

export const getOptions = (optionName: string) => {
  return optionsSelector[optionName];
};

export const getRealValues = (
  fields: ApprovalFieldsPropsValue[],
  entity: Dog | Individual | License,
): ApprovalFieldsPropsValue[] => {
  return fields.map((field): ApprovalFieldsPropsValue => {
    let options;
    if (typeof field.options === "string") {
      options = getOptions(field.options) as {
        label: string;
        value: string;
        default?: boolean;
      }[];
    } else {
      options = field.options;
    }

    const fieldValue: string = (entity as any)[field.name] ?? "";

    return {
      ...field,
      options,
      value: fieldValue,
    };
  });
};

export function convertConfigToRealTabs(tabsConfig: any[], data: any) {
  return tabsConfig.map((tab) => {
    const tabData: any = data[tab.name];
    let entities: any = [];

    if (tab.groups) {
      const normalizedData = Array.isArray(tabData) ? tabData : [tabData];

      entities = normalizedData
        .filter((entity) => entity !== undefined)
        .map((entity) => {
          // Handle standard fields
          let groups = tab.groups!.map((group: any) => ({
            name: group.name,
            label: group.label,
            fields: group.fields.map((field: any) => ({
              ...field,
              options: field.options
                ? typeof field.options === "string"
                  ? getOptions(field.options)
                  : field.options
                : undefined,
              value: entity[field.name] || "",
            })),
          }));

          // Special handling for contacts if present
          if (entity.contacts && Array.isArray(entity.contacts)) {
            const contactGroup = {
              name: "contacts",
              label: "Contacts",
              fields: entity.contacts.map(
                (contact: Contact, index: number) => ({
                  name: `contact`,
                  label: `${contact.type} (${contact.group})`,
                  type: "text",
                  value: contact.value,
                  required: false,
                  contactData: {
                    id: contact.id,
                    type: contact.type,
                  },
                }),
              ),
            };
            groups.push(contactGroup);
          }

          if (entity.addresses && Array.isArray(entity.addresses)) {
            entity.addresses.forEach((address: Address, index: number) => {
              const addressGroupName =
                address.participantAddressType || `address-${index}`;
              const addressGroupLabel = address.participantAddressType
                ? `${address.participantAddressType} Address`
                : `Address ${index + 1}`;

              const addressGroup: any = {
                name: addressGroupName,
                label: addressGroupLabel,
                fields: [
                  {
                    name: `streetAddress_${addressGroupName}`,
                    label: "Street Address",
                    type: "text",
                    value: address.streetAddress,
                    required: true,
                  },
                  {
                    name: `streetAddress2_${addressGroupName}`,
                    label: "Apt/Suite",
                    type: "text",
                    value: address.streetAddress2 || "",
                    required: false,
                  },
                  {
                    name: `city_${addressGroupName}`,
                    label: "City",
                    type: "text",
                    value: address.city,
                    required: true,
                  },
                  {
                    name: `state_${addressGroupName}`,
                    label: "State",
                    type: "select",
                    options: getOptions("stateOptions"),
                    value: address.state,
                    required: true,
                  },
                  {
                    name: `zip_${addressGroupName}`,
                    label: "Zip Code",
                    type: "text",
                    value: address.zip,
                    required: true,
                  },
                ],
              };

              groups.push(addressGroup);
            });
          }

          return {
            entityId: entity.entityId,
            documents: entity.documents,
            groups,
          };
        });
    }

    const { groups, ...tabProps } = tab;

    return { ...tabProps, entities };
  });
}

export function updateFieldValue(
  tabs: ApprovalTabProps[],
  currentTabName: string,
  groupName: string,
  fieldName: string,
  newValue: any,
) {
  return tabs.map((tab: any) => {
    if (tab.name !== currentTabName) return tab;
    return {
      ...tab,
      entities: tab.entities.map((entity: any) => ({
        ...entity,
        groups: entity.groups.map((group: any) => {
          if (group.name !== groupName) return group;
          return {
            ...group,
            fields: group.fields.map((field: any) => {
              if (field.name !== fieldName) return field;
              return { ...field, value: newValue };
            }),
          };
        }),
      })),
    };
  });
}
