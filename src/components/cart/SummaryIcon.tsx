import Image from 'next/image'
import React from 'react'

const SummaryIcon = ({
  item
}:{
  item: string
}) => {
  // Define icon and alt text using switch statement
  let icon = '';
  let alt = '';
  
  switch(item) {
    case 'license':
      icon = '/images/icons/license.png';
      alt = 'Dog License';
      break;
    case 'tag':
      icon = '/images/icons/dog.png';
      alt = 'Dog Tag';
      break;
    case 'entityFee':
      icon = '/images/icons/fee.jpg';
      alt = 'Fee';
      break;
    default:
      icon = '/images/icons/fee.jpg';
      alt = 'Item';
      break;
  }

  return (
    <div className="w-14 h-14 relative rounded overflow-hidden shrink-0">
      <Image
        src={icon}
        alt={alt}
        fill
        style={{
          objectFit: 'cover'
        }}
      />
    </div>
  )
}

export default SummaryIcon