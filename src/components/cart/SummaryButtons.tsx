import React from "react";
import { FiTrash2 } from "react-icons/fi";
import { useMyCart } from "@/hooks/useMyCart";


const SummaryButtons = ({
  cartItemId,
}: {
  cartItemId: number;
}) => {
  const { removeFromCart } = useMyCart()

  const baseStyle = `flex flex-col items-center p-2 rounded w-14 shrink-0 gap-1`;

  return (
    <div className="flex gap-2 justify-center items-center shrink-0">
      <button
        className={`${baseStyle} hover:text-red-50 hover:bg-red-500`}
        onClick={(e) => {
          e.preventDefault();
          e.stopPropagation();
          removeFromCart(cartItemId)
        }}
        type="button"
      >
        <FiTrash2 />
        <small className="text-xs">Remove</small>
      </button>
    </div>
  );
};

export default SummaryButtons;