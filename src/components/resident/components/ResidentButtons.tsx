import { use<PERSON><PERSON><PERSON>, useRouter } from "next/navigation";
import { But<PERSON> } from "@/components/ui/button";
import { cn } from "@/lib/utils";

export const AddLicenseButton = () => {
  
  const { push } = useRouter();

  return (
    <div className="flex flex-col gap-4">
      <Button
        onClick={() => push(`/licenses/licenses/new`)}
        className={cn(
          "w-fit cursor-pointer  rounded bg-blue-600 px-4  py-2 text-center text-white transition-all hover:bg-blue-500 hover:text-white",
        )}
      >
        Apply for a New License
      </Button>
    </div>
  );
};
