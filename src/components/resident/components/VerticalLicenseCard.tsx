import { cardStyle } from "@/components/license/cards/cardStyle";
import VLCSkeleton from "@/components/resident/components/VerticalLicenseCardSkeleton";
import { useGetProfile, useResidentGetProfile } from "@/hooks/api/useProfiles";
import { License } from "@/types/LicenseType";
import { useParams, useRouter } from "next/navigation";

// TODO: This needs to be fixed. it needs to display based on the license not the dog.  This is a profile card for the license.  Draft doesn't show because the dog wasn't created.
const VerticalLicenseCard = ({ license }: { license: License }) => {
  const router = useRouter();
  

  const entityId = license.entityId;

  const { data, isLoading, isError } = useResidentGetProfile(
    "license",
    entityId,
  );

  if (isLoading) return <VLCSkeleton />;
  if (isError) return <div>Failed to load license data</div>;

  console.log(data);

  const { dog, license: lic } = data;

  const licenseNumber = license.licenseNumber;
  const status = license.status ?? "";
  const statusStyle = cardStyle[status];

  console.log(lic);

  return <div className="flex gap-2"></div>;
};

export default VerticalLicenseCard;
