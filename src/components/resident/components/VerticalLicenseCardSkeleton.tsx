import { Skeleton } from "@/components/ui/skeleton";

const VLCSkeleton = () => {
  return (
    <>
      <div className="flex flex-row w-full gap-20 items-center justify-between my-5">
        <div className="flex flex-row gap-2 items-center  grow">
          <Skeleton className="w-16 h-16 rounded-full border-2 shadow-xl shadow-neutral-400" />
          <div className="flex flex-col gap-2">
            <Skeleton className="w-48 h-4" />
            <Skeleton className="w-32 h-3" />
          </div>
        </div>

        <div className="flex flex-row gap-2 items-start justify-center">
          <Skeleton className="w-20 h-6 rounded " />
        </div>

        <div className="flex flex-row gap-2 items-center justify-center">
          <Skeleton className="w-16 h-6" />
          <Skeleton className="w-32 h-6" />
        </div>

        <Skeleton className="w-32 h-10 rounded" />
      </div>
    </>
  );
};

export default VLCSkeleton;
