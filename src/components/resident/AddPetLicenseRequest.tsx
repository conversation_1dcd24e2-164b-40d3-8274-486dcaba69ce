import React, { useEffect, useState } from "react";
import { useForm } from "react-hook-form";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  useCreateMyMergeRequests,
  useGetMyMergeRequests,
} from "@/hooks/api/useResident";
import { useAtom } from "jotai";
import { toastAtom } from "@/components/ui/toast/toast";
import { formatDate } from "../license/licenseHelper";
import { cn } from "@/lib/utils";

// Define license types and their required input fields
const licenseTypes = {
  "Dog License": {
    inputs: ["tagNumber", "licenseNumber"],
    inputLabels: {
      tagNumber: "Dog Tag Number",
      licenseNumber: "License Number",
    },
  },
  // Add more license types here later
};

// Define an interface for the form fields
interface IFormInput {
  licenseType: string;
  tagNumber?: string;
  licenseNumber?: string;
}

export default function AddLicenseRequest({
  title = "Not seeing your existing license?",
  description = "Request to add your existing license to your account.",
  className,
}: {
  title?: string;
  description?: string;
  className?: string;
}) {
  const {
    register,
    handleSubmit,
    reset,
    watch,
    setValue,
    formState: { errors },
  } = useForm<IFormInput>({
    defaultValues: {
      licenseType: "Dog License"
    }
  });

  const [_, setToast] = useAtom(toastAtom);
  const [error, setError] = useState<string | null>(null);
  const { data, refetch } = useGetMyMergeRequests();
  const mergeMutation = useCreateMyMergeRequests();

  const licenseType = watch("licenseType");
  const tagNumber = watch("tagNumber");
  const licenseNumber = watch("licenseNumber");

  useEffect(() => {
    setError(null);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [tagNumber, licenseNumber, licenseType]);

  const onSubmit = (data: IFormInput) => {
    const { licenseType, ...requestData } = data;
    
    mergeMutation.mutate(requestData, {
      onSuccess: () => {
        setToast({
          status: "success",
          label: "Request Submitted",
          message: "Your request has been submitted and is pending approval.",
        });
        reset({ licenseType: data.licenseType });
        refetch();
      },
      onError: (error: any) => {
        setError(
          error?.response?.data?.message ??
            "An error occurred while submitting your request.",
        );
        setToast({
          status: "error",
          label: "Request Failed",
          message:
            error?.response?.data?.message ??
            "An error occurred while submitting your request.",
        });
      },
    });
  };

  const handleLicenseTypeChange = (value: string) => {
    setValue("licenseType", value);
  };

  const currentLicenseInputs = licenseTypes[licenseType as keyof typeof licenseTypes]?.inputs || [];
  const currentInputLabels = licenseTypes[licenseType as keyof typeof licenseTypes]?.inputLabels || {};

  return (
    <div
      className={cn(className)}
      id="add-license-request"
    >
      <div className="text-2xl font-medium">{title}</div>
      <p className="text-sm text-neutral-600">{description}</p>

      {data?.contents?.length > 0 && (
        <div className="mt-4 rounded border border-blue-500 bg-blue-50 p-1">
          <div className="text-sm font-medium">Pending Requests: </div>
          {data?.contents?.map((request: any) => {
            return <DisplayRequests request={request} key={request.entityId} />;
          })}
        </div>
      )}

      <form onSubmit={handleSubmit(onSubmit)} className="mt-10">
        <div className="mb-6">
          <label 
            htmlFor="licenseType" 
            className="mb-2 block text-sm font-medium text-neutral-800"
          >
            License
          </label>
          <Select 
            defaultValue="Dog License" 
            onValueChange={handleLicenseTypeChange}
          >
            <SelectTrigger className="w-full max-w-[240px]">
              <SelectValue placeholder="Select license type" />
            </SelectTrigger>
            <SelectContent>
              {Object.keys(licenseTypes).map((type) => (
                <SelectItem key={type} value={type}>
                  {type}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        {currentLicenseInputs.includes("tagNumber") && (
          <>
            <label
              htmlFor="tagNumber"
              className="mt-4 text-sm font-medium text-neutral-800"
            >
              {currentInputLabels.tagNumber}
            </label>
            <Input
              id="tagNumber"
              {...register("tagNumber")}
              className="w-full max-w-[180px]"
              aria-label={`Input ${currentInputLabels.tagNumber}`}
            />
          </>
        )}

        {currentLicenseInputs.includes("tagNumber") && currentLicenseInputs.includes("licenseNumber") && (
          <div className="my-4 italic text-neutral-600">or</div>
        )}

        {currentLicenseInputs.includes("licenseNumber") && (
          <>
            <label
              htmlFor="licenseNumber"
              className="mt-4 text-sm font-medium text-neutral-800"
            >
              {currentInputLabels.licenseNumber}
            </label>
            <Input
              id="licenseNumber"
              {...register("licenseNumber")}
              className="mb-4 w-full max-w-[180px]"
              aria-label={`Input ${currentInputLabels.licenseNumber}`}
            />
          </>
        )}

        <div className="mb-6 mt-6">
          <p className="mb-1 text-red-600">{error}</p>
          {data?.contents?.length >= 3 && (
            <div className="mb-4 mt-10 rounded border border-red-500 bg-red-50 p-1 text-red-900">
              <div className="text-sm font-bold">
                Max Number of Requests Reached
              </div>
              <p className="text-sm">
                You have reached the maximum number of requests. Don&apos;t
                worry, we will automatically include any additional licenses you
                have created in the past once approved.
              </p>
            </div>
          )}
          <Button
            variant="primary"
            type="submit"
            onSubmit={handleSubmit(onSubmit)}
            disabled={data?.contents?.length >= 3 || mergeMutation.isLoading}
            aria-label="Submit request"
          >
            Submit Request
          </Button>
        </div>
      </form>

      <small>
        *All requests will be reviewed by the clerk&apos;s office before being
        added to your account, please allow 3-5 business days for processing. If
        you do not know your current license information,
        please contact the clerk&apos;s office.
      </small>
    </div>
  );
}

const DisplayRequests = ({
  request,
}: {
  request: {
    entityId: string;
    tagNumber: string;
    licenseNumber: string;
    status: string;
    createdDate: string;
  };
}) => {
  let type = "";
  if (request.tagNumber.length > 0) {
    type = "Tag Number";
  } else if (request.licenseNumber.length > 0) {
    type = "License Number";
  }

  return (
    <p className="text-sm text-neutral-700">
      {type} {request.tagNumber || request.licenseNumber} on{" "}
      {formatDate(request.createdDate)}
    </p>
  );
};