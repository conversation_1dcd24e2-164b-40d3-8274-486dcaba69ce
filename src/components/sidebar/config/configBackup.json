[{"id": "dashboard", "href": "/dashboard", "icon": "<PERSON><PERSON><PERSON>", "label": "Dashboard", "parentId": null, "permissions": ["super-admin"]}, {"id": "home", "href": "/home", "icon": "FiHome", "label": "Home", "parentId": null, "permissions": ["resident"]}, {"id": "search", "href": "/entity/search", "icon": "FiSearch", "label": "Records Search", "parentId": null, "permissions": ["super-admin"]}, {"id": "licensesAdmin", "href": null, "icon": "TbLicense", "label": "Licenses", "groups": [{"id": "dogLicense", "label": "Dog", "links": [{"id": "dogLicenseApprovals", "href": "/approval/license/dog", "icon": "BiCheckSquare", "label": "Approvals", "parentId": "license", "permissions": ["super-admin"]}, {"id": "dogLicenseReport", "href": "/reports/dogFormsAndReports", "icon": "TbReportAnalytics", "label": "Forms & Reports", "parentId": "license", "permissions": ["super-admin"]}]}], "parentId": null, "permissions": ["super-admin"]}, {"id": "officeActivities", "href": null, "icon": "HiOutlineOfficeBuilding", "label": "Office Activities", "groups": [{"id": "residentSupport", "label": "Resident Support", "links": [{"id": "mergeAccounts", "href": "/approval/accounts/merge", "icon": "MdCallMerge", "label": "<PERSON><PERSON> Accounts", "parentId": "activities", "permissions": ["super-admin"]}]}, {"id": "clerkOperations", "label": "Clerk Operations", "links": [{"id": "<PERSON><PERSON><PERSON><PERSON>", "href": "/reports/clerkFormsAndReports", "icon": "TbReportAnalytics", "label": "Clerk Reports", "parentId": "activities", "permissions": ["super-admin"]}]}], "parentId": null, "permissions": ["super-admin"]}, {"id": "licenses", "href": "/licenses", "icon": "BsFiles", "label": "Licenses", "alerts": ["licenseExpired"], "parentId": null, "permissions": ["resident"]}, {"id": "customerService", "href": null, "icon": "BiSupport", "label": "Customer Service", "groups": [{"id": "support", "label": "Support", "links": [{"id": "contactUs", "href": "/customerService/contactUs", "icon": "BiSupport", "label": "Contact Us", "parentId": "customerService", "permissions": ["resident"]}, {"id": "faqs", "href": "/customerService/faqs", "icon": "FiHelpCircle", "label": "FAQs", "parentId": "customerService", "ariaLabel": "Frequently Asked Questions", "permissions": ["resident"]}]}], "parentId": null, "permissions": ["resident"]}, {"id": "store", "href": null, "icon": "BiStore", "label": "Store", "groups": [{"id": "storeCategories", "label": "Categories", "links": [{"id": "replacements", "href": "/store/replacements", "icon": "BiStore", "label": "Replacements", "parentId": "store", "permissions": ["super-admin", "resident"]}]}], "parentId": null, "permissions": ["super-admin"]}]