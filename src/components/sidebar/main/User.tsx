"use client";
import { useEffect, useState } from "react";
import { FiLogOut, FiShield } from "react-icons/fi";
import Image from "next/image";
import Link from "next/link";
import Avatar from "./Avatar";
import { atom, useAtom } from "jotai";
import { useMyProfile } from "@/hooks/providers/useMyProfile";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { cn } from "@/lib/utils";
import { useKeycloak } from "@/hooks/useKeycloak";
import CitySelection from "./CitySelection";

export const userAtom = atom<UserInfo | null>(null);

interface UserInfo {
  sub: string;
  email_verified: boolean;
  name: string;
  preferred_username: string;
  given_name: string;
  family_name: string;
  email: string;
  group: string[];
  avatar: string;
  redmine_api_key?: string;
  active_tenant: string;
}

const User = () => {
  const [isOpen, setIsOpen] = useState<boolean>(false);
  const [user, setUser] = useAtom(userAtom);
  const { hasPermissions } = useMyProfile();
  const { getUserProfile, logout } = useKeycloak();

  useEffect(() => {
    const printUserProfile = async () => {
      try {
        const userInfo = await getUserProfile();
        setUser(userInfo! as UserInfo);
      } catch (error) {
        console.error("Failed to fetch user profile", error);
        setUser(null);
      }
    };

    printUserProfile();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  if (!user) return null;


  const entityId = user?.sub;
  const isMobile = window.innerWidth < 768;
  const requiredPermissions = ["resident"];

  return (
    <Popover>
      <PopoverTrigger
        className="flex items-center gap-3 text-xl"
        onClick={() => {
          setIsOpen(!isOpen);
        }}
      >
        <div className="relative flex items-center">
          {user?.avatar ? (
            <Image
              src={user?.avatar || "/images/avatar.png"}
              width={24}
              height={24}
              alt="Profile Image"
              className="relative -translate-y-0.5 cursor-pointer rounded-full"
              style={{ pointerEvents: "none" }}
            />
          ) : (
            <Avatar name={user?.name || "User"} size=" w-7 h-7" />
          )}
        </div>
      </PopoverTrigger>
      <PopoverContent
        className="mt-4 h-fit w-full md:w-96"
        align={isMobile ? "center" : "end"}
      >
        <div className="flex w-full flex-col gap-2 px-4 pt-3">
          <div className="mb-6 flex gap-2">
            {user?.avatar ? (
              <Image
                src={user?.avatar || "/images/avatar.png"}
                width={24}
                height={24}
                alt="Profile Image"
                className="relative -translate-y-0.5 cursor-pointer rounded-full"
                style={{ pointerEvents: "none" }}
              />
            ) : (
              <Avatar name={user?.name || "User"} size=" w-12 h-12" />
            )}
            <div>
              <h3 className="text-xl font-bold">{user?.name}</h3>
              <button className="flex items-center gap-2 text-sm font-semibold text-blue-800">
                {user?.email}
              </button>
            </div>
          </div>
          {/* <hr /> */}
          <CitySelection />
        </div>
        

        {hasPermissions(requiredPermissions) && (
          <Link
            className={cn(
              "px-4 text-blue-600 transition-all hover:text-blue-700",
            )}
            href={`/profile/individual/${entityId}?tab=profile`}
          >
            My Profile
          </Link>
        )}

        <div className="w-full px-4 pb-3">
          <div className="flex w-full shrink-0 gap-2 ">
            
          </div>
        </div>
        <hr />
        <ul className="flex w-full flex-col gap-2 py-2">
          {[
            {
              icon: FiShield,
              label: "Admin Panel",
              link: `/admin`,
              key: "admin",
              isVisible: false,
            },
            // {
            //   icon: FiSettings,
            //   label: "Settings",
            //   link: `/settings`,
            //   key: "settings",
            //   isVisible: true,
            // },
            {
              icon: FiLogOut,
              label: "Logout",
              button: () => logout(),
              key: "logout",
              isVisible: true,
            },
          ].map(
            (item) =>
              item.isVisible && (
                <li key={item.key} className="w-full px-2">
                  {item?.link && (
                    <Link href={item.link}>
                      <a className="flex items-center gap-4 rounded px-2 py-2 text-sm font-semibold text-slate-700 hover:bg-neutral-100">
                        <item.icon className="text-lg" />
                        <span>{item.label}</span>
                      </a>
                    </Link>
                  )}
                  {item.button && (
                    <button
                      type="button"
                      onClick={item.button}
                      className="flex w-full items-center gap-4 rounded px-2 py-2 text-sm font-semibold text-slate-700 hover:bg-neutral-100"
                    >
                      <item.icon className="text-lg" />
                      <span>{item.label}</span>
                    </button>
                  )}
                </li>
              ),
          )}
        </ul>
      </PopoverContent>
    </Popover>
  );
};

export default User;
