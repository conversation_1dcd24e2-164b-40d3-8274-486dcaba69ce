import React from "react";
import { useParams } from "next/navigation";
import { useSidebar } from "../hooks/useSidebar";
import { LinkItem, SidebarVersion } from "../types/sidebarTypes";

import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/Tooltip";
import Link from "next/link";

const MainLink = ({
  link,
  version,
}: {
  link: LinkItem;
  version: SidebarVersion;
}) => {
  
  const { activePage } = useSidebar("individual");

  const path = link?.href ?? link?.groups?.[0]?.links?.[0]?.href ?? "";
  const href = path || "/dashboard?error=No path found";

  const Icon = link.icon;

  return (
    <TooltipProvider>
      <Tooltip>
        <TooltipTrigger asChild>
          <Link
            href={href}
            aria-label={link.label}
            className={`
              transitional-all relative cursor-pointer rounded-lg text-2xl
            `}
          >
            <span className="flex items-center p-2">
              <Icon aria-label={link.label} />
              <span className="sr-only">{link.label}</span>
            </span>
          </Link>
        </TooltipTrigger>
        <TooltipContent side="right" align="center">
          {link.label}
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  );
};

export default MainLink;
