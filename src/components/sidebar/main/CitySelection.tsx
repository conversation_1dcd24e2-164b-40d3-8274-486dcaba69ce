"use client";
import { toast<PERSON>tom } from "@/components/ui/toast/toast";
import {
  TenantType,
  useGetActiveTenant,
  useUpdateActiveTenantClear,
} from "@/hooks/api/useRealm";
import { useAtom } from "jotai";
import { useEffect, useState } from "react";
import { ArrowRight } from "lucide-react";
import Image from "next/image";

const CitySelection = () => {
  const { data: activeTenantData } = useGetActiveTenant();
  const [active, setActive] = useState<TenantType | null>(null);

  useEffect(() => {
    if (activeTenantData) {
      setActive(activeTenantData);
    }
  }, [activeTenantData]);

  if (!active) return null;

  console.log(active);

  return (
    <div className="mb-6 flex w-full max-w-sm flex-col gap-2 rounded-lg">
      <Title title="Current City" />
      <CityDisplay city={active} />
      <CitySelectionButton />
    </div>
  );
};

export default CitySelection;

const Title = ({ title }: { title: string }) => {
  return <h2 className="text-base font-bold text-neutral-700">{title}</h2>;
};

const CityDisplay = ({ city }: { city: TenantType }) => {
  console.log(city);
  return (
    <div className="flex w-full items-center justify-center gap-2 rounded-lg border border-gray-300 bg-gray-50 p-4 text-left text-lg font-medium text-gray-800 shadow-sm hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-blue-500">
      {city.logo && (
        <Image src={city.logo} alt={city.displayName} width={48} height={48} />
      )}
      <span>{city.displayName}</span>
    </div>
  );
};

const CitySelectionButton = () => {
  const clearActiveTenant = useUpdateActiveTenantClear();
  const [_, setToast] = useAtom(toastAtom);

  return (
    <button
      className="flex w-full items-center justify-between rounded-lg bg-blue-600 px-4 py-2 text-sm font-medium text-white transition-all duration-200 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
      onClick={() => {
        clearActiveTenant.mutate(undefined, {
          onSuccess: () => {
            window.location.reload();
          },
          onError: (error: any) => {
            console.error("Failed to update active tenant", error);
            setToast({
              label: "Error",
              message: "Failed to update active tenant",
              status: "error",
            });
          },
        });
      }}
    >
      <span>Select a Different City</span>
      <ArrowRight className="h-4 w-4" />
    </button>
  );
};
