import React from "react";
import Image from "next/image";
import Services from "@/components/services/Services";
import MainLink from "./MainLink";
import { useSidebar } from "../hooks/useSidebar";
import Link from "next/link";
import { SidebarVersion } from "../types/sidebarTypes";
import { FiMenu } from "react-icons/fi";
import { useParams } from "next/navigation";
import { useMyProfile } from "@/hooks/providers/useMyProfile";

const LogoTable: {
  [key: string]: {
    logo: string;
    alt: string;
  };
} = {
  schenectady: {
    logo: "/logos/schenectadySeal.png",
    alt: "Schenectady County Seal",
  },
  default: {
    logo: "/logos/ClerkXpress.svg",
    alt: "ClerkXpress Logo",
  },
};

const MainLayout = ({
  version,
  isExtended,
  setIsExtended,
}: {
  version: SidebarVersion;
  isExtended: boolean;
  setIsExtended: (extended: boolean) => void;
}) => {
  const { realm }: { realm: string } = useParams();
  const { links } = useSidebar("individual");

  const { hasPermissions } = useMyProfile();
  const permitted = hasPermissions(["super-admin"]);

  return (
    <div
      className={`relative flex w-16 flex-col items-center justify-between gap-6 py-3 shadow-xl lg:bg-transparent`}
    >
      <div className="transitional-all flex flex-col items-center  gap-2">
        <Link href={`/dashboard`}>
          <Image
            src={LogoTable[(realm as string) ?? "default"].logo}
            width={40}
            height={40}
            alt={LogoTable[(realm as string) ?? "default"].alt}
            className="mb-5"
          />
        </Link>
        <button
          className={`mb-5 mt-1 text-2xl`}
          onClick={() => setIsExtended(!isExtended)}
          aria-label="Toggle Sidebar"
        >
          <FiMenu />
          <span className="sr-only">Toggle Sidebar</span>
        </button>

        <small className="text-xs">Menu</small>

        {links &&
          links.map((link: any) => {
            if (link.groups) {
              link.groups.map((group: any) => (
                <MainLink
                  key={group.id}
                  link={group.links[0]}
                  version={version}
                />
              ));
            }

            return <MainLink key={link.id} link={link} version={version} />;
          })}
      </div>

      <div className="transitional-all flex flex-col items-center gap-2 py-2">
        {permitted && <Services extended={isExtended} />}
      </div>
    </div>
  );
};

export default MainLayout;
