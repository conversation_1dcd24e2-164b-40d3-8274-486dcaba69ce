import { usePathname } from 'next/navigation';
import { useGetJSONStorage } from '@/hooks/api/useAdmin';
import { isError } from '@tanstack/react-query';

const useSidebar = (version: string) => {
  const pathname = usePathname();
  const { data, isLoading, isFetching } = useGetJSONStorage("sidebar", version);
  const activePage = data && Array.isArray(data) && data?.find((link:{
    href: string;
    groups: { links: { href: string; }[] }[];
  }) =>
    link.href === pathname ||
    (link.groups && link.groups.some(group =>
      group?.links?.some(innerLink => innerLink.href === pathname)
    ))
  );

  const links = data?.links!;

  const getChildren = (parentId: string | null) => {
    if (!parentId) return [];
  
    const parentLink = data?.links.find((link:{
      id: string;
      groups: { id: string; }[];
    }) => link.id === parentId);
    return parentLink?.groups ?? [];
  };
  
  const getPrimaryPage = (parentId: string | null) => {
    return parentId 
      ? data?.links.find((link:{
        id: string;
      }) => link.id === parentId) 
      : activePage;
  };
  
  return { 
    data,
    isFetching,
    isLoading,
    isError,
    activePage, 
    links, 
    getChildren, 
    getPrimaryPage 
  };
};

export { useSidebar };