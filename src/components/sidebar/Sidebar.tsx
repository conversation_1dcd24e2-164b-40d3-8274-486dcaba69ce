"use client";
import { useEffect, useState } from "react";
// import MainLayout from "./main/MainLayout";
// import SecondaryLayout from "@/components/sidebar/secondary/SecondaryLayout";
// import { SidebarVersion } from "./types/sidebarTypes";
import CombinedLayout from "./combined/CombinedLayout";
// import Loading from "@/components/loading/Loading";
// import { sidebarToggleAtom } from "./atom/atoms";
// import { useAtom } from "jotai";
// import { useParams } from "next/navigation";

const Sidebar = () => {
  // Setup
  // const { activePage, getChildren, getPrimaryPage, links } = useSidebar(
  //   version,
  //   realm as string,
  // );

  // States
  const [isExtended, setIsExtended] = useState(true);
  // const [loading, setLoading] = useState(true);
  // const [isMobile] = useAtom(sidebarToggleAtom);

  useEffect(() => {
    const isExtended = localStorage.getItem("isExtended");
    if (isExtended) setIsExtended(JSON.parse(isExtended));
    // setLoading(false);
  }, []);

  const handleSidebarExtended = (extended: boolean) => {
    localStorage.setItem("isExtended", JSON.stringify(extended));
    setIsExtended(extended);
  };

  // const primary = activePage ? getPrimaryPage(activePage.parentId) : null;
  // const children = primary ? getChildren(primary.id) : null;

  return (
    <CombinedLayout
      isExtended={isExtended}
      setIsExtended={handleSidebarExtended}
    />
  );


  // return (
  //   <div className="relative hidden lg:flex">
  //     <MainLayout
  //       version={version}
  //       isExtended={isExtended}
  //       setIsExtended={handleSidebarExtended}
  //     />
  //     {children && primary && children?.length > 0 && (
  //       <SecondaryLayout
  //         secondaryLinks={children}
  //         label={primary?.label ?? activePage?.label}
  //         version={version}
  //       />
  //     )}
  //   </div>
  // );
};

export default Sidebar;
