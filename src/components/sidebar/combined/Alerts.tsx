import { useMyProfile } from "@/hooks/providers/useMyProfile";
import React from "react";

export default function Alerts({
  alerts,
}: {
  alerts: string[] | null | undefined;
}) {
  const { profile } = useMyProfile();

  if (!alerts || !profile) return null;

  console.log(alerts);

  const alert = hasAlert(alerts, profile);

  if (!alert) return null;

  return (
    <div
      className="
  relative z-10 ml-auto flex h-4 min-w-4 items-center justify-center rounded-full bg-red-300 text-[10px] font-medium text-red-950
  before:absolute before:inset-0 before:-z-10 before:animate-ping before:rounded-full before:bg-red-300 before:content-['']
"
    >
      {alert}
    </div>
  );
}

const hasAlert = (alerts: string[], profile: any) => {
  // Has Balance

  switch (true) {
    case alerts.includes("licenseExpired"):
      const expiredLicenses =
        profile["license"]?.filter(
          (license: any) => license.status === "Expired",
        ) ?? 0;
      return expiredLicenses.length > 0 ? expiredLicenses.length : null;
    case alerts.includes("hasBalance"):
      const outstandingTotalBalance = profile["individual"]?.fees?.totals
        ?.totalOutstandingAmount
        ? "$"
        : null;
      return outstandingTotalBalance;
    default:
      return null;
  }
};
