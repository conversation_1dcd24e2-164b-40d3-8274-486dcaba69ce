import Image from "next/image";
import Link from "next/link";
import { FiMenu, FiX } from "react-icons/fi";
import { sidebarToggleAtom } from "../atom/atoms";
import { useAtom } from "jotai";
import { useParams } from "next/navigation";
import { cn } from "@/lib/utils";
import { useEffect, useState } from "react";
import { TenantType, useGetActiveTenant } from "@/hooks/api/useRealm";

const Header = ({
  isExtended,
  setIsExtended,
}: {
  isExtended: boolean;
  setIsExtended: (extended: boolean) => void;
}) => {
  const [, setIsMobileOpen] = useAtom(sidebarToggleAtom);
  const { data: activeTenantData } = useGetActiveTenant();
  const [active, setActive] = useState<TenantType | null>(null);

  useEffect(() => {
    if (activeTenantData) {
      setActive(activeTenantData);
    }
  }, [activeTenantData]);

  if (!active) return null;

  return (
    <div className="flex w-full items-center justify-between gap-4 px-2">
      <Link
        href={`/dashboard`}
        className="flex h-full shrink-0 gap-2"
        aria-label="Go to dashboard"
      >
        <div className="relative w-10 ">
          <Image
            src={active?.logo ?? "/logos/ClerkXpress.svg"}
            alt="Logo"
            fill={true}
            sizes="50px"
            className="object-contain -translate-y-0.5"
          />
        </div>
        <div className="flex -translate-y-1 flex-col">
          <span
            className={cn("text-lg font-semibold tracking-wider text-white")}
          >
            {active?.displayName}
          </span>
          <span className={cn("-mt-0.5 text-xs italic text-slate-300")}>
            Powered By ClerkXpress
          </span>
        </div>
      </Link>
      {/* <button
        className="mt-1 hidden -translate-y-0.5 text-2xl lg:block"
        onClick={() => setIsExtended(!isExtended)}
        aria-label="Toggle menu"
      >
        <FiMenu />
      </button> */}
      <button
        className="ml-auto text-2xl lg:hidden"
        onClick={() => setIsMobileOpen(false)}
        aria-label="Close Sidebar"
      >
        <FiX />
      </button>
    </div>
  );
};

export default Header;
