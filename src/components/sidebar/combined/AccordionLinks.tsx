import { useEffect, useRef } from "react";
import { Group, LinkItem } from "../types/sidebarTypes";
import { FiChevronDown, FiChevronUp } from "react-icons/fi";
import { usePathname } from "next/navigation";
import Link from "next/link";
import { dmsans } from "@/styles/Fonts";
import { useAtom } from "jotai";
import { sidebarToggleAtom } from "../atom/atoms";
import { motion, AnimatePresence } from "framer-motion";
import Icon from "./Icon";
import dynamicIconImports from "lucide-react/dynamicIconImports";
import { cn } from "@/lib/utils";

// Type guard to validate the icon name
const isValidIconName = (
  icon: string,
): icon is keyof typeof dynamicIconImports => {
  return icon in dynamicIconImports;
};

const AccordionLinks = ({
  group,
  openAccordionId,
  toggleAccordion,
}: {
  group: LinkItem;
  openAccordionId: string;
  toggleAccordion: (id: string) => void;
}) => {
  const pathname = usePathname();
  const isOpen = openAccordionId === group.id;
  const handleAccordionClick = () => toggleAccordion(group.id);
  const [, setIsMobileOpen] = useAtom(sidebarToggleAtom);

  const contentRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (isOpen && contentRef.current) {
      contentRef.current.focus();
    }
  }, [isOpen]);

  // Resolve the group's icon or default to "circle"
  const groupIcon = isValidIconName(group.icon) ? group.icon : "circle";

  return (
    <div>
      <button
        className={`
          flex w-full items-center justify-between px-1 text-left 
          ${dmsans.className} text-base
        `}
        onClick={handleAccordionClick}
        aria-expanded={isOpen}
        aria-controls="accordion-content"
        aria-label={
          isOpen ? "Collapse " + group.label : "Expand " + group.label
        }
      >
        <div className={`flex items-center gap-3 p-1`}>
          <Icon name={groupIcon} className=" text-white" size={18} />{" "}
          {group.label}
        </div>
        {isOpen ? <FiChevronUp /> : <FiChevronDown />}
      </button>

      <AnimatePresence mode="wait">
        {isOpen && (
          <motion.div
            transition={{ duration: 0.1 }}
            className="flex flex-col gap-0"
          >
            {group.groups &&
              group.groups.map((subGroup: Group) => (
                <motion.div
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  transition={{ duration: 0.1 }}
                  key={subGroup.id}
                  className="ml-4 border-l border-neutral-300 pl-[1.35rem] pt-2 last:pb-0"
                >
                  {subGroup.label && (
                    <span
                      className={cn(`text-sm tracking-wide text-slate-400`)}
                    >
                      {subGroup.label}
                    </span>
                  )}
                  <ul
                    className="flex flex-col gap-0.5"
                    aria-label={"Group " + subGroup.id}
                  >
                    {subGroup?.links &&
                      subGroup.links.map((link: LinkItem) => {
                        const active = pathname.includes(link.href ?? "");
                        const linkIcon = isValidIconName(link.icon)
                          ? link.icon
                          : "circle"; // Default to "circle" if invalid

                        return (
                          <li
                            key={link.id}
                            className={cn(
                              `relative -ml-2 rounded px-2 py-1`,
                              active && "bg-blue-300/10",
                            )}
                          >
                            <Link
                              href={link.href ?? ""}
                              className={cn("flex items-center gap-2 text-sm")}
                              onClick={() => {
                                setIsMobileOpen(false);
                              }}
                              aria-label={link.ariaLabel ?? link.label}
                            >
                              {/* <Icon
                                name={linkIcon}
                                className=" text-white"
                                size={16}
                              />{" "} */}
                              {link.label}
                            </Link>
                            {/* {active && (
                              <span className="absolute left-0 top-0 h-full w-1 rounded-lg bg-blue-300" />
                            )} */}
                          </li>
                        );
                      })}
                  </ul>
                </motion.div>
              ))}
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
};

export default AccordionLinks;
