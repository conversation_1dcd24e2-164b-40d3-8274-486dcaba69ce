import dynamic from "next/dynamic";
import React, { useMemo } from "react";
import { LucideProps } from "lucide-react";
import dynamicIconImports from "lucide-react/dynamicIconImports";

// Cache for loaded icons
const iconCache: Record<string, React.ComponentType<LucideProps>> = {};

interface IconProps extends LucideProps {
  name: keyof typeof dynamicIconImports;
}

const Icon = ({ name, ...props }: IconProps) => {
  const LucideIcon = useMemo(() => {
    if (!iconCache[name]) {
      // Dynamically import and cache the icon
      iconCache[name] = dynamic(dynamicIconImports[name]) as React.ComponentType<LucideProps>;
    }
    return iconCache[name];
  }, [name]);

  return <LucideIcon {...props} />;
};

export default Icon;
