"use client";
import Services from "@/components/services/Services";
import Header from "./Header";
import AccordionGroup from "./AccordionGroup";
import { useAtom } from "jotai";
import { sidebarToggleAtom } from "../atom/atoms";
import { cn } from "@/lib/utils";
import { useSidebar } from "../hooks/useSidebar";
import { useMyProfile } from "@/hooks/providers/useMyProfile";
import { Building, User } from "lucide-react";
import Link from "next/link";
import { useMyPermissions } from "@/hooks/useMyPermissions";
import SearchBar from "@/components/navbar/SearchBar";

const CombinedLayout = ({
  isExtended,
  setIsExtended,
}: {
  isExtended: boolean;
  setIsExtended: (extended: boolean) => void;
}) => {
  const [isMobile] = useAtom(sidebarToggleAtom);
  const { hasPermissions } = useMyProfile();
  return (
    <div
      className={cn(
        `fixed left-0 top-0 z-50 h-screen w-screen shrink-0 flex-col gap-6 overflow-y-auto overflow-x-hidden bg-clerk-background p-3 py-6 text-clerk-accent lg:relative lg:z-10 lg:flex lg:h-full lg:w-64 lg:overflow-y-visible lg:bg-transparent lg:py-3 lg:pl-0 lg:pr-2`,
        isMobile ? "flex" : "hidden",
      )}
    >
      <Header isExtended={isExtended} setIsExtended={setIsExtended} />
      <div className="w-full max-w-sm flex items-center mx-auto">
        <AddNewButton />
      </div>
      <div className="flex items-center justify-center lg:hidden">
        {hasPermissions(["super-admin"]) ? <SearchBar /> : null}
      </div>
      <NavigationSection />
      {hasPermissions(["resident"]) && <AssociationsSection />}
      {hasPermissions(["super-admin", "manage-admin-settings"]) && (
        <AdminSection />
      )}

      <div className="transitional-all z-20 flex flex-col gap-4 py-2 text-neutral-100">
        {hasPermissions(["super-admin"]) && <Services extended />}
      </div>
    </div>
  );
};

export default CombinedLayout;

const Title = ({ title }: { title: string }) => {
  return (
    <div className="flex w-full items-center gap-2 px-2 text-xs text-slate-300">
      {title}
    </div>
  );
};

const NavigationSection = () => {
  const { data, isLoading } = useSidebar("individual");
  if (isLoading) return null;
  if (!data) return null;
  return (
    <div className="flex w-full flex-col gap-4 py-2 text-neutral-100">
      <Title title="Navigation" />
      <AccordionGroup links={data ?? []} />
    </div>
  );
};

const AdminSection = () => {
  const { data } = useSidebar("admin");
  if (!data) return null;
  return (
    <div className="flex w-full flex-col gap-4 py-2 text-neutral-100">
      <Title title="Admin" />
      <AccordionGroup links={data ?? []} />
    </div>
  );
};

const AssociationsSection = () => {
  const { data } = useSidebar("associations");
  if (!data) return null;
  return (
    <div className="flex w-full flex-col gap-4 py-2 text-neutral-100">
      <Title title="My Account" />
      <AccordionGroup links={data ?? []} />
    </div>
  );
};

export const AddNewButton = () => {
  const { hasPermissions } = useMyPermissions();
  if (hasPermissions(["super-admin"]))
    return (
      <div className="flex w-full h-full items-center bg-transparent text-base text-slate-800">
        <Link
          href={`/onlineForm/new-resident-registration-clerk`}
          className="flex w-full items-center gap-2 rounded-l bg-blue-600 px-3 py-2 text-base text-white transition-all hover:bg-blue-400"
        >
          New Resident <User size={16} className="ml-auto" />
        </Link>
        <button
          disabled
          className="flex h-full items-center gap-2 rounded-r border-l border-blue-500/80 bg-blue-600 px-2 py-1 text-sm text-white"
        >
          <Building size={16} />
        </button>
      </div>
    );

  return null;
};
