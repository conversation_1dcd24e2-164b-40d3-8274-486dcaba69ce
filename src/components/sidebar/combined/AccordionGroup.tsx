import React, { useState } from "react";
import { LinkItem } from "../types/sidebarTypes";
import { usePathname } from "next/navigation";
import AccordionLinks from "./AccordionLinks";
import Link from "next/link";
import { sidebarToggle<PERSON>tom } from "../atom/atoms";
import { useAtom } from "jotai";
import { cn } from "@/lib/utils";
import { useMyProfile } from "@/hooks/providers/useMyProfile";
import Alerts from "./Alerts";
import Icon from "./Icon";
import dynamicIconImports from "lucide-react/dynamicIconImports";
import { LayoutDashboard } from "lucide-react";

const checkIsOpen = ({
  links,
  pathname,
}: {
  links: LinkItem[];
  pathname: string;
}) => {
  const activePage = links.find((linkGroup: LinkItem) => {
    if (linkGroup.groups) {
      return linkGroup.groups.find((group) => {
        return group?.links?.find((link) => pathname.includes(link.href ?? ""));
      });
    } else {
      return linkGroup.href === pathname;
    }
  });

  return activePage?.id ?? "";
};

const filterLinksByPermissions = (
  links: LinkItem[],
  permissions: string[]
): LinkItem[] => {
  return links
    .filter((link) => {
      const hasPermission = link.permissions?.some((permission) =>
        permissions.includes(permission)
      );
      return hasPermission;
    })
    .map((link) => {
      if (link.groups) {
        return {
          ...link,
          groups: link.groups.map((group) => ({
            ...group,
            links: filterLinksByPermissions(group.links ?? [], permissions),
          })),
        } as LinkItem;
      } else {
        return link;
      }
    });
};

// Type guard to validate the icon name
const isValidIconName = (
  icon: string
): icon is keyof typeof dynamicIconImports => {
  return icon in dynamicIconImports;
};

export default function AccordionGroup({
  links,
}: {
  links: LinkItem[];
}) {
  const { permissions } = useMyProfile();
  const filteredLinks = filterLinksByPermissions(
    links,
    permissions ?? []
  );

  const pathname = usePathname();
  const [openAccordionId, setOpenAccordionId] = useState(
    checkIsOpen({ links: filteredLinks, pathname }),
  );
  const [, setIsMobileOpen] = useAtom(sidebarToggleAtom);

  const toggleAccordion = (id: string) => {
    if (id === openAccordionId) {
      setOpenAccordionId("");
    } else {
      setOpenAccordionId(id);
    }
  };

  return (
    <div className="flex w-full flex-col gap-2">
      {filteredLinks.map((linkGroup: LinkItem) => {
        console.log("linkGroup", linkGroup);
        const active = pathname.includes(linkGroup.id ?? "");

        const iconName = isValidIconName(linkGroup.icon)
          ? linkGroup.icon
          : "circle";

        return (
          <div key={linkGroup.id} className="w-full">
            {linkGroup.groups ? (
              <AccordionLinks
                group={linkGroup}
                openAccordionId={openAccordionId}
                toggleAccordion={toggleAccordion}
              />
            ) : (
              <Link
                href={linkGroup.href ?? ""}
                aria-label={linkGroup.label}
                className={cn(
                  `relative flex w-full items-center gap-3 rounded px-2 py-1 text-base`,
                  active && "bg-blue-300/10",
                )}
                onClick={() => {
                  setIsMobileOpen(false);
                }}
              >
                <Icon
                  name={iconName}
                  className="text-white"
                  size={18}
                />
                {linkGroup.label}
                {active && (
                  <span className="absolute left-0 top-0 h-full w-1 rounded-lg bg-blue-300" />
                )}
                <Alerts alerts={linkGroup?.alerts ?? null} />
              </Link>
            )}
          </div>
        );
      })}
    </div>
  );
}
