import { IconType } from "react-icons";

export type SidebarConfig = {
  logo: string;
  bgImage: string;
  links: LinkItem[];
}

export type Group = {
  label: string;
  id: string;
  links?: LinkItem[];
}

export type LinkItem = {
  label: string;
  id: string;
  href: string | null;
  icon: string;
  parentId: string | null;     
  children?: LinkItem[];  
  order?: number;
  groups?: Group[];
  permissions: string[];
  ariaLabel?: string;
  alerts?: string[] | null;
}

export type SidebarVersion = 'individual' | 'admin';

export type Option= {
  label: string
  style: string
}

export type Options = {
  [key in SidebarVersion]: Option
}

export type UtilityButton = {
  label: string
  href: string
  variant: 'primary' | 'secondary' | 'danger' | 'success'
  icon: IconType
}

export type UtilityButtons = {
  [key in SidebarVersion]: UtilityButton
}