"use client";
import React, { useState } from "react";
import Link from "next/link";
import { AiOutlineMinus, AiOutlinePlus } from "react-icons/ai";
import type { Group, LinkItem } from "@/components/sidebar/types/sidebarTypes";
import { usePathname } from "next/navigation";
import { SidebarVersion } from "@/components/sidebar/types/sidebarTypes";

const SecondaryGroup = ({
  group,
  version,
}: {
  group: Group;
  version: SidebarVersion;
}) => {
  const pathname = usePathname();
  const [isHidden, setIsHidden] = useState(false);

  return (
    <div key={group.label}>
      <div className="mb-2 flex items-center justify-between text-xs font-bold  uppercase">
        <p>{group.label}</p>
        <button
          onClick={() => setIsHidden(!isHidden)}
          className="hover:text-slate-900"
          aria-label={
            isHidden ? `${group.label} expand` : `${group.label} collapse`
          }
        >
          {isHidden ? (
            <AiOutlinePlus aria-label={`${group.label} expand`} />
          ) : (
            <AiOutlineMinus aria-label={`${group.label} collapse`} />
          )}
        </button>
      </div>
      {!isHidden && (
        <div className="flex flex-col gap-1">
          {group.links &&
            group.links.map((linkGroup: LinkItem) => {

              return (
                <Link
                  key={linkGroup.id}
                  href={linkGroup.href ?? ""}
                  aria-label={linkGroup.ariaLabel ?? linkGroup.label}
                  className={`flex w-full items-center gap-4 rounded px-2 py-1`}
                >
                  {/* <linkGroup.icon className="text-xl" /> {linkGroup.label} */}
                </Link>
              );
            })}
        </div>
      )}
    </div>
  );
};

export default SecondaryGroup;
