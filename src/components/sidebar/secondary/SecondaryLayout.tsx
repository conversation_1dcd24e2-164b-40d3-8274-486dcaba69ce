import React from "react";
import SecondaryGroup from "./SecondaryGroup";
import { Group } from "../types/sidebarTypes";
import { SidebarVersion } from "../types/sidebarTypes";

const SecondaryLayout = ({
  secondaryLinks,
  label,
  version,
}: {
  secondaryLinks: Group[];
  label: string;
  version: SidebarVersion;
}) => {
  return (
    <div
      className={`relative mr-2 flex w-[220px] flex-col justify-between gap-10 overflow-hidden overflow-y-auto rounded bg-clerk-accent/10 px-4 pb-10 pt-4
      `}
      style={{ position: "relative", zIndex: 0 }}
    >
      <div className="flex flex-col gap-8">
        <h1 className="mb-1 text-xl font-bold">{label}</h1>
        {secondaryLinks &&
          secondaryLinks.map((group: any) => (
            <SecondaryGroup key={group.id} group={group} version={version} />
          ))}
      </div>
    </div>
  );
};

export default SecondaryLayout;
