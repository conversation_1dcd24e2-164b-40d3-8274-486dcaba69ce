import React, { ReactNode, useEffect, useRef } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { FiX } from "react-icons/fi";

type ModalProps = {
  isOpen: boolean;
  onClose: () => void;
  title: string;
  position?: "left" | "right" | "center";
  children: ReactNode;
  className?: string;
  disableOutsideClick?: boolean;
};

const Modal: React.FC<ModalProps> = ({
  isOpen,
  onClose,
  title,
  position = "center",
  children,
  className = "",
  disableOutsideClick = false,
}) => {
  const modalRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const handleEscapePress = (e: KeyboardEvent) => {
      if (e.key === "Escape" && isOpen) {
        onClose();
      }
    };

    document.addEventListener("keydown", handleEscapePress);
    return () => {
      document.removeEventListener("keydown", handleEscapePress);
    };
  }, [isOpen, onClose]);

  useEffect(() => {
    if (isOpen && modalRef.current) {
      modalRef.current.focus();
    }
  }, [isOpen]);

  const trapFocus = (event: React.FocusEvent<HTMLDivElement>) => {
    if (modalRef.current && !modalRef.current.contains(event.target)) {
      event.stopPropagation();
      modalRef.current.focus();
    }
  };

  const modalStyles = {
    center:
      "bg-white text-neutral-900 z-[60] rounded-lg shadow-lg p-6 max-w-[1000px] w-[50%] overflow-y-auto max-h-[75vh]",
    left: "fixed top-0 left-0 bottom-0 z-50 bg-gray-300 rounded-tl-lg rounded-bl-lg shadow-lg p-6 h-full w-1/4 p-6",
    right:
      "fixed top-0 right-0 bottom-0 z-50 bg-gray-300 shadow-lg h-full w-full max-w-lg p-6",
  };

  const modalAnimate = {
    center: { opacity: [0, 1, 1], translateY: [-100, 20, 0] },
    left: { opacity: [0, 1, 1], translateX: [-300, 50, 0] },
    right: { opacity: [0, 1, 1], translateX: [300, 0, 0] },
  };

  return (
    <AnimatePresence>
      {isOpen && (
        <div
          className="fixed top-0 left-0 w-screen h-screen z-[60] bg-black/50 backdrop-blur-sm flex items-center justify-center overflow-hidden"
          onClick={(e) => {
            e.stopPropagation();
            if (!disableOutsideClick) {
              onClose();
            }
          }}
          onFocusCapture={trapFocus}
        >
          <motion.div
            initial={{ opacity: 0 }}
            animate={modalAnimate[position]}
            transition={{ duration: 0.25 }}
            className={`${modalStyles[position]} bg-white flex flex-col ${className}`}
            onClick={(e) => e.stopPropagation()}
          >
            <div className="flex justify-between mb-4 items-end">
              <h2 className="text-xl font-bold">{title}</h2>
              <button
                className="text-gray-500 hover:text-gray-700 focus:outline-none text-xl"
                onClick={onClose}
              >
                <FiX />
              </button>
            </div>
            {children}
          </motion.div>
        </div>
      )}
    </AnimatePresence>
  );
};

export default Modal;
