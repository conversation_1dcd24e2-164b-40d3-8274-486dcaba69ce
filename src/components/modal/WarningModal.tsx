import React from "react";

type WarningModalProps = {
  showModal: boolean;
  closeModal: () => void;
};

const WarningModal: React.FC<WarningModalProps> = ({ showModal, closeModal }) => {
  if (!showModal) {
    return null;
  }

  return (
    <div className="fixed z-50 inset-0 overflow-y-auto">
      <div className="flex items-center justify-center min-h-screen">
        <div className="bg-white shadow-lg p-6 rounded-lg">
          <h3 className="text-xl mb-4">Warning</h3>
          <p>
            Refreshing the page will clear the form data and start you back at step 1.
            Are you sure you want to continue?
          </p>
          <div className="flex justify-end gap-4 mt-6">
            <button className="bg-gray-300 px-4 py-2 rounded" onClick={closeModal}>
              Cancel
            </button>
            <button
              className="bg-red-600 text-white px-4 py-2 rounded"
              onClick={() => {
                closeModal();
                window.location.reload();
              }}
            >
              Refresh
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default WarningModal;
