import { requests } from "@/utils/agent";
import { addDays, differenceInYears, isBefore, parseISO } from "date-fns";
import { utcToZonedTime } from "date-fns-tz";
import { format } from "date-fns";
import { IconType } from "react-icons";
import { FaDog } from "react-icons/fa";
import { Contact, Individual } from "@/types/IndividualType";
import { Address } from "@/types/AddressType";
import { getAvatarBlob } from "../AvatarImage";

export const licenseMap: {
  [key: string]: {
    label: string;
    id: string;
    Icon: IconType;
  };
} = {
  dogLicense: {
    label: "Dog License",
    id: "dogLicenses",
    Icon: FaDog,
  },
  purebredDogLicense: {
    label: "Purebred Dog License",
    id: "purebredDogLicenses",
    Icon: FaDog,
  },
  draftDogLicenseForm: {
    label: "Draft Dog License",
    id: "draftDogLicenseForm",
    Icon: FaDog,
  },
};

export const downloadLicense = async (licenseId: string, admin: boolean) => {

  if (!licenseId) return null;

  const downloadPdf = (blob: any) => {
    const link = document.createElement("a");
    link.href = URL.createObjectURL(blob);
    link.target = "_blank";
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };
  

  let clerkLink = `/document-service/download?documentUUID=${licenseId}`;
  let resLink = `/document-service/me/download?documentUUID=${licenseId}`;
  const url = admin ? clerkLink : resLink;
  const file = await requests.get(url, { responseType: "blob" });
  downloadPdf(file);
};

export const isVaccineValid = (vaccineDueDate: string): boolean => {
  const currentDate = new Date();
  const dueDateInUTC = addDays(new Date(vaccineDueDate + "T00:00:00Z"), 1);

  return isBefore(currentDate, dueDateInUTC);
};

export const addableToCart = (status: string) => {
  const acceptable = ["Pending Payment"];

  return acceptable.includes(status);
};

export const formatDate = (date: string) => {
  try {
    return format(utcToZonedTime(date, "UTC"), "MMMM dd, yyyy");
  } catch (error) {
    return "";
  }
};

export const isSpayedOrNeutered = (spayedOrNeutered: string, sex: string) => {
  if (!spayedOrNeutered) {
    return "No Info";
  }

  const realValue = spayedOrNeutered?.startsWith("y");
  const value = sex.startsWith("M")
    ? realValue
      ? "Neutered"
      : "Unneutered"
    : realValue
      ? "Spayed"
      : "Unspayed";

  return value;
};

export const dogSex = (sex: string) => {
  if (!sex) {
    return "No Info";
  }

  if (sex.toLocaleLowerCase().startsWith("f")) {
    return "Female";
  }

  return "Male";
};

export const isSenior = (birthDate: string): boolean => {
  const today = new Date();
  const parsedBirthDate = parseISO(birthDate);
  const age = differenceInYears(today, parsedBirthDate);
  return age >= 65;
};

export const getDogLicenseFormType = (inputDate: any) => {
  const today = new Date().setHours(0, 0, 0, 0);
  const givenDate = new Date(inputDate).setHours(0, 0, 0, 0);
  const thirtyDaysBeforeExpiry = addDays(today, -30).setHours(0, 0, 0, 0);

  // Compare the dates
  if (givenDate < thirtyDaysBeforeExpiry) {
    return false;
  }

  return true;
};

export const formatPhoneNumber = (phoneNumber: string) => {
  return phoneNumber.replace(/(\d{3})(\d{3})(\d{4})/, "($1) $2-$3");
};

export const licenseHeader = (owner: Individual, homeAddress: Address) => {
  const fullAddress = `${homeAddress?.streetAddress ?? ""}${
    homeAddress?.streetAddress2 ?? ""
  }, ${homeAddress?.city ?? ""}, ${homeAddress?.state ?? ""} ${
    homeAddress?.zip ?? ""
  }`;

  const warning =
    owner?.status === "Deceased"
      ? "Deceased"
      : !owner?.registered
        ? "Account Not Registered Online"
        : null;

  const phone = owner?.contacts.find(
    (contact: Contact) => contact.type === "Phone" && contact.group === "Home",
  );
  const email = owner?.contacts.find(
    (contact: Contact) =>
      contact.type === "Email" && contact.group === "Primary",
  );

  return {
    primaryDisplay: owner?.firstName + " " + owner?.lastName,
    secondaryDisplay: homeAddress ? fullAddress : "",
    warningDisplay: warning,
    avatarUrl: getAvatarBlob(owner?.documents),
    active: owner?.active,
    contacts: {
      phone: phone?.value ? formatPhoneNumber(phone?.value) : "No Phone",
      email: email?.value ? email.value : "No Email",
    },
  };
};
