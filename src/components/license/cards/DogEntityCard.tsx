import React from "react";
import { Dog } from "@/types/DogType";
import Link from "next/link";
import { usePathname } from "next/navigation";
import { formatDate, isVaccineValid } from "../licenseHelper";
import { Badge } from "@/components/ui/badge";
import {
  Too<PERSON><PERSON>,
  TooltipProvider,
  TooltipTrigger,
  TooltipContent,
} from "@/components/ui/Tooltip";
import { TbVaccineOff } from "react-icons/tb";
import { FaAngry } from "react-icons/fa";
import { BiSad } from "react-icons/bi";
import { IconType } from "react-icons";
import { FiPlus } from "react-icons/fi";
import AvatarImage, { getAvatarBlob } from "@/components/AvatarImage";
import { cn } from "@/lib/utils";

export default function DogEntityCard({
  dog,
  licenseType,
  licenseEntityId,
  canApply,
}: {
  dog: Dog[];
  licenseType: string;
  licenseEntityId: string;
  canApply: boolean;
}) {
  const pathname = usePathname();

  if (!dog || dog.length === 0)
    return (
      <div className="flex flex-col items-center gap-2 rounded p-1 transition-all">
        <div
          className={`
          relative flex h-20
          w-20 items-center justify-center rounded-full bg-neutral-300 p-2 shadow
        `}
        >
          <BiSad className="text-3xl text-neutral-600" />
          <Badge
            variant="outline"
            className="absolute bottom-0 left-1/2 line-clamp-1 flex w-[100px] -translate-x-1/2 translate-y-1/2 items-center justify-center bg-white text-center text-xs shadow "
          >
            No Dog
          </Badge>
        </div>
      </div>
    );

  return (
    <div className="flex flex-row flex-wrap items-center gap-6">
      {dog &&
        dog?.map((dog) => (
          <DogCard
            key={dog.entityId}
            dog={dog}
            licenseEntityId={licenseEntityId}
          />
        ))}

      {/* Add Dog */}
      {licenseType === "purebredDogLicense" && (
        <div
          className={cn(
            "flex flex-col items-center gap-2 rounded p-1 transition-all hover:bg-neutral-200 hover:shadow",
            !canApply && "pointer-events-none opacity-50",
          )}
        >
          <Link
            href={`/license/dogLicenses/create/purebred?entityId=${licenseEntityId}&entityType=purebredDogLicense&action=addAdditionalDog&returnTo=${pathname}`}
            className={cn(
              `
          relative flex h-20
          w-20 items-center justify-center rounded-full bg-neutral-300 p-2 shadow
        `,
              !canApply && "cursor-not-allowed",
            )}
            aria-disabled={!canApply}
          >
            <FiPlus className="text-3xl text-neutral-600" />

            <Badge
              variant="outline"
              className={cn(
                "absolute bottom-0 left-1/2 line-clamp-1 flex w-[100px] -translate-x-1/2 translate-y-1/2 items-center justify-center bg-white text-center text-xs shadow ",
                !canApply && " text-red-500",
              )}
            >
              {canApply ? "Add Dog" : "Limit Reached"}
            </Badge>
          </Link>
        </div>
      )}
    </div>
  );
}

const DogCard = ({
  licenseEntityId,
  dog,
}: {
  licenseEntityId: string;
  dog: Dog;
}) => {
  const isDangerous = dog?.isDangerous ? true : false;
  const isDeceased = dog?.status === "Deceased" ? true : false;
  const isActive = dog?.active ? true : false;
  const status = dog?.status ?? "";
  const avatarUrl = getAvatarBlob(dog?.documents) as string;
  console.log(avatarUrl);
  console.log(dog?.documents);
  const vaccineDueDate: string = dog?.vaccineDueDate
    ? formatDate(dog?.vaccineDueDate)
    : "No Expiration Date";

  const isVaccineExpired = dog?.vaccineDueDate
    ? !isVaccineValid(dog?.vaccineDueDate)
    : "N/A";
  const vaccineExempt = dog?.vaccineDatesExempt;

  const enabled = avatarUrl !== "" || avatarUrl.length > 0;

  console.log(avatarUrl);

  return (
    <Link
      href={`/a/license/${licenseEntityId}?tab=dog&dogId=${dog.entityId}`}
      key={dog.entityId}
      className="flex flex-col items-center rounded p-1 transition-all hover:bg-neutral-200 hover:shadow"
    >
      <div
        className={`
          relative h-20 w-20 rounded-full shadow 
        `}
      >
        <AvatarImage
          entityType={dog?.name.toLowerCase()}
          src={avatarUrl}
          alt="dog"
          height={80}
          width={80}
          className={`
            h-full w-full border-spacing-2 rounded-full border-2
            ${
              !isActive || isDeceased
                ? "border-black grayscale"
                : isDangerous || (isVaccineExpired && !vaccineExempt)
                  ? "border-red-600"
                  : "border-green-600"
            }
          `}
        />

        {!isActive && (
          <div className="absolute left-1/2 top-1/2 -translate-x-1/2 -translate-y-1/2 rounded-full bg-red-500/90 px-2 py-0.5 text-xs uppercase text-red-50 shadow ">
            {status ?? "Inactive"}
          </div>
        )}

        {/* Dog Name */}
        <Badge
          variant="outline"
          className="absolute bottom-0 left-1/2 line-clamp-1 flex w-[100px] -translate-x-1/2 translate-y-1/2 items-center justify-center bg-white text-center text-xs shadow"
        >
          <div className="relative">{dog.dogName ?? "No Name"}</div>
        </Badge>

        {/* Vaccine Expired */}
        {isVaccineExpired && !isDeceased && !vaccineExempt && isActive && (
          <BubbleIcon
            icon={TbVaccineOff}
            className="left-0 top-0 -translate-y-1/3"
          >
            <p>Vaccine Expired: {vaccineDueDate}</p>
          </BubbleIcon>
        )}

        {/* Dangerous Dog */}
        {isDangerous && !isDeceased && isActive && (
          <BubbleIcon icon={FaAngry} className="right-0 top-0 -translate-y-1/3">
            <p>Dangerous Dog</p>
          </BubbleIcon>
        )}
      </div>
    </Link>
  );
};

const BubbleIcon = ({
  icon: Icon,
  children,
  className,
}: {
  icon: IconType;
  children: React.ReactNode;
  className?: string;
}) => {
  return (
    <TooltipProvider>
      <Tooltip>
        <TooltipTrigger>
          <Badge
            variant="destructive"
            className={`
              absolute line-clamp-1 max-w-[100px] border-2 border-white bg-red-600 p-1 text-sm shadow
              ${className}
            `}
          >
            <Icon />
          </Badge>
        </TooltipTrigger>
        <TooltipContent>{children}</TooltipContent>
      </Tooltip>
    </TooltipProvider>
  );
};
