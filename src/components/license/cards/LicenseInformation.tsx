import React from "react";

import { formatDate } from "../licenseHelper";
import { License } from "@/types/LicenseType";

export default function LicenseInformation({ license }: { license: License }) {
  return (
    <div className="border-t px-6 py-2 h-full">
      <div className="font-medium">License Information</div>
      {[
        {
          label: "Start",
          value: license?.validFromDate
            ? formatDate(license?.validFromDate)
            : "No Date",
        },
        {
          label: "Expires",
          value: license?.validToDate
            ? formatDate(license?.validToDate)
            : "No Date",
        },
      ].map((item) => {
        return (
          <div className="flex items-center gap-1" key={item.label}>
            <div className="w-20 text-xs md:text-base">{item.label}:</div>
            <div className={`w-full text-xs md:text-base`}>{item.value}</div>
          </div>
        );
      })}
    </div>
  );
}
