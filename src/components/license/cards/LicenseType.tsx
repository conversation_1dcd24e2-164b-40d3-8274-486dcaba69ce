export type Address = {
  address: string;
  address2: string | null;
  houseNo: string;
  road: string;
  addressType: string;
  entityId?: string;
  houseNumber?: string;
  streetName?: string;
  streetAddress?: string;
  streetAddress2?: string | null;
  town?: string | null;
  city?: string;
  state?: string;
  zip?: string;
  fullAddress?: string;
  latitude?: number;
  longitude?: number;
};

export type Owner = {
  firstName: string;
  lastName: string;
  dateOfBirth: string;
  address: Address;
  phone: string;
  email: string;
  entityId: string;
  contacts: {
    type: string;
    group: string;
    value: string;
  }[];
};

export type Dog = {
  avatarUrl?: string;
  active?: boolean;
  entityId: string;
  dogName: string;
  tagNumber: string;
  dogBreed: string;
  dogBirthDate: string;
  dogSex: string;
  dogSpayedOrNeutered?: string;
  dogPrimaryColor: string;
  dogSecondaryColor: string;
  microchipNumber: string;
  licenseExemptionDocumentsProvided?: string;
  dogBio: string;
  dogMarkings: string;
  catFriendly: boolean;
  dogFriendly: boolean;
  childFriendly: boolean;
  veterinaryName: string;
  veterinarianName: string;
  rabiesTagNumber: string;
  vaccineName: string;
  vaccineBrand: string;
  vaccineProducer: string;
  vaccineAdministeredDate: string;
  vaccineDueDate: string;
  vaccineLotNumber: string;
  vaccineLotExpirationDate: string;
  vaccinePeriod: string;
  insuranceStartDate: string;
  insuranceEndDate: string;
  insuranceExpirationDate: string;
  insuranceCompany: string;
  insurancePolicyNumber: string;
  isDangerous: boolean;
  vaccineDatesExempt?: string;
  status?: string;
  documents?: Documents[];
  licenseExempt?: boolean | null;
};

export type Documents = {
  contentType: string;
  createdBy: string;
  deleted: boolean;
  deletedDate: string;
  documentUuid: string;
  entityId: string;
  fileName: string;
  group: DocumentGroupNames;
  key: DocumentKeys;
  name: DocumentNames;
};

type DocumentKeys =
  | "avatar"
  | "dogPhoto"
  | "dogSpayedOrNeuteredDocument"
  | "dogRabiesVaccinationDocument"
  | "dogSpayedOrNeuteredExemptionDocument"
  | "dogRabiesVaccinationExemptionDocument"
  | "dangerousDogRequirementDocument"
  | "dogProofOfInsuranceDocument"
  | "idCardFront"
  | "idCardBack"
  | "passport"
  | "proofOfResidency";

type DocumentNames =
  | "Avatar"
  | "Dog Photo"
  | "Dog Spayed Or Neutered Document"
  | "Dog Rabies Vaccination Document"
  | "Dog Spayed Or Neutered Exemption Document"
  | "Dog Rabies Vaccination Exemption Document"
  | "Dangerous Dog Requirement Document"
  | "Dog Proof Of Insurance Document"
  | "Id Card Front"
  | "Id Card Back"
  | "Passport"
  | "Proof Of Residency";

type DocumentGroupNames = "profile" | "dog" | "individual";
