import { Badge } from "@/components/ui/badge";
import { Skeleton } from "@/components/ui/skeleton";
import React from "react";

const typeSettings: {
  [key: string]: {
    label: string;
    styling: string;
    background: string;
  };
} = {
  loading: {
    label: "Loading",
    styling: "bg-neutral-600 text-neutral-50",
    background: "bg-neutral-100",
  },
  error: {
    label: "Error",
    styling: "bg-red-600 text-red-50",
    background: "bg-red-100",
  },
};

export const CardEntitySkeleton = () => {
  return (
    <div
      className="flex flex-col items-center gap-2 hover:bg-neutral-200 hover:shadow rounded p-1 transition-all"
    >
      <Skeleton
        className={`
          w-20 h-20 rounded-full relative shadow p-2 bg-neutral-300
        `}
      >
        <Badge
          variant="outline"
          className="line-clamp-1 w-[100px] absolute bottom-0 left-1/2 -translate-x-1/2 translate-y-1/2 bg-white text-center flex items-center justify-center text-xs shadow "
        >
          {
            <Skeleton
              className={`w-20 h-5 ${typeSettings["loading"].background}`}
            />
          }
        </Badge>

      
    
      </Skeleton>
    </div>
  );
};

const CardHeaderSkeleton = ({ type }: { type: string }) => {
  return (
    <>
      <div
        className={`absolute rounded -top-2 right-4 px-2 py-1 font-medium shadow-sm ${typeSettings[type].styling}`}
      >
        {typeSettings[type].label}
      </div>

      <div className="px-6 py-2 border-b border-neutral-200">
        <div className="flex justify-between">
          <div className="flex flex-col gap-1">
            <div className="text-xl">
              <Skeleton
                className={`w-[200px] h-8 ${typeSettings[type].background}`}
              />
            </div>
            <div className="flex gap-2 items-center">
              <div className="text-neutral-600 font-medium">
                <Skeleton
                  className={`w-20 h-5 ${typeSettings[type].background}`}
                />
              </div>
              <div className="text-neutral-500 font-medium hover:text-neutral-800 transition-all"></div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
};

export default function LicenseCardSkeleton({ type }: { type: string }) {
  return (
    <div className="flex flex-col w-full max-w-lg relative shadow-xl bg-white rounded h-[342px]">
      <CardHeaderSkeleton type={type} />
      <div className="h-32 border-b flex w-full justify-start px-6 py-4">
        <CardEntitySkeleton />
      </div>
      <div className=" flex flex-col gap-1 py-2 px-6">
        <Skeleton className={`w-36 h-6 ${typeSettings[type].background}`} />
        <Skeleton className={`w-60 h-10 ${typeSettings[type].background}`} />
      </div>
      <div className="h-16 bg-clerk-background rounded-b"></div>
    </div>
  );
}
