"use client"
import { isVaccineValid } from "@/components/license/licenseHelper";
import { useGetLicenseFeesCalculation } from "@/hooks/api/useLicense";
import { FiChevronDown, FiExternalLink, FiCalendar } from "react-icons/fi";
import { License } from "@/types/LicenseType";
import { cardStyle } from "./cardStyle2";
import Link from "next/link";
import { format } from "date-fns";
import { cn } from "@/lib/utils";
import { Badge } from "@/components/ui/badge";
import { Avatar, AvatarImage } from "@/components/ui/avatar";
import {
  ExternalLink,
  Shield,
  CheckCircle,
  AlertCircle,
  Tag,
  Dog,
} from "lucide-react";
import ProfileActions2 from "@/components/profile/ProfileActions2";
import { useMyPermissions } from "@/hooks/useMyPermissions";
import { Skeleton } from "@/components/ui/skeleton";

export default function LicenseCard({ license }: { license: License }) {
  const { hasPermissions } = useMyPermissions();

  const validFromDate = license.validFromDate
    ? format(new Date(license.validFromDate), "dd MMM yyyy")
    : "No Date";

  const validToDate = license.validToDate
    ? format(new Date(license.validToDate), "dd MMM yyyy")
    : "No Date";

  const dog = license.dogs && license.dogs.length > 0 ? license.dogs[0] : null;

  const validVaccine = dog?.vaccineDatesExempt
    ? true
    : dog?.vaccineDueDate
      ? isVaccineValid(dog.vaccineDueDate)
      : false;

  return (
    <div className="group relative flex flex-col overflow-hidden rounded-xl border border-gray-200/60 bg-white shadow-sm transition-all duration-300 hover:-translate-y-0.5 hover:shadow-lg hover:shadow-gray-900/5">
      {/* Header Section */}
      <div className="relative bg-gradient-to-br from-gray-50 to-white p-3 pb-2 sm:p-4 sm:pb-3">
        <div className="flex flex-wrap items-start justify-between gap-x-3 gap-y-2">
          {/* Title and License Number */}
          <div className="min-w-0 flex-1">
            <div className="mb-1.5 flex items-center gap-2">
              <Shield className="h-4 w-4 shrink-0 text-blue-600 sm:h-5 sm:w-5" />
              <h3 className="text-lg font-semibold text-gray-900 sm:text-xl">
                {license.licenseType.name}
              </h3>
            </div>

            <div className="flex items-center gap-2 sm:gap-3">
              <div className="inline-flex items-center gap-1.5 rounded-lg border border-gray-200 bg-white px-2 py-1 shadow-sm sm:gap-2 sm:px-3 sm:py-1.5">
                <code className="font-mono text-xs font-medium text-gray-700 sm:text-sm">
                  {license.licenseNumber}
                </code>
                <Link
                  className="flex shrink-0 items-center justify-center text-gray-400 transition-colors hover:text-blue-600"
                  href={`/a/license/${license.entityId}`}
                  target="_blank"
                >
                  <FiExternalLink className="h-3 w-3 sm:h-4 sm:w-4" />
                  <span className="sr-only">Open license in new tab</span>
                </Link>
              </div>
            </div>
          </div>

          {/* Status Badge */}
          <Badge
            className={cn(
              cardStyle[license.status]?.styling ?? cardStyle.default.styling,
              "shrink-0 rounded-full border-0 px-3 py-1.5 text-xs font-medium shadow-sm sm:px-4 sm:py-2 sm:text-sm",
            )}
          >
            {license.status}
          </Badge>
        </div>
      </div>

      {/* Dog Profile Section */}
      {dog && (
        <div className="border-t border-gray-100 bg-gray-50/30 p-3 sm:p-4">
          <div className="flex flex-col items-start gap-3 sm:flex-row">
            {/* Dog Avatar */}
            <div className="relative shrink-0">
              <Avatar className="h-12 w-12 rounded-xl border-2 border-white shadow-sm">
                <AvatarImage
                  src="/images/icons/dog.png"
                  alt={dog.dogName}
                  className="object-cover"
                />
              </Avatar>
              <div
                className={cn(
                  "absolute -bottom-1 -right-1 rounded-full p-1",
                  validVaccine ? "bg-green-100" : "bg-red-100",
                )}
              >
                {validVaccine ? (
                  <CheckCircle className="h-3.5 w-3.5 text-green-600" />
                ) : (
                  <AlertCircle className="h-3.5 w-3.5 text-red-600" />
                )}
              </div>
            </div>

            {/* Dog Details */}
            <div className="w-full min-w-0 flex-1">
              <div className="flex flex-col gap-2 sm:flex-row sm:items-center">
                <div className="flex items-center gap-2">
                  <Link
                    href={`/profile/${dog.entityType}/${dog.entityId}?tab=profile`}
                    className="truncate text-base font-semibold text-blue-600 transition-colors hover:text-blue-700 sm:text-lg"
                  >
                    {dog.dogName}
                  </Link>
                  <Link
                    href={`/profile/${dog.entityType}/${dog.entityId}?tab=profile`}
                    className="shrink-0 text-blue-600 transition-colors hover:text-blue-700"
                    target="_blank"
                  >
                    <ExternalLink className="h-4 w-4" />
                  </Link>
                </div>
              </div>

              <div className="flex flex-wrap gap-2 ">
                <div className="flex items-center gap-1.5 text-xs text-gray-600 sm:text-sm">
                  <Dog className="h-3 w-3 sm:h-4 sm:w-4" />
                  <span className="font-mono font-medium">
                    {dog?.dogBreed ?? "No Breed"}
                  </span>
                </div>
                {dog.tagNumber && (
                  <div className="flex items-center gap-1.5 text-xs text-gray-600 sm:text-sm">
                    <Tag className="h-3 w-3 sm:h-4 sm:w-4" />
                    <span className="font-mono font-medium">
                      {dog.tagNumber}
                    </span>
                  </div>
                )}
              </div>

              <div className="flex flex-col items-start justify-between gap-2 sm:flex-row sm:items-center sm:gap-4">
                <Badge
                  variant="outline"
                  className={cn(
                    "border-2 text-xs font-medium",
                    validVaccine
                      ? "border-green-200 bg-green-50 text-green-700 hover:bg-green-100"
                      : "border-red-200 bg-red-50 text-red-700 hover:bg-red-100",
                  )}
                >
                  {validVaccine ? "Valid Vaccine" : "Vaccine Due"}
                </Badge>
              </div>
            </div>

            {hasPermissions(["super-admin"]) && (
              <ProfileActions2
                entity={dog}
                entityType="dog"
                className="flex w-full items-center justify-center gap-2 rounded-lg bg-gray-900 px-3 py-1.5 text-xs font-medium text-white shadow-sm transition-colors hover:bg-gray-800 sm:w-auto sm:text-sm"
              >
                Actions <FiChevronDown className="h-4 w-4" />
              </ProfileActions2>
            )}
          </div>
        </div>
      )}

      {/* License Details Section */}
      <div className="mt-auto border-t border-gray-100 bg-gray-50/50 p-3 sm:p-4">
        <div className="mb-3 flex items-center gap-2">
          <FiCalendar className="h-4 w-4 text-gray-500" />
          <h4 className="text-xs font-semibold uppercase tracking-wide text-gray-700 sm:text-sm">
            License Details
          </h4>
        </div>

        <div className="grid grid-cols-1 gap-3 sm:grid-cols-2 sm:gap-4">
          <div className="space-y-1">
            <div className="text-xs font-medium uppercase tracking-wide text-gray-500">
              Start Date
            </div>
            <div className="text-sm font-semibold text-gray-900 sm:text-base">
              {validFromDate}
            </div>
          </div>
          <div className="space-y-1">
            <div className="text-xs font-medium uppercase tracking-wide text-gray-500">
              Expiry Date
            </div>
            <div className="text-sm font-semibold text-gray-900 sm:text-base">
              {validToDate}
            </div>
          </div>
        </div>
      </div>

      {/* Action Buttons Section */}
      <div className="space-y-2 border-t border-gray-100 bg-gray-50/50 p-3 sm:p-4">
        <div className="flex gap-2">
          <Link
            href={`/a/license/${license.entityId}`}
            className="flex w-full items-center justify-center gap-2 rounded-lg border-2 border-gray-200 bg-neutral-200 px-4 py-2.5 text-xs font-semibold text-neutral-900 transition-all hover:border-blue-300 hover:bg-blue-50 hover:text-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 sm:text-sm"
          >
            <ExternalLink className="h-4 w-4" />
            View License
          </Link>
          <LicenseRenewalButton license={license} />
        </div>
      </div>
    </div>
  );
}

const LicenseRenewalButton = ({ license }: { license: License }) => {
  const {
    data: licenseFees,
    isLoading: isLoadingLicenseFees,
    isError: isErrorLicenseFees,
  } = useGetLicenseFeesCalculation(license.entityId);

  const { hasPermissions } = useMyPermissions();

  const isRenewable = licenseFees?.isrenewable

  if (isLoadingLicenseFees) return <Skeleton className="h-10 w-full" />;
  if (isErrorLicenseFees) return null;

  if (isRenewable && hasPermissions(["resident"])) {
    const code = license?.licenseType?.code ?? null
    if (!code) return null;
    return (
      <Link
        href={`/a/license/${license.entityId}/${code}/renew`}
        className="flex w-full items-center justify-center gap-2 rounded-lg border-2 border-gray-200 bg-blue-600 px-4 py-2.5 text-xs font-semibold text-neutral-50 transition-all hover:border-blue-300 hover:bg-blue-50 hover:text-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 sm:text-sm"
      >
        <ExternalLink className="h-4 w-4" />  
        Renew License
      </Link>
    );
  }

  return null;
};