import React from "react";
import { cardStyle } from "./cardStyle";
import Link from "next/link";
import { FiExternalLink } from "react-icons/fi";
import { License } from "@/types/LicenseType";

export default function CardHeader({
  license,
  navigate = true,
}: {
  license: License;
  navigate?: boolean;
}) {
  const status = cardStyle[license?.status ?? "default"];

  const link = `/a/license/${license?.entityId}`;
  return (
    <>
      <div
        className={`absolute -top-2 right-4 rounded px-2 py-1 font-medium shadow-sm ${status?.styling}`}
      >
        {status?.label}
      </div>

      <div className="px-6 py-2">
        <div className="flex justify-between">
          <div className="flex flex-col">
            <div className="text-xl">{license?.licenseType?.name}</div>
            <div className="flex items-center gap-2">
              {navigate ? (
                <>
                  <Link className="font-medium text-blue-600" href={link}>
                    {license?.licenseNumber}
                  </Link>
                  <Link
                    className="font-medium text-neutral-500 transition-all hover:text-neutral-800"
                    href={link}
                    target="_blank"
                  >
                    <FiExternalLink />
                  </Link>
                </>
              ) : (
                <span className="font-medium text-neutral-500">
                  {license?.licenseNumber}
                </span>
              )}
            </div>
          </div>
        </div>
      </div>
    </>
  );
}
