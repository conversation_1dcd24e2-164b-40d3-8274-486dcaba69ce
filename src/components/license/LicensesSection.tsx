"use client";
import React from "react";
import { License } from "@/types/LicenseType";
import LicenseList from "./LicenseList";

export default function LicensesSection({
  licenses,
}: {
  licenses: License[];
}) {
  const currentLicenses =
    licenses?.filter((license) => license.status === "Active") ?? [];
  const pendingLicenses = licenses?.filter(
    (license) =>
      license?.status === "Pending Payment" ||
      license?.status === "Pending" ||
      license?.status === "Pending Approval",
  );

  const rejectedLicenses = licenses?.filter(
    (license) => license?.status === "Rejected",
  );

  // const draftLicenses = licenses?.filter(
  //   (license) => license?.status === "Draft",
  // );

  // Expired Licenses
  const expiredLicenses = licenses?.filter(
    (license) => license?.status === "Expired",
  );

  return (
    <div className="flex w-full flex-col gap-20">
      {/* Rejected */}
      {rejectedLicenses.length > 0 && (
        <LicenseList label="Rejected Licenses" licenses={rejectedLicenses} />
      )}

      {/* Expired */}
      {expiredLicenses.length > 0 && (
        <LicenseList label="Expired Licenses" licenses={expiredLicenses} />
      )}

      {/* Pending */}
      {pendingLicenses.length > 0 && (
        <LicenseList
          label="Pending Licenses"
          licenses={pendingLicenses}
          column={false}
        />
      )}

      {/* Current */}
      <LicenseList
        label="Current Licenses"
        licenses={currentLicenses}
        column={false}
      />

      {/* Draft */}
      {/* <LicenseList label="Draft Licenses" licenses={draftLicenses} /> */}
    </div>
  );
}
