import React from "react";
import { License } from "@/types/LicenseType";
import { cn } from "@/lib/utils";
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";
import LicenseCard from "@/components/license/cards/LicenseCard";
import { ChevronDown } from "lucide-react";
import Image from "next/image";

export default function LicenseList({
  label,
  licenses,
  max = null,
  column = true,
}: {
  label: string;
  licenses: License[];
  max?: number | null;
  column?: boolean;
}) {
  const licensesToShow = max ? licenses.slice(0, max) : licenses;

  return (
    <div className="w-full rounded-lg border border-gray-200">
      <Accordion
        type="single"
        collapsible
        defaultValue="item-1"
        className="w-full"
      >
        <AccordionItem value="item-1" className="border-0">
          <div className="flex items-center justify-between px-6 py-4">
            <div className="flex items-center gap-3">
              <h2 className="text-lg font-medium text-gray-900">{label}</h2>
              <span className="flex h-6 min-w-6 items-center justify-center rounded-full bg-blue-100 px-2 text-sm font-medium text-blue-800">
                {licensesToShow?.length}
              </span>
            </div>
            <AccordionTrigger className="p-0 hover:no-underline">
              {/* <ChevronDown className="h-5 w-5 shrink-0 text-gray-500 transition-transform duration-200" /> */}
              <span className="sr-only">Toggle licenses</span>
            </AccordionTrigger>
          </div>

          <AccordionContent className="border-t border-gray-200 px-6 pb-6 pt-6">
            {licensesToShow?.length > 0 ? (
              <div className="grid w-full grid-cols-1 gap-6 md:grid-cols-1 2xl:grid-cols-2">
                {licensesToShow?.map((license: License) => (
                  <LicenseCard key={license.entityId} license={license} />
                  // <SimpleLicenseCard key={license.entityId} license={license} />
                ))}
              </div>
            ) : (
              <div className="py-8 text-center">
                <p className="text-gray-500">No licenses found</p>
              </div>
            )}
          </AccordionContent>
        </AccordionItem>
      </Accordion>
    </div>
  );
}