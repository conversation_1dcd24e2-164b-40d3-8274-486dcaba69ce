"use client";
import React from "react";
import Modal from "@/components/modal/Modal";
import { useGetJSONStorage } from "@/hooks/api/useAdmin";
import { Licenses } from "../application/ApplicationCard";

const AddLicense = ({
  isOpen,
  onClose,
  entityId,
}: {
  isOpen: boolean;
  onClose: () => void;
  entityId: string;
}) => {
  const { data: list } = useGetJSONStorage("list", "onlineForms");

  if (!list) return null;

  return (
    <Modal
      isOpen={isOpen}
      onClose={onClose}
      title="New License"
      className="w-full"
    >
      <Licenses />
    </Modal>
  );
};

export default AddLicense;
