interface ApiExecutorParams {
    functionId: string;
    config: ProfileConfig;
    params: Record<string, string>;
    data?: any;
}

interface FunctionConfig {
    type: "rest" | "function";
    function?: string;
    url?: string;
    body?: {
        [key: string]: string;
    };
    parameters?: {
        [key: string]: string;
    }
    format?: "formData" | "json";
    method?: "GET" | "POST" | "PUT" | "DELETE" | "PATCH";
    description?: string;
    meUrl?: string;
}