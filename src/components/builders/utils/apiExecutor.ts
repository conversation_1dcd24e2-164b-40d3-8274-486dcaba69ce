import { requests } from "@/utils/agent";

export const getApiExecutor = () => {
  const parseBody = (body: any, format: string) => {
    let requestBody: any = body;

    if (format === "formData") {
      requestBody = new FormData();
      Object.entries(body ?? {}).forEach(([key, value]) => {
        requestBody.append(key, value instanceof File ? value : String(value));
      });
    }
    return requestBody;
  };

  const getContextValue = (path: string, context: Record<string, any>) => {
    const keys = path.split(".");
    let currentValue: any = context;

    for (let key of keys) {
      if (currentValue?.[key] !== undefined) {
        return currentValue[key];
      } else {
        return null;
      }
    }

    return "";
  };

  const replaceContextPlaceholders = (
    str: string,
    context: Record<string, any>,
  ) => {
    const matches = str.match(/{{context:([^}]+)}}/g);

    if (matches) {
      for (const match of matches) {
        const path = match.slice(10, -2);
        const value = getContextValue(path, context);
        if (value instanceof File) {
          return value;
        } else if (value === undefined) {
          throw new Error(`Error: ${path} not found in context`);
        } else if (value === "" || value === null) {
          return null;
        } else if (typeof value === "object") {
          return value;
        } else {
          str = str.replace(match, value.toString());
        }
      }
    }
    return str;
  };

  /**
   * This function resolves the body of the request by calling the function recursively for each item if it is an array,
   */
  const resolveBody = (body: any, context: Record<string, any>): any => {
    if (body === null || body === undefined) return body;

    if (Array.isArray(body))
      return body.map((item: any) => resolveBody(item, context));

    if (typeof body !== "object")
      return replaceContextPlaceholders(body, context) as string;

    const result: any = {};
    Object.entries(body).forEach(([key, value]) => {
      if (typeof value === "string") {
        result[replaceContextPlaceholders(key, context) as string] =
          replaceContextPlaceholders(value, context);
      } else if (value && (typeof value === "object" || Array.isArray(value))) {
        result[key] = resolveBody(value, context);
      } else {
        result[key] = value;
      }
    });
    return result;
  };

  const handleRestApi = async (
    apiCall: FunctionConfig,
    context: Record<string, any>,
    permitted: boolean=false,
  ) => {
    const { method, url, meUrl, body, format } = apiCall;
    const replacedUrl = replaceContextPlaceholders(url as string, context);
    const replacedMeUrl = replaceContextPlaceholders(meUrl as string, context);
    
    // Modify URL based on permission
    const effectiveUrl = permitted 
      ? replacedUrl 
      : replacedMeUrl;

    const resolvedBody: any = resolveBody(body, context) ?? {};
    const requestBody = parseBody(resolvedBody, format ?? "json");

    return await makeApiRequest(
      method ?? "GET",
      effectiveUrl as string,
      requestBody,
    );
  };

  const makeApiRequest = async (
    method: string,
    url: string,
    requestBody?: any,
  ) => {
    switch (method.toUpperCase()) {
      case "GET":
        return await requests.get(url);
      case "POST":
        return await requests.post(url, requestBody);
      case "PUT":
        return await requests.put(url, requestBody);
      case "DELETE":
        return await requests.del(url, { data: requestBody });
      case "PATCH":
        return await requests.patch(url, requestBody);
      default:
        throw new Error(`Unsupported HTTP method: ${method}`);
    }
  };

  const executeApiFunction = async (
    apiCall: FunctionConfig,
    customContext?: Record<string, any>,
    permitted: boolean = false,
  ) => {
    const effectiveContext = { ...customContext };
    switch (apiCall.type) {
      case "rest":
        return handleRestApi(apiCall, effectiveContext, permitted);
      case "function":
        return "function";
      default:
        throw new Error(`Invalid API call type: ${apiCall.type}`);
    }
  };

  return { executeApiFunction };
};
