import React, { useEffect, useState, useMemo } from "react";
import { cn } from "@/lib/utils";
import { validateField } from "../validators";
import { Label } from "@/components/ui/label";
import {
  evaluateConditions,
  evaluateRequiredOrConditions,
} from "@/components/builders/profileBuilder/utils/evaluate";
import { useProfileContext } from "../context/ProfileContext";
import DynamicInputField from "./inputField/DynamicInputField";
import {
  Edit3,
  FileUp,
  FlagIcon,
  Check,
  X,
  Loader2,
  CheckCircle,
  XCircle,
} from "lucide-react";
import { useAtom } from "jotai";
import { toastAtom } from "@/components/ui/toast/toast";
import parameterResolver from "../resolver/parameterResolver";
import { resolver, updateField } from "../resolver/resolver";

const FieldRenderer: React.FC<FieldRendererProps> = ({
  field,
  groupIndex,
  fieldIndex,
  onSaveField,
  onCancelEdit,
  onFlagField,
  columnCount,
  checkIfFieldIsFlagged,
  entityType,
  isAnyFieldLoading,
  setIsAnyFieldLoading,
}) => {
  const {
    fieldValues,
    updateFieldValue,
    editingField,
    setEditingField,
    getColSpanClass,
    hasPermissions,
    checkIfFieldNeedsReview,
  } = useProfileContext();

  const isSuperAdmin = useMemo(
    () => hasPermissions && hasPermissions(["super-admin"]),
    [hasPermissions],
  );
  const fieldKey = useMemo(
    () => resolver(fieldValues, field.key),
    [fieldValues, field.key],
  );
  const fieldValue = resolver(fieldValues, field.value);

  const [tempValue, setTempValue] = useState(fieldValue);
  const [validationErrors, setValidationErrors] = useState<string[]>([]);
  const [loading, setLoading] = useState(false);
  const [flagLoading, setFlagLoading] = useState(false);
  const [, setToast] = useAtom(toastAtom);
  const [isFlagged, setIsFlagged] = useState(checkIfFieldIsFlagged(field.id));
  const [approvalLoading, setApprovalLoading] = useState(false);
  const [showReview, setShowReview] = useState(
    checkIfFieldNeedsReview(field.id) && isSuperAdmin && !isFlagged,
  );

  // Update showReview when dependencies change
  useEffect(() => {
    setShowReview(checkIfFieldNeedsReview(field.id) && isSuperAdmin && !isFlagged);
  }, [checkIfFieldNeedsReview, field.id, isSuperAdmin, isFlagged]);

  // Handle toggle field logic
  if (field.type === "toggleField") {
    if (evaluateConditions(field.toggleCondition, fieldValues)) {
      field = { id: field.id, ...field.if };
    } else {
      field = { id: field.id, ...field.else };
    }
  }

  const required = evaluateRequiredOrConditions(
    field.required,
    fieldValues,
    hasPermissions,
  );

  const hidden = !evaluateConditions(
    field.visible,
    fieldValues,
    hasPermissions,
  );
  const flaggable = evaluateConditions(
    field.flaggable,
    fieldValues,
    hasPermissions,
  );

  const disableEditing = evaluateRequiredOrConditions(
    field.disableEditing,
    fieldValues,
    hasPermissions,
  );

  // For non-switch fields, use the editing state to reset tempValue when needed.
  useEffect(() => {
    if (editingField === field.id) {
      setTempValue(fieldValue);
      setValidationErrors([]);
    } else if (editingField !== null && editingField !== field.id) {
      setTempValue(fieldValue);
    }
  }, [editingField, fieldValue, field.id]);

  if (hidden) return null;

  const handleSave = async () => {
    setLoading(true);
    setIsAnyFieldLoading(true);
    const additionalParamsToEndpoint = parameterResolver(
      field,
      fieldValues,
      tempValue,
    );
    try {
      const errors = validateField(field, tempValue, fieldValues, required);
      if (errors.length > 0) {
        setValidationErrors(errors);
        setLoading(false);
        setIsAnyFieldLoading(false);
        return;
      }
      setValidationErrors([]);
   
      updateField(field.value, fieldValues, tempValue);
      const saveObj = {
        [fieldKey]: tempValue,
        ...additionalParamsToEndpoint,
      };

      // Handle review fields if needed.
      if (!isSuperAdmin) {
          await onFlagField(field.id, isSuperAdmin, true);
      } else {
        if (showReview) {
          await onFlagField(field.id, isSuperAdmin, false);
          setShowReview(false);
        } else if (isFlagged) {
          await onFlagField(field.id, isSuperAdmin, false);
        }
      }

      await onSaveField(saveObj, isSuperAdmin);
      setIsFlagged(false);
      setEditingField(null);
      setToast({
        status: "success",
        label: `${entityType.charAt(0).toUpperCase()}${entityType.slice(1)} Profile Updated`,
        message: `Successfully updated ${field.label.toLowerCase()}`,
      });
    } catch (error) {
      console.error("Error saving field:", error);
      setToast({
        status: "error",
        label: "Error",
        message: `Failed to update ${field.label.toLowerCase()}`,
      });
    } finally {
      setLoading(false);
      setIsAnyFieldLoading(false);
    }
  };

  const handleFlagging = async (isReviewField: boolean = false) => {
    setFlagLoading(true);
    setIsAnyFieldLoading(true);
    try {
      if (isReviewField) {
        await onFlagField(field.id, isSuperAdmin, true);
        setIsFlagged(true);
        setShowReview(false);
      } else {
        // For regular flagging, simply toggle the flag state.
        const isFlag = !isFlagged;
        await onFlagField(field.id, isSuperAdmin, isFlag);
        setIsFlagged(isFlag);
      }
      setToast({
        status: "success",
        label: isReviewField
          ? "Field Rejected"
          : isFlagged
            ? "Flag Removed"
            : "Field Flagged",
        message: isReviewField
          ? `Successfully rejected ${field.label.toLowerCase()}`
          : isFlagged
            ? `Successfully removed flag from ${field.label.toLowerCase()}`
            : `Successfully flagged ${field.label.toLowerCase()}`,
      });
    } catch (error) {
      console.error("Error flagging field:", error);
      setToast({
        status: "error",
        label: "Error",
        message: `Failed to update flag for ${field.label.toLowerCase()}`,
      });
    } finally {
      setFlagLoading(false);
      setIsAnyFieldLoading(false);
    }
  };

  const handleApproval = async () => {
    const type = "approve";
    setApprovalLoading(true);
    setIsAnyFieldLoading(true);
    try {
      await onFlagField(field.id, isSuperAdmin, false);
      setShowReview(false);
      setToast({
        status: "success",
        label: type === "approve" ? "Field Approved" : "Field Rejected",
        message: `Successfully ${type === "approve" ? "approved" : "rejected"} ${field.label.toLowerCase()}`,
      });
    } catch (error) {
      console.error(`Error ${type}ing field:`, error);
      setToast({
        status: "error",
        label: "Error",
        message: `Failed to ${type} ${field.label.toLowerCase()}`,
      });
    } finally {
      setIsAnyFieldLoading(false);
      setApprovalLoading(false);
    }
  };

  // For non-switch fields, allow switching to editing mode.
  const handleStartEditing = () => {
    if (isAnyFieldLoading) return;
    if (editingField && editingField !== field.id) {
      setTempValue(fieldValue);
    }
    setEditingField(field.id);
  };

  const handleCancel = () => {
    setTempValue(fieldValue);
    setValidationErrors([]);
    setEditingField(null);
    onCancelEdit();
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !loading) {
      e.preventDefault();
      handleSave();
    }
    if (e.key === 'Escape' && !loading) {
      e.preventDefault();
      handleCancel();
    }
  };

  const renderContent = () => {
    // For switch type, always render editable input without edit mode.
    if (field.type === "switch") {
      return (
        <div className="flex items-center">
          <DynamicInputField
            field={field}
            tempValue={fieldValue}
            handleChange={async (newValue: any) => {
              setTempValue(newValue);
              updateField(field.value, fieldValues, newValue);
              const additionalParamsToEndpoint = parameterResolver(
                field,
                fieldValues,
                newValue,
              );
              const saveObj = {
                [fieldKey]: newValue,
                ...additionalParamsToEndpoint,
              };
              try {
                setLoading(true);
                setIsAnyFieldLoading(true);
                // Remove flag if the field is flagged
                if (isFlagged) {
                  await onFlagField(field.id, isSuperAdmin, false);
                  setIsFlagged(false);
                }

                // Proceed with saving the updated switch value
                await onSaveField?.(saveObj, isSuperAdmin);

                setToast({
                  status: "success",
                  label: `${entityType.charAt(0).toUpperCase()}${entityType.slice(1)} Profile Updated`,
                  message: `Successfully updated ${field.label.toLowerCase()}`,
                });
              } catch (error) {
                console.error("Error saving switch field:", error);
                setToast({
                  status: "error",
                  label: "Error",
                  message: `Failed to update ${field.label.toLowerCase()}`,
                });
              } finally {
                setLoading(false);
                setIsAnyFieldLoading(false);
              }
            }}
            required={required}
            setTempValue={setTempValue}
            isDisplay={loading || isAnyFieldLoading}
            className="w-full"
          />
        </div>
      );
    }

    // If field type is file, render with separate editing and display modes.
    if (field.type === "file") {
      return (
        <div className="relative flex w-full flex-col">
          {editingField === field.id ? (
            <div className="relative flex w-full flex-col space-y-4">
              <div
                className={cn(
                  "flex-1 transition-opacity duration-300",
                  loading && "opacity-60",
                )}
              >
                                  <DynamicInputField
                    field={field}
                    tempValue={tempValue}
                    handleChange={setTempValue}
                    required={required}
                    setTempValue={setTempValue}
                    isDisplay={false}
                    className="h-10 w-full px-3 py-2 text-sm border-2 border-slate-200 rounded-lg focus:border-blue-500 focus:ring-2 focus:ring-blue-200 transition-all duration-200"
                    disabled={loading}
                    onKeyDown={handleKeyDown}
                  />
              </div>
                             <div className="flex items-center justify-end gap-2">
                 <button
                   onClick={handleSave}
                   disabled={loading}
                   className={cn(
                     "flex items-center gap-2 px-4 py-2 rounded-lg text-sm font-semibold transition-all duration-300 transform",
                     loading
                       ? "bg-slate-100 text-slate-400 cursor-not-allowed scale-95"
                       : "bg-gradient-to-r from-emerald-500 to-green-600 text-white hover:from-emerald-600 hover:to-green-700 shadow-lg hover:shadow-emerald-500/25 hover:scale-105 active:scale-95",
                   )}
                 >
                   {loading ? (
                     <Loader2 className="h-4 w-4 animate-spin" />
                   ) : (
                     <Check className="h-4 w-4" />
                   )}
                   {loading ? "Saving..." : "Save"}
                 </button>
                 <button
                   onClick={handleCancel}
                   disabled={loading}
                   className="flex items-center gap-2 px-4 py-2 rounded-lg text-sm font-semibold text-slate-600 bg-slate-100 hover:bg-slate-200 transition-all duration-300 transform hover:scale-105 active:scale-95 disabled:opacity-50 disabled:hover:bg-slate-100 disabled:hover:scale-100"
                 >
                   <X className="h-4 w-4" />
                   Cancel
                 </button>
               </div>
            </div>
          ) : (
            <>
              <div className="relative group">
                <div className="w-full">
                  <DynamicInputField
                    field={field}
                    tempValue={tempValue}
                    handleChange={setTempValue}
                    required={required}
                    setTempValue={setTempValue}
                    isDisplay={true}
                  />
                </div>
                <button
                  onClick={handleStartEditing}
                  disabled={isAnyFieldLoading}
                  className={cn(
                    "flex h-10 w-full items-center rounded-lg border-2 border-dashed border-slate-300 bg-gradient-to-br from-slate-50 to-slate-100 px-3 py-2 transition-all duration-300 hover:border-blue-400 hover:bg-gradient-to-br hover:from-blue-50 hover:to-slate-50 hover:shadow-md group",
                    isAnyFieldLoading &&
                      "cursor-not-allowed opacity-50 hover:border-slate-300 hover:bg-gradient-to-br hover:from-slate-50 hover:to-slate-100",
                  )}
                >
                  <div className="flex items-center justify-center w-8 h-8 rounded-lg bg-blue-100 group-hover:bg-blue-200 transition-colors duration-200 mr-3">
                    <FileUp className="h-4 w-4 text-blue-600" />
                  </div>
                  <div className="flex-1 text-left">
                    <div className="text-sm font-medium text-slate-700 group-hover:text-blue-700 transition-colors duration-200">
                      {tempValue?.fileName || tempValue?.name || "Upload file"}
                    </div>
                    <div className="text-xs text-slate-500 group-hover:text-blue-500 transition-colors duration-200">
                      Click to {tempValue?.fileName || tempValue?.name ? "change" : "select"} file
                    </div>
                  </div>
                  <Edit3 className="ml-3 h-4 w-4 text-slate-400 group-hover:text-blue-500 transition-colors duration-200" />
                </button>
              </div>
            </>
          )}
        </div>
      );
    }

    // For non-switch, non-file types.
          return (
        <div className="relative flex min-h-[40px] w-full min-w-0 items-center">
        {editingField === field.id ? (
          <div className="w-full min-w-0">
                          <div
                className={cn(
                  "relative flex w-full min-w-0 flex-col gap-3",
                  field.type === "textarea" ? "items-start" : "items-center",
                )}
              >
              <div className="w-full min-w-0">
                <div
                  className={cn(
                    "w-full min-w-0 transition-opacity duration-300",
                    loading && "opacity-60",
                  )}
                >
                  <DynamicInputField
                    field={field}
                    tempValue={tempValue}
                    handleChange={setTempValue}
                    required={required}
                    setTempValue={setTempValue}
                    isDisplay={false}
                    className={cn(
                      "w-full min-w-0 px-3 py-2 text-sm border-2 border-slate-200 rounded-lg focus:border-blue-500 focus:ring-2 focus:ring-blue-200 transition-all duration-200 bg-white hover:border-slate-300",
                      field.type !== "textarea" && "h-10",
                    )}
                    disabled={loading}
                    onKeyDown={handleKeyDown}
                  />
                </div>
              </div>
              <div className="flex w-full items-center justify-end gap-2">
                <button
                  onClick={handleSave}
                  disabled={loading}
                  className={cn(
                    "flex items-center gap-2 px-4 py-2 rounded-lg text-sm font-semibold transition-all duration-300 transform",
                    loading
                      ? "bg-slate-100 text-slate-400 cursor-not-allowed scale-95"
                      : "bg-gradient-to-r from-emerald-500 to-green-600 text-white hover:from-emerald-600 hover:to-green-700 shadow-lg hover:shadow-emerald-500/25 hover:scale-105 active:scale-95",
                  )}
                >
                  {loading ? (
                    <Loader2 className="h-4 w-4 animate-spin" />
                  ) : (
                    <Check className="h-4 w-4" />
                  )}
                  {loading ? "Saving..." : "Save"}
                </button>
                <button
                  onClick={handleCancel}
                  disabled={loading}
                  className="flex items-center gap-2 px-4 py-2 rounded-lg text-sm font-semibold text-slate-600 bg-slate-100 hover:bg-slate-200 transition-all duration-300 transform hover:scale-105 active:scale-95 disabled:opacity-50 disabled:hover:bg-slate-100 disabled:hover:scale-100"
                >
                  <X className="h-4 w-4" />
                  Cancel
                </button>
              </div>
            </div>
          </div>
        ) : field?.type !== "display" ? (
          <>
            <div className="min-w-0 flex-1">
              <button
                onClick={() => {
                  if (!disableEditing) {
                    handleStartEditing();
                  }
                }}
                disabled={isAnyFieldLoading || disableEditing === true}
                className={cn(
                  "group relative flex h-10 w-full min-w-0 items-center overflow-hidden rounded-lg border-2 border-slate-200 bg-gradient-to-br from-white to-slate-50 px-3 py-2 transition-all duration-300 hover:border-blue-300 hover:bg-gradient-to-br hover:from-blue-50 hover:to-white hover:shadow-md",
                  isAnyFieldLoading &&
                    "cursor-not-allowed opacity-50 hover:border-slate-200 hover:bg-gradient-to-br hover:from-white hover:to-slate-50",
                  disableEditing && "cursor-not-allowed bg-slate-50 hover:border-slate-200 hover:bg-slate-50",
                )}
              >
                <div className="w-full min-w-0 overflow-hidden pr-2">
                  <DynamicInputField
                    field={field}
                    tempValue={fieldValue}
                    handleChange={setTempValue}
                    required={required}
                    setTempValue={setTempValue}
                    isDisplay={true}
                  />
                </div>
                {!disableEditing && (
                  <div className="flex items-center bg-gradient-to-br from-white to-slate-50 pl-2 group-hover:from-blue-50 group-hover:to-white transition-all duration-300">
                    <div className="flex items-center justify-center w-8 h-8 rounded-lg bg-slate-100 group-hover:bg-blue-100 transition-colors duration-200">
                      <Edit3 className="h-4 w-4 text-slate-500 group-hover:text-blue-600 transition-colors duration-200" />
                    </div>
                  </div>
                )}
              </button>
            </div>
          </>
        ) : (
          <div className="flex h-10 w-full min-w-0 items-center overflow-hidden rounded-lg border-2 border-slate-200 bg-slate-50 px-3 py-2">
            <div className="w-full min-w-0">
              <DynamicInputField
                field={field}
                tempValue={tempValue}
                handleChange={setTempValue}
                required={required}
                setTempValue={setTempValue}
                isDisplay={true}
              />
            </div>
          </div>
        )}
      </div>
    );
  };

  return (
    <div
      className={cn(
        "group relative flex flex-col gap-2 p-1 sm:p-4 h-fit rounded-xl bg-white border border-slate-100 hover:border-slate-300 transition-all duration-300 hover:shadow-lg",
        getColSpanClass(field.span, columnCount),
        isFlagged && "border-red-300 bg-red-50/50 shadow-red-100/50",
        showReview && "border-amber-300 bg-amber-50/50 shadow-amber-100/50",
        loading && "animate-pulse",
      )}
    >
      <div className="flex min-h-[24px] w-full flex-wrap items-start justify-between gap-1 sm:flex-nowrap sm:items-center">
        <div className="min-w-[160px] flex-1">
          <Label
            htmlFor={field.id}
            className={cn(
              "flex items-center gap-2 text-sm font-medium leading-tight text-slate-700 sm:text-sm md:text-base",
              required && "font-semibold",
            )}
          >
            {field.label}
            {required && <span className="text-red-500 text-lg">*</span>}
          </Label>
        </div>

        {/* Only show flag/edit buttons for non-switch fields */}
        {field.type !== "switch" && (
          <div className="flex shrink-0 justify-end gap-2">
            {!editingField && (
              <>
                {showReview && !isFlagged ? (
                  <div className="flex items-center gap-2">
                    <button
                      type="button"
                      disabled={approvalLoading || isAnyFieldLoading}
                      onClick={() => handleApproval()}
                      className={cn(
                        "flex h-8 w-8 items-center justify-center rounded-lg transition-all duration-300 transform",
                        approvalLoading || isAnyFieldLoading
                          ? "cursor-not-allowed bg-slate-100 text-slate-400 hover:bg-slate-100 scale-95"
                          : "bg-gradient-to-r from-emerald-500 to-green-600 text-white hover:from-emerald-600 hover:to-green-700 shadow-lg hover:shadow-emerald-500/25 hover:scale-110 active:scale-95",
                      )}
                    >
                      {approvalLoading ? (
                        <Loader2 className="h-4 w-4 animate-spin" />
                      ) : (
                        <CheckCircle className="h-4 w-4" />
                      )}
                    </button>
                    <button
                      type="button"
                      disabled={approvalLoading || isAnyFieldLoading}
                      onClick={() => handleFlagging(true)}
                      className={cn(
                        "flex h-8 w-8 items-center justify-center rounded-lg transition-all duration-300 transform",
                        approvalLoading || isAnyFieldLoading
                          ? "cursor-not-allowed bg-slate-100 text-slate-400 hover:bg-slate-100 scale-95"
                          : "bg-gradient-to-r from-red-500 to-rose-600 text-white hover:from-red-600 hover:to-rose-700 shadow-lg hover:shadow-red-500/25 hover:scale-110 active:scale-95",
                      )}
                    >
                      {approvalLoading ? (
                        <Loader2 className="h-4 w-4 animate-spin" />
                      ) : (
                        <XCircle className="h-4 w-4" />
                      )}
                    </button>
                  </div>
                ) : flaggable ? (
                                      <button
                      type="button"
                      disabled={flagLoading || isAnyFieldLoading}
                      onClick={() => handleFlagging(false)}
                      className={cn(
                        "flex h-8 w-8 items-center justify-center rounded-lg transition-all duration-300 transform",
                        isFlagged
                          ? "opacity-100"
                          : "opacity-100 md:opacity-0 md:group-hover:opacity-100",
                        flagLoading || isAnyFieldLoading
                          ? "bg-slate-100 text-slate-400 hover:bg-slate-100 scale-95"
                          : isFlagged
                          ? "bg-gradient-to-r from-red-500 to-rose-600 text-white shadow-lg hover:shadow-red-500/25"
                          : "bg-slate-100 text-slate-500 hover:bg-gradient-to-r hover:from-red-500 hover:to-rose-600 hover:text-white hover:shadow-lg hover:shadow-red-500/25",
                        "hover:scale-110 active:scale-95",
                        isAnyFieldLoading && "cursor-not-allowed opacity-50 hover:scale-100",
                      )}
                    >
                    {flagLoading ? (
                      <Loader2 className="h-4 w-4 animate-spin" />
                    ) : (
                      <FlagIcon className="h-4 w-4" />
                    )}
                  </button>
                ) : null}
              </>
            )}
          </div>
        )}
      </div>

      <div className="h-fit">{renderContent()}</div>

      {(validationErrors.length > 0 || isFlagged || showReview) && (
        <div
          className={cn(
            "mt-1 px-3 py-2 rounded-lg text-sm font-medium",
            showReview 
              ? "text-amber-700 bg-amber-100 border border-amber-200" 
              : "text-red-700 bg-red-100 border border-red-200",
          )}
        >
          {validationErrors.length > 0
            ? validationErrors.join(" • ")
            : showReview
              ? `${field.label} Needs Review`
              : `${field.label} Requires Correction`}
        </div>
      )}
    </div>
  );
};

export default FieldRenderer;
