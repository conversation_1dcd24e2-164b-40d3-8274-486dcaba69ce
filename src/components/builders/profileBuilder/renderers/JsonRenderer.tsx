import {
  Card,
  CardContent,
  CardDescription,
  Card<PERSON>eader,
  CardTitle,
} from "@/components/ui/card";
import <PERSON><PERSON>enderer from "./FieldRenderer";
import { useEffect, useState } from "react";
import { useProfileContext } from "../context/ProfileContext";

const JsonRenderer: React.FC<any> = ({
  config,
  handleSaveField,
  handleCancelEdit,
  handleFlagField,
  checkIfFieldIsFlagged,
  columnCount,
}) => {
  const [isAnyFieldLoading, setIsAnyFieldLoading] = useState(false);
  const { fieldValues } = useProfileContext();

  useEffect(() => {
    if (!fieldValues.active) {
      setIsAnyFieldLoading(true);
    }
  }, [fieldValues.active]);

  return (
    <div className="w-full max-w-full overflow-hidden">
      {!fieldValues.active && (
        <div className="bg-red-600 px-1 py-0.5 text-center text-white rounded-md mb-4">
          Account is not active. If this is a mistake please contact your local
          city clerk&apos;s office.
        </div>
      )}
      {config.profile.groups
        .sort((a: any, b: any) => a.order - b.order)
        .map((group: any, index: number) => (
          <Card key={group.title} className="mb-6">
            <CardHeader className="px-4 sm:px-6">
              <CardTitle className="text-lg sm:text-xl">{group.title}</CardTitle>
              <CardDescription className="text-sm">{group.description}</CardDescription>
            </CardHeader>
            <CardContent className="grid w-full grid-cols-1 sm:grid-cols-6 lg:grid-cols-12 gap-3 px-4 sm:px-6">
              {group.fields.map((field: ProfileField, fieldIndex: number) => (
                <FieldRenderer
                  key={field.id}
                  field={field}
                  groupIndex={index}
                  fieldIndex={fieldIndex}
                  onSaveField={handleSaveField}
                  onCancelEdit={handleCancelEdit}
                  onFlagField={handleFlagField}
                  checkIfFieldIsFlagged={checkIfFieldIsFlagged}
                  columnCount={columnCount}
                  entityType={config.profile.entityType}
                  isAnyFieldLoading={isAnyFieldLoading}
                  setIsAnyFieldLoading={setIsAnyFieldLoading}
                />
              ))}
            </CardContent>
          </Card>
        ))}
    </div>
  );
};

export default JsonRenderer;
