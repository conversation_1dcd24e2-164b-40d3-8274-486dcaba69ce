import { Input } from "@/components/ui/input";
import { useEffect, useRef } from "react";

const TextInputField: React.FC<InputFieldProps> = ({
  field,
  tempValue,
  handleChange,
  required,
  setTempValue,
  isDisplay = true,
  type = "text",
  onKeyDown,
}) => {
  const inputRef = useRef<HTMLInputElement>(null);
  const displayValue = field?.display?.[String(tempValue)] || tempValue || "";
  
  useEffect(() => {
    if (!isDisplay && inputRef.current) {
      inputRef.current.focus();
    }
  }, [isDisplay]);

  return (
    <div className="w-full min-w-0">
      {isDisplay ? (
        <div className="w-full min-w-0">
          <span 
            className="block w-full text-left py-1 text-xs sm:text-xs md:text-base text-gray-700 truncate whitespace-nowrap overflow-hidden"
            title={displayValue}
          >
            {displayValue}
          </span>
        </div>
      ) : (
        <div className="w-full min-w-0">
          <Input
            id={field.id}
            value={tempValue}
            onChange={(e) => handleChange(e.target.value)}
            onBlur={(e) => handleChange(e.target.value.trim())}
            onKeyDown={onKeyDown}
            required={required}
            placeholder={field.placeholder || "Enter text"}
            className="w-full min-w-0 text-xs px-3 overflow-hidden text-ellipsis h-9"
            style={{
              textOverflow: 'ellipsis',
              whiteSpace: 'nowrap'
            }}
            title={displayValue}
            type={type}
            ref={inputRef}
          />
        </div>
      )}
    </div>
  );
};

export default TextInputField;
