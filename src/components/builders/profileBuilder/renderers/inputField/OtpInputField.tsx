import {
  InputOTP,
  InputOTPGroup,
  InputOTPSlot,
} from "@/components/ui/input-otp";
import { REGEXP_ONLY_DIGITS_AND_CHARS } from "input-otp";
import { useEffect, useRef } from "react";

interface InputFieldProps {
  field: {
    length: number;
    placeholder?: string;
    display?: { [key: string]: string };
  };
  tempValue: string;
  handleChange?: (value: string) => void;
  required?: boolean;
  setTempValue: (value: string) => void;
  isDisplay: boolean;
}

const OtpInputField: React.FC<InputFieldProps> = ({
  field,
  tempValue,
  handleChange,
  required,
  setTempValue,
  isDisplay,
}) => {
  const inputRef = useRef<HTMLInputElement>(null);

  useEffect(() => {
    if (!isDisplay && inputRef.current) {
      inputRef.current.focus();
      inputRef.current.setSelectionRange(0, 0);
    }
  }, [isDisplay]);

  return (
    <>
      {isDisplay ? (
        <span className="block w-full text-left py-1 text-xs sm:text-xs md:text-base text-gray-700 truncate whitespace-nowrap overflow-hidden">
          {field?.display?.[String(tempValue)] || tempValue || ""}
        </span>
      ) : (
        <div className="w-full max-w-full overflow-x-auto px-2 py-1">
          <InputOTP
            pattern={REGEXP_ONLY_DIGITS_AND_CHARS}
            pushPasswordManagerStrategy="none"
            maxLength={field.length}
            value={tempValue}
            onChange={(value) => {
              const newValue = value.toUpperCase();
              setTempValue(newValue);
              if (handleChange) handleChange(newValue);
            }}
            placeholder={
              field.placeholder || `Enter ${field.length} characters`
            }
            ref={inputRef}
          >
            <InputOTPGroup className="flex w-full items-center justify-start gap-0">
              {Array.from({ length: field.length }).map((_, index) => (
                <InputOTPSlot
                  key={index}
                  index={index}
                  className="flex-1 min-w-10 h-12 px-3 py-2 text-lg font-medium text-center bg-white shadow-sm border border-gray-300 focus:border-black focus:z-10 transition-all duration-200 outline-none"
                />
              ))}
            </InputOTPGroup>
          </InputOTP>
        </div>
      )}
    </>
  );
};

export default OtpInputField;