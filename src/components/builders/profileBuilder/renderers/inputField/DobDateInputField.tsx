import React, { useState, useEffect, useRef } from 'react';
import { cn } from "@/lib/utils";
import { format, parseISO } from 'date-fns';

interface DateInputFieldProps {
  field: {
    id: string;
    name?: string;
    label: string;
    formatPattern?: string; // Format like "MMMM dd, yyyy" or "MM/dd/yyyy"
  };
  tempValue: string;
  handleChange: (name: string, value: string) => void;
  setTempValue?: (value: string) => void;
  isDisplay?: boolean;
  required?: boolean;
  className?: string;
  disabled?: boolean;
}

const DateInputField: React.FC<DateInputFieldProps> = ({
  field,
  tempValue,
  handleChange,
  setTempValue,
  isDisplay = false,
  required = false,
  className,
  disabled = false,
}) => {
  const inputRef = useRef<HTMLInputElement>(null);
  
  // Convert yyyy-mm-dd to Date object for the input
  const parseDate = (dateStr: string): string => {
    if (!dateStr || dateStr.length !== 10) return '';
    return dateStr; // Native date input expects yyyy-mm-dd format
  };

  const [dateValue, setDateValue] = useState(parseDate(tempValue));
  
  // Get current year for validation
  const currentYear = new Date().getFullYear();
  const minDate = "1900-01-01"; // Set a reasonable minimum date
  const maxDate = `${currentYear}-12-31`; // Set maximum date to end of current year

  useEffect(() => {
    if (tempValue) {
      setDateValue(parseDate(tempValue));
    } else {
      setDateValue('');
    }
  }, [tempValue]);

  // Focus handler to position cursor at the beginning (day part)
  const handleFocus = () => {
    if (inputRef.current) {
      // For date inputs, we can't directly control cursor position
      // But we can ensure the input is focused
      inputRef.current.focus();
    }
  };

  const formatDateForDisplay = (dateString: string) => {
    if (!dateString || dateString.length !== 10) return dateString;
    
    try {
      const date = parseISO(dateString);
      
      const formatPattern = field.formatPattern || 'MMMM dd, yyyy';
      
      if (formatPattern === 'MMMM dd, yyyy') {
        return format(date, 'MMMM dd, yyyy');
      } else if (formatPattern === 'MM/dd/yyyy') {
        return format(date, 'MM/dd/yyyy');
      } else if (formatPattern === 'dd/MM/yyyy') {
        return format(date, 'dd/MM/yyyy');
      }
      
      return dateString;
    } catch (e) {
      return dateString;
    }
  };

  const handleDateChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setDateValue(value);
    
    if (value) {
      handleChange(field.id, value);
      if (setTempValue) {
        setTempValue(value);
      }
    } else {
      handleChange(field.id, '');
      if (setTempValue) {
        setTempValue('');
      }
    }
  };

  if (isDisplay) {
    const displayValue = formatDateForDisplay(tempValue) || "";
    return (
      <div className="w-full min-w-0">
        <span 
          className="block w-full truncate text-left text-xs text-gray-700 sm:text-xs md:text-base" 
          title={displayValue}
        >
          {displayValue}
        </span>
      </div>
    );
  }

  return (
    <div className="w-full min-w-0">
      <input
        ref={inputRef}
        type="date"
        id={field.id}
        name={field.id}
        value={dateValue}
        min={minDate}
        max={maxDate}
        onChange={handleDateChange}
        onFocus={handleFocus}
        className={cn(
          "block w-full min-w-0 rounded-md border px-2",
          "border-gray-300 focus:border-indigo-500 focus:ring-indigo-500",
          "focus:outline-none text-xs sm:text-base",
          "disabled:cursor-not-allowed disabled:opacity-50",
          "h-9 py-1 overflow-hidden text-ellipsis",
          "[&::-webkit-calendar-picker-indicator]:hidden",
          className 
        )}
        required={required}
        disabled={disabled}
        placeholder="DD-MM-YYYY"
        style={{
          textOverflow: 'ellipsis',
          whiteSpace: 'nowrap'
        }}
        title={dateValue}
        data-field={field.id}
      />
    </div>
  );
};

export default DateInputField;