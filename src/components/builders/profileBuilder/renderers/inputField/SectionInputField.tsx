import { useMemo, useState, useEffect, useRef } from "react";
import { Input } from "@/components/ui/input";
import { useGetSettingsByOption } from "@/hooks/api/useAdmin";
import { ChevronDown, Trash2 } from "lucide-react";
import { performTokenizedSearch, SearchableOption } from "../../utils/SearchTokenizer";

interface OptionType extends SearchableOption {
  [key: string]: any;
}

interface InputFieldProps {
  field: {
    options: OptionType[] | string;
    display?: Record<string, string>;
    searchKeys?: string[];
    placeholder?: string;
  };
  tempValue: string | number;
  handleChange: (value: string) => void;
  required?: boolean;
  setTempValue: (value: string | number) => void;
  isDisplay: boolean;
}

const SectionInputField: React.FC<InputFieldProps> = ({
  field,
  tempValue,
  handleChange,
  required,
  setTempValue,
  isDisplay,
}) => {
  const [searchQuery, setSearchQuery] = useState("");
  const [isSearching, setIsSearching] = useState(false);
  const [isOpen, setIsOpen] = useState(false);
  const dropdownRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLInputElement>(null);

  const dynamicParams = useMemo(() => {
    if (
      typeof field.options === "string" &&
      field.options.startsWith("{{") &&
      field.options.endsWith("}}")
    ) {
      const content = field.options.slice(2, -2).trim();
      if (content.startsWith("settings:")) {
        const parts = content.slice("settings:".length).trim().split(".");
        if (parts.length === 3) return parts;
      }
    }
    return null;
  }, [field.options]);

  const { data: dynamicOptions, isLoading: dynamicOptionsIsLoading } =
    useGetSettingsByOption(
      dynamicParams ? dynamicParams[0] : "",
      dynamicParams ? dynamicParams[1] : "",
      dynamicParams ? dynamicParams[2] : "",
    );

  const filteredOptions = useMemo(() => {
    if (isSearching) {
      const searchableOptions = Array.isArray(field.options) 
        ? field.options 
        : dynamicOptions || [];
      
      return performTokenizedSearch(searchableOptions, searchQuery, {
        searchKeys: field.searchKeys,
        matchMode: 'includes'
      });
    }
    return Array.isArray(field.options) ? field.options : dynamicOptions || [];
  }, [field.options, dynamicOptions, searchQuery, isSearching, field.searchKeys]);

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsOpen(false);
        setIsSearching(false);
        setSearchQuery("");
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);

  // Add new effect to handle initial dropdown state
  useEffect(() => {
    if (!isDisplay) {
      // When entering edit mode, show dropdown
      setIsOpen(true);
      setIsSearching(false);
      if (inputRef.current) inputRef.current.focus();
    } else {
      // When returning to display mode, reset states and blur input
      setIsOpen(false);
      setIsSearching(false);
      setSearchQuery("");
      setTempValue("");
      if (inputRef.current) inputRef.current.blur();
    }
  }, [isDisplay, setTempValue]);

  const handleOptionClick = (value: string) => {
    handleChange(value);
    setTempValue(value);
    setIsOpen(false);
    setIsSearching(false);
    setSearchQuery("");
    inputRef.current?.blur();
  };

  const toggleDropdown = (e: React.MouseEvent) => {
    e.stopPropagation(); // Prevent event from bubbling
    const newState = !isOpen;
    setIsOpen(newState);
    if (!newState) {
      setIsSearching(false);
      setSearchQuery("");
      inputRef.current?.blur();
    }
  };

  // Handle input click
  const handleInputClick = () => {
    if (!isOpen) {
      setIsOpen(true);
      setIsSearching(true);
      inputRef.current?.focus();
    }
  };

  // Find the currently selected option label
  const getSelectedLabel = () => {
    let selectedOption;
    
    if (Array.isArray(field.options)) {
      selectedOption = field.options.find(opt => String(opt.value) === String(tempValue));
    } else if (Array.isArray(dynamicOptions)) {
      selectedOption = dynamicOptions.find(opt => String(opt.value) === String(tempValue));
    }
    
    return field?.display?.[String(tempValue)] || 
           (selectedOption ? selectedOption.label : tempValue) || 
           "";
  };

  const handleClear = (e: React.MouseEvent) => {
    e.stopPropagation();
    handleChange("");
    setTempValue("");
    setIsOpen(false);
    setIsSearching(false);
    setSearchQuery("");
    if (inputRef.current) inputRef.current.blur();
  };

  return (
    <div className="w-full">
      {isDisplay ? (
        <span className="block w-full text-left py-1 sm:text-sm md:text-base text-gray-700 truncate whitespace-nowrap overflow-hidden">
          {getSelectedLabel()}
        </span>
      ) : (
        <div className="relative w-full overflow-visible" ref={dropdownRef}>
          <div className="relative">
            <div className="flex items-center w-full border border-gray-300 rounded-md">
              <Input
                ref={inputRef}
                placeholder={field.placeholder || "Search options..."}
                value={isSearching ? searchQuery : (tempValue ? getSelectedLabel() : "")}
                onChange={(e) => {
                  setSearchQuery(e.target.value);
                  setIsSearching(true);
                  if (!isOpen) setIsOpen(true);
                }}
                onClick={handleInputClick}
                className="w-full border-0 rounded-md px-3 py-1.5 text-sm md:text-base focus-visible:ring-0 focus-visible:ring-offset-0"
              />
              <div className="flex items-center gap-1 pr-2">
                {tempValue && !isSearching && (
                  <button
                    type="button"
                    onClick={handleClear}
                    className="p-1 hover:bg-gray-100 rounded-full"
                  >
                    <Trash2 className="h-4 w-4 text-gray-500" />
                  </button>
                )}
                <button 
                  type="button"
                  onClick={toggleDropdown}
                  className="cursor-pointer"
                >
                  <ChevronDown className={`h-4 w-4 transition-transform ${isOpen ? 'rotate-180' : ''}`} />
                </button>
              </div>
            </div>
          </div>
          
          {isOpen && (
            <div className="absolute z-10 w-full mt-1 max-h-60 overflow-y-auto bg-white rounded-md shadow-lg border">
              {dynamicOptionsIsLoading && filteredOptions.length === 0 ? (
                <div className="px-4 py-2 text-sm sm:text-sm text-gray-500">
                  Loading options...
                </div>
              ) : filteredOptions.length > 0 ? (
                filteredOptions.map((option: OptionType) => (
                  <button
                    key={String(option.value)}
                    type="button"
                    className={`w-full px-4 py-2 text-left text-sm sm:text-sm hover:bg-gray-100 ${
                      String(tempValue) === String(option.value) ? 'bg-gray-100' : ''
                    }`}
                    onClick={() => handleOptionClick(String(option.value))}
                  >
                    {option.label}
                  </button>
                ))
              ) : (
                <div className="px-4 py-2 text-sm sm:text-sm text-gray-500">
                  No matching options
                </div>
              )}
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default SectionInputField;