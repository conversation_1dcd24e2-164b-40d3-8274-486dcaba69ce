import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { Calendar } from "@/components/ui/calendar";
import { CalendarIcon } from "lucide-react";
import { format } from "date-fns";
import { Button } from "@/components/ui/button";

const DateInputField: React.FC<InputFieldProps> = ({
  field,
  tempValue,
  handleChange,
  required,
  setTempValue,
  isDisplay,
}) => {
  return (
    <>
      {isDisplay ? (
        <span className="block w-full text-left py-1 text-xs sm:text-xs md:text-base text-gray-700 truncate whitespace-nowrap overflow-hidden">
          {tempValue
            ? format(new Date(tempValue), field.formatPattern || "PPP")
            : ""}
        </span>
      ) : (
        <Popover>
          <PopoverTrigger asChild>
            <Button
              id={field.id}
              data-field={field.id}
              variant="outline"
              className={`w-full justify-start text-left border border-gray-300 bg-white ${
                !tempValue ? "text-muted-foreground" : ""
              }`}
            >
              <CalendarIcon className="mr-2 h-4 w-4" />
              {tempValue
                ? format(new Date(tempValue), field.formatPattern || "PPP")
                : field.placeholder || "Pick a date"}
            </Button>
          </PopoverTrigger>
          <PopoverContent className="w-auto p-0" align="start">
            <Calendar
              mode="single"
              defaultMonth={tempValue ? new Date(tempValue) : undefined}
              selected={tempValue ? new Date(tempValue) : undefined}
              onSelect={(date) =>
                handleChange(date ? format(date, "yyyy-MM-dd") : null)
              }
              required={required}
            />
          </PopoverContent>
        </Popover>
      )}
    </>
  );
};

export default DateInputField;
