import { useProfileContext } from "../../context/ProfileContext";
import { cn } from "@/lib/utils";
import { FileUp, Trash2 } from "lucide-react";
import Image from "next/image";
import RenderDownloadedFile from "@/app/(app)/(realm)/(protected)/(resident)/(registered)/resident/[entityType]/[entityId]/components/RenderDownloadedFile";
import { useState, useEffect, memo, useRef, useCallback } from "react";
import { useDropzone } from "react-dropzone";

// Create a separate component for the file preview to isolate it from parent rerenders
const FilePreview = memo(
  ({
    tempValue,
    preview,
  }: {
    tempValue: any;
    preview: string | null;
  }) => {
    if (!tempValue) return null;

    return (
      <div className="overflow-hidden rounded-lg border border-gray-200 bg-gray-50/50 p-3 hidden sm:block">
        <div className="mx-auto flex w-full max-w-[600px] flex-col items-center justify-center">
          {tempValue?.type ? (
            tempValue?.type === "application/pdf" ? (
              <div className="relative h-[300px] w-full sm:h-[200px]">
                <object
                  data={URL.createObjectURL(tempValue)}
                  type="application/pdf"
                  className="h-full w-full rounded-md"
                >
                  <div className="flex items-center gap-2 rounded-md bg-gray-50 p-4">
                  <FileUp className="text-muted-foreground pointer-events-none h-4 sm:h-5 md:h-6 lg:h-6 xl:h-7 shrink-0" />
                    <p className="text-muted-foreground text-xs">
                      PDF preview not available.{" "}
                      <a
                        href={URL.createObjectURL(tempValue)}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="text-primary hover:underline"
                      >
                        Open PDF
                      </a>
                    </p>
                  </div>
                </object>
              </div>
            ) : tempValue?.type?.startsWith("image/") ? (
              <div className="relative flex min-h-[200px] w-full items-center justify-center">
                <Image
                  src={preview || URL.createObjectURL(tempValue)}
                  alt="Preview"
                  width={400}
                  height={300}
                  className="max-h-[300px] w-auto rounded-md object-contain sm:max-h-[200px] sm:w-full"
                  style={{ objectFit: "contain" }}
                />
              </div>
            ) : (
              <div className="flex w-full items-center gap-2 rounded-md bg-white p-3">
                {/* <FileUp className="text-primary h-4 sm:h-5 md:h-6 lg:h-6 xl:h-7" /> */}
                <span className="text-muted-foreground text-xs">
                  {tempValue?.name}
                </span>
              </div>
            )
          ) : (
            <div className="w-full">
              <RenderDownloadedFile
                downloadObject={tempValue}
                classNameLoading="p-2"
                className="flex items-center justify-center rounded-md bg-white"
              />
            </div>
          )}
        </div>
      </div>
    );
  },
  (prevProps, nextProps) => {
    return prevProps.tempValue === nextProps.tempValue && prevProps.preview === nextProps.preview;
  }
);

FilePreview.displayName = "FilePreview";

// Create a separate component for the file input to isolate it from parent rerenders
const FileInput = memo(
  ({
    field,
    tempValue,
    handleFileSelect,
    handleRemove,
    required,
    placeholder,
    showRemove,
  }: {
    field: any;
    tempValue: any;
    handleFileSelect: (file: File) => void;
    handleRemove: (e: React.MouseEvent) => void;
    required: boolean;
    placeholder: string;
    showRemove: boolean;
  }) => {
    const displayValue = tempValue?.fileName ?? tempValue?.name ?? "No file uploaded";

    const acceptTypes = (field.accept || []).reduce((acc: Record<string, string[]>, type: string) => {
      if (type.startsWith('.')) {
        acc['application/octet-stream'] = [...(acc['application/octet-stream'] || []), type];
      } else {
        acc[type] = [];
      }
      return acc;
    }, {});

    const onDrop = useCallback((acceptedFiles: File[]) => {
      if (!acceptedFiles.length) return;
      const file = acceptedFiles[0];
      if (file.size > field.maxSize) {
        return;
      }
      const allowedTypes = field.accept || [];
      const isMimeAllowed = allowedTypes.includes(file.type);
      const isExtAllowed = allowedTypes.some((type: any) =>
        type.startsWith('.') && file.name.toLowerCase().endsWith(type)
      );
      if (isMimeAllowed || isExtAllowed) {
        handleFileSelect(file);
      }
    },[handleFileSelect, field.accept, field.maxSize]);

    const { getRootProps, getInputProps, isDragActive } = useDropzone({
      onDrop,
      multiple: false,
      accept: acceptTypes,
    });

    return (
      <div {...getRootProps({
        className: cn(
          "group relative flex w-full items-center gap-3 rounded-lg border-2 border-dashed border-gray-300 bg-white px-4 py-3 transition-all duration-200 hover:border-gray-400 cursor-pointer",
          isDragActive ? "border-gray-400 bg-gray-50" : ""
        )
      })}>
        <input {...getInputProps()} id={field.id} required={required} />
        <div className="flex h-12 w-12 items-center justify-center rounded-full bg-gray-100 text-gray-500">
          <FileUp className="h-6 w-6" />
        </div>
        <div className="flex flex-1 flex-col items-start">
          <span className="text-base font-medium text-gray-700">
            {displayValue && displayValue !== "No file uploaded"
              ? displayValue
              : placeholder}
          </span>
          <span className="text-sm text-gray-500">
            Drag & drop or click to browse
          </span>
        </div>
        {showRemove && (
          <button
            type="button"
            className="flex h-8 w-8 items-center justify-center rounded-full bg-red-100 text-red-600 hover:bg-red-200 transition-colors duration-200 ml-2"
            onClick={handleRemove}
          >
            <Trash2 className="h-4 w-4" />
          </button>
        )}
      </div>
    );
  },
  (prevProps, nextProps) => {
    return prevProps.tempValue === nextProps.tempValue;
  }
);

FileInput.displayName = "FileInput";

const FileInputField: React.FC<InputFieldProps> = ({
  field,
  tempValue,
  handleChange,
  required,
  setTempValue,
  isDisplay,
}) => {
  const { editingField } = useProfileContext();
  const [preview, setPreview] = useState<string | null>(null);
  const [isEditing, setIsEditing] = useState(false);

  const tempValueRef = useRef(tempValue);
  const editingFieldRef = useRef(editingField);

  const isValidFile = tempValue && !(tempValue instanceof File && tempValue.name === "");

  useEffect(() => {
    tempValueRef.current = tempValue;
    editingFieldRef.current = editingField;
  }, [tempValue, editingField]);

  useEffect(() => {
    if (editingField !== field.id) {
      setIsEditing(false);
      return;
    }

    setIsEditing(true);

    if (isValidFile && tempValue?.type?.startsWith("image/")) {
      const reader = new FileReader();
      reader.onloadend = () => setPreview(reader.result as string);
      reader.readAsDataURL(tempValue);
    } else {
      setPreview(null);
    }
  }, [editingField, field.id, tempValue, isValidFile]);

  const handleFileSelect = (selectedFile: File) => {
    if (selectedFile) {
      handleChange(selectedFile);
      setTempValue(selectedFile);

      if (selectedFile.type.startsWith("image/")) {
        const reader = new FileReader();
        reader.onloadend = () => setPreview(reader.result as string);
        reader.readAsDataURL(selectedFile);
      }
    }
  };

  const handleRemove = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    const emptyFile = new File([], '', { type: 'application/octet-stream' });
    setTempValue(emptyFile);
    handleChange(emptyFile);
    setPreview(null);
  };

  const placeholder = field.placeholder || "Upload a file";

  if (isDisplay) {
    if(!isValidFile) return null;
    return (
      <div className="w-full space-y-3">
        <FilePreview tempValue={tempValue} preview={preview} />
      </div>
    );
  }

  return (
    <div className="w-full space-y-2 sm:space-y-3">
      {/* Only show preview on larger screens */}
      {(isEditing || isDisplay) && isValidFile && (
        <FilePreview tempValue={tempValue} preview={preview} />
      )}
      
      
      <FileInput
        field={field}
        tempValue={tempValue}
        handleFileSelect={handleFileSelect}
        handleRemove={handleRemove}
        required={required}
        placeholder={placeholder}
        showRemove={isValidFile}
      />
    </div>
  );
};

// Custom comparison function for the main component
const arePropsEqual = (prevProps: InputFieldProps, nextProps: InputFieldProps) => {
  // Only rerender if these specific props change
  return (
    prevProps.field.id === nextProps.field.id &&
    prevProps.tempValue === nextProps.tempValue &&
    prevProps.required === nextProps.required &&
    prevProps.isDisplay === nextProps.isDisplay
  );
};

// Memoize the entire component with a custom comparison function
export default memo(FileInputField, arePropsEqual);