import { Textarea } from "@/components/ui/textarea";

const TextAreaInputField: React.FC<InputFieldProps> = ({
  field,
  tempValue,
  handleChange,
  required,
  setTempValue,
  isDisplay,
  onKeyDown,
}) => {
  return (
    <>
      {isDisplay ? (
        <span className="block w-full text-left py-1 text-xs sm:text-xs md:text-base text-gray-700 truncate whitespace-nowrap overflow-hidden">
          {field?.display?.[String(tempValue)] || tempValue || ""}
        </span>
      ) : (
        <Textarea
          id={field.id}
          value={tempValue}
          onChange={(e) => handleChange(e.target.value)}
          onKeyDown={(e) => {
            // For textarea, only save on Ctrl+Enter or Cmd+Enter, not just Enter
            if ((e.ctrlKey || e.metaKey) && e.key === 'Enter' && onKeyDown) {
              onKeyDown(e);
            } else if (e.key === 'Escape' && onKeyDown) {
              onKeyDown(e);
            }
          }}
          required={required}
          placeholder={field.placeholder || "Enter text"}
          className="w-full min-h-[80px] resize-y"
        />
      )}
    </>
  );
};

export default TextAreaInputField;
