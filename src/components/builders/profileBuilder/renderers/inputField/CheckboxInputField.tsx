import React from "react";
import { Checkbox } from "@/components/ui/checkbox";
import { Label } from "@/components/ui/label";

interface InputFieldProps {
  field: { id: string };
  tempValue: any;
  handleChange: (value: boolean) => void;
  required?: boolean;
  setTempValue?: (value: any) => void;
  isDisplay?: boolean;
}

const CheckboxInputField: React.FC<InputFieldProps> = ({
  field,
  tempValue,
  handleChange,
  required,
  setTempValue,
  isDisplay = true,
}) => {
  const handleCheckboxChange = (checked: boolean) => {
    if (tempValue === "") {
      handleChange(checked);
    } else {
      handleChange(checked ? true : false);
    }
  };

  return (
    <div className="block w-full text-left py-1 text-xs sm:text-xs md:text-base text-gray-700 truncate whitespace-nowrap overflow-hidden">
      <Checkbox
        id={field.id}
        checked={tempValue === true}
        onCheckedChange={handleCheckboxChange}
        className="w-4 h-4"
      />
      {/* Uncomment below to display Yes/No text based on state
      <Label htmlFor={field.id} className="text-xs font-medium text-gray-700">
        {tempValue === "" ? "" : tempValue ? "Yes" : "No"}
      </Label>
      */}
    </div>
  );
};

export default CheckboxInputField;
