import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { useEffect, useRef, useState } from "react";

const BooleanInputField: React.FC<InputFieldProps> = ({
  field,
  tempValue,
  handleChange,
  required,
  setTempValue,
  isDisplay,
}) => {
  const [open, setOpen] = useState(false);
  const triggerRef = useRef<HTMLButtonElement>(null);

  useEffect(() => {
    if (!isDisplay) setOpen(true);
    else setOpen(false);
  }, [isDisplay]);

  function handleOpenChange(isOpen: boolean) {
    setOpen(isOpen);
    if (isOpen && triggerRef.current) {
      triggerRef.current?.focus();
    }
  }

  return (
    <>
      {isDisplay ? (
        <span className="block w-full text-left py-1 text-xs sm:text-xs md:text-base text-gray-700 truncate whitespace-nowrap overflow-hidden">
          {tempValue === null || tempValue === undefined || tempValue === ""
            ? ""
            : tempValue
              ? "Yes"
              : "No"}
        </span>
      ) : (
        <Select
          open={open}
          onOpenChange={handleOpenChange}
          value={
            tempValue === null || tempValue === undefined
              ? ""
              : tempValue
              ? "Yes"
              : "No"
          }
          onValueChange={(value) => handleChange(value === "Yes")}
        >
          <SelectTrigger
            ref={triggerRef}
            className="w-full border border-gray-300 bg-white"
          >
            <SelectValue placeholder={field.placeholder || "Select an option"} />
          </SelectTrigger>
          <SelectContent>
            {field.options.map((option: any) => (
              <SelectItem
                key={String(option.value)}
                value={String(option.value)}
              >
                {option.label}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      )}
    </>
  );
};

export default BooleanInputField;
