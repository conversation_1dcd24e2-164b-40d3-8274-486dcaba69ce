import { Switch } from "@/components/ui/switch";
import { Label } from "@/components/ui/label";
import { Loader2 } from "lucide-react";

const SwitchInputField: React.FC<InputFieldProps> = ({
  field,
  tempValue,
  handleChange,
  required,
  setTempValue,
  isDisplay = false,  // this act as a loading state
}) => {
  const handleSwitchChange = (checked: boolean) => {
    handleChange(checked ? true : false);
  };

  return (
    <div className="flex items-center space-x-2">
      {isDisplay ? (
        <div className="flex h-6 w-11 items-center justify-center rounded-full bg-gray-200">
          <Loader2 className="h-4 w-4 animate-spin text-gray-500" />
        </div>
      ) : (
        <Switch
          id={field.id}
          checked={tempValue === true}
          onCheckedChange={handleSwitchChange}
          className="data-[state=checked]:bg-blue-600 data-[state=unchecked]:bg-gray-300"
        />
      )}
    </div>
  );
};

export default SwitchInputField;
