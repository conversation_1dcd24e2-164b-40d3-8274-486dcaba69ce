import BooleanInputField from "./BooleanInputField";
import DateInput<PERSON>ield from "./DateInputField";
import FileInputField from "./FileInputField";
import OtpInputField from "./OtpInputField";
import PhoneInputField from "./PhoneInputField";
import SectionInput<PERSON>ield from "./SectionInputField";
import TextAreaInputField from "./TextAreaInputField";
import TextInputField from "./TextInputField";
import DobDateInputField from "./DobDateInputField";
import SwitchInputField from "./SwitchInputField";
import CheckboxInputField from "./CheckboxInputField";
import AgeInputField from "./AgeInputField";

const DynamicInputField: React.FC<any> = ({
  field,
  tempValue,
  handleChange,
  required,
  setTempValue,
  isDisplay = false,
  onKeyDown,
}) => {
  switch (field.type) {
    case "text":
    case "email":
      return (
        <TextInputField
          field={field}
          tempValue={tempValue}
          handleChange={handleChange}
          required={required}
          setTempValue={setTempValue}
          isDisplay={isDisplay}
          type={field.type}
          onKeyDown={onKeyDown}
        />
      );
    case "textarea":
      return (
        <TextAreaInputField
          field={field}
          tempValue={tempValue}
          handleChange={handleChange}
          required={required}
          setTempValue={setTempValue}
          isDisplay={isDisplay}
          onKeyDown={onKeyDown}
        />
      );
    case "date":
      return (
        <DateInputField
          field={field}
          tempValue={tempValue}
          handleChange={handleChange}
          required={required}
          setTempValue={setTempValue}
          isDisplay={isDisplay}
        />
      );
    case "dateofbirth":
      return (
        <DobDateInputField
          field={field}
          tempValue={tempValue}
          handleChange={handleChange}
          required={required}
          setTempValue={setTempValue}
          isDisplay={isDisplay}
        />
      );
    case "age":
      return (
        <AgeInputField
          field={field}
          tempValue={tempValue}
          handleChange={handleChange}
          required={required}
          setTempValue={setTempValue}
          isDisplay={isDisplay}
        />
      );
    case "file":
      return (
        <FileInputField
          field={field}
          tempValue={tempValue}
          handleChange={handleChange}
          required={required}
          setTempValue={setTempValue}
          isDisplay={isDisplay}
        />
      );

    case "customSelect":
    case "select":
      return (
        <SectionInputField
          field={field}
          tempValue={tempValue}
          handleChange={handleChange}
          required={required}
          setTempValue={setTempValue}
          isDisplay={isDisplay}
        />
      );

    case "boolean":
      return (
        <BooleanInputField
          field={field}
          tempValue={tempValue}
          handleChange={handleChange}
          required={required}
          setTempValue={setTempValue}
          isDisplay={isDisplay}
        />
      );

    case "switch":
      return (
        <SwitchInputField
          field={field}
          tempValue={tempValue}
          handleChange={handleChange}
          required={required}
          setTempValue={setTempValue}
          isDisplay={isDisplay}
        />
      );
    
    case "checkbox":
      return (
        <CheckboxInputField
          field={field}
          tempValue={tempValue}
          handleChange={handleChange}
          required={required}
          setTempValue={setTempValue}
          isDisplay={isDisplay}
        />
      );
      
    case "otp":
      return (
        <OtpInputField
          field={field}
          tempValue={tempValue}
          handleChange={handleChange}
          required={required}
          setTempValue={setTempValue}
          isDisplay={isDisplay}
        />
      );

    case "phone":
      return (
        <PhoneInputField
          field={field}
          tempValue={tempValue}
          handleChange={handleChange}
          required={required}
          setTempValue={setTempValue}
          isDisplay={isDisplay}
        />
      );

    default:
      return (
      <div className="w-full min-w-0">
      <span 
        className="block w-full text-left py-1 text-xs sm:text-xs md:text-base text-gray-700 truncate whitespace-nowrap overflow-hidden"
        title={tempValue || ""}
      >
        {tempValue || ""}
      </span>
    </div>
    )
      
  }
};

export default DynamicInputField;
