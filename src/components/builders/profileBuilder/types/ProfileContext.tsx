interface ProfileContextType {
  fieldValues: Record<string, any>;
  updateFieldValue: (fieldId: string, value: any) => void;
  editingField: string | null;
  setEditingField: React.Dispatch<React.SetStateAction<string | null>>;
  getColSpanClass: (span: string, totalColumns?: number) => string;
  checkIfFieldIsFlagged: (fieldId: string) => boolean;
  setFieldValues: (fieldValues: Record<string, any>) => void;
  hasPermissions?: (permissions: string[]) => boolean;
  checkIfFieldNeedsReview: (fieldId: string) => boolean;
}


interface ProfileProviderProps {
    children: React.ReactNode;
    initialValues: Record<string, any>;
  }