interface ApiFunction {
  url: string;
  method: string;
}

interface ApiExecutorParams {
  functionId: string;
  config: ProfileConfig;
  params: Record<string, string>;
  data?: any;
}

interface ProfileConfig {
  id: string;
  functions: Record<string, ApiFunction>;
  profile: {
    entityType: string;
    groups: ProfileGroup[];
  };
}
interface ProfileField {
  id: string;
  type: string;
  label: string;
  required: boolean;
  flagKey?: string;
  functions: {
    save?: string;
    flag?: string;
    unflag?: string;
  };
}

interface ProfileGroup {
  title: string;
  description: string;
  order: number;
  fields: ProfileField[];
}

interface ProfileBuilderProps {
  config: any;
  entity: any;
  entityRefetch: () => void;
  entityIsFetching: boolean;
}

interface ProfileField {
  id: string;
  label: string;
  type: string;
  required: boolean;
  flaggable: boolean;
  span: string;
  options?: SelectOption[] | string;
  // Options can be a templated string (like "{{settings: entity.dog.breeds}}")
  placeholder?: string;
  length?: number;
  formatPattern?: string;
  accept?: string[];
  editPermissions?: string[];
  visible?: {
    conditions: Condition[];
  };
  validate?: FieldValidation[];
  computed?: string;
}

interface SelectOption {
  label: string;
  value: string | boolean;
}

interface Condition {
  field: string;
  equals?: any;
  and?: {
    field: string;
    equals: any;
  };
  unless?: {
    permissions: string[];
  };
  when?: {
    field: string;
    equals: any;
  };
  then?: string;
  default?: string;
}

interface FieldValidation {
  type: string;
  value: number | string;
  message: string;
  unit?: string;
}