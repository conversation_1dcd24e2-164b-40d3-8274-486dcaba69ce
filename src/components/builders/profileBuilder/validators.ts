type ValidationFunction = (value: any, errors: string[]) => boolean;

const validationFunctions: Record<string, ValidationFunction> = {
  isEmail: (value: string, errors: string[]) => {
    if (!value) return true;
    const emailRegex = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;
    if (!emailRegex.test(value)) {
      errors.push("Invalid email format");
      return false;
    }
    return true;
  },
  isNotFutureDate: (value: string, errors: string[]) => {
    const inputDate = new Date(value);
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    
    if (inputDate > today) {
      errors.push("Date cannot be in the future");
      return false;
    }
    return true;
  },
  isFutureDate: (value: string, errors: string[]) => {
    const inputDate = new Date(value);
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    if (inputDate < today) {
      errors.push("Date cannot be in the past or today");
      return false;
    }
    return true;
  },
  isAdult: (value: string, errors: string[]) => {
    const inputDate = new Date(value);
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    // Calculate the date 18 years ago from today
    const adultDate = new Date(today);
    adultDate.setFullYear(adultDate.getFullYear() - 18);

    if (inputDate > adultDate) {
      errors.push("Age must be at least 18 years");
      return false;
    }
    return true;
  },
  // Renewal eligibility validation - vaccine due date must have at least 1 month validity for renewal
  hasRenewalEligibility: (value: string, errors: string[]) => {
    if (!value) return true; // Skip validation if no value provided
    
    const vaccineDueDate = new Date(value);
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    
    // Calculate the minimum required date (1 month from today)
    const minRequiredDate = new Date(today);
    minRequiredDate.setMonth(minRequiredDate.getMonth() + 1);
    
    if (vaccineDueDate < minRequiredDate) {
      errors.push("Vaccine expires too soon for renewal eligibility");
      return false;
    }
    return true;
  }
}

function isEmptyFile(value: File | null | undefined): boolean {
  return (
    !value ||
    (value instanceof File && value.size === 0 && value.name === '')
  );
}

export function validateField(
  field: ProfileField,
  value: any,
  allValues: Record<string, any>,
  required: boolean,
): string[] {
  const errors: string[] = [];

  // Check required condition
  if (field.type !== "boolean" &&
    required &&
    ((typeof value === "string" && value.trim() === "") ||
      value === null ||
      value === undefined || isEmptyFile(value))
  ) {
    errors.push(`${field.label} is required.`);
  }

  // Run through additional validations (minLength, maxLength, etc.)
  if (field.validate) {
    field.validate.forEach((rule: FieldValidation) => {
      if (rule.type === "minLength" && value?.length < Number(rule.value)) {
        errors.push(rule.message);
      }
      if (rule.type === "maxLength" && value?.length > Number(rule.value)) {
        errors.push(rule.message);
      }
      if (rule.type === "futureMin") {
        // Handle futureMin validation (e.g., vaccine due date must have at least 1 month validity)
        if (value) {
          const inputDate = new Date(value);
          const today = new Date();
          today.setHours(0, 0, 0, 0);
          
          // Calculate minimum required date based on rule
          const minRequiredDate = new Date(today);
          if (rule.unit === "months") {
            minRequiredDate.setMonth(minRequiredDate.getMonth() + Number(rule.value));
          } else if (rule.unit === "days") {
            minRequiredDate.setDate(minRequiredDate.getDate() + Number(rule.value));
          } else if (rule.unit === "years") {
            minRequiredDate.setFullYear(minRequiredDate.getFullYear() + Number(rule.value));
          }
          
          if (inputDate < minRequiredDate) {
            errors.push(rule.message || `Date must have at least ${rule.value} ${rule.unit} validity remaining`);
          }
        }
      }
      if (rule.type === "futureMax") {
        // Handle futureMax validation (e.g., vaccine administered date must be in the past)
        if (value) {
          const inputDate = new Date(value);
          const today = new Date();
          today.setHours(0, 0, 0, 0);
          
          // Calculate maximum allowed date based on rule
          const maxAllowedDate = new Date(today);
          if (rule.unit === "months") {
            maxAllowedDate.setMonth(maxAllowedDate.getMonth() + Number(rule.value));
          } else if (rule.unit === "days") {
            maxAllowedDate.setDate(maxAllowedDate.getDate() + Number(rule.value));
          } else if (rule.unit === "years") {
            maxAllowedDate.setFullYear(maxAllowedDate.getFullYear() + Number(rule.value));
          }
          
          if (inputDate > maxAllowedDate) {
            errors.push(rule.message || `Date cannot be more than ${rule.value} ${rule.unit} in the future`);
          }
        }
      }
      if (rule.type === "function") {
        validationFunctions?.[rule.value as string](value, errors);
      }
    });
  }

  return errors;
}
