import { useEffect, useRef, useState } from "react";

export const useResize = () => {
  const containerRef = useRef<HTMLDivElement>(null);
  const [containerWidth, setContainerWidth] = useState<number>(0);

  const getColumnCount = (width: number): number => {
    if (width < 500) {
      return 1;
    }
    return 12;
  };
  useEffect(() => {
    function handleResize() {
      if (containerRef.current) {
        setContainerWidth(containerRef.current.clientWidth);
      }
    }
    window.addEventListener("resize", handleResize);
    handleResize();

    return () => {
      window.removeEventListener("resize", handleResize);
    };
  }, []);

  return { containerRef, columnCount: getColumnCount(containerWidth) };
};
