import { useGetJSONStorage } from "@/hooks/api/useAdmin";
import ProfileBuilder from "./ProfileBuilder";
import LoadingSpinner from "@/components/ui/LoadingSpinner";

interface ProfileBuilderFactoryProps {
  profileType: "dog" | "individual";
  entity: any;
  entityIsFetching: boolean;
  entityRefetch: () => void;
}

const PROFILE_FORM_MAP: Record<string, string> = {
  dog: "dog-profile-builder",
  individual: "individual-profile-builder",
} as const;

const PROFILE_KEY = "onlineProfile";

export default function ProfileBuilderFactory({
  profileType,
  entity,
  entityIsFetching,
  entityRefetch,
}: ProfileBuilderFactoryProps) { 
  const currentForm = PROFILE_FORM_MAP[profileType] ?? null;
  const { data, isLoading } = useGetJSONStorage(PROFILE_KEY, currentForm);

  if (isLoading) {
    return <LoadingSpinner />;
  }

  if (!data) {
    return null;
  }

  return (
    <ProfileBuilder
      config={data}
      entity={entity}
      entityRefetch={entityRefetch}
      entityIsFetching={entityIsFetching}
    />
  );
}
