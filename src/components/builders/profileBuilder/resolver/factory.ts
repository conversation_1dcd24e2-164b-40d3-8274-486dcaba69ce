import { differenceInMonths, differenceInYears } from "date-fns";
import { isSenior as isSenior<PERSON><PERSON><PERSON> } from "@/components/license/licenseHelper";
import { getNestedValue } from "./resolver";

const ADDRESS_COMPONENTS: any[] = [
  { key: "address", value: "streetAddress" },
  { key: "address2", value: "streetAddress2" },
  { key: "city", value: "city" },
  { key: "state", value: "state" },
  { key: "zip", value: "zip" },
];

const ADDRESS_TYPE_HOME = "Home";
const ADDRESS_TYPE_MAILING = "Mailing";

const resolverFactory = () => {
  function getAddressParameters(
    addressType: string,
    entity: any,
    currentAddressKey: string,
    currentAddressValue: string,
  ) {
    return ADDRESS_COMPONENTS.reduce(
      (acc: Record<string, string>, component: any) => {
        const isCurrentComponent = component.key === currentAddressKey;
        const homeAddressId =
          getNestedValue(
            entity,
            `addresses[participantAddressType='${ADDRESS_TYPE_HOME}'].participantAddressId`,
          ) ?? "newHome";
        const mailingAddressId =
          getNestedValue(
            entity,
            `addresses[participantAddressType='${ADDRESS_TYPE_MAILING}'].participantAddressId`,
          ) ?? "newMailing";
        const value = isCurrentComponent
          ? currentAddressValue
          : getNestedValue(
              entity,
              `addresses[participantAddressType='${addressType}'].${component.value}`,
            );

        if (entity?.mailingSameAsPrimary) {
          // Set both home and mailing addresses to the same value
          const homeKey = `address_${homeAddressId}_${component.key}`;
          const mailingKey = `address_${mailingAddressId}_${component.key}`;

          acc[homeKey] = value;
          acc[mailingKey] = value;
        } else {
          if(addressType === ADDRESS_TYPE_HOME) {
            // Set only home address
            const homeKey = `address_${homeAddressId}_${component.key}`;
            acc[homeKey] = value;
          } else {
            // Set only mailing address
            const mailingKey = `address_${mailingAddressId}_${component.key}`;
            acc[mailingKey] = value;
          }
        }

        return acc;
      },
      {},
    );
  }
  function calculateAgeInMonthsOrYears(startDateString: string): string {
    if (!startDateString) return "Unknown";

    const startDate = new Date(startDateString);
    const currentDate = new Date();

    const months = differenceInMonths(currentDate, startDate);
    const years = differenceInYears(currentDate, startDate);

    if (years < 1) {
      return `${months} months`;
    }
    return `${years} years`;
  }

  function isSenior(birthDate: string): string {
    return isSeniorHelper(birthDate) ? "Yes" : "No";
  }

  function toBoolean(value: any): boolean {
    return (value === "true" || value === true) ? true : false;
  }

  function mailingSameAsPrimary(mailingSameAsPrimary: boolean): boolean {
    return mailingSameAsPrimary;
  }
  return {
    calculateAgeInMonthsOrYears,
    isSenior,
    mailingSameAsPrimary,
    getAddressParameters,
    toBoolean,
  };
};

export default resolverFactory;
