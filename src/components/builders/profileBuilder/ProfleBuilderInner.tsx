import React, { useEffect } from "react";
import { useProfileContext } from "./context/ProfileContext";
import { getHandlers } from "./utils/handlers";
import { useResize } from "./hooks/useResize";
import JsonRenderer from "./renderers/JsonRenderer";
import { useAtom } from "jotai";
import { toastAtom } from "@/components/ui/toast/toast";

export const ProfileBuilderInner = ({
  config,
  entity,
  entityRefetch,
}: ProfileBuilderProps) => {
  const { setEditingField, checkIfFieldIsFlagged, setFieldValues } =
    useProfileContext();
  const [_, setToast] = useAtom(toastAtom);
  const { handleSaveField, handleCancelEdit, handleFlagField } = getHandlers(
    entity,
    config,
    entityRefetch,
    setEditingField,
    checkIfFieldIsFlagged,
    setToast,
  );
  const { containerRef, columnCount } = useResize();

  useEffect(() => {
    if (entity) {
      setFieldValues(entity);
    }
  }, [entity, setFieldValues]);

  if (!config || !entity) {
    return (
      <div className="flex h-24 items-center justify-center">
        <p className="text-muted-foreground text-sm">
          Loading profile builder...
        </p>
      </div>
    );
  }

  return (
    <div ref={containerRef}>
      <h1 className="mb-4 text-2xl font-semibold">
        {config.profile.displayName}
      </h1>
      <JsonRenderer
        config={config}
        handleSaveField={handleSaveField}
        handleCancelEdit={handleCancelEdit}
        handleFlagField={handleFlagField}
        checkIfFieldIsFlagged={checkIfFieldIsFlagged}
        columnCount={columnCount}
      />
    </div>
  );
};
