{"id": "individual-profile-builder", "title": "Profile Configuration", "profile": {"tab": "Profile", "icon": "PawP<PERSON>t", "groups": [{"order": 1, "title": "Personal Information", "fields": [{"id": "firstName", "key": "firstName", "span": "1/2", "type": "text", "label": "First Name", "value": "{{firstName}}", "required": true, "flaggable": {"conditions": [{"permissions": ["super-admin"]}], "conditionType": "AND"}, "placeholder": "Enter first name"}, {"id": "middleName", "key": "middleName", "span": "1/2", "type": "text", "label": "Middle Name", "value": "{{middleName}}", "required": false, "flaggable": {"conditions": [{"permissions": ["super-admin"]}], "conditionType": "AND"}, "placeholder": "Enter middle name"}, {"id": "lastName", "key": "lastName", "span": "1/2", "type": "text", "label": "Last Name", "value": "{{lastName}}", "required": true, "flaggable": {"conditions": [{"permissions": ["super-admin"]}], "conditionType": "AND"}, "placeholder": "Enter last name"}, {"id": "dateOfBirth", "key": "dateOfBirth", "span": "1/2", "type": "dateofbirth", "label": "Date of Birth", "value": "{{dateOfBirth}}", "required": true, "validate": [{"type": "function", "value": "isAdult"}], "flaggable": {"conditions": [{"permissions": ["super-admin"]}], "conditionType": "AND"}, "placeholder": "DD-MM-YYYY", "formatPattern": "MMMM dd, yyyy"}, {"id": "senior", "key": "senior", "span": "1/2", "type": "display", "label": "Senior", "value": "isSenior({{dateOfBirth}})", "required": false, "flaggable": false}, {"id": "phone", "key": "contact+{{contacts[type='Phone' && group='Home'].id}}", "span": "full", "type": "phone", "label": "Phone", "value": "{{contacts[type='Phone' && group='Home'].value}}", "required": true, "maxLength": 14, "validate": [{"type": "<PERSON><PERSON><PERSON><PERSON>", "value": 0, "message": "Phone number must be at least 0 digits"}, {"type": "max<PERSON><PERSON><PERSON>", "value": 14, "message": "Phone number must be under 10 digits"}], "flaggable": {"conditions": [{"permissions": ["super-admin"]}], "conditionType": "AND"}, "placeholder": "Enter phone number"}, {"id": "email", "key": "contact+{{contacts[type='Email' && group='Primary'].id}}", "span": "full", "type": "text", "label": "Email", "value": "{{contacts[type='Email' && group='Primary'].value}}", "required": true, "validate": [{"type": "function", "value": "isEmail"}], "flaggable": {"conditions": [{"permissions": ["super-admin"]}], "conditionType": "AND"}, "placeholder": "Enter email address"}], "description": "Personal Information about the individual"}, {"order": 2, "title": "Address Information", "fields": [{"id": "address", "key": "address_+{{addresses[participantAddressType='Home'].participantAddressId}}+_address", "span": "1/2", "type": "text", "label": "Street Address", "value": "{{addresses[participantAddressType='Home'].streetAddress}}", "flagKey": "homeStreetAddress", "required": true, "flaggable": {"conditions": [{"permissions": ["super-admin"]}], "conditionType": "AND"}, "addressType": "Home", "placeholder": "Enter street address", "hasAdditionalParams": true}, {"id": "address2", "key": "address_+{{addresses[participantAddressType='Home'].participantAddressId}}+_address2", "span": "1/2", "type": "text", "label": "Apt, Suite, etc.", "value": "{{addresses[participantAddressType='Home'].streetAddress2}}", "flagKey": "homeStreetAddress2", "required": false, "flaggable": {"conditions": [{"permissions": ["super-admin"]}], "conditionType": "AND"}, "addressType": "Home", "placeholder": "Enter Apt, Suite, etc.", "hasAdditionalParams": true}, {"id": "city", "key": "address_+{{addresses[participantAddressType='Home'].participantAddressId}}+_city", "span": "1/2", "type": "text", "label": "City", "value": "{{addresses[participantAddressType='Home'].city}}", "flagKey": "homeCity", "required": true, "flaggable": {"conditions": [{"permissions": ["super-admin"]}], "conditionType": "AND"}, "addressType": "Home", "placeholder": "Enter city", "hasAdditionalParams": true}, {"id": "state", "key": "address_+{{addresses[participantAddressType='Home'].participantAddressId}}+_state", "span": "1/2", "type": "select", "label": "State", "value": "{{addresses[participantAddressType='Home'].state}}", "flagKey": "homeState", "options": [{"label": "AL", "value": "AL"}, {"label": "AK", "value": "AK"}, {"label": "AZ", "value": "AZ"}, {"label": "AR", "value": "AR"}, {"label": "CA", "value": "CA"}, {"label": "CO", "value": "CO"}, {"label": "CT", "value": "CT"}, {"label": "DE", "value": "DE"}, {"label": "FL", "value": "FL"}, {"label": "GA", "value": "GA"}, {"label": "HI", "value": "HI"}, {"label": "ID", "value": "ID"}, {"label": "IL", "value": "IL"}, {"label": "IN", "value": "IN"}, {"label": "IA", "value": "IA"}, {"label": "KS", "value": "KS"}, {"label": "KY", "value": "KY"}, {"label": "LA", "value": "LA"}, {"label": "ME", "value": "ME"}, {"label": "MD", "value": "MD"}, {"label": "MA", "value": "MA"}, {"label": "MI", "value": "MI"}, {"label": "MN", "value": "MN"}, {"label": "MS", "value": "MS"}, {"label": "MO", "value": "MO"}, {"label": "MT", "value": "MT"}, {"label": "NE", "value": "NE"}, {"label": "NV", "value": "NV"}, {"label": "NH", "value": "NH"}, {"label": "NJ", "value": "NJ"}, {"label": "NM", "value": "NM"}, {"label": "NY", "value": "NY"}, {"label": "NC", "value": "NC"}, {"label": "ND", "value": "ND"}, {"label": "OH", "value": "OH"}, {"label": "OK", "value": "OK"}, {"label": "OR", "value": "OR"}, {"label": "PA", "value": "PA"}, {"label": "RI", "value": "RI"}, {"label": "SC", "value": "SC"}, {"label": "SD", "value": "SD"}, {"label": "TN", "value": "TN"}, {"label": "TX", "value": "TX"}, {"label": "UT", "value": "UT"}, {"label": "VT", "value": "VT"}, {"label": "VA", "value": "VA"}, {"label": "WA", "value": "WA"}, {"label": "WV", "value": "WV"}, {"label": "WI", "value": "WI"}, {"label": "WY", "value": "WY"}], "required": true, "flaggable": {"conditions": [{"permissions": ["super-admin"]}], "conditionType": "AND"}, "addressType": "Home", "placeholder": "Enter state", "hasAdditionalParams": true}, {"id": "zip", "key": "address_+{{addresses[participantAddressType='Home'].participantAddressId}}+_zip", "span": "1/2", "type": "text", "label": "Zip", "value": "{{addresses[participantAddressType='Home'].zip}}", "flagKey": "homeZip", "required": true, "flaggable": {"conditions": [{"permissions": ["super-admin"]}], "conditionType": "AND"}, "addressType": "Home", "placeholder": "Enter zip code", "hasAdditionalParams": true, "validate": [{"type": "<PERSON><PERSON><PERSON><PERSON>", "value": 0, "message": "Zip must be at least 0 characters"}, {"type": "max<PERSON><PERSON><PERSON>", "value": 10, "message": "Zip must be under 10 characters"}]}, {"id": "mailingSameAsPrimary", "key": "mailingSameAsPrimary", "span": "full", "type": "switch", "label": "Mailing Address is the same", "value": "toBoolean({{mailingSameAsPrimary}})", "required": true, "flaggable": false, "defaultValue": true}, {"id": "mailaddress", "key": "address_+{{addresses[participantAddressType='Mailing'].participantAddressId}}+_address", "span": "1/2", "type": "text", "label": "Mail Street Address", "value": "{{addresses[participantAddressType='Mailing'].streetAddress}}", "visible": {"conditions": [{"field": "mailingSameAsPrimary", "value": false, "comparison": "equals"}], "conditionType": "AND"}, "required": true, "flaggable": {"conditions": [{"permissions": ["super-admin"]}], "conditionType": "AND"}, "addressType": "Mailing", "placeholder": "Enter mail street address", "hasAdditionalParams": true}, {"id": "mailaddress2", "key": "address_+{{addresses[participantAddressType='Mailing'].participantAddressId}}+_address2", "span": "1/2", "type": "text", "label": "Mail Apt, Suite, etc.", "value": "{{addresses[participantAddressType='Mailing'].streetAddress2}}", "visible": {"conditions": [{"field": "mailingSameAsPrimary", "value": false, "comparison": "equals"}], "conditionType": "AND"}, "required": false, "flaggable": {"conditions": [{"permissions": ["super-admin"]}], "conditionType": "AND"}, "addressType": "Mailing", "placeholder": "Enter mail Apt, Suite, etc.", "hasAdditionalParams": true}, {"id": "mailcity", "key": "address_+{{addresses[participantAddressType='Mailing'].participantAddressId}}+_city", "span": "1/2", "type": "text", "label": "Mail City", "value": "{{addresses[participantAddressType='Mailing'].city}}", "visible": {"conditions": [{"field": "mailingSameAsPrimary", "value": false, "comparison": "equals"}], "conditionType": "AND"}, "required": true, "flaggable": {"conditions": [{"permissions": ["super-admin"]}], "conditionType": "AND"}, "addressType": "Mailing", "placeholder": "Enter mail city", "hasAdditionalParams": true}, {"id": "mailstate", "key": "address_+{{addresses[participantAddressType='Mailing'].participantAddressId}}+_state", "span": "1/2", "type": "select", "label": "Mail State", "value": "{{addresses[participantAddressType='Mailing'].state}}", "options": [{"label": "AL", "value": "AL"}, {"label": "AK", "value": "AK"}, {"label": "AZ", "value": "AZ"}, {"label": "AR", "value": "AR"}, {"label": "CA", "value": "CA"}, {"label": "CO", "value": "CO"}, {"label": "CT", "value": "CT"}, {"label": "DE", "value": "DE"}, {"label": "FL", "value": "FL"}, {"label": "GA", "value": "GA"}, {"label": "HI", "value": "HI"}, {"label": "ID", "value": "ID"}, {"label": "IL", "value": "IL"}, {"label": "IN", "value": "IN"}, {"label": "IA", "value": "IA"}, {"label": "KS", "value": "KS"}, {"label": "KY", "value": "KY"}, {"label": "LA", "value": "LA"}, {"label": "ME", "value": "ME"}, {"label": "MD", "value": "MD"}, {"label": "MA", "value": "MA"}, {"label": "MI", "value": "MI"}, {"label": "MN", "value": "MN"}, {"label": "MS", "value": "MS"}, {"label": "MO", "value": "MO"}, {"label": "MT", "value": "MT"}, {"label": "NE", "value": "NE"}, {"label": "NV", "value": "NV"}, {"label": "NH", "value": "NH"}, {"label": "NJ", "value": "NJ"}, {"label": "NM", "value": "NM"}, {"label": "NY", "value": "NY"}, {"label": "NC", "value": "NC"}, {"label": "ND", "value": "ND"}, {"label": "OH", "value": "OH"}, {"label": "OK", "value": "OK"}, {"label": "OR", "value": "OR"}, {"label": "PA", "value": "PA"}, {"label": "RI", "value": "RI"}, {"label": "SC", "value": "SC"}, {"label": "SD", "value": "SD"}, {"label": "TN", "value": "TN"}, {"label": "TX", "value": "TX"}, {"label": "UT", "value": "UT"}, {"label": "VT", "value": "VT"}, {"label": "VA", "value": "VA"}, {"label": "WA", "value": "WA"}, {"label": "WV", "value": "WV"}, {"label": "WI", "value": "WI"}, {"label": "WY", "value": "WY"}], "visible": {"conditions": [{"field": "mailingSameAsPrimary", "value": false, "comparison": "equals"}], "conditionType": "AND"}, "required": true, "flaggable": {"conditions": [{"permissions": ["super-admin"]}], "conditionType": "AND"}, "addressType": "Mailing", "placeholder": "Enter mail state", "hasAdditionalParams": true}, {"id": "mailzip", "key": "address_+{{addresses[participantAddressType='Mailing'].participantAddressId}}+_zip", "span": "1/2", "type": "text", "label": "Mail Zip", "value": "{{addresses[participantAddressType='Mailing'].zip}}", "visible": {"conditions": [{"field": "mailingSameAsPrimary", "value": false, "comparison": "equals"}], "conditionType": "AND"}, "required": true, "validate": [{"type": "<PERSON><PERSON><PERSON><PERSON>", "value": 0, "message": "Phone number must be at least 10 digits"}, {"type": "max<PERSON><PERSON><PERSON>", "value": 10, "message": "Phone number must be under 15 digits"}], "flaggable": {"conditions": [{"permissions": ["super-admin"]}], "conditionType": "AND"}, "addressType": "Mailing", "placeholder": "Enter mail zip code", "hasAdditionalParams": true}], "description": "Home Address"}], "entityType": "individual", "displayName": "Profile"}, "functions": {"flagField": {"url": "/license/profile/{{context:entityType}}/{{context:entityId}}/reject-fields", "body": {"fields": ["{{context:fieldId}}"]}, "type": "rest", "meUrl": "/license/me/profile/{{context:entityType}}/{{context:entityId}}/reject-fields", "format": "json", "method": "PATCH", "description": "Flag field for review"}, "unflagField": {"url": "/license/profile/{{context:entityType}}/{{context:entityId}}/reject-fields?field={{context:fieldId}}", "type": "rest", "meUrl": "/license/me/profile/{{context:entityType}}/{{context:entityId}}/reject-fields?field={{context:fieldId}}", "format": "json", "method": "DELETE", "description": "Remove flag from field"}, "saveProfileField": {"url": "/license/participant/{{context:entityId}}", "body": "{{context:body}}", "type": "rest", "meUrl": "/license/me/participant/{{context:entityType}}", "format": "formData", "method": "PATCH", "description": "Save individual field value"}}}