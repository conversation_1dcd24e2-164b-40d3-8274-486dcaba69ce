interface Permission {
  permissions: string[];
}

interface Condition extends Permission {
  field: string;
  value: any;
  comparison?:
    | "greaterOrEqual"
    | "lessOrEqual"
    | "lessThan"
    | "greaterThan"
    | "equals"
    | "coercion"
    | "not";
  conditions?: ConditionGroup;
}

interface ConditionGroup {
  conditionType?: "AND" | "OR";
  conditions: Condition[];
}

function evaluateCondition(
  condition: Condition,
  context: any,
  hasPermissions?: (permissions: string[]) => boolean,
): boolean {
  if (condition?.permissions) {
    return hasPermissions
      ? condition?.comparison === "not"
        ? !hasPermissions(condition?.permissions)
        : hasPermissions(condition?.permissions)
      : false;
  }
  const fieldValue = getConditional(condition?.field, context);
  switch (condition?.comparison) {
    case "greaterOrEqual":
      return fieldValue >= condition?.value;
    case "lessOrEqual":
      return fieldValue <= condition?.value;
    case "lessThan":
      return fieldValue < condition?.value;
    case "greaterThan":
      return fieldValue > condition?.value;
    case "equals":
      return fieldValue === condition?.value;
    case "coercion":
      return !!fieldValue === !!condition?.value;
    default:
      return fieldValue === condition?.value;
  }
}

export function evaluateConditions(
  conditionsGroup: ConditionGroup,
  context: any,
  hasPermissions?: (permissions: string[]) => boolean,
): boolean {
  if (typeof conditionsGroup === "boolean") {
    return conditionsGroup;
  }
  if (
    !conditionsGroup?.conditions ||
    conditionsGroup?.conditions?.length === 0
  ) {
    return true;
  }

  const evaluate = (condition: Condition) =>
    condition.conditions
      ? evaluateConditions(condition?.conditions, context, hasPermissions)
      : evaluateCondition(condition, context, hasPermissions);

  return conditionsGroup?.conditionType === "AND"
    ? conditionsGroup?.conditions.every(evaluate)
    : conditionsGroup?.conditions.some(evaluate);
}

export function evaluateRequiredOrConditions(
  required: any,
  context: any,
  hasPermissions?: (permissions: string[]) => boolean,
) {
  if (typeof required === "boolean") {
    return required;
  }

  if (typeof required === "object" && required.conditions) {
    return evaluateConditions(required, context, hasPermissions);
  }

  return false;
}

export const getConditional = (path: string, object: any) => {
  return path.split(".").reduce((acc, part) => acc && acc[part], object);
};
