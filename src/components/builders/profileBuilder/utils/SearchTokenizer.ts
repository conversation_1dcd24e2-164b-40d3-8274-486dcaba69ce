export interface SearchableOption {
    label: string;
    value: string | number;
    [key: string]: any;
  }
  
  export interface TokenizedSearchOptions {
    searchKeys?: string[];
    caseSensitive?: boolean;
    matchMode?: 'startsWith' | 'includes' | 'exact';
  }
  
  const DEFAULT_OPTIONS: TokenizedSearchOptions = {
    caseSensitive: false,
    matchMode: 'includes'
  };
  
  export function performTokenizedSearch<T extends SearchableOption>(
    options: T[],
    query: string,
    searchOptions?: TokenizedSearchOptions
  ): T[] {
    if (!query?.trim()) return options;
  
    const finalOptions = { ...DEFAULT_OPTIONS, ...searchOptions };
    const searchText = finalOptions.caseSensitive ? query : query.toLowerCase();
    const tokens = searchText.split(/\s+/).filter(token => token !== "");
  
    if (tokens.length === 0) return options;
  
    return options.filter((option) => {
      let haystack = finalOptions.caseSensitive 
        ? option.label 
        : option.label.toLowerCase();
  
      if (finalOptions.searchKeys?.length) {
        finalOptions.searchKeys.forEach((key) => {
          if (option[key] != null) {
            const value = finalOptions.caseSensitive 
              ? String(option[key])
              : String(option[key]).toLowerCase();
            haystack += ` ${value}`;
          }
        });
      }
  
      return tokens.every((token) => {
        switch (finalOptions.matchMode) {
          case 'startsWith':
            return haystack.split(/\s+/).some(word => word.startsWith(token));
          case 'exact':
            return haystack.split(/\s+/).includes(token);
          case 'includes':
          default:
            return haystack.includes(token);
        }
      });
    });
  }
  