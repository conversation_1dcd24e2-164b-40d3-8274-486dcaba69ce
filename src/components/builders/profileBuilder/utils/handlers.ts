import { getApiExecutor } from "@/components/builders/utils/apiExecutor";

export const getHandlers = (
  entity: any,
  config: any,
  entityRefetch: any,
  setEditingField: any,
  checkIfFieldIsFlagged: any,
  setToast: any,
) => {
  const { executeApiFunction } = getApiExecutor();

  const handleSaveField = async (body: any, permitted: boolean) => {
    const saveFunction = config.functions.saveProfileField;
    await executeApiFunction(
      saveFunction,
      {
        body: body,
        entityType: entity.entityType,
        entityId: entity.entityId,
      },
      permitted,
    );
    entityRefetch();
    setEditingField(null);
  };

  const handleCancelEdit = () => {
    setEditingField(null);
  };

  const handleFlagField = async (
    fieldId: string,
    permitted: boolean,
    isAdd: boolean,
  ) => {
    if (!isAdd) {
      const unflagField = config.functions.unflagField;
      await executeApiFunction(
        unflagField,
        {
          entityType: entity.entityType,
          entityId: entity.entityId,
          fieldId: fieldId,
        },
        permitted,
      );
      entityRefetch();
    } else {
      const flagField = config.functions.flagField;
      await executeApiFunction(
        flagField,
        {
          entityType: entity.entityType,
          entityId: entity.entityId,
          fieldId: fieldId,
        },
        permitted,
      );
      entityRefetch();
    }
  };

  return { handleSaveField, handleCancelEdit, handleFlagField };
};
