import { useState } from "react";
import { ProfileContext } from "./ProfileContext";
import { useMyProfile } from "@/hooks/providers/useMyProfile";

export const ProfileProvider: React.FC<ProfileProviderProps> = ({
  children,
  initialValues,
}) => {
  const [fieldValues, setFieldValues] = useState<Record<string, any>>(
    initialValues || {},
  );
  const { hasPermissions } = useMyProfile();
  const [editingField, setEditingField] = useState<string | null>(null);

  const updateFieldValue = (fieldId: string, value: any) => {
    setFieldValues((prev) => ({ ...prev, [fieldId]: value }));
  };

  // Determines if the field should span multiple columns based on screen size
  function getColSpanClass(span: string, totalColumns: number = 12): string {
    if (span === "full" || totalColumns === 1) {
      return "col-span-full";
    }
    const parts = span.split("/");
    if (parts.length === 2) {
      const numerator = parseInt(parts[0]);
      const denominator = parseInt(parts[1]);
      if (!isNaN(numerator) && !isNaN(denominator) && denominator !== 0) {
        const ratio = numerator / denominator;
        const lgSpan = Math.round(ratio * 12);
        const smSpan = Math.round(ratio * 6);
        return `col-span-full sm:col-span-${smSpan} lg:col-span-${lgSpan}`;
      }
    }
    return "col-span-full sm:col-span-3 lg:col-span-6";
  }

  // Checks if the field is flagged
  const checkIfFieldIsFlagged = (fieldId: string) => {
    if (fieldValues.rejectedFields) {
      return fieldValues.rejectedFields.includes(fieldId);
    }
  };

  const checkIfFieldNeedsReview = (fieldId: string) => {
    if (fieldValues?.reviewFields) {
      return fieldValues.reviewFields.includes(fieldId);
    }
  };

  return (
    <ProfileContext.Provider
      value={{
        fieldValues,
        editingField,
        updateFieldValue,
        setEditingField,
        getColSpanClass,
        checkIfFieldIsFlagged,
        setFieldValues,
        hasPermissions,
        checkIfFieldNeedsReview,
      }}
    >
      {children}
    </ProfileContext.Provider>
  );
};
