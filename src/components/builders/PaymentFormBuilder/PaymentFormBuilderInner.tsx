import PageContainer from "@/components/ui/Page/PageContainer";
import { ErrorMessage } from "@hookform/error-message";
import { useEffect } from "react";
import { CgCreditCard } from "react-icons/cg";
import { BsCash } from "react-icons/bs";
import { LiaMoneyCheckSolid } from "react-icons/lia";
import { AiOutlineBank } from "react-icons/ai";
import { useFormContext } from "react-hook-form";
import { useMyCart } from "@/hooks/useMyCart";
import { toastAtom } from "@/components/ui/toast/toast";
import { useAtom } from "jotai";

interface PaymentFormItem {
  key: string;
  displayName: string;
  paymentMode: string;
  icon: string;
  isDisabled?: boolean;
}

const FormSection = ({ children }: { children: React.ReactNode }) => {
  return <div className="mb-4 flex flex-wrap">{children}</div>;
};

const FormSectionLabel = ({ children }: { children: React.ReactNode }) => {
  return <h3 className="text-lg font-bold ">{children}</h3>;
};

export const PaymentFormBuilderInner = ({
  setValue,
  config,
}: PaymentFormBuilderProps) => {
  const {
    register,
    formState: { errors },
    clearErrors,
  } = useFormContext();
  const { cartSummary, setPaymentMethod, paymentMethod } = useMyCart();
  const [, setToast] = useAtom(toastAtom);

  useEffect(() => {
    if (cartSummary?.hasAdditionalFee) {
      setToast({
        status: "success",
        label: `Cart Updated`,
        message: `Additional fee updated to cart`,
      });
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [cartSummary]);

  const handlePaymentMethodChange = (
    newMethod: string,
    paymentMode: string,
  ) => {
    // Batch all state updates together
    setValue("paymentMode", paymentMode);
    setValue("paymentReference", "");
    clearErrors("paymentReference");
    setPaymentMethod(newMethod);
    
  };

  const IconsMap: Record<string, JSX.Element> = {
    "credit-card": <CgCreditCard />,
    cash: <BsCash />,
    "personal-check": <LiaMoneyCheckSolid />,
    "certified-check": <LiaMoneyCheckSolid />,
    "money-order": <AiOutlineBank />,
  };

  return (
    <div className="flex w-full flex-col gap-4">
      <PageContainer className="w-full">
        <FormSectionLabel>
          Payment <span className="text-red-500">*</span>
        </FormSectionLabel>
        <FormSection>
          <div className="lg:grid-col-3 mt-2 grid w-full grid-cols-2 gap-4 2xl:grid-cols-5">
            {config.map((eachPaymentMethod: PaymentFormItem) => (
              <button
                type="button"
                key={eachPaymentMethod.key}
                onClick={() =>
                  handlePaymentMethodChange(
                    eachPaymentMethod.key,
                    eachPaymentMethod.paymentMode,
                  )
                }
                className={`
                flex w-full flex-col items-start gap-2 rounded border p-4
                ${eachPaymentMethod?.isDisabled ? "cursor-not-allowed opacity-50" : ""}
                ${
                  paymentMethod === eachPaymentMethod.key
                    ? "border-sky-500 text-sky-500"
                    : "text-neutral-600"
                }
              `}
                disabled={eachPaymentMethod?.isDisabled}
              >
                <span className="text-2xl">
                  {IconsMap[eachPaymentMethod?.icon]}
                </span>
                <span className="text-left font-semibold">
                  {eachPaymentMethod?.displayName}
                </span>
                {eachPaymentMethod?.isDisabled && <small>Unavailable</small>}
              </button>
            ))}
          </div>

          <div className="mt-4">
            {(paymentMethod === "personalCheck" ||
              paymentMethod === "certifiedCheck") && (
              <input
                type="text"
                placeholder="Check Number"
                className="w-full rounded border p-4"
                {...register("paymentReference", {
                  required: "Check Number is required.",
                })}
              />
            )}
            {paymentMethod === "moneyOrder" && (
              <input
                type="text"
                placeholder="Money Order Number"
                className="w-full rounded border p-4"
                {...register("paymentReference", {
                  required: "Money Order Number is required.",
                })}
              />
            )}

            {/* {paymentMethod === "card" && (
          <CreditCard cardInfo={null} setCardInfo={null} />
        )} */}
          </div>

          <ErrorMessage
            errors={errors}
            name={"paymentMethod"}
            render={({ message }) => (
              <p className="mt-1 text-xs text-red-500">{message}</p>
            )}
          />
        </FormSection>
      </PageContainer>
    </div>
  );
};
