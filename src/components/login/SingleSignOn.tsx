"use client";
import Image from "next/image";
import React from "react";
import { useGetIdentityProviders } from "@/hooks/api/useRealm";
import Loading from "@/app/(app)/loading";
import { useKeycloak } from "@/hooks/useKeycloak";

const SingleSignOn = ({ type }: { type: "login" | "signup" }) => {
  const {
    data: identityProviders,
    isError,
    isLoading,
  } = useGetIdentityProviders();
  const { hintLogin } = useKeycloak();

  if (isLoading) return <Loading text="Loading Login" />;
  if (isError) return <div>There was an error</div>;

  const hasGoogle = identityProviders?.includes("google");
  const hasMicrosoft = identityProviders?.includes("microsoft");
  // const github = identityProviders?.includes("github");
  // const hasIDMe = identityProviders?.includes("idme");

  return (
    <>
      <div className="flex w-fit flex-col items-center gap-3">
        {/* Google Login */}
        {hasGoogle && (
          <button
            onClick={() => hintLogin("google")}
            className="flex min-h-[44px] w-full min-w-[220px] items-center gap-3 rounded-full border border-gray-200 bg-white px-4 text-base font-semibold shadow-md transition-all duration-200 hover:shadow-lg focus:outline-none focus:ring-2 focus:ring-[#4285F4]/30"
            style={{ color: "#4285F4", borderColor: "#4285F4" }}
            aria-label="Continue with Google"
          >
            <Image
              src={`/logos/google.png`}
              alt={`Google Logo`}
              width={24}
              height={24}
              className="bg-white"
            />
            {type === "login" ? "Continue with Google" : "Sign up with Google"}
          </button>
        )}
        {/* Microsoft Login */}
        {hasMicrosoft && (
          <button
            onClick={() => hintLogin("microsoft")}
            className="flex min-h-[44px] w-full min-w-[220px] items-center gap-3 rounded-full border border-gray-200 bg-white px-4 text-base font-semibold shadow-md transition-all duration-200 hover:shadow-lg focus:outline-none focus:ring-2 focus:ring-[#2F2F2F]/30"
            style={{ color: "#2F2F2F", borderColor: "#2F2F2F" }}
            aria-label="Continue with Microsoft"
          >
            <Image
              src={`/logos/microsoft.png`}
              alt={`Microsoft Logo`}
              width={24}
              height={24}
              className="bg-white"
            />
            {type === "login"
              ? "Continue with Microsoft"
              : "Sign up with Microsoft"}
          </button>
        )}
        {/* I removed ID.me because we didn't get it working, but i did make the design for it incase we do get it working -- Sean B */}
        {/* {hasIDMe && (
          <button
            onClick={() => hintLogin(realm, "idme")}
            className="flex min-h-[44px] w-full min-w-[220px] items-center justify-center gap-3 rounded border-2 border-[#2E3F51] bg-white px-4 font-medium text-[#2E3F51] "
          >
            {type === "login" ? "Sign in with" : "Sign up with"}
            <Image
              src={`/logos/idme.png`}
              alt={`ID.me Logo`}
              width={44}
              height={44}
              className="bg-white"
            />
          </button>
        )} */}
      </div>
    </>
  );
};

export default SingleSignOn;
