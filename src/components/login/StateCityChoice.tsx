import React, { useState } from "react";
import { ComposableMap, Geographies, Geography } from "react-simple-maps";
import { Button } from "../ui/button";
import { useUpdateActiveTenant } from "@/hooks/api/useRealm";
import { ArrowLeft } from "lucide-react";

interface TenantType {
  id: string;
  name: string;
  displayName: string;
  logo: string;
  enabled: boolean;
  group: string;
}

const USA_TOPO_JSON = "https://cdn.jsdelivr.net/npm/us-atlas@3/states-10m.json";

const StateCityChoice = ({ tenants }: { tenants: TenantType[] }) => {
  const [selectedState, setSelectedState] = useState<string>("");
  const [cities, setCities] = useState<TenantType[]>([]);
  const makeActiveTenant = useUpdateActiveTenant();

  const statesWithCities = new Set(tenants.map((tenant) => tenant.group));

  const handleStateClick = (stateName: string) => {
    if (!statesWithCities.has(stateName)) return;
    setSelectedState(stateName);

    const filteredCities = tenants.filter(
      (tenant) => tenant.group === stateName,
    );
    setCities(filteredCities);
  };

  return (
    <div className="space-y-6 p-6">
      {!selectedState ? (
        <StateSelection
          statesWithCities={statesWithCities}
          handleStateClick={handleStateClick}
        />
      ) : (
        <CitySelection
          cities={cities}
          selectedState={selectedState}
          setSelectedState={setSelectedState}
          makeActiveTenant={makeActiveTenant}
        />
      )}
    </div>
  );
};

const StateSelection = ({
  statesWithCities,
  handleStateClick,
}: {
  statesWithCities: Set<string>;
  handleStateClick: (stateName: string) => void;
}) => {
  const [searchTerm, setSearchTerm] = useState<string>("");

  const filteredStates = Array.from(statesWithCities)
    .filter((state) => state?.toLowerCase()?.includes(searchTerm.toLowerCase()))
    .sort((a, b) => a.localeCompare(b));

  return (
    <div className="space-y-6">
      <h2 className="text-center text-xl font-bold text-gray-700">
        Select Your State
      </h2>
      {/* Map - Hidden on medium screens and smaller */}
      <div
        className="relative hidden w-full lg:block"
        style={{ aspectRatio: "16 / 9" }}
      >
        <ComposableMap projection="geoAlbersUsa" className="h-full w-full">
          <Geographies geography={USA_TOPO_JSON}>
            {({ geographies }) =>
              geographies.map((geo) => {
                const stateName = geo.properties.name;
                const isClickable = statesWithCities.has(stateName);

                return (
                  <Geography
                    key={geo.rsmKey}
                    geography={geo}
                    onClick={() => handleStateClick(stateName)}
                    style={{
                      default: {
                        fill: isClickable ? "#9AD35C" : "#a5a5a5",
                        outline: "none",
                        cursor: isClickable ? "pointer" : "not-allowed",
                      },
                      hover: {
                        fill: isClickable ? "#17A9CF" : "#D1D5DB",
                        outline: "none",
                        cursor: isClickable ? "pointer" : "",
                      },
                      pressed: {
                        fill: isClickable ? "#3B82F6" : "#D1D5DB",
                        outline: "none",
                      },
                    }}
                  />
                );
              })
            }
          </Geographies>
        </ComposableMap>
      </div>
      {/* List of States */}
      <div className="mx-auto max-w-2xl text-center">
        <div className="mt-4">
          <input
            type="text"
            placeholder="Search for your state..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="mb-8 w-full rounded border border-gray-300 px-4 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
          />
        </div>
        <h3 className="hidden text-lg font-semibold text-gray-700 lg:block">
          Or select your state from the list
        </h3>
        <div className="mt-4 grid grid-cols-1 gap-4 sm:grid-cols-2 md:grid-cols-3">
          {filteredStates.map((state) => (
            <button
              key={state}
              onClick={() => handleStateClick(state)}
              className="rounded bg-gray-200 px-4 py-2 text-gray-700 hover:bg-blue-600 hover:text-white"
            >
              {state}
            </button>
          ))}
          {filteredStates.length === 0 && (
            <p className="col-span-full text-center text-gray-500">
              No states match your search.
            </p>
          )}
        </div>
      </div>
    </div>
  );
};

const CitySelection = ({
  cities,
  selectedState,
  setSelectedState,
  makeActiveTenant,
}: {
  cities: TenantType[];
  selectedState: string;
  setSelectedState: React.Dispatch<React.SetStateAction<string>>;
  makeActiveTenant: ReturnType<typeof useUpdateActiveTenant>;
}) => {
  const [searchTerm, setSearchTerm] = useState<string>("");

  // Filter and sort cities
  const filteredCities = cities
    .filter((city) =>
      city.displayName.toLowerCase().includes(searchTerm.toLowerCase()),
    )
    .sort((a, b) => a.displayName.localeCompare(b.displayName));

  return (
    <div className="mx-auto max-w-2xl space-y-6">
      <Button
        variant="outline"
        onClick={() => setSelectedState("")}
        className="flex items-center gap-2 rounded-full bg-gray-100 px-4 py-2 text-gray-700 shadow hover:bg-blue-100 hover:text-blue-600 focus:outline-none focus:ring-2 focus:ring-blue-500"
      >
        <ArrowLeft size={16} /> Back to State Selection
      </Button>

      <h2 className="text-center text-xl font-bold text-gray-700">
        Select Your City in {selectedState}
      </h2>
      <div className="mt-4">
        {/* Search Box */}
        <input
          type="text"
          placeholder="Search for your city..."
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
          className="w-full rounded border border-gray-300 px-4 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
        />
      </div>
      <div className="mt-4 grid grid-cols-1 gap-4 sm:grid-cols-2 md:grid-cols-3">
        {filteredCities.map((city) => (
          <button
            key={city.id}
            onClick={() => {
              console.log(`Selected city: ${city.displayName}`);
              makeActiveTenant.mutate(city.name, {
                onSuccess: () => {
                  window.location.reload();
                },
              });
            }}
            className="rounded bg-gray-200 px-4 py-2 text-gray-700 hover:bg-blue-600 hover:text-white"
          >
            {city.displayName}
          </button>
        ))}
        {filteredCities.length === 0 && (
          <p className="col-span-full text-center text-gray-500">
            No cities match your search.
          </p>
        )}
      </div>
    </div>
  );
};

export default StateCityChoice;
