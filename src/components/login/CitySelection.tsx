"use client";
import LoginSignupContainer from "@/components/landingPage/LoginSignupContainer";
import Image from "next/image";
import { useGetAllTenants, useUpdateActiveTenant } from "@/hooks/api/useRealm";
import Loading from "@/app/(app)/loading";
import { useKeycloak } from "@/hooks/useKeycloak";
import { useEffect, useState } from "react";
import { Button } from "../ui/button";
import StateCityChoice from "./StateCityChoice";
import { useSearchParams } from "next/navigation";

export default function CitySelection({ type }: { type: "login" | "signup" }) {
  const tenant = useSearchParams().get("tenant");

  const { data, isError, isLoading } = useGetAllTenants();
  const { getUserProfile, logout } = useKeycloak();
  const [user, setUser] = useState<any>(null);
  const [loading, setLoading] = useState<boolean>(true);
  const makeActiveTenant = useUpdateActiveTenant();
  const [tenantCheckComplete, setTenantCheckComplete] = useState(false);

  useEffect(() => {
    const userProfile = async () => {
      try {
        const userInfo = await getUserProfile();
        setUser(userInfo);
        setLoading(false);
      } catch (error) {
        console.error("Failed to fetch user profile", error);
        setUser(null);
        setLoading(false);
      }
    };
    userProfile();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  useEffect(() => {
    if (!isLoading && data) {
      const tenantExists = data?.some((t: any) => t.name === tenant);
      if (tenantExists) {
        makeActiveTenant.mutate(tenant as string, {
          onSuccess: () => {
            window.location.reload();
          },
        });
      } else {
        setTenantCheckComplete(true);
      }
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [isLoading, data]);


  if (isLoading || makeActiveTenant.isLoading || !tenantCheckComplete) return <Loading text="Loading Cities" />;
  if (isError) {
    return (
      <div className="m-20 flex items-center justify-center">
        <div className="rounded-md border border-red-500 bg-red-100 p-10 text-center">
          <h2 className="mb-2 text-xl font-semibold text-red-600">Oops!</h2>
          <p className="text-red-500">
            There was an error loading the city selection. Please try again
            later.
          </p>
        </div>
      </div>
    );
  }


  const primaryDisplay = "Welcome";
  const secondaryDisplay =
    "Before you can access your account, you must first select your city.";

  return (
    <LoginSignupContainer>
      <Image
        src="/logos/clerkxpressLogo.png"
        width={220}
        height={200}
        alt={`Logo for ClerkXpress`}
        className="my-10"
      />
      <h1 className="mb-2 text-3xl font-semibold">{primaryDisplay}</h1>
      <p>{secondaryDisplay}</p>
      <div className="mt-20 w-full">
        <StateCityChoice tenants={data} />

        {loading && <div>Getting User Profile</div>}
        {!loading && user && (
          <div className="mt-20 flex flex-col gap-4 text-center items-center ">
            <div className="flex flex-col text-sm">
              <p className="text-sm font-semibold">Currently signed in as:</p>
              <p className="text-lg font-medium">{user?.name || "User"}</p>
              <p className="text-gray-500">
                {user?.email || "Email not available"}
              </p>
            </div>

            <Button
              variant="outline"
              size="sm"
              onClick={logout}
              aria-label="Logout"
              className="transition hover:bg-gray-100 w-full max-w-[200px] text-gray-600"
            >
              Logout
            </Button>
          </div>
        )}
      </div>
    </LoginSignupContainer>
  );
}