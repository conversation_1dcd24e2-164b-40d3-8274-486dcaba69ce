"use client";

import { useKeycloak } from "@/hooks/useKeycloak";

const EmailLoginButton = ({
  type,
}: {
  type: "login" | "signup";
}) => {
  const { login, register } = useKeycloak();
  return (
    <div className="min-w-[240px] ">
      <button
        className="bg-clerk-primary text-white font-semibold w-full py-3 rounded-full shadow-md transition-all duration-200 hover:bg-clerk-primary/90 focus:ring-2 focus:ring-blue-300 focus:outline-none text-base"
        type="button"
        onClick={() => {
          console.log("EmailLoginButton clicked");
          type === "login" ? login() : register()
        }}
      >
        Continue with Email
      </button>
    </div>
  );
};

export default EmailLoginButton;
