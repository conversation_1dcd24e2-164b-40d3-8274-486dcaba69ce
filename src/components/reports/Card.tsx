"use client";
import Modal from "@/components/modal/Modal";
import React, { useState } from "react";
import { IconType } from "react-icons";
import { MdOutlineCalendarMonth } from "react-icons/md";

import { FormType } from "@/components/reports/types/cardTypes";

import NoCardReport from "./NoCardReport";

const BGColors: {
  [key: string]: string;
} = {
  blue: "bg-gradient-to-br from-blue-700 to-blue-600 text-blue-100",
  green: "bg-gradient-to-br from-teal-700 to-emerald-600 text-teal-100",
  red: "bg-gradient-to-br from-red-700 to-red-600 text-red-100",
  yellow: "bg-gradient-to-br from-yellow-700 to-yellow-600 text-yellow-100",
  purple: "bg-gradient-to-br from-purple-700 to-purple-600 text-purple-100",
  pink: "bg-gradient-to-br from-pink-700 to-pink-600 text-pink-100",
  indigo: "bg-gradient-to-br from-indigo-700 to-indigo-600 text-indigo-100",
  teal: "bg-gradient-to-br from-teal-700 to-teal-600 text-teal-100",
  neutral: "bg-gradient-to-br from-neutral-700 to-neutral-600 text-neutral-100",
};

const Icons: {
  [key: string]: IconType;
} = {
  date: MdOutlineCalendarMonth,
};

const Icon = ({ type, color = "blue" }: { type: string; color: string }) => {
  return (
    <div
      className={`${BGColors[color]} text-white w-8 h-8 rounded-full flex items-center justify-center`}
    >
      {React.createElement(Icons[type], {
        className: "group-hover:text-neutral-100",
      })}
    </div>
  );
};

const CardInformation = ({ card }: { card: FormType }) => {
  return (
    <div className="flex flex-col gap-2 h-full">
      <h2 className="font-bold text-lg w-full mb-auto line-clamp-2 text-neutral-900">
        {card.label}
      </h2>
      <p className="text-xs text-neutral-800 line-clamp-4 mb-auto">
        {card.description}
      </p>
    </div>
  );
};

const Card = ({ card, color }: { card: FormType; color: string }) => {
  const [showModal, setShowModal] = useState(false);

  return (
    <>
      <button
        key={card.reportTypeId}
        className="
          group bg-white p-4 rounded flex flex-col gap-2 transition-all shadow hover:shadow-xl text-left opacity-80 hover:opacity-100"
        onClick={() => setShowModal(true)}
      >
        <div className="flex justify-between">
          <Icon type={card.type} color={color} />
        </div>
        <CardInformation card={card} />
      </button>

      <Modal
        title={card.label}
        isOpen={showModal}
        onClose={() => setShowModal(false)}
      >
        <NoCardReport
          card={card}
          closeModal={() => {
            setShowModal(false);
          }}
        />
      </Modal>
    </>
  );
};

export default Card;
