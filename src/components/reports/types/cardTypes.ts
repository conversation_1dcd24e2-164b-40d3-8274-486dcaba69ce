export type ParameterType = {
  type: string;  // now it's a generic string
  label: string;
};

export type exportType = {
  icon: string;
  color: string;
  label: string;
  type: string;
}

export type DateRangeParameterType = ParameterType & {
  type: 'dateRange'; 
  params: string[];
  defaultParam: string;
};

export type TextboxParameterType = ParameterType & {
  type: 'text';
  fieldName: string;
  placeholder?: string;
  required?: boolean;
  validation?: {
    regex: string;
    message: string;
  }
}

export type FormType = {
  reportTypeId: string;
  label: string;
  description: string;
  link?: string;
  type: string;
  parameters?: (ParameterType | DateRangeParameterType | TextboxParameterType)[]; 
  exportTypes?: string[];
};