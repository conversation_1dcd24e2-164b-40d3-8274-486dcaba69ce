"use client";
interface FormatOptionProps {
  selectedFormat: string;
  setSelectedFormat: (format: string) => void;
  value: string;
  icon: JSX.Element;
  color: string;
}

const FormatOption = ({ selectedFormat, setSelectedFormat, value, icon, color }: FormatOptionProps) => {
  const isSelected = selectedFormat === value;

  return (
    <label
      className={`flex items-center gap-3 p-2 rounded-lg bg-white border border-gray-200 cursor-pointer transition-all hover:shadow-md hover:border-${color}-300 w-fit`}
    >
      <input
        type="radio"
        value={value}
        checked={isSelected}
        onChange={(e) => setSelectedFormat(e.target.value)}
        className="hidden"
      />
      <div
        className={`flex items-center justify-center w-6 h-6 rounded-full border ${isSelected ? `border-${color}-500 bg-${color}-500` : "border-gray-300"}`}
      >
        {isSelected && <div className="w-2 h-2 bg-white rounded-full"></div>}
      </div>
      {icon}
      <span className={`font-medium ${`text-${color}-600`}`}>{value.toUpperCase()}</span>
    </label>
  );
};

export default FormatOption;
