"use client";
import Button from "../ui/buttons/Button";
import { FormType } from "@/components/reports/types/cardTypes";
import { useReportWizard } from "./hooks/useReportWizard";
import FormatOption from "./FormatOption";
import dynamicIconImport from "@/utils/dynamicImport";
import { FaFilePdf } from "react-icons/fa";

const NoCardReport = ({
  card,
  closeModal,
}: {
  card: FormType;
  closeModal: () => void;
}) => {
  const { downloadLoading, handleSubmit, onsubmit, Parameter, selectedFormat, setSelectedFormat } = useReportWizard(card, closeModal);
  return (
    <div className="flex flex-col gap-5">
      <div className="">{card.description}</div>
      <div className="flex flex-col gap-4 p-2 rounded-lg max-w-md">
        <div className="flex flex-wrap gap-4">
          {card?.exportTypes ? (
            card?.exportTypes?.map(exportType => {
              const dynamicIcon = dynamicIconImport(exportType, "text-2xl");
              if (dynamicIcon) {
                return (<FormatOption
                  key={exportType}
                  selectedFormat={selectedFormat}
                  setSelectedFormat={setSelectedFormat}
                  value={exportType}
                  color={dynamicIcon.color}
                  icon={dynamicIcon.Icon}
                />)
              }
            })
          ) : <FormatOption
            key="pdf"
            selectedFormat={selectedFormat}
            setSelectedFormat={setSelectedFormat}
            value="pdf"
            color="red"
            icon={<FaFilePdf className="text-2xl text-red-500"/>}
          />}

        </div>
      </div>
      <form onSubmit={handleSubmit(onsubmit)} className="flex flex-col gap-16">
        {card.parameters?.map((parameter, index) => (
          <Parameter key={index} parameter={parameter} />
        ))}
        <Button variant="primary" type="submit" disabled={downloadLoading}>
          {downloadLoading ? "Generating..." : "Download"}
        </Button>
      </form>
    </div>
  );
};

export default NoCardReport;
