import { requests } from "@/utils/agent";
import { atom } from "jotai";

export const modalOpenAtom = atom<boolean>(false);
export const downloadListAtom = atom<ReportType[]>([]);

export type ReportType = {
  reportId: string;
  documentId: string;
  title: string;
  status: string;
  createdBy: string;
  completedDate: string;
  updatedAt: string;
};

// Function to update the download list
export const updateDownloadList = (
  currentList: ReportType[],
  updatedReport: ReportType
) => {
  // Assign current date and time to updatedReport
  updatedReport.updatedAt = new Date().toISOString();

  const reportIndex = currentList.findIndex(
    (r) => r.reportId === updatedReport.reportId
  );

  let newList;
  if (reportIndex !== -1) {
    newList = [...currentList];
    newList[reportIndex] = updatedReport;
  } else {
    newList = [updatedReport, ...currentList];
  }

  // Sort the list by updatedAt in descending order
  newList.sort((a, b) => new Date(b.updatedAt).getTime() - new Date(a.updatedAt).getTime());

  localStorage.setItem("downloadList", JSON.stringify(newList));
  return newList;
};

// Function to remove a report from the list
export const removeFromList = (currentList: ReportType[], reportId: string) => {
  const updatedList = currentList.filter(
    (report) => report.reportId !== reportId
  );
  localStorage.setItem("downloadList", JSON.stringify(updatedList));
  return updatedList;
};


export const downloadReport = async (documentId:string) => {
  const url = '/document-service/download?documentUUID=' + documentId;
  console.log(url)
  const blob = await requests.get(url, {
    responseType: "blob",
  });
  downloadPdf(blob);
}

const downloadPdf = (blob: any) => {
  const link = document.createElement("a");
  link.href = URL.createObjectURL(blob);
  link.target = "_blank";
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
};
