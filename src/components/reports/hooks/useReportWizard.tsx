import { useState, useEffect } from "react";
import { Controller, useForm } from "react-hook-form";
import { formatDate, getTodaysDate } from "@/components/reports/dateUtils";
import {
  DateRangeParameterType,
  FormType,
  ParameterType,
  TextboxParameterType,
} from "@/components/reports/types/cardTypes";
import {
  GenerateReport,
  useGenerateReport,
  useGetReportById,
} from "@/hooks/api/useReport";
import { useAtom } from "jotai";
import {
  downloadListAtom,
  modalOpenAtom,
  updateDownloadList,
} from "../scripts/downloadCenterUtils";
import {
  addMonths,
  endOfMonth,
  startOfMonth,
  subDays,
  subMonths,
} from "date-fns";

export const useReportWizard = (card: FormType, closeModal: any) => {
  const [activeDate, setActiveDate] = useState<string>("");
  const [downloadLoading, setDownloadLoading] = useState<boolean>(false);
  const [selectedFormat, setSelectedFormat] = useState("pdf");
  const {
    register,
    handleSubmit,
    control,
    setValue,
    formState: { errors },
  } = useForm();
  const [reportUuid, setReportUuid] = useState<string | null>(null);
  const [downloadList, setDownloadList] = useAtom(downloadListAtom);
  const [modalOpen, setModalOpen] = useAtom(modalOpenAtom);

  const generateReport = useGenerateReport();
  const { data: report, isLoading: isLoadingReport } =
    useGetReportById(reportUuid);

  useEffect(() => {
    const dateRangeParameter = card.parameters?.find(
      (p): p is DateRangeParameterType => p.type === "dateRange",
    );
    if (dateRangeParameter) {
      getDateValues(dateRangeParameter.defaultParam);
    } else {
      getDateValues("today");
    }

    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  useEffect(() => {
    if (generateReport.isSuccess && generateReport.data?.uuid) {
      console.log("success");
      setReportUuid(generateReport.data.uuid);
      setDownloadLoading(false);
    }
  }, [generateReport.isSuccess, generateReport.data]);

  useEffect(() => {
    if (report) {
      report.title = card.label;
      console.log(report);
      const list = updateDownloadList(downloadList, report);
      console.log(list);
      setDownloadList(list);
      setModalOpen(true);
      closeModal(false);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [report]);

  const onsubmit = (data: { [key: string]: string }) => {
    console.log(data);
    setDownloadLoading(true);
    const requestData: GenerateReport = {
      reportTypeId: card.reportTypeId,
      selectedFormat: selectedFormat,
      body: data,
    };

    generateReport.mutate(requestData);
  };

  const getDateValues = (defaultParam: string) => {
    const currentDate = new Date();

    switch (defaultParam) {
      case "today":
        const today = formatDate(currentDate);
        setValue("startDate", today);
        setValue("endDate", today);
        setActiveDate("today");
        break;
      case "yesterday":
        const yesterday = formatDate(subDays(currentDate, 1));
        setValue("startDate", yesterday);
        setValue("endDate", yesterday);
        setActiveDate("yesterday");
        break;
      case "lastMonth":
        const lastMonth = subMonths(currentDate, 1);

        setValue("startDate", formatDate(startOfMonth(lastMonth)));
        setValue("endDate", formatDate(endOfMonth(lastMonth)));
        setActiveDate("lastMonth");
        break;
      case "currentMonth":
        setValue("startDate", formatDate(startOfMonth(currentDate)));
        setValue("endDate", formatDate(endOfMonth(currentDate)));
        setActiveDate("currentMonth");
        break;
      case "nextMonth":
        const nextMonth = addMonths(currentDate, 1);
        setValue("startDate", formatDate(startOfMonth(nextMonth)));
        setValue("endDate", formatDate(endOfMonth(nextMonth)));
        setActiveDate("nextMonth");
        break;
      default:
        setValue("startDate", formatDate(getTodaysDate()));
        setValue("endDate", formatDate(getTodaysDate()));
        setActiveDate("today");
        break;
    }
  };

  const DateButton = ({
    label,
    value,
    onClick,
  }: {
    label: string;
    value: string;
    onClick: () => void;
  }) => (
    <button
      className={`
        ${activeDate === value ? "text-blue-500" : "text-neutral-400"}
      `}
      onClick={onClick}
    >
      {label}
    </button>
  );

  const DateInput = ({ control, name }: { control: any; name: string }) => (
    <Controller
      control={control}
      name={name}
      render={({ field }) => (
        <input
          {...field}
          type="date"
          className="rounded-sm border border-neutral-300 p-2"
        />
      )}
    />
  );

  const Parameter = ({ parameter }: { parameter: ParameterType }) => {
    switch (parameter.type) {
      case "dateRange":
        const dateRangeParameter = parameter as DateRangeParameterType;
        return (
          <div className="flex flex-col gap-2">
            <small className="text-sm font-semibold">Date Range</small>
            <div className="mb-6 flex gap-4">
              {dateRangeParameter.params.map((param) => {
                switch (param) {
                  case "yesterday":
                    return (
                      <DateButton
                        key={param}
                        label="Yesterday"
                        value="yesterday"
                        onClick={() => {
                          getDateValues("yesterday");
                        }}
                      />
                    );
                  case "today":
                    return (
                      <DateButton
                        key={param}
                        label="Today"
                        value="today"
                        onClick={() => {
                          getDateValues("today");
                        }}
                      />
                    );
                  case "lastMonth":
                    return (
                      <DateButton
                        key={param}
                        label="Last Month"
                        value="lastMonth"
                        onClick={() => {
                          getDateValues("lastMonth");
                        }}
                      />
                    );
                  case "currentMonth":
                    return (
                      <DateButton
                        key={param}
                        label="Current Month"
                        value="currentMonth"
                        onClick={() => {
                          getDateValues("currentMonth");
                        }}
                      />
                    );
                  case "nextMonth":
                    return (
                      <DateButton
                        key={param}
                        label="Next Month"
                        value="nextMonth"
                        onClick={() => {
                          getDateValues("nextMonth");
                        }}
                      />
                    );
                  default:
                    return null;
                }
              })}
            </div>
            <div className="flex gap-2">
              <DateInput control={control} name="startDate" />
              <DateInput control={control} name="endDate" />
            </div>
          </div>
        );

      case "text":
        const textBoxParameter = parameter as TextboxParameterType;
        let regexPattern = undefined;
        let validationMessage = "Invalid input";

        if (textBoxParameter.validation) {
          regexPattern = new RegExp(textBoxParameter.validation.regex);
          if (textBoxParameter.validation.message) {
            validationMessage = textBoxParameter.validation.message;
          }
        }
        return (
          <div className="flex flex-col gap-2">
            <small className="text-sm font-semibold">
              {textBoxParameter?.label}{" "}
              {textBoxParameter?.required && (
                <span className="text-red-600">(required)</span>
              )}
            </small>
            <div className="flex gap-4">
              <input
                {...register(textBoxParameter.fieldName, {
                  required: textBoxParameter.required ? "This field is required." : false,
                  pattern: regexPattern
                    ? {
                        value: regexPattern,
                        message: validationMessage,
                      }
                    : undefined,
                })}
                type="text"
                className="rounded-sm border border-neutral-300 p-2"
                placeholder={textBoxParameter?.placeholder}
                required={textBoxParameter?.required ?? false}
              />
            </div>
            {errors && errors[textBoxParameter.fieldName] && (
              <div className="text-sm text-red-600">
                {(errors as any)[textBoxParameter?.fieldName]?.message}
              </div>
            )}
          </div>
        );

      default:
        return null;
    }
  };

  const downloadPdf = (blob: any) => {
    const link = document.createElement("a");
    link.href = URL.createObjectURL(blob);
    link.target = "_blank";
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  return {
    register,
    handleSubmit,
    control,
    Parameter,
    onsubmit,
    downloadLoading,
    activeDate,
    setValue,
    closeModal,
    downloadPdf,
    selectedFormat,
    setSelectedFormat
  };
};
