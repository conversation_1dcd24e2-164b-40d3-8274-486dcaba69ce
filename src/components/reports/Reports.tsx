"use client";
import { useState } from "react";
import { FiSearch } from "react-icons/fi";
import Card from "@/components/reports/Card";
import HeaderContainer from "@/components/ui/header/HeaderContainer";
import { useGetAllReportBySection } from "@/hooks/api/useReport";
import Loading from "@/app/(app)/loading";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { useParams, useSearchParams } from "next/navigation";
import { cn } from "@/lib/utils";
import Link from "next/link";

const Reports = () => {
  const [filter, setFilter] = useState("");
  const activeTab = useSearchParams().get("tab");
  const { data, isError, isLoading } = useGetAllReportBySection(
    activeTab as string,
  );

  if (!activeTab) return <div>Invalid Report Group</div>;

  const reports = data?.reports ?? [];

  const filteredForms = reports?.filter(
    (form: any) =>
      form.label.toLowerCase().includes(filter.toLowerCase()) ||
      form.description.toLowerCase().includes(filter.toLowerCase()),
  );

  const tabOptions = [
    {
      tab: "clerkFormsAndReports",
      label: "Clerk Documents",
    },
    {
      tab: "dogFormsAndReports",
      label: "Dog documents",
    },
  ];

  return (
    <div className="flex min-h-full flex-col gap-6 overflow-y-auto">
      <HeaderContainer
        label="Forms and Reports"
        description={tabOptions.find((tab) => tab.tab === activeTab)?.label}
        color={"blue"}
      >
        <div className="grid grid-cols-4"></div>
      </HeaderContainer>
      <div className="flex items-center px-6">
        {tabOptions.map((tab) => {
          return (
            <Link
              key={tab.tab}
              href={`/reports?tab=${tab.tab}`}
              aria-disabled={tab.tab === activeTab}
              className={cn(
                "cursor-pointer border-b-[3px] px-4 py-2 text-lg",
                tab.tab === activeTab
                  ? "border-blue-500 bg-white font-semibold text-blue-500"
                  : "text-gray-500 hover:bg-neutral-200 hover:text-neutral-700",
              )}
            >
              {tab.label}
            </Link>
          );
        })}
      </div>
      {isLoading ? (
        <Loading fixed={false} />
      ) : (
        <div className="flex flex-col gap-6 p-6">
          {!data ? (
            <div className="text-xl font-semibold text-red-500">No Reports</div>
          ) : (
            <>
              <div className="flex max-w-sm items-center gap-2 rounded-sm bg-white p-2 shadow">
                <FiSearch className="shrink-0" />
                <input
                  id="search"
                  name="search"
                  type="text"
                  onChange={(e) => setFilter(e.target.value)}
                  value={filter}
                  className="w-full focus:outline-none"
                />
              </div>
              <div
                className="
                  grid grid-cols-1 gap-4
                  md:grid-cols-2
                  lg:grid-cols-3
                  xl:grid-cols-4
                "
              >
                {filteredForms &&
                  filteredForms.map((form: any) => (
                    <Card key={form.reportId} card={form} color={"blue"} />
                  ))}
              </div>
            </>
          )}
        </div>
      )}
    </div>
  );
};

export default Reports;
