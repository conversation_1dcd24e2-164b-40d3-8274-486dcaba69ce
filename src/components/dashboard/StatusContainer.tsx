type StatusContainerProps = {
  status: string | undefined;
};

const StatusContainer = ({ status }: StatusContainerProps) => {
  const colorMap: { [key: string]: string } = {
    "Review Required": "border-sky-600 text-sky-600",
    "Pending Payment": "border-orange-600 text-orange-600",
    Expired: "border-red-600 text-red-600",
    New: "border-green-600 text-green-600",
    "Payment Received": "border-green-600 text-green-600",
  };

  return (
    <div
      className={`px-4 py-1 text-sm inline-flex items-center justify-center rounded border ${
        colorMap[status ?? ""]
      }`}
    >
      {status}
    </div>
  );
};

export default StatusContainer;
