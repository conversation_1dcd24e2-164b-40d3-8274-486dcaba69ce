"use client";
import { FiChevronLeft, FiChevronRight } from "react-icons/fi";
import { useState } from "react";

type PaginationContainerProps = {
  table: any;
};

const PaginationContainer = ({ table }: PaginationContainerProps) => {
  const [opacityLeft, setOpacityLeft] = useState(1);
  const [opacityRight, setOpacityRight] = useState(1);

  return (
    <div className="flex items-center gap-2">
      <FiChevronLeft
        className="cursor-pointer"
        size={30}
        style={{ opacity: opacityLeft }}
        onMouseDown={() => table.getCanPreviousPage() && setOpacityLeft(0.5)}
        onMouseUp={() => setOpacityLeft(1)}
        onClick={() => table.getCanPreviousPage() && table.previousPage()}
      >
        {"<"}
      </FiChevronLeft>
      <span className="flex items-center gap-1">
        <p className="text-sm font-semibold text-neutral-500">
          {table.getState().pagination.pageIndex + 1} of {table.getPageCount()}
        </p>
      </span>
      <FiChevronRight
        className="cursor-pointer"
        size={30}
        style={{ opacity: opacityRight }}
        onMouseDown={() => table.getCanNextPage() && setOpacityRight(0.5)}
        onMouseUp={() => setOpacityRight(1)}
        onClick={() => table.getCanNextPage() && table.nextPage()}
      >
        {">"}
      </FiChevronRight>
    </div>
  );
};

export default PaginationContainer;
