import { useQuery, useMutation } from "@tanstack/react-query";
import { requests } from "@/utils/agent";

// Get Form by Id
export const useGetForm = (formId: string) => {
  console.log("Running useGetForm", formId);
  return useQuery({
    queryKey: ["form", formId],
    queryFn: () => requests.get<any>(`/license/multi-form-builder/${formId}`),
  });
};

// Get all Forms
export const useGetAllForms = () => {
  console.log("Running useGetAllForms");
  return useQuery({
    queryKey: ["forms"],
    queryFn: () => requests.get<any>(`/license/multi-form-builder`),
  });
};

// Create Form
export const useCreateForm = () => {
  console.log("Running useCreateForm");
  return useMutation((data: any) =>
    requests.post<any>(`/license/multi-form-builder`, data)
  );
};

// Update Form
export const useUpdateForm = (formId: string) => {
  console.log("Running useUpdateForm");
  return useMutation((data: any) =>
    requests.put<any>(`/license/multi-form-builder/${formId}`, data)
  );
};

// Get form by Id with optional query params
export const useGetFormWithQuery = (formId: string, queryParams: any) => {
  console.log("Running useGetFormWithQuery", formId, queryParams);

  const queryString = new URLSearchParams(queryParams).toString();
  const endpoint =
    `/coordinator/report/:reportId/${formId}` +
    (queryString ? `?${queryString}` : "");

  return useQuery({
    queryKey: ["form", formId, queryParams],
    queryFn: () => requests.get<any>(endpoint),
  });
};
