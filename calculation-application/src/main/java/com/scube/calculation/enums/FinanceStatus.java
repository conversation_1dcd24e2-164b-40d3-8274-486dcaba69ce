package com.scube.calculation.enums;

import lombok.Getter;

import java.util.ArrayList;
import java.util.List;

@Getter
public enum FinanceStatus {
    UNPAID("unpaid"),
    PARTIALLY_PAID("partiallyPaid"),
    AUTHORIZED("authorized"),
    CAPTURED("captured"),
    REFUNDED("refunded"),
    PARTIALLY_REFUNDED("partiallyRefunded");

    private final String key;

    FinanceStatus(String key) {
        this.key = key;
    }

    public static FinanceStatus valueOfIgnoreCase(String key) {
        for (FinanceStatus status : FinanceStatus.values()) {
            if (status.getKey().equalsIgnoreCase(key)) {
                return status;
            }
        }
        return null;
    }

    public static List<FinanceStatus> getAllStatuses() {
        List<FinanceStatus> statuses = new ArrayList<>();
        for (FinanceStatus status : FinanceStatus.values()) {
            statuses.add(status);
        }
        return statuses;
    }
}


