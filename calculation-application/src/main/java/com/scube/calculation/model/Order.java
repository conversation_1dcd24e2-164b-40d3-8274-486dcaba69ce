package com.scube.calculation.model;

import com.scube.audit.auditable.entity.AuditableBaseWithProperties;
import com.scube.calculation.enums.FinanceStatus;
import com.scube.calculation.enums.OrderStatus;
import jakarta.persistence.*;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.hibernate.envers.Audited;

import java.time.Instant;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

@Getter
@Setter
@Entity
@Audited
@Table(name = "order_")
@NoArgsConstructor
@AllArgsConstructor
public class Order extends AuditableBaseWithProperties<UUID> {
    public static final String ORDER_ID = "order_id";

    @Id
    @GeneratedValue(strategy = GenerationType.UUID)
    @Column(name = "order_id")
    private UUID id;

    @Size(max = 255)
    private String userId;

    @Enumerated(EnumType.STRING)
    private OrderStatus status;

    @Enumerated(EnumType.STRING)
    private FinanceStatus financeStatus;

    @Size(max = 255)
    private String orderNumber;
    private Instant orderPaidDate;

    @OneToMany(mappedBy = "order", cascade = CascadeType.ALL)
    private List<OrderItem> orderItems = new ArrayList<>();
    @OneToMany(mappedBy = "order", cascade = CascadeType.ALL)
    private List<OrderAdditionalFee> orderAdditionalFees = new ArrayList<>();

    public Order(Cart cart) {
        this.status = OrderStatus.ORDER_DRAFT;
        this.financeStatus = FinanceStatus.UNPAID;
        this.userId = cart.getUserId();
        this.orderItems = copyCartItems(cart);
        this.orderAdditionalFees = copyCartAdditionalFee(cart);
    }

    public Order(String userId, List<OrderItem> orderItems) {
        this.status = OrderStatus.ORDER_DRAFT;
        this.financeStatus = FinanceStatus.UNPAID;
        this.userId = userId;
        this.orderItems = orderItems;
    }

    public List<OrderAdditionalFee> copyCartAdditionalFee(Cart cart) {
        List<OrderAdditionalFee> items = new ArrayList<>();
        for (AdditionalFee additionalFee : cart.getAdditionalFees()) {
            OrderAdditionalFee orderAdditionalFee = new OrderAdditionalFee(additionalFee,this);
            orderAdditionalFee.setOrder(this);
            items.add(orderAdditionalFee);
        }
        return items;
    }

    public List<OrderItem> copyCartItems(Cart cart) {
        List<OrderItem> items = new ArrayList<>();
        for (CartItem cartItem : cart.getCartItems()) {
            OrderItem orderItem = new OrderItem(cartItem);
            orderItem.setOrder(this);
            items.add(orderItem);
        }
        return items;
    }

    public void lockOrder() {
        if (this.status == OrderStatus.ORDER_DRAFT) {
            this.status = OrderStatus.ORDER_PENDING;
        }
    }

    public void markOrderAuthorized() {
        if (this.status == OrderStatus.ORDER_DRAFT || this.status == OrderStatus.ORDER_PENDING) {
            this.status = OrderStatus.ORDER_PENDING;
            this.financeStatus = FinanceStatus.AUTHORIZED;
        }
    }

    public void markOrderPaid() {
        if (this.status == OrderStatus.ORDER_DRAFT || this.status == OrderStatus.ORDER_PENDING) {
            this.status = OrderStatus.ORDER_ACTIVE;
            this.financeStatus = FinanceStatus.CAPTURED;
            this.orderPaidDate = Instant.now();
        }
    }
}