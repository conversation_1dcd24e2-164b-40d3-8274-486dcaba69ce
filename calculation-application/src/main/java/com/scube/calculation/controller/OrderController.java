package com.scube.calculation.controller;

import com.c4_soft.springaddons.security.oidc.OpenidClaimSet;
import com.scube.calculation.dto.AddItemRequest;
import com.scube.calculation.dto.GetOrderIDResponse;
import com.scube.calculation.dto.PageDTO;
import com.scube.calculation.dto.order.OrderInvoiceResponse;
import com.scube.calculation.dto.order.OrderSummaryResponse;
import com.scube.calculation.permission.Permissions;
import com.scube.calculation.service.OrderService;
import com.scube.client.ServiceUrlConstant;
import com.scube.client.annotation.GenerateHttpExchange;
import jakarta.annotation.security.RolesAllowed;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import org.springframework.http.HttpStatus;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.util.ObjectUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDate;
import java.util.List;
import java.util.Map;
import java.util.UUID;

@RestController
@RequestMapping("order")
@AllArgsConstructor
@GenerateHttpExchange(value = ServiceUrlConstant.CALCULATION_SERVICE)
@Validated
public class OrderController {
    private final OrderService orderService;

    @PostMapping("cart/{cartId}")
    @RolesAllowed(Permissions.Order.CREATE_ORDER_FROM_CART)
    public OrderInvoiceResponse createOrderFromCart(@PathVariable UUID cartId) {
        return orderService.makeOrderFromCartWithResponse(cartId);
    }

    @PostMapping("/{orderId}/rollBackToCart/{cartId}")
    @ResponseStatus(HttpStatus.OK)
    @RolesAllowed(Permissions.Order.ROLL_BACK_TO_CART)
    public void rollBackToCart(@PathVariable UUID cartId, @PathVariable UUID orderId) {
        orderService.rollbackOrderToCart(cartId, orderId);
    }

    @PostMapping("{orderId}/cancel")
    @ResponseStatus(HttpStatus.NO_CONTENT)
    @RolesAllowed(Permissions.Order.CANCEL_ORDER)
    public void cancelOrder(@PathVariable UUID orderId) {
        orderService.cancelOrder(orderId);
    }

    @PostMapping("{orderId}/transfer")
    @ResponseStatus(HttpStatus.NO_CONTENT)
    @RolesAllowed(Permissions.Order.TRANSFER_ORDER)
    public void transferOrder(@PathVariable UUID orderId, @RequestParam(required = false) @Size(max = 255) String userId, @AuthenticationPrincipal OpenidClaimSet jwt) {
        if (ObjectUtils.isEmpty(userId))
            userId = jwt.getSubject();
        orderService.transferOrder(orderId, userId);
    }

    @PatchMapping("{orderId}/edit")
    @ResponseStatus(HttpStatus.NO_CONTENT)
    @RolesAllowed(Permissions.Order.EDIT_ORDER)
    public void editOrder(@PathVariable UUID orderId) {
        orderService.makeCartFromOrder(orderId);
    }

    @GetMapping("{orderId}")
    @RolesAllowed(Permissions.Order.GET_ORDER)
    public OrderInvoiceResponse getOrder(@PathVariable UUID orderId) {
        return orderService.getOrderInvoice(orderId);
    }

    @GetMapping("id/{itemId}")
    @RolesAllowed(Permissions.Order.GET_ORDER_ID)
    public GetOrderIDResponse getOrderId(@PathVariable UUID itemId) {
        return orderService.getOrderId(itemId);
    }

    @PostMapping("fromItems")
    @RolesAllowed(Permissions.Order.CREATE_ORDER_FROM_ITEMS)
    public OrderInvoiceResponse createOrderFromItems(@AuthenticationPrincipal OpenidClaimSet jwt,
                                                     @RequestBody List<AddItemRequest> request,
                                                     @RequestParam(required = false) @Size(max = 255) String userId) {
        if (ObjectUtils.isEmpty(userId))
            userId = jwt.getSubject();

        return orderService.makeOrderFromItems(userId, request);
    }

    @GetMapping
    @RolesAllowed(Permissions.Order.LIST_ORDERS)
    public PageDTO<OrderSummaryResponse> listOrders(@RequestParam(required = false) @Size(max = 255) String userId,
                                                    @RequestParam(required = false) @Size(max = 255) String status,
                                                    @RequestParam(required = false, defaultValue = "1") int pageNumber,
                                                    @RequestParam(required = false, defaultValue = "10") int pageSize,
                                                    @AuthenticationPrincipal OpenidClaimSet jwt) {
        if (ObjectUtils.isEmpty(userId))
            userId = jwt.getSubject();
        return orderService.listOrders(userId, status, pageNumber, pageSize);
    }

    @GetMapping("refundable")
    @RolesAllowed(Permissions.Order.LIST_REFUNDABLE_ORDERS)
    public PageDTO<OrderSummaryResponse> listRefundableOrders(@RequestParam(required = false) @Size(max = 255) String userId,
                                                              @RequestParam(required = false) @Size(max = 255) String orderNumber,
                                                              @RequestParam(value = "startDate", required = false) LocalDate startDate,
                                                              @RequestParam(value = "endDate", required = false) LocalDate endDate,
                                                              @RequestParam(required = false, defaultValue = "1") int pageNumber,
                                                              @RequestParam(required = false, defaultValue = "10") int pageSize) {

        return orderService.listRefundableOrders(userId, orderNumber, startDate, endDate, pageNumber, pageSize);
    }

    @GetMapping("refundable/{orderId}")
    @RolesAllowed(Permissions.Order.GET_ORDER)
    public OrderInvoiceResponse getRefundableOrder(@PathVariable UUID orderId) {
        return orderService.getRefundableOrderInvoice(orderId);
    }

    @GetMapping("{orderId}/includeCancelled")
    @RolesAllowed(Permissions.Order.GET_ORDER)
    public OrderInvoiceResponse getOrderIncludingCancelled(@PathVariable UUID orderId) {
        return orderService.getOrderIncludingCancelled(orderId);
    }

    @GetMapping("filters")
    @ResponseStatus(HttpStatus.OK)
    @RolesAllowed(Permissions.Order.FIND_ORDERS_BY_FILTERS)
    public List<OrderInvoiceResponse> findOrdersByFilters(@RequestParam(value = "startDate", required = false) LocalDate startDate,
                                                          @RequestParam(value = "endDate", required = false) LocalDate endDate,
                                                          @RequestParam Map<String, String> queryParams) {
        queryParams.put("startDate", startDate.toString());
        queryParams.put("endDate", endDate.toString());
        return orderService.findOrdersByFilters(queryParams);
    }
}