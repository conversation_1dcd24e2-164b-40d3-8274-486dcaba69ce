package com.scube.calculation.dto.order;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.Instant;
import java.util.List;

@Data
@NoArgsConstructor
public class OrderSummaryResponse {
    private String orderId;
    private String orderNumber;
    private String status;
    private Instant createdDate;
    private Instant updatedDate;
    private List<String> itemNames;
    private String userId;

    public OrderSummaryResponse(OrderDto order) {
        this.orderId = order.getOrderId().toString();
        this.orderNumber = order.getOrderNumber();
        this.status = order.getStatus().getKey();
        this.createdDate = order.getCreatedDate();
        this.updatedDate = order.getLastModifiedDate();
        if (order.getOrderItems() != null)
            this.itemNames = order.getOrderItems().stream().map(OrderItemDto::getName).toList();
        this.userId = order.getUserId();
    }
}
