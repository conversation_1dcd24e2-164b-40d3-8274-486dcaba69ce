package com.scube.calculation.dto.order;

import com.scube.calculation.dto.cart.AdditionalFeeDto;
import com.scube.calculation.enums.FinanceStatus;
import com.scube.calculation.enums.OrderStatus;
import lombok.Data;

import java.time.Instant;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

@Data
public class OrderDto {
    private UUID orderId;
    private String userId;
    private OrderStatus status;
    private FinanceStatus financeStatus;
    private String orderNumber;
    private Instant orderPaidDate;
    private List<OrderItemDto> orderItems = new ArrayList<>();
    private Instant createdDate;
    private String createdBy;
    private Instant lastModifiedDate;
    private String lastModifiedBy;
    private List<AdditionalFeeDto> orderAdditionalFees = new ArrayList<>();
}