package com.scube.calculation.repository;

import com.scube.audit.auditable.repositories.AuditableBaseWithPropertiesRepository;
import com.scube.calculation.enums.OrderStatus;
import com.scube.calculation.model.Order;
import jakarta.validation.constraints.Size;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.time.LocalDate;
import java.util.List;
import java.util.Map;
import java.util.UUID;

public interface OrderRepository extends AuditableBaseWithPropertiesRepository<Order, UUID> {
    @Query("select o from Order o where (?1 is null or o.userId = ?1)  and (o.status in ?2 or :#{#statuses} is null)")
    public Page<Order> findFilteredPaginatedOrders(@Size(max = 255) String userId, List<OrderStatus> statuses, Pageable pageable);

    @Query("""
            SELECT o FROM Order o
            WHERE (:userId IS NULL OR o.userId = :userId)
            AND (:orderNumber IS NULL OR o.orderNumber = :orderNumber)
            AND (:statuses IS NULL OR o.status IN :statuses)
            AND ( COALESCE(:startDate, '') = '' OR DATE(o.orderPaidDate) >= :startDate)
            AND ( COALESCE(:endDate, '') = '' OR DATE(o.orderPaidDate) <= :endDate)
            """)
    Page<Order> findFilteredPaginatedRefundableOrders(
            @Param("userId") @Size(max = 255) String userId,
            @Param("orderNumber") String orderNumber,
            @Param("startDate") LocalDate startDate,
            @Param("endDate") LocalDate endDate,
            @Param("statuses") List<OrderStatus> statuses,
            Pageable pageable
    );

    @Query(value = """
            SELECT o.id FROM Order o
            INNER JOIN orderItems oi
            WHERE oi.uniqueItemId = :uniqueItemId
            AND o.status in ('ORDER_DRAFT', 'ORDER_PENDING', 'ORDER_ACTIVE')
            ORDER BY oi.lastModifiedDate DESC
            LIMIT 1
            """)
    List<UUID> findOrderIdByItemId(UUID uniqueItemId);

    @Query("""
            SELECT o FROM Order o
            JOIN o.orderItems oi 
            WHERE ( COALESCE(:status, '') = '' OR o.status = :status)
            AND ( COALESCE(:startDate, '') = '' OR DATE(o.orderPaidDate) >= :startDate)
            AND ( COALESCE(:endDate, '') = '' OR DATE(o.orderPaidDate) <= :endDate)
            AND ( COALESCE(:cartItemName, '') = '' OR oi.name LIKE :cartItemName%)
            AND ( COALESCE(:cartItemId, '') = '' OR oi.itemTypeId = :cartItemId )
            ORDER BY o.orderPaidDate 
            """)
    List<Order> findOrdersByFilters(OrderStatus status,
                                    LocalDate startDate,
                                    LocalDate endDate,
                                    @Size(max = 255) String cartItemName,
                                    @Size(max = 255) String cartItemId);

    default List<Order> findOrdersByFilters(Map<String, String> queryParams) {
        OrderStatus status = queryParams.get("status") == null ? null : OrderStatus.valueOf(queryParams.get("status"));
        LocalDate startDate = queryParams.get("startDate") == null ? null : LocalDate.parse(queryParams.get("startDate"));
        LocalDate endDate = queryParams.get("endDate") == null ? null : LocalDate.parse(queryParams.get("endDate"));
        String cartItemName = queryParams.get("cartItemName");
        String cartItemId = queryParams.get("cartItemId");

        return findOrdersByFilters(status, startDate, endDate, cartItemName, cartItemId);
    }

    boolean existsByIdAndUserId(UUID orderId, @Size(max = 255) String userId);
}