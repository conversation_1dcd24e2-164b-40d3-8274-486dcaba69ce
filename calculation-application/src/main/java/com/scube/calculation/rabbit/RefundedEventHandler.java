package com.scube.calculation.rabbit;

import com.scube.calculation.dto.order.OrderInvoiceResponse;
import com.scube.calculation.enums.FinanceStatus;
import com.scube.calculation.model.Order;
import com.scube.calculation.repository.OrderRepository;
import com.scube.calculation.service.OrderService;
import com.scube.rabbit.core.fanout.subscriber.FanoutListener;
import jakarta.transaction.Transactional;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Component;
import org.springframework.web.server.ResponseStatusException;

import java.math.BigDecimal;
import java.util.UUID;

@Component
@RequiredArgsConstructor
@Slf4j
public class RefundedEventHandler extends FanoutListener<RefundedEventHandler.RefundedEvent> {
    private final OrderRepository orderRepository;
    private final OrderService orderService;

    @Override
    @Transactional
    public void consume(RefundedEvent event) {
        log.info("RefundedEventHandler received event: orderId={}, refundedAmount={}", event.orderId(), event.refundedAmount());
        
        Order order = orderRepository.findById(event.orderId())
                .orElseThrow(() -> new ResponseStatusException(HttpStatus.NOT_FOUND, "No such order with id " + event.orderId()));

        OrderInvoiceResponse orderInvoiceResponse = orderService.getRefundableOrderInvoice(order.getId());

        // Determine if this is a full or partial refund and update finance status
        if (event.refundedAmount().compareTo(orderInvoiceResponse.getTotal()) == 0) {
            log.info("Processing full refund for order {}", event.orderId());
            order.setFinanceStatus(FinanceStatus.REFUNDED);
        } else if (event.refundedAmount().compareTo(BigDecimal.ZERO) > 0) {
            log.info("Processing partial refund for order {}: refunded={}, total={}", 
                    event.orderId(), event.refundedAmount(), orderInvoiceResponse.getTotal());
            order.setFinanceStatus(FinanceStatus.PARTIALLY_REFUNDED);
        } else {
            log.warn("Invalid refund amount {} for order {}", event.refundedAmount(), event.orderId());
            return;
        }

        orderRepository.save(order);
        log.info("Updated order {} finance status to {}", event.orderId(), order.getFinanceStatus());
    }

    public record RefundedEvent(UUID orderId, BigDecimal refundedAmount) implements com.scube.rabbit.core.fanout.subscriber.IRabbitFanoutSubscriber {
    }
}
