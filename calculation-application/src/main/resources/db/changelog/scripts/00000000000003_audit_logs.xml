<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd">
    <changeSet author="davidr (generated)" id="1694792102096-10">
        <createSequence cacheSize="1" cycle="false" dataType="bigint" incrementBy="50" maxValue="9223372036854775807"
                        minValue="1" sequenceName="cart_item_seq" startValue="1"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1694792102096-11">
        <createSequence cacheSize="1" cycle="false" dataType="bigint" incrementBy="50" maxValue="9223372036854775807"
                        minValue="1" sequenceName="fee_seq" startValue="1"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1694792102096-12">
        <addColumn tableName="cart">
            <column name="created_by" type="varchar(250 BYTE)" defaultValue="liquibase">
                <constraints nullable="false"/>
            </column>
        </addColumn>
    </changeSet>
    <changeSet author="davidr (generated)" id="1694792102096-13">
        <addColumn tableName="cart_item">
            <column name="created_by" type="varchar(250 BYTE)" defaultValue="liquibase">
                <constraints nullable="false"/>
            </column>
        </addColumn>
    </changeSet>
    <changeSet author="davidr (generated)" id="1694792102096-14">
        <addColumn tableName="fee">
            <column name="created_by" type="varchar(250 BYTE)" defaultValue="liquibase">
                <constraints nullable="false"/>
            </column>
        </addColumn>
    </changeSet>
    <changeSet author="davidr (generated)" id="1694792102096-15">
        <addColumn tableName="cart">
            <column name="created_date" type="timestamptz" defaultValue="now()">
                <constraints nullable="false"/>
            </column>
        </addColumn>
    </changeSet>
    <changeSet author="davidr (generated)" id="1694792102096-16">
        <addColumn tableName="cart_item">
            <column name="created_date" type="timestamptz" defaultValue="now()">
                <constraints nullable="false"/>
            </column>
        </addColumn>
    </changeSet>
    <changeSet author="davidr (generated)" id="1694792102096-17">
        <addColumn tableName="fee">
            <column name="created_date" type="timestamptz" defaultValue="now()">
                <constraints nullable="false"/>
            </column>
        </addColumn>
    </changeSet>
    <changeSet author="davidr (generated)" id="1694792102096-18">
        <addColumn tableName="cart">
            <column name="last_modified_by" type="varchar(250 BYTE)" defaultValue="liquibase">
                <constraints nullable="false"/>
            </column>
        </addColumn>
    </changeSet>
    <changeSet author="davidr (generated)" id="1694792102096-19">
        <addColumn tableName="cart_item">
            <column name="last_modified_by" type="varchar(250 BYTE)" defaultValue="liquibase">
                <constraints nullable="false"/>
            </column>
        </addColumn>
    </changeSet>
    <changeSet author="davidr (generated)" id="1694792102096-20">
        <addColumn tableName="fee">
            <column name="last_modified_by" type="varchar(250 BYTE)" defaultValue="liquibase">
                <constraints nullable="false"/>
            </column>
        </addColumn>
    </changeSet>
    <changeSet author="davidr (generated)" id="1694792102096-21">
        <addColumn tableName="cart">
            <column name="last_modified_date" type="timestamptz" defaultValue="now()">
                <constraints nullable="false"/>
            </column>
        </addColumn>
    </changeSet>
    <changeSet author="davidr (generated)" id="1694792102096-22">
        <addColumn tableName="cart_item">
            <column name="last_modified_date" type="timestamptz" defaultValue="now()">
                <constraints nullable="false"/>
            </column>
        </addColumn>
    </changeSet>
    <changeSet author="davidr (generated)" id="1694792102096-23">
        <addColumn tableName="fee">
            <column name="last_modified_date" type="timestamptz" defaultValue="now()">
                <constraints nullable="false"/>
            </column>
        </addColumn>
    </changeSet>
    <changeSet author="davidr (generated)" id="1694792102096-24">
        <dropColumn columnName="created_time" tableName="cart"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1694792102096-25">
        <dropColumn columnName="updated_time" tableName="cart"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1694792102096-1">
        <dropForeignKeyConstraint baseTableName="cart_item" constraintName="fk1uobyhgl1wvgt1jpccia8xxs3"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1694792102096-2">
        <addForeignKeyConstraint baseColumnNames="cart_id" baseTableName="cart_item"
                                 constraintName="fk1uobyhgl1wvgt1jpccia8xxs3" onDelete="NO ACTION" onUpdate="NO ACTION"
                                 referencedColumnNames="cart_id" referencedTableName="cart"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1694792102096-3">
        <modifyDataType columnName="amount" newDataType="number(38,2)" tableName="fee"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1694792102096-4">
        <dropNotNullConstraint columnDataType="number(38,2)" columnName="amount" tableName="fee"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1694792102096-5">
        <modifyDataType columnName="cart_item_id" newDataType="bigint" tableName="cart_item"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1694792102096-6">
        <modifyDataType columnName="cart_item_id" newDataType="bigint" tableName="cart_item_fees"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1694792102096-7">
        <modifyDataType columnName="fee_id" newDataType="bigint" tableName="cart_item_fees"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1694792102096-8">
        <modifyDataType columnName="fee_id" newDataType="bigint" tableName="fee"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1694792102096-9">
        <modifyDataType columnName="price" newDataType="number(38,2)" tableName="cart_item"/>
    </changeSet>
</databaseChangeLog>
