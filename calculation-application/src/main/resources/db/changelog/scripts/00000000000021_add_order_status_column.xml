<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd">
    
    <changeSet id="addColumn_order_status" author="system">
        <preConditions onFail="MARK_RAN">
            <not>
                <columnExists tableName="order_" columnName="order_status"/>
            </not>
        </preConditions>
        <addColumn tableName="order_">
            <column name="order_status" type="VARCHAR(255)">
                <constraints nullable="true"/>
            </column>
        </addColumn>
    </changeSet>

    <changeSet id="dropColumn_order_status" author="system">
        <preConditions onFail="MARK_RAN">
            <columnExists tableName="order_" columnName="order_status"/>
        </preConditions>
        <dropColumn tableName="order_" columnName="order_status"/>
    </changeSet>

    <changeSet id="migrate_status_to_order_status" author="system">
        <preConditions onFail="MARK_RAN">
            <columnExists tableName="order_" columnName="order_status"/>
            <columnExists tableName="order_" columnName="status"/>
        </preConditions>
        <sql>
            UPDATE order_ SET order_status = status WHERE order_status IS NULL;
        </sql>
    </changeSet>

    <changeSet id="addColumn_audit_log_order_status" author="system">
        <preConditions onFail="MARK_RAN">
            <not>
                <columnExists tableName="audit_log_order_" columnName="order_status"/>
            </not>
        </preConditions>
        <addColumn tableName="audit_log_order_">
            <column name="order_status" type="VARCHAR(255)">
                <constraints nullable="true"/>
            </column>
        </addColumn>
    </changeSet>

    <changeSet id="migrate_audit_status_to_order_status" author="system">
        <preConditions onFail="MARK_RAN">
            <columnExists tableName="audit_log_order_" columnName="order_status"/>
            <columnExists tableName="audit_log_order_" columnName="status"/>
        </preConditions>
        <sql>
            UPDATE audit_log_order_ SET order_status = status WHERE order_status IS NULL;
        </sql>
    </changeSet>

</databaseChangeLog>

