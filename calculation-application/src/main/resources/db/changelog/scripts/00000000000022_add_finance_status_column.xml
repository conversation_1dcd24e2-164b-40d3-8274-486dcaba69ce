<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd">
    
    <!-- Add finance_status column to order_ table -->
    <changeSet id="addColumn_finance_status" author="system">
        <preConditions onFail="MARK_RAN">
            <not>
                <columnExists tableName="order_" columnName="finance_status"/>
            </not>
        </preConditions>
        <addColumn tableName="order_">
            <column name="finance_status" type="VARCHAR(255)">
                <constraints nullable="true"/>
            </column>
        </addColumn>
    </changeSet>

    <!-- Set default value for existing orders to UNPAID -->
    <changeSet id="set_default_finance_status" author="system">
        <preConditions onFail="MARK_RAN">
            <columnExists tableName="order_" columnName="finance_status"/>
        </preConditions>
        <sql>
            UPDATE order_ SET finance_status = 'UNPAID' WHERE finance_status IS NULL;
        </sql>
    </changeSet>

    <!-- Add finance_status column to audit_log_order_ table -->
    <changeSet id="addColumn_audit_log_finance_status" author="system">
        <preConditions onFail="MARK_RAN">
            <not>
                <columnExists tableName="audit_log_order_" columnName="finance_status"/>
            </not>
        </preConditions>
        <addColumn tableName="audit_log_order_">
            <column name="finance_status" type="VARCHAR(255)">
                <constraints nullable="true"/>
            </column>
        </addColumn>
    </changeSet>

    <!-- Migrate finance_status to audit log -->
    <changeSet id="migrate_audit_finance_status" author="system">
        <preConditions onFail="MARK_RAN">
            <columnExists tableName="audit_log_order_" columnName="finance_status"/>
        </preConditions>
        <sql>
            UPDATE audit_log_order_ a
            SET finance_status = o.finance_status
            FROM order_ o
            WHERE a.order_id = o.order_id
            AND a.finance_status IS NULL;
        </sql>
    </changeSet>
    
</databaseChangeLog>


