<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
                   http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd">

    <!-- Update ORDER_PAID to ORDER_ACTIVE in order_ table -->
    <changeSet id="updateOrderStatusToActive" author="system">
        <sql>
            UPDATE order_
            SET status = 'ORDER_ACTIVE'
            WHERE status = 'ORDER_PAID';
        </sql>
    </changeSet>

    <!-- Update ORDER_PAID to ORDER_ACTIVE in audit_log_order_ table -->
    <changeSet id="updateAuditLogOrderStatusToActive" author="system">
        <sql>
            UPDATE audit_log_order_
            SET status = 'ORDER_ACTIVE'
            WHERE status = 'ORDER_PAID';
        </sql>
    </changeSet>

    <!-- Update ORDER_LOCKED to ORDER_ACTIVE in order_ table -->
    <changeSet id="updateOrderLockedToActive" author="system">
        <sql>
            UPDATE order_
            SET status = 'ORDER_ACTIVE'
            WHERE status = 'ORDER_LOCKED';
        </sql>
    </changeSet>

    <!-- Update ORDER_LOCKED to ORDER_ACTIVE in audit_log_order_ table -->
    <changeSet id="updateAuditLogOrderLockedToActive" author="system">
        <sql>
            UPDATE audit_log_order_
            SET status = 'ORDER_ACTIVE'
            WHERE status = 'ORDER_LOCKED';
        </sql>
    </changeSet>

    <!-- Update PAID to CAPTURED in order_ table -->
    <changeSet id="updateFinanceStatusToCaptured" author="system">
        <sql>
            UPDATE order_
            SET finance_status = 'CAPTURED'
            WHERE finance_status = 'PAID';
        </sql>
    </changeSet>

    <!-- Update PAID to CAPTURED in audit_log_order_ table -->
    <changeSet id="updateAuditLogFinanceStatusToCaptured" author="system">
        <sql>
            UPDATE audit_log_order_
            SET finance_status = 'CAPTURED'
            WHERE finance_status = 'PAID';
        </sql>
    </changeSet>

</databaseChangeLog>

