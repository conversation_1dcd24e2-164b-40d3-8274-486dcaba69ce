<?xml version="1.0" encoding="utf-8"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.4.xsd">
    <!--@formatter:off-->
        <include file="classpath:db/changelog/scripts/00000000000000_initial_schema.xml" relativeToChangelogFile="false"/>
        <include file="classpath:db/changelog/scripts/00000000000001_add_fee_key.xml" relativeToChangelogFile="false"/>
        <include file="classpath:db/changelog/scripts/00000000000002_remove_item_category.xml" relativeToChangelogFile="false"/>
        <include file="classpath:db/changelog/scripts/00000000000003_audit_logs.xml" relativeToChangelogFile="false"/>
        <include file="classpath:db/changelog/scripts/00000000000004_audit_history.xml" relativeToChangelogFile="false"/>
        <include file="classpath:db/changelog/scripts/00000000000005_history_tables.xml" relativeToChangelogFile="false"/>
        <include file="classpath:db/changelog/scripts/00000000000006_identity_id_changes.xml" relativeToChangelogFile="false"/>
        <include file="classpath:db/changelog/scripts/00000000000007_cart_user_id_filtered_index.xml" relativeToChangelogFile="false"/>
        <include file="classpath:db/changelog/scripts/00000000000008_add_order_number.xml" relativeToChangelogFile="false"/>
        <include file="classpath:db/changelog/scripts/00000000000009_add_order_paid_date_column.xml" relativeToChangelogFile="false"/>
        <include file="classpath:db/changelog/scripts/00000000000010_add_scheduler_table_tbl.xml" relativeToChangelogFile="false"/>
        <include file="classpath:db/changelog/scripts/000000000000011_update_audit_columns_to_clerk.xml" relativeToChangelogFile="false"/>
        <include file="classpath:db/changelog/scripts/000000000000012_add_order_tables.xml" relativeToChangelogFile="false"/>
        <include file="classpath:db/changelog/scripts/property_type.xml" relativeToChangelogFile="false"/>
        <include file="classpath:db/changelog/scripts/000000000000013_custom_fields_as_json.xml" relativeToChangelogFile="false"/>
        <include file="classpath:db/changelog/scripts/000000000000014_cart_item_fees.xml" relativeToChangelogFile="false"/>
        <include file="classpath:db/changelog/scripts/000000000000015_order_item_fees.xml" relativeToChangelogFile="false"/>
        <include file="classpath:db/changelog/scripts/000000000000016_shedlock.xml" relativeToChangelogFile="false"/>
        <include file="classpath:db/changelog/scripts/revision_entity.xml" relativeToChangelogFile="false"/>
        <include file="classpath:db/changelog/scripts/00000000000017_cart_additional_fee.xml" relativeToChangelogFile="false"/>
        <include file="classpath:db/changelog/scripts/00000000000018_order_additional_fee.xml" relativeToChangelogFile="false"/>
        <include file="classpath:db/changelog/scripts/00000000000019_cart_additional_item_fee.xml" relativeToChangelogFile="false"/>
        <include file="classpath:db/changelog/scripts/00000000000020_order_additional_item_fee.xml" relativeToChangelogFile="false"/>
        <include file="classpath:db/changelog/scripts/00000000000021_add_order_status_column.xml" relativeToChangelogFile="false"/>
        <include file="classpath:db/changelog/scripts/00000000000022_add_finance_status_column.xml" relativeToChangelogFile="false"/>
        <include file="classpath:db/changelog/scripts/00000000000023_update_order_status_to_completed.xml" relativeToChangelogFile="false"/>
</databaseChangeLog>