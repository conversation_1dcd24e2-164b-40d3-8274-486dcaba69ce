package com.scube.calculation.service;

import com.scube.audit.auditable.entity.AuditableBase;
import com.scube.calculation.SharedTestConfig;
import com.scube.calculation.dto.PageDTO;
import com.scube.calculation.dto.order.OrderInvoiceResponse;
import com.scube.calculation.dto.order.OrderSummaryResponse;
import com.scube.calculation.enums.CartStatus;
import com.scube.calculation.enums.FeeType;
import com.scube.calculation.enums.OrderStatus;
import com.scube.calculation.model.*;
import com.scube.calculation.repository.CartItemRepository;
import com.scube.calculation.repository.CartRepository;
import com.scube.calculation.repository.FeeRepository;
import com.scube.calculation.repository.OrderRepository;
import jakarta.persistence.EntityManager;
import org.hamcrest.MatcherAssert;
import org.junit.jupiter.api.Test;
import org.mockito.ArgumentCaptor;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.http.HttpStatus;
import org.springframework.web.server.ResponseStatusException;

import java.math.BigDecimal;
import java.time.Instant;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

import static org.hamcrest.Matchers.*;
import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;

public class OrderServiceTest extends SharedTestConfig {

    @MockBean
    private CartRepository cartRepository;

    @MockBean
    private OrderRepository orderRepository;

    @MockBean
    private FeeRepository feeRepository;

    @MockBean
    private CartItemRepository cartItemRepository;

    @MockBean
    private EntityManager entityManager;

    @Autowired
    private OrderService service;

    @Test
    void getOrderInvoiceWithAllPossibleFees() {
        UUID orderId = UUID.randomUUID();

        Fee additionalFlatFee = new Fee();
        additionalFlatFee.setId(4L);
        additionalFlatFee.setFeeName("Additional Flat Fee");
        additionalFlatFee.setAmount(new BigDecimal(2));
        additionalFlatFee.setOperation(FeeType.FLAT);
        additionalFlatFee.setPayableId(3);

        Fee additionalPercentFee = new Fee();
        additionalPercentFee.setId(5L);
        additionalPercentFee.setFeeName("Additional Percentage Fee");
        additionalPercentFee.setAmount(new BigDecimal(20));
        additionalPercentFee.setOperation(FeeType.PERCENTAGE);
        additionalPercentFee.setPayableId(4);

        Fee includedFlatFee = new Fee();
        includedFlatFee.setId(6L);
        includedFlatFee.setFeeName("Included Flat Fee");
        includedFlatFee.setAmount(new BigDecimal(3));
        includedFlatFee.setOperation(FeeType.FLAT);
        includedFlatFee.setPayableId(5);

        Fee includedPercentFee = new Fee();
        includedPercentFee.setId(7L);
        includedPercentFee.setFeeName("Included Percentage Fee");
        includedPercentFee.setAmount(new BigDecimal(8));
        includedPercentFee.setOperation(FeeType.PERCENTAGE);
        includedPercentFee.setPayableId(6);

        OrderItem item1 = new OrderItem();
        item1.setFees(List.of(additionalFlatFee, includedPercentFee));

        OrderItem item2 = new OrderItem();
        item2.setFees(List.of(includedFlatFee, additionalPercentFee));

        Order order = new Order();
        order.setStatus(OrderStatus.ORDER_DRAFT);
        order.setId(orderId);
        order.setOrderItems(List.of(item1, item2));
        Mockito.when(orderRepository.findById(orderId)).thenReturn(Optional.of(order));

        OrderInvoiceResponse response = service.getOrderInvoice(orderId);

        assertEquals(2, response.getItems().size());

        // Included fees are included in the list price of the item.
        // They should be counted in what's sent to payment service, but not listed on a separate line.
        var fees = response.getFees();
        MatcherAssert.assertThat(fees, containsInAnyOrder(
                allOf(
                        hasProperty("label", equalTo("Additional Flat Fee")),
                        hasProperty("amount", comparesEqualTo(BigDecimal.valueOf(2l)))
                ),
                allOf(
                        hasProperty("label", equalTo("Additional Percentage Fee")),
                        hasProperty("amount", comparesEqualTo(BigDecimal.valueOf(0.6)))
                ),
                allOf(
                        hasProperty("label", equalTo("Included Flat Fee")),
                        hasProperty("amount", comparesEqualTo(BigDecimal.valueOf(3l)))
                ),
                allOf(
                        hasProperty("label", equalTo("Included Percentage Fee")),
                        hasProperty("amount", comparesEqualTo(BigDecimal.valueOf(0.16)))
                )
        ));
    }

    @Test
    public void getOrderInvoiceInvalidStatus() {
        UUID orderId = UUID.randomUUID();

        OrderItem item1 = new OrderItem();
        item1.setPrice(new BigDecimal(50));

        OrderItem item2 = new OrderItem();
        item2.setPrice(new BigDecimal(50));

        Order order = new Order();
        order.setStatus(OrderStatus.ORDER_CANCELED);
        order.setId(orderId);
        order.setOrderItems(List.of(item1, item2));
        Mockito.when(orderRepository.findById(orderId)).thenReturn(Optional.of(order));

        ResponseStatusException exception = assertThrows(ResponseStatusException.class, () -> service.getOrderInvoice(orderId));
        assertEquals(HttpStatus.NOT_FOUND.value(), exception.getStatusCode().value());
        assertEquals("The specified order does not exist", exception.getReason());
    }

    @Test
    public void getOrderInvoiceNotFound() {
        Mockito.when(cartRepository.findById(any())).thenReturn(Optional.empty());

        ResponseStatusException exception = assertThrows(ResponseStatusException.class, () -> service.getOrderInvoice(UUID.randomUUID()));
        assertEquals(HttpStatus.NOT_FOUND.value(), exception.getStatusCode().value());
    }

    @Test
    public void makeOrderFromCart() {
        // Occasionally, this test runs too fast to verify that the updated date is correct.
        // Therefore, set the start time just slightly in the past to compensate.
        Instant testStart = Instant.now().minusSeconds(1);
        UUID cartId = UUID.randomUUID();
        Cart cart = new Cart();
        cart.setStatus(CartStatus.CART_ACTIVE);
        cart.setId(cartId);

        Fee additionalFlatFee = new Fee();
        additionalFlatFee.setId(4L);
        additionalFlatFee.setFeeName("Additional Flat Fee");
        additionalFlatFee.setAmount(new BigDecimal(2));
        additionalFlatFee.setOperation(FeeType.FLAT);
        additionalFlatFee.setPayableId(3);

        Fee additionalPercentFee = new Fee();
        additionalPercentFee.setId(5L);
        additionalPercentFee.setFeeName("Additional Percentage Fee");
        additionalPercentFee.setAmount(new BigDecimal(20));
        additionalPercentFee.setOperation(FeeType.PERCENTAGE);
        additionalPercentFee.setPayableId(4);

        Fee includedFlatFee = new Fee();
        includedFlatFee.setId(6L);
        includedFlatFee.setFeeName("Included Flat Fee");
        includedFlatFee.setAmount(new BigDecimal(3));
        includedFlatFee.setOperation(FeeType.FLAT);
        includedFlatFee.setPayableId(5);

        Fee includedPercentFee = new Fee();
        includedPercentFee.setId(7L);
        includedPercentFee.setFeeName("Included Percentage Fee");
        includedPercentFee.setAmount(new BigDecimal(8));
        includedPercentFee.setOperation(FeeType.PERCENTAGE);
        includedPercentFee.setPayableId(6);

        CartItem item1 = new CartItem();
        item1.setPrice(new BigDecimal(50));
        item1.setFees(List.of(additionalFlatFee, includedPercentFee));

        CartItem item2 = new CartItem();
        item2.setPrice(new BigDecimal(50));
        item2.setFees(List.of(includedFlatFee, additionalPercentFee));

        cart.setCartItems(List.of(item1, item2));

        Mockito.when(cartRepository.findById(cartId)).thenReturn(Optional.of(cart));
        ArgumentCaptor<Order> captor = ArgumentCaptor.forClass(Order.class);
        Mockito.when(orderRepository.save(any())).thenReturn(new Order(cart));

        service.makeOrderFromCartWithResponse(cartId);

        Mockito.verify(orderRepository).save(captor.capture());
        Order newOrder = captor.getValue();
        assertEquals(OrderStatus.ORDER_DRAFT, newOrder.getStatus());
        assertTrue(newOrder.getLastModifiedDate().isAfter(testStart));
        assertEquals(newOrder.getOrderItems().size(), 2);
        assertEquals(newOrder.getOrderItems().get(0).getOrderItemFees().size(), 2);
        assertEquals(newOrder.getOrderItems().get(1).getOrderItemFees().size(), 2);
        assertEquals(newOrder.getOrderItems().get(0).getOrderItemFees().get(0).getFee().getFeeName(), "Additional Flat Fee");
        assertEquals(newOrder.getOrderItems().get(0).getOrderItemFees().get(1).getFee().getFeeName(), "Included Percentage Fee");
        assertEquals(newOrder.getOrderItems().get(1).getOrderItemFees().get(0).getFee().getFeeName(), "Included Flat Fee");
        assertEquals(newOrder.getOrderItems().get(1).getOrderItemFees().get(1).getFee().getFeeName(), "Additional Percentage Fee");
    }

    @Test
    public void makeOrderFromCartInvalidStatus() {
        UUID cartId = UUID.randomUUID();
        Cart cart = new Cart();
        cart.setStatus(CartStatus.CART_SUSPENDED);
        cart.setId(cartId);
        cart.setCartItems(List.of(new CartItem()));

        Mockito.when(cartRepository.findById(cartId)).thenReturn(Optional.of(cart));

        ResponseStatusException exception = assertThrows(ResponseStatusException.class, () -> service.makeOrderFromCartWithResponse(cartId));
        assertEquals(HttpStatus.BAD_REQUEST.value(), exception.getStatusCode().value());
        assertEquals("An order can only be placed using an active cart", exception.getReason());
    }

    @Test
    public void makeOrderFromCartEmptyCart() {
        UUID cartId = UUID.randomUUID();
        Cart cart = new Cart();
        cart.setStatus(CartStatus.CART_ACTIVE);
        cart.setId(cartId);
        cart.setCartItems(List.of());

        Mockito.when(cartRepository.findById(cartId)).thenReturn(Optional.of(cart));

        ResponseStatusException exception = assertThrows(ResponseStatusException.class, () -> service.makeOrderFromCartWithResponse(cartId));
        assertEquals(HttpStatus.BAD_REQUEST.value(), exception.getStatusCode().value());
        assertEquals("An order can only be placed using a non-empty cart", exception.getReason());
    }

    @Test
    public void makeOrderFromCartNotFound() {
        Mockito.when(cartRepository.findById(any())).thenReturn(Optional.empty());

        ResponseStatusException exception = assertThrows(ResponseStatusException.class, () -> service.makeOrderFromCartWithResponse(UUID.randomUUID()));
        assertEquals(HttpStatus.NOT_FOUND.value(), exception.getStatusCode().value());
    }

    @Test
    public void makeCartFromOrder() {
        // Add an offset of 1 ns just in case the test runs too fast
        Instant testStart = Instant.now().minusSeconds(1);
        UUID orderId = UUID.randomUUID();
        Order order = new Order();
        order.setStatus(OrderStatus.ORDER_DRAFT);
        order.setId(orderId);

        Mockito.when(orderRepository.findById(orderId)).thenReturn(Optional.of(order));
        ArgumentCaptor<Cart> captor = ArgumentCaptor.forClass(Cart.class);
        ArgumentCaptor<Order> orderCaptor = ArgumentCaptor.forClass(Order.class);

        service.makeCartFromOrder(orderId);

        Mockito.verify(cartRepository).save(captor.capture());
        Mockito.verify(orderRepository).save(orderCaptor.capture());
        Cart savedCart = captor.getValue();
        Order savedOrder = orderCaptor.getValue();

        assertEquals(orderId, order.getId());
        assertEquals(OrderStatus.ORDER_CANCELED, savedOrder.getStatus());
        assertEquals(CartStatus.CART_ACTIVE, savedCart.getStatus());
        assertTrue(savedCart.getLastModifiedDate().isAfter(testStart));
    }

    @Test
    public void makeCartFromOrderInvalidStatus() {
        UUID orderId = UUID.randomUUID();
        Order order = new Order();
        order.setStatus(OrderStatus.ORDER_ACTIVE);
        order.setId(orderId);

        Mockito.when(orderRepository.findById(orderId)).thenReturn(Optional.of(order));

        ResponseStatusException exception = assertThrows(ResponseStatusException.class, () -> service.makeCartFromOrder(orderId));
        assertEquals(HttpStatus.BAD_REQUEST.value(), exception.getStatusCode().value());
        assertEquals("A cart can only be created from a pending or open order", exception.getReason());
    }

    @Test
    public void makeCartFromOrderNotFound() {
        Mockito.when(cartRepository.findById(any())).thenReturn(Optional.empty());

        ResponseStatusException exception = assertThrows(ResponseStatusException.class, () -> service.makeCartFromOrder(UUID.randomUUID()));
        assertEquals(HttpStatus.NOT_FOUND.value(), exception.getStatusCode().value());
    }

    @Test
    void cancelOrder() {
        Instant testStart = Instant.now().minusSeconds(1);
        UUID orderId = UUID.randomUUID();
        Order order = new Order();
        order.setStatus(OrderStatus.ORDER_DRAFT);
        order.setId(orderId);

        Mockito.when(orderRepository.findById(orderId)).thenReturn(Optional.of(order));
        ArgumentCaptor<Order> captor = ArgumentCaptor.forClass(Order.class);

        service.cancelOrder(orderId);

        Mockito.verify(orderRepository).save(captor.capture());
        Order savedOrder = captor.getValue();
        assertEquals(OrderStatus.ORDER_CANCELED, savedOrder.getStatus());
        assertTrue(savedOrder.getLastModifiedDate().isAfter(testStart));
    }

    @Test
    public void cancelOrderInvalidStatus() {
        UUID orderId = UUID.randomUUID();
        Order order = new Order();
        order.setStatus(OrderStatus.ORDER_REFUNDED); // Use REFUNDED status which is not allowed to be cancelled
        order.setId(orderId);

        Mockito.when(orderRepository.findById(orderId)).thenReturn(Optional.of(order));

        ResponseStatusException exception = assertThrows(ResponseStatusException.class, () -> service.cancelOrder(orderId));
        assertEquals(HttpStatus.BAD_REQUEST.value(), exception.getStatusCode().value());
        assertEquals("Only an open, locked, paid, or partially refunded order can be canceled", exception.getReason());
    }

    @Test
    public void cancelOrderNotFound() {
        Mockito.when(cartRepository.findById(any())).thenReturn(Optional.empty());

        ResponseStatusException exception = assertThrows(ResponseStatusException.class, () -> service.cancelOrder(UUID.randomUUID()));
        assertEquals(HttpStatus.NOT_FOUND.value(), exception.getStatusCode().value());
    }

    @Test
    public void listOrders() {
        String userId = "user";
        Order order1 = new Order();
        order1.setId(UUID.randomUUID());
        order1.setStatus(OrderStatus.ORDER_DRAFT);
        Order order2 = new Order();
        order2.setStatus(OrderStatus.ORDER_ACTIVE);
        order2.setId(UUID.randomUUID());
        Order order3 = new Order();
        order3.setStatus(OrderStatus.ORDER_CANCELED);
        order3.setId(UUID.randomUUID());
        Page<Order> page = Mockito.mock(Page.class);
        Mockito.when(page.stream()).thenReturn(List.of(order1, order2, order3).stream());
        Mockito.when(page.getNumber()).thenReturn(0);
        Mockito.when(page.getSize()).thenReturn(10);
        Mockito.when(page.getTotalElements()).thenReturn(3L);
        Mockito.when(orderRepository.findFilteredPaginatedOrders(userId, OrderStatus.getAllStatuses(), PageRequest.of(0, 10, Sort.by(AuditableBase.LAST_MODIFIED_DATE_CAMEL_CASE).descending()))).thenReturn(page);

        PageDTO<OrderSummaryResponse> response = service.listOrders(userId, null, 1, 10);
        assertEquals(1, response.getPageIndex());
        assertEquals(10, response.getPageSize());
        assertEquals(3, response.getTotalCount());
        assertEquals(3, response.getItems().size());
        assertEquals("orderOpen", response.getItems().get(0).getStatus());
        assertEquals("orderLocked", response.getItems().get(1).getStatus());
        assertEquals("orderCancelled", response.getItems().get(2).getStatus());
    }

    @Test
    void transferOrder() {
        UUID orderId = UUID.randomUUID();
        String userId = "user";
        Order order = new Order();
        order.setId(orderId);
        order.setStatus(OrderStatus.ORDER_DRAFT);

        Mockito.when(orderRepository.findById(orderId)).thenReturn(Optional.of(order));
        ArgumentCaptor<Order> captor = ArgumentCaptor.forClass(Order.class);
        service.transferOrder(orderId, userId);
        Mockito.verify(orderRepository).save(captor.capture());
        Order savedOrder = captor.getValue();
        assertEquals(userId, savedOrder.getUserId());

    }

    @Test
    public void getOrderById() {
        UUID orderId = UUID.randomUUID();
        Order order = new Order();
        order.setId(orderId);
        Mockito.when(orderRepository.findById(orderId)).thenReturn(Optional.of(order));
        Order result = service.getOrderById(orderId);
        assertEquals(order, result);
    }

    @Test
    public void getOrderByIdNotFound() {
        UUID orderId = UUID.randomUUID();
        Mockito.when(orderRepository.findById(orderId)).thenReturn(Optional.empty());
        ResponseStatusException exception = assertThrows(ResponseStatusException.class, () -> service.getOrderById(orderId));
        assertEquals(HttpStatus.NOT_FOUND.value(), exception.getStatusCode().value());
    }
}