package com.scube.payment.features.providers.gateway;

import com.scube.config_utils.app_property.utils.AppPropertyUtils;
import com.scube.payment.features.payment.processing.dto.refund.RefundPaymentRequestDto;
import com.scube.payment.features.payment.processing.rabbit.OrderAuthorizedEvent;
import com.scube.payment.features.payment.storage.model.RefundTransaction;
import com.scube.payment.features.webhook.IWebhookPayload;
import com.scube.rabbit.core.AmqpGateway;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.retry.annotation.Backoff;
import org.springframework.retry.annotation.Retryable;
import org.springframework.stereotype.Service;

import java.util.Map;

@Service
@RequiredArgsConstructor
@Slf4j
public class PaymentProviderGateway implements IPaymentProviderGateway<PaymentTokenRequest, PaymentTokenResponse, IWebhookPayload> {
    public static final String GATEWAY = "Gateway";
    private final Map<String, IPaymentProviderGateway> paymentProviderGateways;
    private final AmqpGateway amqpGateway;

    private IPaymentProviderGateway<PaymentTokenRequest, PaymentTokenResponse, IWebhookPayload> getPaymentProviderGateway() {
        var paymentProviderName = AppPropertyUtils.get("payment-providers.provider");
        return paymentProviderGateways.get(paymentProviderName + GATEWAY);
    }

    @Override
    @Retryable(retryFor = Exception.class, maxAttempts = 10, backoff = @Backoff(delay = 1000))
    public PaymentTokenResponse getToken(PaymentTokenRequest request) {
        var res =  getPaymentProviderGateway().getToken(request);
        
        // Publish event to mark order as authorized
        log.debug("Publishing OrderAuthorizedEvent for orderId: {}", request.getOrderId());
        amqpGateway.publish(new OrderAuthorizedEvent(request.getOrderId().toString()));
        
        return res;
    }

    @Override
    public void createRefund(RefundTransaction refundTransaction) {
        getPaymentProviderGateway().createRefund(refundTransaction);
    }

    @Override
    public void authCapture(IWebhookPayload request) {
        getPaymentProviderGateway().authCapture(request);
    }

    @Override
    public void settle(IWebhookPayload request) {
        getPaymentProviderGateway().settle(request);
    }

    @Override
    public void refund(IWebhookPayload request) {
        getPaymentProviderGateway().refund(request);
    }

    @Override
    public void refundFailed(IWebhookPayload request) {
        getPaymentProviderGateway().refundFailed(request);
    }

    @Override
    public void _void(IWebhookPayload request) {
        getPaymentProviderGateway()._void(request);
    }
}