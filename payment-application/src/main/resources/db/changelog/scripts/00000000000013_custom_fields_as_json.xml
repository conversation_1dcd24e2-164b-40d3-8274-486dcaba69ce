<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog" xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext" xmlns:pro="http://www.liquibase.org/xml/ns/pro" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-latest.xsd http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd">
    <changeSet author="postgres (generated)" id="1711565300604-59">
        <addColumn tableName="audit_log_authdotnet_mapping">
            <column name="properties" type="jsonb"/>
        </addColumn>
    </changeSet>
    <changeSet author="postgres (generated)" id="1711565300604-60">
        <addColumn tableName="audit_log_payee">
            <column name="properties" type="jsonb"/>
        </addColumn>
    </changeSet>
    <changeSet author="postgres (generated)" id="1711565300604-61">
        <addColumn tableName="audit_log_payment">
            <column name="properties" type="jsonb"/>
        </addColumn>
    </changeSet>
    <changeSet author="postgres (generated)" id="1711565300604-62">
        <addColumn tableName="audit_log_webhook_inbox">
            <column name="properties" type="jsonb"/>
        </addColumn>
    </changeSet>
    <changeSet author="postgres (generated)" id="1711565300604-63">
        <addColumn tableName="authdotnet_mapping">
            <column name="properties" type="jsonb"/>
        </addColumn>
    </changeSet>
    <changeSet author="postgres (generated)" id="1711565300604-64">
        <addColumn tableName="payee">
            <column name="properties" type="jsonb"/>
        </addColumn>
    </changeSet>
    <changeSet author="postgres (generated)" id="1711565300604-65">
        <addColumn tableName="payment">
            <column name="properties" type="jsonb"/>
        </addColumn>
    </changeSet>
    <changeSet author="postgres (generated)" id="1711565300604-66">
        <addColumn tableName="webhook_inbox">
            <column name="properties" type="jsonb"/>
        </addColumn>
    </changeSet>
    <changeSet author="postgres (generated)" id="1711565300604-67">
        <addColumn tableName="audit_log_authdotnet_mapping">
            <column defaultValueComputed="gen_random_uuid()" name="authdotnet_mapping_uuid" type="uuid"/>
        </addColumn>
    </changeSet>
    <changeSet author="postgres (generated)" id="1711565300604-68">
        <addColumn tableName="authdotnet_mapping">
            <column defaultValueComputed="gen_random_uuid()" name="authdotnet_mapping_uuid" type="uuid">
                <constraints nullable="false"/>
            </column>
        </addColumn>
    </changeSet>
    <changeSet author="postgres (generated)" id="1711565300604-69">
        <addForeignKeyConstraint baseColumnNames="revision_id" baseTableName="audit_log_property_type" constraintName="fkph3vvdjiaedcco923ivk6qkxc" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="audit_log_revision_id" referencedTableName="audit_log_revision" validate="true"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="1711565300604-70">
        <dropUniqueConstraint constraintName="audit_log_property_type_property_type_uuid_key" tableName="audit_log_property_type"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="1711565300604-71">
        <dropUniqueConstraint constraintName="unique_payment_number" tableName="payment"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="1711565300604-72">
        <dropColumn columnName="created_by" tableName="audit_log_scheduler"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="1711565300604-73">
        <dropColumn columnName="created_date" tableName="audit_log_scheduler"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="1711565300604-1">
        <modifyDataType columnName="created_by" newDataType="varchar(255)" tableName="authdotnet_mapping"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="1711565300604-2">
        <modifyDataType columnName="created_by" newDataType="varchar(255)" tableName="payee"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="1711565300604-3">
        <modifyDataType columnName="created_by" newDataType="varchar(255)" tableName="payment"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="1711565300604-4">
        <modifyDataType columnName="created_by" newDataType="varchar(255)" tableName="payment_provider"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="1711565300604-5">
        <modifyDataType columnName="created_by" newDataType="varchar(255)" tableName="property_type"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="1711565300604-6">
        <modifyDataType columnName="created_by" newDataType="varchar(255)" tableName="scheduler"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="1711565300604-7">
        <modifyDataType columnName="created_by" newDataType="varchar(255)" tableName="webhook_inbox"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="1711565300604-8">
        <modifyDataType columnName="created_date" newDataType="timestamp" tableName="property_type"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="1711565300604-9">
        <modifyDataType columnName="created_date" newDataType="timestamp" tableName="scheduler"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="1711565300604-10">
        <addNotNullConstraint columnDataType="timestamp" columnName="created_date" tableName="scheduler" validate="true"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="1711565300604-11">
        <modifyDataType columnName="last_modified_by" newDataType="varchar(255)" tableName="audit_log_property_type"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="1711565300604-12">
        <dropNotNullConstraint columnDataType="varchar(255)" columnName="last_modified_by" tableName="audit_log_property_type"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="1711565300604-13">
        <modifyDataType columnName="last_modified_by" newDataType="varchar(255)" tableName="audit_log_scheduler"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="1711565300604-14">
        <modifyDataType columnName="last_modified_by" newDataType="varchar(255)" tableName="authdotnet_mapping"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="1711565300604-15">
        <modifyDataType columnName="last_modified_by" newDataType="varchar(255)" tableName="payee"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="1711565300604-16">
        <modifyDataType columnName="last_modified_by" newDataType="varchar(255)" tableName="payment"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="1711565300604-17">
        <modifyDataType columnName="last_modified_by" newDataType="varchar(255)" tableName="payment_provider"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="1711565300604-18">
        <modifyDataType columnName="last_modified_by" newDataType="varchar(255)" tableName="property_type"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="1711565300604-19">
        <modifyDataType columnName="last_modified_by" newDataType="varchar(255)" tableName="scheduler"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="1711565300604-20">
        <modifyDataType columnName="last_modified_by" newDataType="varchar(255)" tableName="webhook_inbox"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="1711565300604-21">
        <modifyDataType columnName="last_modified_date" newDataType="timestamp" tableName="audit_log_property_type"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="1711565300604-22">
        <dropNotNullConstraint columnDataType="timestamp" columnName="last_modified_date" tableName="audit_log_property_type"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="1711565300604-23">
        <modifyDataType columnName="last_modified_date" newDataType="timestamp" tableName="audit_log_scheduler"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="1711565300604-24">
        <modifyDataType columnName="last_modified_date" newDataType="timestamp" tableName="property_type"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="1711565300604-25">
        <modifyDataType columnName="last_modified_date" newDataType="timestamp" tableName="scheduler"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="1711565300604-26">
        <addNotNullConstraint columnDataType="timestamp" columnName="last_modified_date" tableName="scheduler" validate="true"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="1711565300604-27">
        <addDefaultValue columnDataType="uuid" columnName="payee_uuid" defaultValueComputed="gen_random_uuid()" tableName="audit_log_payee"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="1711565300604-29">
        <addDefaultValue columnDataType="uuid" columnName="payee_uuid" defaultValueComputed="gen_random_uuid()" tableName="payee"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="1711565300604-78">
        <sql>
            update payee
            set payee_uuid = gen_random_uuid()
            where payee_uuid is null;
        </sql>
    </changeSet>
    <changeSet author="postgres (generated)" id="1711565300604-28">
        <addNotNullConstraint columnDataType="uuid" columnName="payee_uuid" tableName="payee" validate="true"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="1711565300604-30">
        <dropNotNullConstraint columnDataType="varchar(255)" columnName="payment_number" tableName="payment"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="1711565300604-31">
        <addDefaultValue columnDataType="uuid" columnName="payment_provider_uuid" defaultValueComputed="gen_random_uuid()" tableName="audit_log_payment_provider"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="1711565300604-32">
        <addNotNullConstraint columnDataType="uuid" columnName="payment_provider_uuid" tableName="payment_provider" validate="true"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="1711565300604-33">
        <addDefaultValue columnDataType="uuid" columnName="payment_provider_uuid" defaultValueComputed="gen_random_uuid()" tableName="payment_provider"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="1711565300604-34">
        <addDefaultValue columnDataType="uuid" columnName="payment_uuid" defaultValueComputed="gen_random_uuid()" tableName="audit_log_payment"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="1711565300604-36">
        <addDefaultValue columnDataType="uuid" columnName="payment_uuid" defaultValueComputed="gen_random_uuid()" tableName="payment"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="1711565300604-79">
        <sql>
            update payment
            set payment_uuid = gen_random_uuid()
            where payment_uuid is null;
        </sql>
    </changeSet>
    <changeSet author="postgres (generated)" id="1711565300604-35">
        <addNotNullConstraint columnDataType="uuid" columnName="payment_uuid" tableName="payment" validate="true"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="1711565300604-37">
        <modifyDataType columnName="property_name" newDataType="varchar(250)" tableName="audit_log_property_type"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="1711565300604-38">
        <dropNotNullConstraint columnDataType="varchar(250)" columnName="property_name" tableName="audit_log_property_type"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="1711565300604-39">
        <dropDefaultValue columnDataType="varchar(250)" columnName="property_name" tableName="audit_log_property_type"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="1711565300604-40">
        <modifyDataType columnName="property_name" newDataType="varchar(250)" tableName="property_type"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="1711565300604-41">
        <dropDefaultValue columnDataType="varchar(250)" columnName="property_name" tableName="property_type"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="1711565300604-42">
        <dropNotNullConstraint columnDataType="varchar(255)" columnName="property_type" tableName="audit_log_property_type"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="1711565300604-43">
        <dropDefaultValue columnDataType="varchar(255)" columnName="property_type" tableName="audit_log_property_type"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="1711565300604-44">
        <dropDefaultValue columnDataType="varchar(255)" columnName="property_type" tableName="property_type"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="1711565300604-45">
        <dropNotNullConstraint columnDataType="uuid" columnName="property_type_uuid" tableName="audit_log_property_type"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="1711565300604-46">
        <addDefaultValue columnDataType="uuid" columnName="property_type_uuid" defaultValueComputed="gen_random_uuid()" tableName="audit_log_property_type"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="1711565300604-47">
        <addDefaultValue columnDataType="uuid" columnName="property_type_uuid" defaultValueComputed="gen_random_uuid()" tableName="property_type"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="1711565300604-48">
        <modifyDataType columnName="revision_id" newDataType="int" tableName="audit_log_property_type"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="1711565300604-49">
        <dropNotNullConstraint columnDataType="smallint(5)" columnName="revision_type" tableName="audit_log_property_type"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="1711565300604-50">
        <dropNotNullConstraint columnDataType="uuid" columnName="scheduler_uuid" tableName="scheduler"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="1711565300604-51">
        <dropNotNullConstraint columnDataType="varchar(100)" columnName="table_name" tableName="audit_log_property_type"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="1711565300604-52">
        <dropDefaultValue columnDataType="varchar(100)" columnName="table_name" tableName="audit_log_property_type"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="1711565300604-53">
        <dropDefaultValue columnDataType="varchar(100)" columnName="table_name" tableName="property_type"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="1711565300604-54">
        <addDefaultValue columnDataType="uuid" columnName="webhook_inbox_uuid" defaultValueComputed="gen_random_uuid()" tableName="audit_log_webhook_inbox"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="1711565300604-56">
        <addDefaultValue columnDataType="uuid" columnName="webhook_inbox_uuid" defaultValueComputed="gen_random_uuid()" tableName="webhook_inbox"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="1711565300604-80">
        <sql>
            update webhook_inbox
            set webhook_inbox_uuid = gen_random_uuid()
            where webhook_inbox_uuid is null;
        </sql>
    </changeSet>
    <changeSet author="postgres (generated)" id="1711565300604-55">
        <addNotNullConstraint columnDataType="uuid" columnName="webhook_inbox_uuid" tableName="webhook_inbox" validate="true"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="1711565300604-57">
        <addUniqueConstraint columnNames="authdotnet_mapping_uuid" constraintName="uk_p4rjw8mkedhjivn2vxde5on1c" tableName="authdotnet_mapping"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="1711565300604-58">
        <addUniqueConstraint columnNames="table_name, property_name" constraintName="property_type_unique" tableName="property_type"/>
    </changeSet>
</databaseChangeLog>
