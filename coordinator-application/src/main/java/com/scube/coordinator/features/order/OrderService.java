package com.scube.coordinator.features.order;

import com.scube.calculation.dto.order.gen_dto.OrderInvoiceResponse;
import com.scube.client.calculation.generated.CalculationServiceConnection;
import com.scube.coordinator.features.payment.dto.CheckoutDto;
import com.scube.coordinator.features.payment.dto.CoordinatorSubmitPaymentRequestByCartDto;
import com.scube.coordinator.features.payment.dto.CoordinatorSubmitPaymentRequestByOrderDto;
import com.scube.coordinator.features.payment.dto.CoordinatorSubmitPaymentRequestDto;
import lombok.AllArgsConstructor;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.web.server.ResponseStatusException;

import java.math.BigDecimal;
import java.util.UUID;

@Service
@AllArgsConstructor
public class OrderService {
    private final CalculationServiceConnection calculationServiceConnection;

    public OrderInvoiceResponse getOrder(UUID orderId, boolean isMe) {
        OrderInvoiceResponse order = null;
        if (isMe) {
            order = calculationServiceConnection.loggedInUserOrder().getOrder(orderId);
        } else {
            order = calculationServiceConnection.order().getOrder(orderId);
        }
        return order;
    }

    public OrderInvoiceResponse getOrderForPayment(CoordinatorSubmitPaymentRequestByOrderDto paymentRequest, boolean isMe) {
        OrderInvoiceResponse order = null;
        if (isMe) {
            order = calculationServiceConnection.loggedInUserOrder().getOrder(paymentRequest.getOrderId());
        } else {
            order = calculationServiceConnection.order().getOrder(paymentRequest.getOrderId());
        }
        validateOrder(order, paymentRequest.getAmount());
        return order;
    }

    public OrderInvoiceResponse createOrderForPayment(CoordinatorSubmitPaymentRequestByCartDto paymentRequest, boolean isMe) {
        OrderInvoiceResponse order = null;
        if (isMe) {
            order = calculationServiceConnection.loggedInUserOrder().createOrderFromCart(paymentRequest.getCartId());
        } else {
            order = calculationServiceConnection.order().createOrderFromCart(paymentRequest.getCartId());
        }
        validateOrder(order, paymentRequest.getAmount());
        return order;
    }

    public OrderInvoiceResponse createOrderForPayment(CheckoutDto checkoutRequest, boolean isMe) {
        OrderInvoiceResponse order = null;
        if (isMe) {
            order = calculationServiceConnection.loggedInUserOrder().createOrderFromCart(checkoutRequest.getCartId());
        } else {
            order = calculationServiceConnection.order().createOrderFromCart(checkoutRequest.getCartId());
        }
        validateOrder(order, checkoutRequest.getAmount());
        return order;
    }

    public OrderInvoiceResponse createOrderByItemsForPayment(CoordinatorSubmitPaymentRequestDto paymentRequest) {
        var order = calculationServiceConnection.order().createOrderFromItems(paymentRequest.getItems());
        validateOrder(order, paymentRequest.getAmount());
        return order;
    }


    public void rollbackOrderToCart(UUID cartId, UUID orderId, boolean isMe) {
        if (isMe) {
            calculationServiceConnection.loggedInUserOrder().rollBackToCart(cartId, orderId);
        } else {
            calculationServiceConnection.order().rollBackToCart(cartId, orderId);
        }
    }

    public void validateOrder(OrderInvoiceResponse order, BigDecimal amount) {
        // if order is already active throw exception
        if (order.getStatus().equals("orderActive")) {
            throw new ResponseStatusException(HttpStatus.BAD_REQUEST, "Order is already active");
        }

        //If order is not draft or pending throw exception
        if (!order.getStatus().equals("orderDraft")
                && !order.getStatus().equals("orderPending")) {
            throw new ResponseStatusException(HttpStatus.BAD_REQUEST, "Order must be draft or pending to accept payment");
        }

        if (amount.equals(BigDecimal.ZERO)) {
            //If the order total is greater than 0, then payment must be greater than 0
            if (order.getTotal().compareTo(BigDecimal.ZERO) > 0) {
                throw new ResponseStatusException(HttpStatus.BAD_REQUEST, "Payment amount must be greater than 0");
            }
        }
    }
}