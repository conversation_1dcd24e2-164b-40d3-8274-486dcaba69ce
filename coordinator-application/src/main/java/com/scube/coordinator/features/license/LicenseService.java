package com.scube.coordinator.features.license;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.scube.auth.library.ITokenService;
import com.scube.client.calculation.generated.CalculationServiceConnection;
import com.scube.client.documentTemplate.generated.DocumentTemplateServiceConnection;
import com.scube.client.license.generated.LicenseServiceConnection;
import com.scube.coordinator.features.license.dto.GenerateLicenseFormRequest;
import com.scube.coordinator.features.license.dto.LicenseData;
import com.scube.coordinator.features.notification.exceptions.LicenseNotificationDataStringConversionException;
import com.scube.coordinator.features.notification.license_form_notification.GetLicenseDataQuery;
import com.scube.coordinator.features.reports.dto.LicenseProjection;
import com.scube.coordinator.rabbit.license.service.LicenseRabbitService;
import com.scube.rabbit.core.AmqpGateway;
import com.scube.rabbit.core.RabbitResult;
import com.scube.rabbit.core.fanout.publisher.IRabbitFanoutPublisher;
import com.scube.rabbit.core.fanout.publisher.IRabbitFanoutPublisherRpc;
import io.micrometer.common.util.StringUtils;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.lang.NonNull;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;
import org.springframework.util.ObjectUtils;
import org.springframework.web.server.ResponseStatusException;

import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.stream.Collectors;

import static com.scube.coordinator.constants.URIConstants.DOC_STORAGE_URI;
import static org.apache.commons.lang3.ObjectUtils.isEmpty;

@Service
@AllArgsConstructor
@Slf4j
public class LicenseService {
    private final LicenseRabbitService licenseRabbitService;
    private final ObjectMapper objectMapper;
    private final AmqpGateway amqpGateway;
    private final DocumentTemplateServiceConnection documentTemplateServiceConnection;
    private final ITokenService tokenService;
    private final CalculationServiceConnection calculationServiceConnection;
    private final LicenseServiceConnection licenseServiceConnection;

//    public void checkHouseHoldRule(UUID cartId) {
//        var token = tokenService.getNewAccessTokenFromCurrentRealm();
//        var cartItems = calculationServiceConnection.cart().getCartInvoice(cartId, token).getItems().stream()
//                .filter(Objects::nonNull)
//                .filter(ci -> ci.getItemType().equalsIgnoreCase("license"))
//                .map(CartInvoiceItem::getItemId)
//                .toList();
//
//        Map<UUID, Set<HouseHoldLicense>> addressToLicense = new HashMap<>();
//
//        for (var itemId : cartItems) {
//            CheckNotMoreThanThreeLicensesResponse license = licenseServiceConnection.license().getHouseHoldCountByLicense(itemId, token);
//            if (!license.isAllowed()) {
//                throw new ResponseStatusException(HttpStatus.BAD_REQUEST, "Cannot have more than 3 licenses in a household");
//            }
//
//            var houseHoldLicenses = license.getCurrentHouseHoldLicensesByAddress();
//            houseHoldLicenses.forEach(houseHoldAddress ->
//                    addressToLicense.computeIfAbsent(houseHoldAddress.getAddressEntityId(), k -> new HashSet<>()).addAll(houseHoldAddress.getLicenses()));
//
//            // check if the itemId is in the pending list and add it to the addressToLicense map
//            for (HouseHoldAddress address : license.getPendingLicensesByAddress()) {
//                for (HouseHoldLicense lic : address.getLicenses()) {
//                    if (!ObjectUtils.isEmpty(lic.getLicenseEntityId()) && lic.getLicenseEntityId().equals(itemId)) {
//                        addressToLicense.computeIfAbsent(address.getAddressEntityId(), k -> new HashSet<>()).add(lic);
//                    }
//                }
//            }
//        }
//
//        // check if any address has more than 3 licenses
//        addressToLicense.forEach((address, licenses) -> {
//            if (licenses.size() > 3) {
//                throw new ResponseStatusException(HttpStatus.BAD_REQUEST, "Cannot have more than 3 licenses in a household");
//            }
//        });
//    }

    public LicenseData getLicense(UUID entityId) {
        return getLicenseData(licenseRabbitService.getLicense(entityId));
    }

    public List<LicenseData> getLicenses(String entityType, UUID entityId) {
        return licenseRabbitService.getLicenses(entityId, entityType)
                .stream()
                .map(this::getLicenseData)
                .collect(Collectors.toList());
    }

    public LicenseData getLicenseData(LicenseProjection licenseProjection) {
        LicenseData licenseData = LicenseData.of(licenseProjection);

        licenseData.setLicenseDocumentUrl(
                StringUtils.isNotBlank(licenseProjection.getLicenseFormUuid())
                        ? DOC_STORAGE_URI + licenseProjection.getLicenseFormUuid()
                        : null
        );
        licenseData.setActions(getActions(licenseProjection.getEntityId()));
        return licenseData;
    }

    public void generateFormAsync(GenerateLicenseFormRequest request) {
        generateForm(request);
    }

    public String generateForm(GenerateLicenseFormRequest request) {
        var triggerTypes = Map.of(
                "dogLicense", "DogLicenseTemplate",
                "purebredDogLicense", "PurebredDogLicenseTemplate"
        );

        //get the license data thru RPC
        var formattedDataResult = amqpGateway.queryResult(new GetLicenseDataQuery(request.getLicenseEntityId(), true));
        Assert.notNull(formattedDataResult, "License data is null");
        Assert.notNull(formattedDataResult.getResult(), "Formatted data is null");
        var formattedData = formattedDataResult.getResult();

        if (isEmpty(formattedData)) {
            throw new ResponseStatusException(HttpStatus.NOT_FOUND, "License with id " + request.getLicenseEntityId() + " not found");
        }

        if (("Cancelled").equalsIgnoreCase((String) formattedData.get("license_status_code"))) {
            throw new ResponseStatusException(HttpStatus.BAD_REQUEST, "License with id " + request.getLicenseEntityId() + " is canceled");
        }

        var licenseType = formattedData.getOrDefault("license_type_code", "").toString();

        if (ObjectUtils.isEmpty(licenseType)) {
            throw new ResponseStatusException(HttpStatus.BAD_REQUEST, "License type " + licenseType + " is not supported");
        }

        var documentTemplateName = triggerTypes.get(licenseType);

        JsonNode formattedDataString = objectMapper.valueToTree(formattedData);
        if (formattedDataString == null) throw new LicenseNotificationDataStringConversionException();

        generateQrCode(formattedDataString, request.getLicenseEntityId());

        var token = tokenService.getNewAccessTokenFromCurrentRealm();
        var templateResponse = documentTemplateServiceConnection.documentTemplate().fillTemplate(documentTemplateName, formattedDataString, token);
        if (templateResponse == null || templateResponse.getDocumentId() == null)
            throw new ResponseStatusException(HttpStatus.INTERNAL_SERVER_ERROR, "Error while generating document");

        amqpGateway.publish(new DocumentGeneratedEvent(request.getLicenseEntityId(), licenseType, templateResponse.getDocumentId().toString()));

        return templateResponse.getDocumentId().toString();
    }

    public void generateQrCode(@NonNull JsonNode licenseData, @NonNull UUID entityId) {
        GenerateQrCodeCommandResponse result = queryResult(new GenerateQrCodeCommand("license", entityId), RuntimeException::new);
        Assert.notNull(result, "QR code generation failed");
        var bytes = result.bytes();
        Assert.notNull(bytes, "QR code generation failed to return bytes");
        var qrCodeImage = new TemplateImage(bytes);
        // add the qr code image to the license data
        ((ObjectNode) licenseData).putPOJO("qrCode", objectMapper.valueToTree(qrCodeImage));
    }

    public List<String> getActions(UUID entityId) {
        return queryResult(new GetLicenseActionsQuery(entityId), RuntimeException::new)
                .actions();
    }

    private <T, E extends RuntimeException> T queryResult(IRabbitFanoutPublisherRpc<T> request, ExceptionFactory<E> exceptionFactory) {
        RabbitResult<T> rabbitResult = amqpGateway.queryResult(request);

        if (rabbitResult == null) {
            throw exceptionFactory.create("Rabbit result was null");
        } else if (!rabbitResult.isSuccess()) {
            throw exceptionFactory.create("Rabbit RPC failed: " + rabbitResult.getErrorMessage());
        }

        return rabbitResult.getResult();
    }

    @FunctionalInterface
    public interface ExceptionFactory<E extends RuntimeException> {
        E create(String message);
    }

    //@formatter:off
    public record GetLicenseActionsQuery(UUID entityId) implements IRabbitFanoutPublisherRpc<GetLicenseActionsQueryResponse> { }
    public record GetLicenseActionsQueryResponse(List<String> actions) { }
    public record GenerateQrCodeCommandResponse(byte[] bytes) { }
    public record GenerateQrCodeCommand(String entityType, UUID entityId) implements IRabbitFanoutPublisherRpc<GenerateQrCodeCommandResponse>{}
    //@formatter:on

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class DocumentGeneratedEvent implements IRabbitFanoutPublisher {
        private UUID parentId;
        private String parentType;
        private String documentId;
    }

    @Data
    public static class TemplateImage {
        @JsonProperty("_type")
        private String type = "image";

        private Object source;
        private String format = "image/png";
        private int width = 100;
        private int height = 100;

        public TemplateImage(@NonNull byte[] bytes) {
            this.source = bytes;
        }
    }
}