package com.scube.coordinator.features.payment.services;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.scube.auth.library.ITokenService;
import com.scube.calculation.dto.order.gen_dto.OrderInvoiceItem;
import com.scube.calculation.dto.order.gen_dto.OrderInvoiceItemFee;
import com.scube.calculation.dto.order.gen_dto.OrderInvoiceResponse;
import com.scube.coordinator.features.notification.TenantDto;
import com.scube.coordinator.features.notification.TenantService;
import com.scube.coordinator.features.payment.dto.CoordinatorSubmitPaymentRequestDto;
import com.scube.coordinator.features.payment.dto.ReceiptTemplateData;
import com.scube.payment.features.payment.processing.dto.gen_dto.GetPaymentResponseDto;
import com.scube.rabbit.core.AmqpGateway;
import com.scube.rabbit.core.fanout.publisher.IRabbitFanoutPublisher;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.UUID;

import static com.scube.coordinator.features.payment.dto.ReceiptTemplateData.combineDuplicateItems;
import static com.scube.coordinator.util.DateUtil.*;
import static com.scube.coordinator.util.StringUtils.camelCaseToCapitalizedWords;
import static org.apache.commons.lang3.StringUtils.isBlank;

@Service
@AllArgsConstructor
@Slf4j
public class ReceiptService {
    private final AmqpGateway amqpGateway;
    private final ObjectMapper objectMapper;
    private final ITokenService tokenService;
    private final TenantService tenantService;

    public void generateReceipt(GetPaymentResponseDto payment, OrderInvoiceResponse order) {
        CoordinatorSubmitPaymentRequestDto<?> paymentRequest = CoordinatorSubmitPaymentRequestDto.from(payment);
        generateReceipt(paymentRequest, payment.getPaymentId(), order);
    }

    public static BigDecimal toBigDecimal(Object value) {
        if (value instanceof BigDecimal) {
            return (BigDecimal) value;
        } else if (value instanceof Number) {
            return BigDecimal.valueOf(((Number) value).doubleValue());
        } else if (value instanceof String) {
            try {
                return new BigDecimal((String) value);
            } catch (NumberFormatException e) {
                // Handle invalid numeric string
                throw new IllegalArgumentException("Invalid numeric string: " + value);
            }
        } else {
            throw new IllegalArgumentException("Unsupported type: " + value.getClass());
        }
    }

    public void generateReceipt(CoordinatorSubmitPaymentRequestDto<?> payment, UUID paymentId, OrderInvoiceResponse cartInvoice) {
        log.debug("Generating receipt for payment {}", payment);
        //Call DocumentTemplate Service to generate template
        ReceiptTemplateData receiptTemplateData = new ReceiptTemplateData();

        List<ReceiptTemplateData.ReceiptItem> receiptItems = new ArrayList<>();

        /*
         * Dog Licenses can not be combined in the receipt, so they have to be stored in a separate list
         * that won't be combined at the end.
         */
        List<ReceiptTemplateData.ReceiptItem> dogLicenseReceiptItems = new ArrayList<>();

        /*
         * Loop through invoice items and create receipt items
         */
        for (var item : cartInvoice.getItems()) {
            ReceiptTemplateData.ReceiptItem receiptItem = new ReceiptTemplateData.ReceiptItem();
            if (item == null || item.getItemType().equals("ADDITIONAL_FEE")) {
                continue;
            }
            receiptItem.setItem(cleanString(formatItem(item)));
            receiptItem.setUnitPrice(item.getTotal().setScale(2, RoundingMode.CEILING).toString());
            receiptItem.setNote(cleanString(item.getSecondaryDisplay()));
            receiptItem.setUserId(tokenService.getLoggedInUserInfo().getPreferredUsername());
            receiptItem.setQuantity("1");
            receiptItem.setAmount(item.getTotal().setScale(2, RoundingMode.CEILING).toString());

            /*
             * Separate dog licenses from other items
             */
            if (item.getItemType().equals("license")) {
                dogLicenseReceiptItems.add(receiptItem);
            } else {
                receiptItems.add(receiptItem);
            }
        }

        /*
         * Combine duplicate items into a single item with an updated quantity and amount
         */
        receiptItems = combineDuplicateItems(receiptItems);

        receiptItems.addAll(dogLicenseReceiptItems);

        var subTotal = cartInvoice.getSubtotal().setScale(2, RoundingMode.CEILING);
        var discounts = cartInvoice.getItems().stream().map(OrderInvoiceItem::getDiscount)
                .reduce(BigDecimal.ZERO, BigDecimal::add).setScale(2, RoundingMode.CEILING);
        List<Map<String, Object>> additionalFee = new ArrayList<>();
        for (Object obj : cartInvoice.getSummary()) {
            if (obj instanceof Map<?,?>) {
                Map<String, Object> feeMap = (Map<String, Object>) obj;

                BigDecimal priceStr = toBigDecimal(feeMap.get("amount"));
                try {
                    feeMap.put("amount", priceStr.setScale(2, RoundingMode.CEILING).toString());
                } catch (NumberFormatException e) {
                    System.err.println("Invalid price: " + priceStr);
                }
                additionalFee.add(feeMap);
            }
        }
        var total = cartInvoice.getTotal().setScale(2, RoundingMode.CEILING);

        var paymentAmount = payment.getAmount();
        var balance = total.subtract(paymentAmount);

        var payee = ReceiptTemplateData.Payee.of(payment.getPayee());

        receiptTemplateData.setOrderNumber(" " + cartInvoice.getOrderNumber());
        receiptTemplateData.setOrderDate(parseInstant(payment.getTransactionDate(), USA_DATE_FORMAT));
        receiptTemplateData.setOrderTime(parseInstant(payment.getTransactionDate(), USA_TIME_FORMAT));
        receiptTemplateData.setReceiptItems(receiptItems);
        receiptTemplateData.setSubTotal(subTotal.toString());
        receiptTemplateData.setDiscounts(discounts.toString());
        receiptTemplateData.setAdditionalFee(additionalFee);
        receiptTemplateData.setTotal(paymentAmount.setScale(2, RoundingMode.CEILING).toString());
        String paymentReference = (isBlank(payment.getPaymentReference()) ? "" : "\n#" + payment.getPaymentReference());
        receiptTemplateData.setPaymentType(camelCaseToCapitalizedWords(payment.getPaymentType()) + paymentReference);

        receiptTemplateData.setBalance(balance.toString());
        receiptTemplateData.setPayee(List.of(payee));
        receiptTemplateData.setNotes("");

        TenantDto tenant = tenantService.getTenant();
        receiptTemplateData.setMunicipality(new ReceiptTemplateData.Municipality(tenant));

        log.debug("Receipt data: {}", getString(receiptTemplateData));

        amqpGateway.publish(new GenerateDocumentCommand(paymentId,
                "paymentReceipt", "ReceiptTemplate", getString(receiptTemplateData)));
    }

    public String formatItem(OrderInvoiceItem item) {
        if (item == null) {
            return "";
        } else if (item.getItemType().equals("license") && item.getPrimaryDisplay().equals("Dog License")) {
            return formatDogLicense(item);
        } else {
            return cleanString(item.getPrimaryDisplay());
        }
    }

    public String formatDogLicense(OrderInvoiceItem item) {
        List<OrderInvoiceItemFee> fees = item.getFees();
        if (fees == null || fees.isEmpty()) {
            return "Dog License";
        } else if (fees.stream().anyMatch(f -> f.getFeeCode().equals("DL-M-UNALT"))) {
            return "Dog License, Unaltered";
        } else {
            return "Dog License, Altered";
        }
    }

    private static String cleanString(String str) {
        return str == null ? "" : str.trim();
    }

    public JsonNode getString(Object data) {
        log.debug("ReportService.getString()");
        return objectMapper.valueToTree(data);
    }

    @NoArgsConstructor
    @AllArgsConstructor
    @Data
    public static class GenerateDocumentCommand implements IRabbitFanoutPublisher {
        private UUID parentId;
        private String parentType;
        private String nameKey;
        private JsonNode data;
    }
}

