package com.scube.config.app_property.rabbit;

import com.scube.config.app_property.AppPropertyService;
import com.scube.rabbit.core.RabbitResult;
import com.scube.rabbit.core.fanout.subscriber.FanoutListenerRpc;
import com.scube.rabbit.core.fanout.subscriber.IRabbitFanoutSubscriberRpc;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
public class GetPropertyByKeyEventHandler extends FanoutListenerRpc<GetPropertyByKeyEventHandler.GetPropertyByKeyEvent, GetPropertyByKeyEventHandler.GetPropertyByKeyEventResponse> {
    private final AppPropertyService appPropertyService;

    @Override
    public RabbitResult<GetPropertyByKeyEventResponse> consume(GetPropertyByKeyEvent event) {
        return RabbitResult.of(() -> new GetPropertyByKeyEventResponse(appPropertyService.getProperty(event.key())));
    }

    public record GetPropertyByKeyEvent(
            String key) implements IRabbitFanoutSubscriberRpc<GetPropertyByKeyEventResponse> {
    }

    public record GetPropertyByKeyEventResponse(String value) {
    }
}