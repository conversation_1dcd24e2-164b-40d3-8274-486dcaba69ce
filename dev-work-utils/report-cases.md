Based on the code excerpts, here are all the dependencies and usages of License Service in the Report Service:

## 1. **Direct License Service Connections**

### License Service HTTP Client:
````java path=report-application/src/main/java/com/scube/report/features/licensing/dog/service/AdhocLicenseReportService.java mode=EXCERPT
@Service("AdhocLicenseReport")
@RequiredArgsConstructor
public class AdhocLicenseReportService implements IReportQueryService {
    private final LicenseServiceConnection licenseService;
    
    var result = licenseService.report().getAdhocReportAsync(new ReportQueryRequest(request.getSql(), request.getParams()));
}
````

## 2. **Report Service Dependencies**

### Dog License Renewal Notice:
````java path=report-application/src/main/java/com/scube/report/features/licensing/dog/service/DelinquencyNoticeService.java mode=EXCERPT
var result = licenseService.report().getDogLicenseRenewalNoticeAsync(new ReportQueryRequest(sql, sqlParams));

List<Map<String, Object>> licenses = result.collectList().block();
````

### Online Announcement Letter:
````java path=report-application/src/main/java/com/scube/report/features/licensing/dog/service/OnlineAnnouncementLetterService.java mode=EXCERPT
var result = licenseService.report().getDogLicenseOnlineAnnouncementLetterAsync(new ReportQueryRequest(sql, sqlParams));

List<Map<String, Object>> licenses = result.collectList().block();
````

## 3. **RabbitMQ License Queries**

### License Projection Queries:
````java path=report-application/src/main/java/com/scube/report/features/licensing/dog/service/DogTransactionReportService.java mode=EXCERPT
//Retrieve the dog license information from license service
LicenseProjection dogLicense = RabbitMqUtil.query(new GetLicenseProjectionQuery(item.getItemId().toString()));
List<String> licenseActivities = RabbitMqUtil.query(new GetLicenseActivitiesByOrderIdQuery(order.getOrderId().toString())).licenseActivities();
````

### Delinquent License Queries:
````java path=report-application/src/main/java/com/scube/report/features/licensing/dog/service/DelinquentDogReportService.java mode=EXCERPT
//call license service to retrieve delinquent licenses
var rabbitResult = amqpGateway.queryResult(new GetDelinquentLicensesQuery((String) params.get("startDate"), (String) params.get("endDate")));

if (rabbitResult == null) {
    throw new ResponseStatusException(HttpStatus.INTERNAL_SERVER_ERROR,
            "Failed to retrieve delinquent licenses: License Service result was null");
}
````

## 4. **License Data Processing**

### License Report Data Query:
````java path=license-application/src/main/java/com/scube/licensing/features/license/reports/GetReportLicenseDataQueryListener.java mode=EXCERPT
public RabbitResult<ReportLicenseDataResponseDto> consume(GetReportLicenseDataQuery event) {
    return RabbitResult.of(() -> {
        List<ResponseDataDto> responseData = new ArrayList<>();
        
        for (var requestData : event.request().requestData()) {
            Optional<License> license = licenseRepository.findByUuid(requestData.item().getItemId());
        }
    });
}
````

## 5. **Database View Dependencies**

### License Report Views:
````sql path=license-application/src/main/resources/seed/sql/10_view_license_for_reports.sql mode=EXCERPT
CREATE OR REPLACE VIEW view_license_for_reports AS
WITH PivotCTE AS (
    SELECT
        l.license_id,
        l.license_uuid as entity_id,
        l.license_number,
        l.properties,
        DATE(l.valid_from_date) as valid_from_date,
        DATE(l.valid_to_date) as valid_to_date,
        l.issued_date as licenseIssuedDate,
        ls.name AS license_status,
        lt.name AS license_type_name,
        lt.code AS license_type_code
    FROM license l
    JOIN license_status ls ON ls.license_status_id = l.license_status_id
    JOIN license_type lt ON lt.license_type_id = l.license_type_id
````

### Pivoted License View:
````sql path=report-application/src/main/java/com/scube/report/features/licensing/dog/service/DelinquencyNoticeService.java mode=EXCERPT
SELECT * FROM license.view_pivoted_license 
WHERE license_status_code = 'EXPIRED' 
AND DATE(valid_to_date) BETWEEN DATE(?) AND DATE(?)
AND "owner.paperless" = false
ORDER by license_type_code, license_number
````

## 6. **Report Service Methods**

### License Service Report Methods:
````java path=license-application/src/main/java/com/scube/licensing/features/license/reports/service/ReportService.java mode=EXCERPT
public List<Map<String, Object>> getLicenseRenewalForm(ReportQueryRequest request, boolean isResident) {
    List<Map<String, Object>> licenseData = licenseService.getLicenseDataDynamic(request.getSql(), request.getParams());
    licenseData = mergeLicenses(licenseData);
    return licenseData;
}

public List<Map<String, Object>> getDogLicenseOnlineAnnouncementLetter(ReportQueryRequest query) {
    return licenseService.getLicenseDataDynamic(query.getSql(), query.getParams());
}

public List<Map<String, Object>> getAdhocReport(ReportQueryRequest query) {
    return licenseService.getLicenseDataDynamic(query.getSql(), query.getParams());
}
````

## 7. **QR Code Generation Dependencies**

### License QR Code Generation:
````java path=report-application/src/main/java/com/scube/report/features/licensing/dog/service/DelinquencyNoticeService.java mode=EXCERPT
for (var licenseEntityId : licenseEntityIds.keySet()) {
    commands.add(createQrCodeCommand("license", licenseEntityId, "lookup"));
    commands.add(createQrCodeCommand("license", licenseEntityId, "renew"));
}

switch (request.getEntityType().toLowerCase()) {
    case "license" -> {
        var license = licenseEntityIds.get(entityId);
        switch (request.getAction().toLowerCase()) {
            case "lookup" -> license.put("licenseQrCode", generateQrCode(response, 60, 60));
            case "renew" -> license.put("renewQrCode", generateQrCode(response, 80, 80));
        }
    }
}
````

## **Key Report Types Using License Service:**

1. **DogLicenseMonthlyReport** - Basic report structure
2. **ClerksMonthlyReport** - License and tag order reports  
3. **DelinquencyNoticeService** - Expired license notifications
4. **DelinquentDogReportService** - Delinquent license queries
5. **DogTransactionReportService** - License transaction reports
6. **OnlineAnnouncementLetterService** - Active license announcements
7. **AdhocLicenseReportService** - Custom SQL license reports

## **Impact of Service_Record Migration:**

When migrating to Service_Record, you'll need to:
- Update all `LicenseServiceConnection` references to `RecordServiceConnection`
- Modify database views to use generic record tables instead of license-specific tables
- Update RabbitMQ queries from license-specific to record-specific queries
- Change QR code generation from `"license"` entity type to generic record types
- Update all SQL queries to use record tables instead of license tables
- Modify report data processing to handle generic record structures

The report service has the most extensive integration with license service, using both HTTP connections and RabbitMQ queries extensively.
