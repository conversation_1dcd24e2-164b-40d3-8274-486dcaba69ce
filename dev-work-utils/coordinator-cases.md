Based on the code excerpts, here are all the dependencies and usages of License Service responses in the Coordinator Service:

## 1. **LicenseService (Coordinator)**

### Direct License Service Calls:
````java path=coordinator-application/src/main/java/com/scube/coordinator/features/license/LicenseService.java mode=EXCERPT
public LicenseData getLicense(UUID entityId) {
    return getLicenseData(licenseRabbitService.getLicense(entityId));
}

public List<LicenseData> getLicenses(String entityType, UUID entityId) {
    return licenseRabbitService.getLicenses(entityId, entityType)
            .stream()
            .map(this::getLicenseData)
            .collect(Collectors.toList());
}
````

### License Data Processing:
````java path=coordinator-application/src/main/java/com/scube/coordinator/features/license/LicenseService.java mode=EXCERPT
public LicenseData getLicenseData(LicenseProjection licenseProjection) {
    LicenseData licenseData = LicenseData.of(licenseProjection);
    
    licenseData.setLicenseDocumentUrl(
        StringUtils.isNotBlank(licenseProjection.getLicenseFormUuid())
            ? DOC_STORAGE_URI + licenseProjection.getLicenseFormUuid()
            : null
    );
    licenseData.setActions(getActions(licenseProjection.getEntityId()));
    return licenseData;
}
````

## 2. **LicenseRabbitService**

### RPC Queries to License Service:
````java path=coordinator-application/src/main/java/com/scube/coordinator/rabbit/license/service/LicenseRabbitService.java mode=EXCERPT
public LicenseProjection getLicense(UUID entityId) {
    var rabbitResult = amqpGateway.queryResult(new GetLicenseProjectionQuery(entityId));
    return rabbitResult.getResult();
}

public List<LicenseProjection> getLicenses(UUID entityId, String entityType) {
    var rabbitResult = amqpGateway.queryResult(new GetLicenseIdByParticipantQuery(entityId, entityType));
    return rabbitResult.getResult().getLicenses();
}
````

## 3. **PaymentService**

### License Service Connection:
````java path=coordinator-application/src/main/java/com/scube/coordinator/features/payment/services/PaymentService.java mode=EXCERPT
import com.scube.client.license.generated.LicenseServiceConnection;
import com.scube.coordinator.features.license.LicenseService;
````

## 4. **DemoDataService**

### HTTP Exchange with License Service:
````java path=coordinator-application/src/main/java/com/scube/coordinator/features/demo_data/DemoDataService.java mode=EXCERPT
CreateLicenseResponse license = createLicense(faker, participant.getEntityId());
makePaymentOnLicense(faker, license.getLicenseEntityId());
````

### HTTP Exchange Interface:
````java path=coordinator-application/src/main/java/com/scube/coordinator/features/demo_data/LicenseServiceHttpExchange.java mode=EXCERPT
@PostExchange(value = "/license/create", contentType = "multipart/form-data")
CreateLicenseResponse createLicense(@RequestParam UUID participantId,
                                   @RequestParam String licenseType,
                                   @RequestBody LinkedMultiValueMap<String, String> fields,
                                   @RequestParam Integer duration);
````

## 5. **ParticipantService (Coordinator)**

### License Service Connection:
````java path=coordinator-application/src/main/java/com/scube/coordinator/features/participant/ParticipantService.java mode=EXCERPT
private final LicenseServiceConnection licenseServiceConnection;

public JsonNode getParticipant(UUID participantEntityId) {
    var token = tokenService.getNewAccessTokenFromCurrentRealm();
    return licenseServiceConnection.profile().getIndividualEntityAndAllAssociations(participantEntityId, token);
}
````

## 6. **Document Generation Dependencies**

### License Form Generation:
````java path=coordinator-application/src/main/java/com/scube/coordinator/features/license/LicenseService.java mode=EXCERPT
var triggerTypes = Map.of(
    "dogLicense", "DogLicenseTemplate",
    "purebredDogLicense", "PurebredDogLicenseTemplate"
);

var formattedDataResult = amqpGateway.queryResult(new GetLicenseDataQuery(request.getLicenseEntityId(), true));
````

## 7. **Controller Dependencies**

### License Controller:
````java path=coordinator-application/src/main/java/com/scube/coordinator/features/license/LoggedInUserLicenseController.java mode=EXCERPT
@PostMapping("generate-form")
public Map<String, String> generateForm(@RequestBody @Valid GenerateLicenseFormRequest request) {
    var documentId = licenseService.generateForm(request);
    return Map.of("url", documentId);
}
````

## **Key Response Types Used:**
- `LicenseProjection`
- `CreateLicenseResponse` 
- `LicenseData`
- `GetLicenseDataQuery` results
- `GetLicenseActionsQuery` results
- Document generation responses

All these dependencies will need to be updated to work with your generic Service_Record structure.